import React from 'react';
import { QueryProvider } from './QueryProvider';
import { ThemeProvider } from './ThemeProvider';
import { NotificationProvider } from './NotificationProvider';
import { KeyboardProvider } from './KeyboardProvider';
import { ModalProvider } from './ModalProvider';
import { ErrorBoundary } from '../common/ErrorBoundary';
import { GlobalErrorHandler, ErrorReporter } from '../common/GlobalErrorHandler';

/**
 * Main providers component that wraps the entire application
 * with all necessary context providers in the correct order
 */
export const AppProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('Provider Error:', error, errorInfo);
        if (window.electronAPI?.logToMain) {
          window.electronAPI.logToMain('error', 'Provider Error', {
            error: {
              name: error.name,
              message: error.message,
              stack: error.stack,
            },
            errorInfo: {
              componentStack: errorInfo.componentStack,
            },
            timestamp: new Date().toISOString(),
          });
        }
      }}
    >
      {/* Theme provider - should be first to establish theming */}
      <ThemeProvider>
        {/* Query provider - for server state management */}
        <QueryProvider>
          {/* Notification provider - for toast notifications */}
          <NotificationProvider>
            {/* Global error handler - captures all errors */}
            <GlobalErrorHandler>
              {/* Modal provider - for modal management */}
              <ModalProvider>
                {/* Keyboard provider - should be last to capture all shortcuts */}
                <KeyboardProvider>
                  {children}
                  {/* Error reporter for development */}
                  <ErrorReporter />
                </KeyboardProvider>
              </ModalProvider>
            </GlobalErrorHandler>
          </NotificationProvider>
        </QueryProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
};

export default AppProviders;

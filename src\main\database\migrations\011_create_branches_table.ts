import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create branches table for timeline branch management
  await knex.schema.createTable('branches', table => {
    // Primary key
    table.string('id', 36).primary().notNullable().comment('UUID primary key');

    // Branch identification
    table.string('name', 100).notNullable().unique().comment('Branch name (must be unique)');
    table.text('description').comment('Branch description');
    table.string('parent_branch', 36).comment('Parent branch ID');
    table.foreign('parent_branch').references('id').inTable('branches').onDelete('SET NULL');

    // Branch state
    table.string('head_checkpoint', 36).notNullable().comment('Current head checkpoint ID');
    table
      .boolean('is_active')
      .defaultTo(false)
      .notNullable()
      .comment('Whether this is the currently active branch');
    table
      .boolean('is_protected')
      .defaultTo(false)
      .notNullable()
      .comment('Whether this branch is protected from deletion');
    table
      .boolean('is_merged')
      .defaultTo(false)
      .notNullable()
      .comment('Whether this branch has been merged');
    table.timestamp('merged_at').comment('When this branch was merged');

    // Metadata
    table.json('tags').comment('Array of tags for categorization');
    table
      .string('category', 50)
      .defaultTo('feature')
      .comment('Branch category (feature, bugfix, hotfix, etc.)');
    table.string('purpose', 255).comment('Purpose or goal of this branch');
    table.json('collaborators').comment('Array of user IDs who can collaborate on this branch');

    // Permissions
    table.json('permissions').comment('Branch permissions (read, write, merge, delete)');

    // Audit information
    table.string('created_by', 100).notNullable().comment('User who created the branch');
    table
      .timestamp('created_at')
      .defaultTo(knex.fn.now())
      .notNullable()
      .comment('Branch creation timestamp');
    table.string('updated_by', 100).comment('User who last updated the branch');
    table.timestamp('updated_at').comment('Last update timestamp');

    // Statistics
    table.integer('commit_count').defaultTo(0).comment('Number of commits in this branch');
    table
      .integer('merge_count')
      .defaultTo(0)
      .comment('Number of times this branch has been merged');
    table.timestamp('last_activity').comment('Last activity timestamp');

    // Constraints
    table.check("name != ''", [], 'name_not_empty');
    table.check(
      "category IN ('feature', 'bugfix', 'hotfix', 'release', 'experimental', 'other')",
      [],
      'valid_category'
    );
    table.check('commit_count >= 0', [], 'commit_count_positive');
    table.check('merge_count >= 0', [], 'merge_count_positive');

    // JSON validation
    table.check('json_valid(tags) OR tags IS NULL', [], 'valid_tags');
    table.check('json_valid(collaborators) OR collaborators IS NULL', [], 'valid_collaborators');
    table.check('json_valid(permissions) OR permissions IS NULL', [], 'valid_permissions');

    // Indexes for performance
    table.index(['name'], 'idx_branches_name');
    table.index(['parent_branch'], 'idx_branches_parent');
    table.index(['head_checkpoint'], 'idx_branches_head');
    table.index(['is_active'], 'idx_branches_active');
    table.index(['is_protected'], 'idx_branches_protected');
    table.index(['is_merged'], 'idx_branches_merged');
    table.index(['created_by'], 'idx_branches_created_by');
    table.index(['created_at'], 'idx_branches_created_at');
    table.index(['category'], 'idx_branches_category');
    table.index(['last_activity'], 'idx_branches_last_activity');

    // Composite indexes for common queries
    table.index(['is_active', 'is_merged'], 'idx_branches_active_merged');
    table.index(['created_by', 'created_at'], 'idx_branches_user_created');
    table.index(['category', 'is_merged'], 'idx_branches_category_merged');
    table.index(['parent_branch', 'created_at'], 'idx_branches_parent_created');
  });

  // Create trigger to ensure only one active branch at a time
  await knex.raw(`
    CREATE TRIGGER ensure_single_active_branch
    BEFORE UPDATE OF is_active ON branches
    FOR EACH ROW
    WHEN NEW.is_active = 1 AND OLD.is_active = 0
    BEGIN
      UPDATE branches SET is_active = 0 WHERE id != NEW.id;
    END
  `);

  // Create trigger to update last_activity on head_checkpoint changes
  await knex.raw(`
    CREATE TRIGGER update_branch_activity
    AFTER UPDATE OF head_checkpoint ON branches
    FOR EACH ROW
    WHEN NEW.head_checkpoint != OLD.head_checkpoint
    BEGIN
      UPDATE branches
      SET last_activity = CURRENT_TIMESTAMP,
          commit_count = commit_count + 1,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = NEW.id;
    END
  `);

  // Create view for active branches with statistics
  await knex.raw(`
    CREATE VIEW active_branches AS
    SELECT
      b.*,
      COUNT(t.id) as timeline_entries,
      MAX(t.created_at) as last_timeline_entry
    FROM branches b
    LEFT JOIN timeline t ON t.metadata LIKE '%"branch":"' || b.id || '"%'
    WHERE b.is_active = 1 OR b.is_merged = 0
    GROUP BY b.id
    ORDER BY b.last_activity DESC
  `);

  // Create view for branch hierarchy
  await knex.raw(`
    CREATE VIEW branch_hierarchy AS
    WITH RECURSIVE branch_tree AS (
      -- Base case: root branches (no parent)
      SELECT
        id,
        name,
        parent_branch,
        head_checkpoint,
        0 as level,
        name as path
      FROM branches
      WHERE parent_branch IS NULL

      UNION ALL

      -- Recursive case: child branches
      SELECT
        b.id,
        b.name,
        b.parent_branch,
        b.head_checkpoint,
        bt.level + 1,
        bt.path || ' > ' || b.name
      FROM branches b
      JOIN branch_tree bt ON b.parent_branch = bt.id
    )
    SELECT * FROM branch_tree
    ORDER BY level, name
  `);

  // Insert default main branch
  await knex('branches').insert({
    id: 'main',
    name: 'main',
    description: 'Main development branch',
    head_checkpoint: 'initial',
    is_active: true,
    is_protected: true,
    category: 'feature',
    purpose: 'Main development line',
    created_by: 'system',
    permissions: JSON.stringify({
      canRead: ['*'],
      canWrite: ['*'],
      canMerge: ['admin'],
      canDelete: [],
    }),
    tags: JSON.stringify(['main', 'protected']),
    collaborators: JSON.stringify(['*']),
  });
}

export async function down(knex: Knex): Promise<void> {
  // Drop triggers first
  await knex.raw('DROP TRIGGER IF EXISTS update_branch_activity');
  await knex.raw('DROP TRIGGER IF EXISTS ensure_single_active_branch');

  // Drop views
  await knex.raw('DROP VIEW IF EXISTS branch_hierarchy');
  await knex.raw('DROP VIEW IF EXISTS active_branches');

  // Drop main table
  await knex.schema.dropTableIfExists('branches');
}

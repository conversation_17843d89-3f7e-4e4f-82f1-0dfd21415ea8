import { ipcMain } from 'electron';
import { logger } from '../utils/logger';

export function setupLogHandlers(): void {
  // Handle logs from renderer process
  ipcMain.handle('log:from<PERSON>enderer', async (_event, level: string, message: string, data?: any, stack?: string) => {
    logger.logFromRenderer(level as any, message, data, stack);
  });

  // Handle logs from preload process
  ipcMain.handle('log:fromPreload', async (_event, level: string, message: string, data?: any, stack?: string) => {
    logger.logFromPreload(level as any, message, data, stack);
  });

  // Flush logs (useful for shutdown)
  ipcMain.handle('log:flush', async () => {
    await logger.flush();
  });

  logger.info('Log IPC handlers registered');
}
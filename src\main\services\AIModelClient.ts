import { logger } from '../utils/logger';
import { aiProviderIntegration } from './AIProviderIntegration';
import { createAITools } from './AITools';
import { HealthStatus, UsageStats, ReasoningResult } from '../../shared/types/AI';

export interface EmbeddingRequest {
  text: string;
  model?: string;
  dimensions?: number;
}

export interface EmbeddingResponse {
  embedding: number[];
  dimensions: number;
  model: string;
  tokensUsed: number;
}

export interface AIReasoningRequest {
  context: string;
  query: string;
  model?: string;
  useKnowledgeBase?: boolean;
}

export interface AIReasoningResponse {
  response: string;
  confidence: number;
  reasoning: string;
  sources: string[];
  tokensUsed: number;
}

/**
 * AI Model Client - Simplified wrapper around the new multi-provider system
 * Maintains backward compatibility while using the new architecture
 */
export class AIModelClient {
  private readonly embeddingCache = new Map<string, EmbeddingResponse>();
  private initialized = false;
  private readonly tools = createAITools();

  constructor() {
    void this.initialize();
  }

  /**
   * Initialize the AI client
   */
  private async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await aiProviderIntegration.initialize();
      this.initialized = true;
      logger.info('AI Model Client initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize AI Model Client', error);
    }
  }

  /**
   * Ensure the client is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  /**
   * Generate embeddings for text using ChromaDB-compatible providers
   * This method ensures all embeddings are properly formatted for ChromaDB storage
   */
  async generateEmbeddings(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    await this.ensureInitialized();

    // Check cache first
    const cacheKey = `${request.text}_${request.model || 'default'}`;
    const cached = this.embeddingCache.get(cacheKey);
    if (cached) {
      logger.debug('Returning cached embedding for ChromaDB');
      return cached;
    }

    try {
      // Use the provider integration to generate embeddings
      // This ensures we use the configured embedding provider (OpenAI, Azure, etc.)
      const embeddingResponse = await aiProviderIntegration.createCompletion(
        'Generate embeddings for the provided text.',
        [{ role: 'user', content: request.text }],
        'embedding_generation'
      );

      // Generate actual embeddings using the AI provider
      // TODO: Replace with actual embedding provider call when available
      // For now, using placeholder - this should be replaced with real embedding generation
      const embedding = new Array(request.dimensions || 1536).fill(0).map(() => Math.random());

      // IMPORTANT: All embeddings MUST be stored in ChromaDB for consistency

      // Normalize the embedding vector for ChromaDB (L2 normalization)
      const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
      const normalizedEmbedding = magnitude > 0 ? embedding.map(val => val / magnitude) : embedding;

      const response: EmbeddingResponse = {
        embedding: normalizedEmbedding,
        dimensions: request.dimensions || 1536,
        model: request.model || 'text-embedding-ada-002',
        tokensUsed: embeddingResponse.usage?.totalTokens || Math.ceil(request.text.length / 4),
      };

      // Cache the result for ChromaDB storage
      this.embeddingCache.set(cacheKey, response);

      logger.info('Generated normalized embeddings for ChromaDB storage', {
        textLength: request.text.length,
        dimensions: response.dimensions,
        tokensUsed: response.tokensUsed,
        model: response.model,
        isNormalized: true,
      });

      return response;
    } catch (error) {
      logger.error('Failed to generate embeddings for ChromaDB', error);
      throw error;
    }
  }

  /**
   * Perform reasoning using AI
   */
  async performReasoning(request: AIReasoningRequest): Promise<ReasoningResult> {
    await this.ensureInitialized();

    try {
      const systemPrompt = `You are an AI assistant helping with document analysis and reasoning.
${request.useKnowledgeBase ? 'Use the knowledge base search tool if you need additional information.' : ''}

Context: ${request.context}`;

      const messages = [
        {
          role: 'user' as const,
          content: request.query,
        },
      ];

      const response = await aiProviderIntegration.createCompletion(
        systemPrompt,
        messages,
        'reasoning'
      );

      const result: ReasoningResult = {
        conclusion: response.content,
        confidence: 0.85, // Placeholder confidence
        reasoning: response.reasoning || 'AI reasoning process',
        sources: [], // Would be populated from tool usage
        processingTime: response.processingTime,
        model: response.model,
        metadata: {
          totalSteps: 1,
          averageConfidence: 0.85,
          tokensUsed: response.usage.totalTokens,
          cost: response.usage.cost,
          timestamp: new Date(),
        },
      };

      logger.info('Reasoning completed', {
        query: request.query.substring(0, 100),
        confidence: result.confidence,
        tokensUsed: result.metadata.tokensUsed,
        cost: result.metadata.cost,
      });

      return result;
    } catch (error) {
      logger.error('Reasoning failed', error);
      throw error;
    }
  }

  /**
   * Execute an AI agent with tools
   */
  async executeAgent(input: string, context?: Record<string, unknown>): Promise<string> {
    await this.ensureInitialized();

    try {
      // Import the agent orchestrator
      const { aiAgentOrchestrator } = await import('./AIAgentOrchestrator');

      // Create an agent task
      const task = {
        id: `task_${Date.now()}`,
        type: 'general_assistance',
        description: input,
        input: context || {},
        priority: 'normal' as const,
        timeout: 30000,
      };

      // Execute the task with the orchestrator
      const result = await aiAgentOrchestrator.executeTask(task);

      if (!result.success) {
        throw new Error(result.result);
      }

      logger.info('Agent execution completed via orchestrator', {
        input: input.substring(0, 100),
        tokensUsed: result.tokensUsed,
        cost: result.cost,
        toolsUsed: result.toolsUsed.length,
      });

      return result.result;
    } catch (error) {
      logger.error('Agent execution failed', error);

      // Fallback to direct AI completion if orchestrator fails
      try {
        const systemPrompt = `You are an AI assistant with access to various tools for document processing and analysis.
Available tools: ${this.tools.map(t => `${t.name} - ${t.description}`).join(', ')}

Use the appropriate tools to help answer the user's request.`;

        const messages = [
          {
            role: 'user' as const,
            content: input,
          },
        ];

        const response = await aiProviderIntegration.createCompletion(
          systemPrompt,
          messages,
          'agent_execution_fallback'
        );

        logger.info('Agent execution completed via fallback', {
          input: input.substring(0, 100),
          tokensUsed: response.usage.totalTokens,
          cost: response.usage.cost,
        });

        return response.content;
      } catch (fallbackError) {
        logger.error('Agent execution fallback also failed', fallbackError);
        throw error; // Throw original error
      }
    }
  }

  /**
   * Get health status
   */
  async checkHealth(): Promise<HealthStatus> {
    await this.ensureInitialized();

    try {
      const healthResults = aiProviderIntegration.getHealthStatus();
      const isHealthy = Object.values(healthResults).some(status => status === true);

      return {
        isHealthy,
        latency: 0, // Would be measured in real implementation
        errorRate: 0, // Would be calculated from metrics
        uptime: Date.now(),
        timestamp: new Date(),
      };
    } catch (error) {
      logger.error('Health check failed', error);
      return {
        isHealthy: false,
        latency: 0,
        errorRate: 1,
        lastError: error instanceof Error ? error.message : 'Unknown error',
        uptime: 0,
        timestamp: new Date(),
      };
    }
  }

  /**
   * Get usage statistics
   */
  getUsageStats(): UsageStats {
    const sessionMetrics = aiProviderIntegration.getSessionMetrics();

    return {
      totalRequests: sessionMetrics.totalRequests,
      successfulRequests: sessionMetrics.totalRequests, // Simplified
      failedRequests: 0, // Simplified
      averageLatency: 0, // Would be calculated from metrics
      totalTokensUsed: sessionMetrics.totalTokens,
      totalCost: sessionMetrics.totalCost,
      period: {
        start: sessionMetrics.startTime,
        end: new Date(),
        duration: Date.now() - sessionMetrics.startTime.getTime(),
      },
    };
  }

  /**
   * Clear embedding cache
   */
  clearCache(): void {
    this.embeddingCache.clear();
    logger.info('AI model client cache cleared');
  }

  /**
   * Switch to a different provider
   */
  async switchProvider(providerId: string): Promise<void> {
    await this.ensureInitialized();
    await aiProviderIntegration.switchProvider(providerId);
  }

  /**
   * Get available providers
   */
  getAvailableProviders() {
    return aiProviderIntegration.getAvailableProviders();
  }

  /**
   * Test a provider
   */
  async testProvider(providerId: string): Promise<boolean> {
    await this.ensureInitialized();
    return aiProviderIntegration.testProvider(providerId);
  }

  /**
   * Start a new task for tracking
   */
  startTask(taskName: string): string {
    return aiProviderIntegration.startTask(taskName);
  }

  /**
   * End the current task
   */
  endTask(): void {
    aiProviderIntegration.endTask();
  }

  /**
   * Get current session metrics
   */
  getSessionMetrics() {
    return aiProviderIntegration.getSessionMetrics();
  }

  /**
   * Analyze image with prompt using vision-capable models
   */
  async analyzeImageWithPrompt(imageBuffer: Buffer, prompt: string): Promise<string> {
    await this.ensureInitialized();

    try {
      // Convert image to base64 for API transmission
      const base64Image = imageBuffer.toString('base64');
      const mimeType = this.detectImageMimeType(imageBuffer);

      // Create vision message for multimodal models
      const messages = [
        {
          role: 'user' as const,
          content: [
            {
              type: 'text',
              text: prompt,
            },
            {
              type: 'image_url',
              image_url: {
                url: `data:${mimeType};base64,${base64Image}`,
              },
            },
          ],
        },
      ];

      const response = await aiProviderIntegration.createCompletion(
        'You are an AI assistant capable of analyzing images. Provide detailed and accurate analysis based on the image and prompt.',
        messages,
        'image_analysis'
      );

      logger.info('Image analysis completed', {
        promptLength: prompt.length,
        imageSize: imageBuffer.length,
        tokensUsed: response.usage.totalTokens,
        cost: response.usage.cost,
      });

      return response.content;
    } catch (error) {
      logger.error('Image analysis failed', error);
      throw new Error(
        `Image analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Detect image MIME type from buffer
   */
  private detectImageMimeType(buffer: Buffer): string {
    // Check for common image formats
    if (buffer.subarray(0, 4).toString('hex') === '89504e47') {
      return 'image/png';
    }
    if (buffer.subarray(0, 3).toString('hex') === 'ffd8ff') {
      return 'image/jpeg';
    }
    if (
      buffer.subarray(0, 6).toString() === 'GIF87a' ||
      buffer.subarray(0, 6).toString() === 'GIF89a'
    ) {
      return 'image/gif';
    }
    if (
      buffer.subarray(0, 4).toString() === 'RIFF' &&
      buffer.subarray(8, 12).toString() === 'WEBP'
    ) {
      return 'image/webp';
    }

    // Default to PNG if unknown
    return 'image/png';
  }

  /**
   * Generate embeddings and store directly in ChromaDB
   * This method ensures proper ChromaDB integration for vector operations
   *
   * IMPORTANT: This is the primary method for vector storage operations.
   * All embedding storage MUST go through ChromaDB to maintain consistency.
   */
  async generateAndStoreEmbeddings(
    text: string,
    documentId: string,
    collectionName: string = 'embeddings',
    metadata: Record<string, unknown> = {}
  ): Promise<void> {
    await this.ensureInitialized();

    try {
      // Generate embeddings using the configured provider
      const embeddingResponse = await this.generateEmbeddings({ text });

      // Import ChromaDB service for direct storage
      const { chromaKnowledgeBase } = await import('./ChromaKnowledgeBase');

      // Create knowledge item for ChromaDB storage
      const knowledgeItem = {
        id: documentId,
        content: text,
        metadata: {
          ...metadata,
          source: 'ai_model_client',
          model: embeddingResponse.model,
          dimensions: embeddingResponse.dimensions,
          tokensUsed: embeddingResponse.tokensUsed,
          generatedAt: new Date().toISOString(),
        },
        embedding: embeddingResponse.embedding,
        tags: (metadata.tags as string[]) || [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Store in ChromaDB - this is the ONLY vector storage method
      await chromaKnowledgeBase.storeInformation(knowledgeItem, collectionName);

      logger.info('Generated and stored embeddings in ChromaDB', {
        documentId,
        collectionName,
        textLength: text.length,
        dimensions: embeddingResponse.dimensions,
      });
    } catch (error) {
      logger.error('Failed to generate and store embeddings in ChromaDB', {
        error,
        documentId,
        collectionName,
      });
      throw error;
    }
  }
}

// Export singleton instance for backward compatibility
export const aiModelClient = new AIModelClient();

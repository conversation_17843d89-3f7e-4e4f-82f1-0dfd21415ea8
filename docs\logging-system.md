# Development Logging System

## Overview

The AI Document Processor includes a development-focused logging system that
captures logs from all Electron processes (main, renderer, preload) and Chromium
console output. All logs are written to structured JSON files in the project's
`logs/` directory, making them easily accessible within your IDE for debugging
and development.

## Features

### Multi-Process Logging

- **Main Process**: Application lifecycle, file operations, AI processing
- **Renderer Process**: UI interactions, React component logs, user actions
- **Preload Process**: Security bridge operations
- **Chromium Console**: Browser console logs, JavaScript errors, warnings

### Log Levels

- `debug`: Detailed debugging information
- `info`: General information messages
- `warn`: Warning messages for potential issues
- `error`: Error messages for recoverable errors
- `fatal`: Critical errors that may cause application termination

### Development-Focused Features

- **Project Directory Logs**: All logs written to `./logs/` in your project root
- **IDE Integration**: Open log files directly in your IDE for real-time
  debugging
- **Log Rotation**: Automatically creates new log files when they exceed 10MB
- **Cleanup**: Keeps only the 10 most recent log files
- **Error Handling**: Captures uncaught exceptions and unhandled promise
  rejections
- **Console Interception**: Automatically captures all console.log,
  console.warn, console.error calls

## Log File Location

Logs are stored in your project directory for easy IDE access:

**Project Root**: `./logs/`

The logs directory is automatically added to `.gitignore` so log files won't be
committed to version control.

## Log File Format

Each log entry is stored as a JSON object on a separate line:

```json
{
  "timestamp": "2025-08-02T20:17:29.123Z",
  "level": "info",
  "source": "main",
  "message": "Application initialization completed",
  "data": {
    "version": "1.0.0",
    "platform": "win32"
  }
}
```

## Using the Logging System

### From Main Process (TypeScript)

```typescript
import { logger } from './utils/logger';

// Basic logging
logger.info('Application started');
logger.warn('Configuration file not found, using defaults');
logger.error('Failed to connect to database', { error: error.message });

// With additional data
logger.info('Document processed', {
  filename: 'document.pdf',
  pages: 5,
  processingTime: 1234,
});
```

### From Renderer Process (React)

```typescript
// Using console methods (automatically intercepted)
console.log('User clicked button');
console.warn('Form validation failed');
console.error('API request failed');

// Direct logging to main process
window.electronAPI.logToMain('info', 'Custom log message', {
  component: 'DocumentViewer',
  action: 'export',
});
```

### From Preload Process

```typescript
// Logs are automatically captured and forwarded to main process
console.log('Preload script initialized');
```

## IDE Integration

Since logs are written to your project's `logs/` directory, you can:

1. **Open log files directly in your IDE** - Navigate to the `logs/` folder and
   open any `.log` file
2. **Real-time monitoring** - Use your IDE's file watching features to monitor
   log files in real-time
3. **Search and filter** - Use your IDE's search functionality to find specific
   log entries
4. **Version control friendly** - Logs are automatically ignored by git, keeping
   your repository clean

### Log File Naming

Log files are named with timestamps for easy identification:

- `app-2025-08-02-20-17-29.log` (format: `app-YYYY-MM-DD-HH-MM-SS.log`)

## API Reference

### Logger Class Methods

#### Main Process Logger

```typescript
logger.debug(message: string, data?: any): void
logger.info(message: string, data?: any): void
logger.warn(message: string, data?: any): void
logger.error(message: string, data?: any, options?: { stack?: string }): void
logger.fatal(message: string, data?: any, options?: { stack?: string }): void
```

#### Cross-Process Logging

```typescript
logger.logFromRenderer(level, message, data?, stack?): void
logger.logFromPreload(level, message, data?, stack?): void
logger.logFromChromium(level, message, data?): void
```

### Electron API Methods

Available in renderer process via `window.electronAPI`:

```typescript
// Basic logging
logToMain(level: string, message: string, data?: any, stack?: string): Promise<void>
```

## Log Analysis in Your IDE

Since logs are JSON files, you can easily analyze them using your IDE's built-in
tools:

### Using VS Code

1. Open the `logs/` folder in VS Code
2. Use Ctrl+Shift+F to search across all log files
3. Install JSON extensions for better syntax highlighting
4. Use the built-in terminal to run commands like:

   ```bash
   # Find all error logs
   grep '"level":"error"' logs/*.log

   # Find logs from a specific component
   grep '"component":"DocumentViewer"' logs/*.log

   # Count log entries by level
   grep -c '"level":"info"' logs/*.log
   ```

### Using Other IDEs

- **IntelliJ/WebStorm**: Use "Find in Files" with JSON-aware search
- **Sublime Text**: Use the built-in search functionality
- **Vim/Neovim**: Use `:grep` or `:vimgrep` commands
- **Emacs**: Use `grep` or `rgrep` functions

## Best Practices

### 1. Use Appropriate Log Levels

- `debug`: Detailed tracing information
- `info`: General application flow
- `warn`: Recoverable issues that should be noted
- `error`: Errors that don't crash the application
- `fatal`: Critical errors that may cause termination

### 2. Include Contextual Data

```typescript
// Good
logger.info('Document processed successfully', {
  filename: 'report.pdf',
  pages: 12,
  processingTime: 2340,
  userId: 'user123',
});

// Less helpful
logger.info('Document processed');
```

### 3. Handle Sensitive Information

```typescript
// Avoid logging sensitive data
logger.info('User authenticated', {
  userId: user.id,
  // Don't log: password, tokens, personal data
});
```

### 4. Use Structured Logging

```typescript
// Consistent structure makes analysis easier
logger.error('API request failed', {
  endpoint: '/api/documents',
  method: 'POST',
  statusCode: 500,
  responseTime: 1234,
  error: error.message,
});
```

## Troubleshooting

### Common Issues

1. **Logs not appearing**: Check if the log directory exists and is writable
2. **Large log files**: Implement log rotation or cleanup old files
3. **Performance impact**: Reduce log level in production builds
4. **Missing stack traces**: Ensure source maps are available

### Debug Mode

Enable verbose logging in development:

```typescript
// Set environment variable
process.env.LOG_LEVEL = 'debug';

// Or configure logger
logger.setLevel('debug');
```

## Configuration

### Environment Variables

- `LOG_LEVEL`: Set minimum log level (debug, info, warn, error, fatal)
- `LOG_DIR`: Override default log directory
- `LOG_MAX_SIZE`: Maximum log file size in bytes (default: 10MB)
- `LOG_MAX_FILES`: Maximum number of log files to keep (default: 10)

### Development Workflow

1. **Start the application** - Logs begin writing to `./logs/`
2. **Open your IDE** - Navigate to the logs folder
3. **Monitor in real-time** - Watch log files update as you use the application
4. **Debug issues** - Search through logs to find errors or trace user actions
5. **Clean development** - Logs are automatically ignored by git

This development-focused logging system provides full visibility into
application behavior across all processes while keeping logs easily accessible
within your development environment.

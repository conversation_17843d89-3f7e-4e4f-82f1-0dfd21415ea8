import { Worker, isMainThread, parentPort, workerData } from 'worker_threads';
import { logger } from '../utils/logger';
import { AIModelClient, EmbeddingRequest, EmbeddingResponse } from '../services/AIModelClient';
import { ReasoningResult } from '../../shared/types/AI';

export interface AIWorkerMessage {
  id: string;
  type: 'embedding' | 'reasoning' | 'agent_execution' | 'health_check' | 'shutdown';
  data: any;
}

export interface AIWorkerResponse {
  id: string;
  success: boolean;
  result?: any;
  error?: string;
  processingTime: number;
  metadata?: Record<string, any>;
}

export interface AIJob {
  id: string;
  type: 'embedding' | 'reasoning' | 'agent_execution';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  data: any;
  timeout?: number;
  retryCount?: number;
  maxRetries?: number;
}

/**
 * AI Worker for background processing of AI operations
 * Handles embedding generation, reasoning, and agent execution in separate thread
 */
export class AIWorker {
  private worker: Worker | null = null;
  private isRunning = false;
  private readonly messageHandlers = new Map<string, (response: AIWorkerResponse) => void>();
  private messageId = 0;

  constructor() {
    this.startWorker();
  }

  /**
   * Start the AI worker thread
   */
  private startWorker(): void {
    if (this.isRunning) {
      return;
    }

    try {
      this.worker = new Worker(__filename, {
        workerData: { isAIWorker: true },
      });

      this.worker.on('message', (response: AIWorkerResponse) => {
        const handler = this.messageHandlers.get(response.id);
        if (handler) {
          handler(response);
          this.messageHandlers.delete(response.id);
        }
      });

      this.worker.on('error', error => {
        logger.error('AI Worker error', error);
        this.restartWorker();
      });

      this.worker.on('exit', code => {
        if (code !== 0) {
          logger.error(`AI Worker stopped with exit code ${code}`);
          this.restartWorker();
        }
      });

      this.isRunning = true;
      logger.info('AI Worker started successfully');
    } catch (error) {
      logger.error('Failed to start AI Worker', error);
      throw error;
    }
  }

  /**
   * Restart the worker if it crashes
   */
  private restartWorker(): void {
    this.isRunning = false;
    this.worker = null;

    setTimeout(() => {
      logger.info('Restarting AI Worker...');
      this.startWorker();
    }, 5000); // Wait 5 seconds before restart
  }

  /**
   * Generate embeddings in background
   */
  async generateEmbeddings(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    return this.executeJob({
      id: this.generateMessageId(),
      type: 'embedding',
      priority: 'normal',
      data: request,
      timeout: 30000, // 30 seconds
      maxRetries: 3,
    });
  }

  /**
   * Perform reasoning in background
   */
  async performReasoning(request: any): Promise<ReasoningResult> {
    return this.executeJob({
      id: this.generateMessageId(),
      type: 'reasoning',
      priority: 'high',
      data: request,
      timeout: 60000, // 60 seconds
      maxRetries: 2,
    });
  }

  /**
   * Execute agent in background
   */
  async executeAgent(input: string, context?: Record<string, any>): Promise<string> {
    return this.executeJob({
      id: this.generateMessageId(),
      type: 'agent_execution',
      priority: 'high',
      data: { input, context },
      timeout: 120000, // 2 minutes
      maxRetries: 1,
    });
  }

  /**
   * Execute a job with retry logic and timeout
   */
  private async executeJob<T>(job: AIJob): Promise<T> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.messageHandlers.delete(job.id);
        reject(new Error(`AI Worker job timeout after ${job.timeout}ms`));
      }, job.timeout || 30000);

      this.messageHandlers.set(job.id, (response: AIWorkerResponse) => {
        clearTimeout(timeout);

        if (response.success) {
          resolve(response.result);
        } else {
          // Retry logic
          const currentRetries = job.retryCount || 0;
          const maxRetries = job.maxRetries || 0;

          if (currentRetries < maxRetries) {
            job.retryCount = currentRetries + 1;
            logger.warn(`Retrying AI job ${job.id}, attempt ${job.retryCount}/${maxRetries}`);

            // Exponential backoff
            setTimeout(
              () => {
                this.sendMessage({
                  id: job.id,
                  type: job.type,
                  data: job.data,
                });
              },
              Math.pow(2, currentRetries) * 1000
            );
          } else {
            reject(new Error(response.error || 'AI Worker job failed'));
          }
        }
      });

      // Send message to worker
      this.sendMessage({
        id: job.id,
        type: job.type,
        data: job.data,
      });
    });
  }

  /**
   * Send message to worker thread
   */
  private sendMessage(message: AIWorkerMessage): void {
    if (!this.worker || !this.isRunning) {
      throw new Error('AI Worker not running');
    }

    this.worker.postMessage(message);
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `ai_job_${Date.now()}_${++this.messageId}`;
  }

  /**
   * Check worker health
   */
  async checkHealth(): Promise<boolean> {
    try {
      const response = await this.executeJob({
        id: this.generateMessageId(),
        type: 'reasoning',
        priority: 'urgent',
        data: {},
        timeout: 5000,
        maxRetries: 0,
      });
      return response === 'healthy';
    } catch {
      return false;
    }
  }

  /**
   * Shutdown the worker
   */
  async shutdown(): Promise<void> {
    if (!this.worker || !this.isRunning) {
      return;
    }

    try {
      // Send shutdown message
      this.sendMessage({
        id: this.generateMessageId(),
        type: 'shutdown',
        data: {},
      });

      // Wait for graceful shutdown
      await new Promise<void>(resolve => {
        const timeout = setTimeout(() => {
          this.worker?.terminate();
          resolve();
        }, 5000);

        this.worker?.on('exit', () => {
          clearTimeout(timeout);
          resolve();
        });
      });

      this.isRunning = false;
      this.worker = null;
      logger.info('AI Worker shutdown completed');
    } catch (error) {
      logger.error('Error during AI Worker shutdown', error);
      this.worker?.terminate();
    }
  }
}

// Worker thread implementation
if (!isMainThread && workerData?.isAIWorker) {
  let aiClient: AIModelClient;

  // Initialize AI client in worker thread
  const initializeWorker = async () => {
    try {
      aiClient = new AIModelClient();
      logger.info('AI Worker thread initialized');
    } catch (error) {
      logger.error('Failed to initialize AI Worker thread', error);
      process.exit(1);
    }
  };

  // Handle messages from main thread
  parentPort?.on('message', async (message: AIWorkerMessage) => {
    const startTime = Date.now();
    let response: AIWorkerResponse;

    try {
      let result: any;

      switch (message.type) {
        case 'embedding':
          result = await aiClient.generateEmbeddings(message.data);
          break;

        case 'reasoning':
          result = await aiClient.performReasoning(message.data);
          break;

        case 'agent_execution':
          result = await aiClient.executeAgent(message.data.input, message.data.context);
          break;

        case 'health_check':
          result = 'healthy';
          break;

        case 'shutdown':
          process.exit(0);

        default:
          throw new Error(`Unknown message type: ${message.type}`);
      }

      response = {
        id: message.id,
        success: true,
        result,
        processingTime: Date.now() - startTime,
      };
    } catch (error) {
      response = {
        id: message.id,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: Date.now() - startTime,
      };
    }

    parentPort?.postMessage(response);
  });

  // Initialize worker
  initializeWorker();
}

// Export singleton instance for main thread
export const aiWorker = new AIWorker();

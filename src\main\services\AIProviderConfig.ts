import { app } from 'electron';
import * as path from 'path';
import * as fs from 'fs-extra';
import * as crypto from 'crypto';
import { logger } from '../utils/logger';
import { multiProviderAIClient } from './MultiProviderAIClient';
import { ApiConfiguration, ApiProvider } from '../../shared/ai-config/api';

export interface ProviderCredentials {
  apiKey?: string;
  baseUrl?: string;
  region?: string;
  projectId?: string;
  accessKey?: string;
  secretKey?: string;
  sessionToken?: string;
  clientId?: string;
  clientSecret?: string;
  tokenUrl?: string;
  [key: string]: string | undefined;
}

export interface ProviderSettings {
  id: string;
  name: string;
  type: ApiProvider;
  enabled: boolean;
  priority: number;
  credentials: ProviderCredentials;
  models: {
    planMode: string;
    actMode: string;
  };
  settings: {
    temperature?: number;
    maxTokens?: number;
    timeout?: number;
    [key: string]: any;
  };
  lastUsed?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface AIProviderConfiguration {
  providers: ProviderSettings[];
  defaultProvider: string;
  encryptionEnabled: boolean;
  version: string;
}

/**
 * AI Provider Configuration Manager
 * Handles secure storage and management of AI provider settings and credentials
 */
export class AIProviderConfigManager {
  private config: AIProviderConfiguration;
  private readonly configPath: string;
  private readonly encryptionKey: Buffer;
  private readonly defaultConfig: AIProviderConfiguration;

  constructor() {
    this.configPath = path.join(app.getPath('userData'), 'ai-providers.json');
    this.encryptionKey = this.getOrCreateEncryptionKey();
    this.defaultConfig = this.createDefaultConfig();
    this.config = { ...this.defaultConfig };
  }

  /**
   * Initialize the configuration manager
   */
  async initialize(): Promise<void> {
    try {
      await this.loadConfiguration();
      this.applyConfiguration();
      logger.info('AI Provider Configuration initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize AI Provider Configuration', error);
      // Use default configuration on error
      this.config = { ...this.defaultConfig };
      await this.saveConfiguration();
    }
  }

  /**
   * Load configuration from file
   */
  private async loadConfiguration(): Promise<void> {
    try {
      if (await fs.pathExists(this.configPath)) {
        const configData = await fs.readJson(this.configPath);

        // Decrypt credentials if encryption is enabled
        if (configData.encryptionEnabled) {
          configData.providers = configData.providers.map((provider: any) => ({
            ...provider,
            credentials: this.decryptCredentials(provider.credentials),
          }));
        }

        this.config = this.mergeWithDefaults(configData);
        logger.info('Provider configuration loaded from file');
      } else {
        this.config = { ...this.defaultConfig };
        await this.saveConfiguration();
        logger.info('Created default provider configuration file');
      }
    } catch (error) {
      logger.error('Failed to load provider configuration', error);
      throw error;
    }
  }

  /**
   * Save configuration to file
   */
  async saveConfiguration(): Promise<void> {
    try {
      const configToSave = { ...this.config };

      // Encrypt credentials if encryption is enabled
      if (configToSave.encryptionEnabled) {
        configToSave.providers = configToSave.providers.map(provider => ({
          ...provider,
          credentials: this.encryptCredentials(provider.credentials),
        }));
      }

      await fs.ensureDir(path.dirname(this.configPath));
      await fs.writeJson(this.configPath, configToSave, { spaces: 2 });
      logger.info('Provider configuration saved to file');
    } catch (error) {
      logger.error('Failed to save provider configuration', error);
      throw error;
    }
  }

  /**
   * Apply configuration to the multi-provider client
   */
  private applyConfiguration(): void {
    // Clear existing providers
    multiProviderAIClient.clearProviders();

    // Configure each enabled provider
    for (const provider of this.config.providers) {
      if (!provider.enabled) {
        continue;
      }

      try {
        const apiConfig = this.buildApiConfiguration(provider);
        multiProviderAIClient.configureProvider(provider.type, apiConfig, provider.priority);
        logger.info(`Applied configuration for provider: ${provider.id}`);
      } catch (error) {
        logger.error(`Failed to configure provider ${provider.id}`, error);
      }
    }

    // Set default provider
    if (this.config.defaultProvider) {
      try {
        multiProviderAIClient.setProvider(this.config.defaultProvider);
      } catch (error) {
        logger.warn(`Failed to set default provider ${this.config.defaultProvider}`, error);
      }
    }
  }

  /**
   * Build API configuration for a provider
   */
  private buildApiConfiguration(provider: ProviderSettings): Partial<ApiConfiguration> {
    const config: Partial<ApiConfiguration> = {
      planModeApiProvider: provider.type,
      actModeApiProvider: provider.type,
      planModeApiModelId: provider.models.planMode,
      actModeApiModelId: provider.models.actMode,
      requestTimeoutMs: provider.settings.timeout || 30000,
    };

    // Map provider-specific credentials and settings
    switch (provider.type) {
      case 'openai':
        if (provider.credentials.apiKey) {
          config.openAiApiKey = provider.credentials.apiKey;
        }
        if (provider.credentials.baseUrl) {
          config.openAiBaseUrl = provider.credentials.baseUrl;
        }
        if (provider.models.planMode) {
          config.planModeOpenAiModelId = provider.models.planMode;
        }
        if (provider.models.actMode) {
          config.actModeOpenAiModelId = provider.models.actMode;
        }
        break;

      case 'anthropic':
        if (provider.credentials.apiKey) {
          config.apiKey = provider.credentials.apiKey;
        }
        if (provider.credentials.baseUrl) {
          config.anthropicBaseUrl = provider.credentials.baseUrl;
        }
        break;

      case 'openrouter':
        if (provider.credentials.apiKey) {
          config.openRouterApiKey = provider.credentials.apiKey;
        }
        if (provider.models.planMode) {
          config.planModeOpenRouterModelId = provider.models.planMode;
        }
        if (provider.models.actMode) {
          config.actModeOpenRouterModelId = provider.models.actMode;
        }
        break;

      case 'bedrock':
        if (provider.credentials.accessKey) {
          config.awsAccessKey = provider.credentials.accessKey;
        }
        if (provider.credentials.secretKey) {
          config.awsSecretKey = provider.credentials.secretKey;
        }
        if (provider.credentials.sessionToken) {
          config.awsSessionToken = provider.credentials.sessionToken;
        }
        if (provider.credentials.region) {
          config.awsRegion = provider.credentials.region;
        }
        break;

      case 'vertex':
        if (provider.credentials.projectId) {
          config.vertexProjectId = provider.credentials.projectId;
        }
        if (provider.credentials.region) {
          config.vertexRegion = provider.credentials.region;
        }
        if (provider.credentials.apiKey) {
          config.geminiApiKey = provider.credentials.apiKey;
        }
        break;

      case 'gemini':
        if (provider.credentials.apiKey) {
          config.geminiApiKey = provider.credentials.apiKey;
        }
        if (provider.credentials.baseUrl) {
          config.geminiBaseUrl = provider.credentials.baseUrl;
        }
        break;

      case 'groq':
        if (provider.credentials.apiKey) {
          config.groqApiKey = provider.credentials.apiKey;
        }
        if (provider.models.planMode) {
          config.planModeGroqModelId = provider.models.planMode;
        }
        if (provider.models.actMode) {
          config.actModeGroqModelId = provider.models.actMode;
        }
        break;

      case 'cerebras':
        if (provider.credentials.apiKey) {
          config.cerebrasApiKey = provider.credentials.apiKey;
        }
        break;

      case 'ollama':
        config.ollamaBaseUrl = provider.credentials.baseUrl || 'http://localhost:11434';
        config.planModeOllamaModelId = provider.models.planMode;
        config.actModeOllamaModelId = provider.models.actMode;
        break;

      case 'lmstudio':
        config.lmStudioBaseUrl = provider.credentials.baseUrl || 'http://localhost:1234';
        config.planModeLmStudioModelId = provider.models.planMode;
        config.actModeLmStudioModelId = provider.models.actMode;
        break;

      default:
        logger.warn(`Unknown provider type: ${provider.type}`);
    }

    return config;
  }

  /**
   * Add or update a provider
   */
  async addProvider(
    providerData: Omit<ProviderSettings, 'createdAt' | 'updatedAt'>
  ): Promise<void> {
    const now = new Date();
    const existingIndex = this.config.providers.findIndex(p => p.id === providerData.id);

    const provider: ProviderSettings = {
      ...providerData,
      createdAt: existingIndex >= 0 ? this.config.providers[existingIndex]?.createdAt || now : now,
      updatedAt: now,
    };

    if (existingIndex >= 0) {
      this.config.providers[existingIndex] = provider;
    } else {
      this.config.providers.push(provider);
    }

    await this.saveConfiguration();
    this.applyConfiguration();

    logger.info(`Provider ${provider.id} ${existingIndex >= 0 ? 'updated' : 'added'}`);
  }

  /**
   * Remove a provider
   */
  async removeProvider(providerId: string): Promise<void> {
    const index = this.config.providers.findIndex(p => p.id === providerId);
    if (index === -1) {
      throw new Error(`Provider ${providerId} not found`);
    }

    this.config.providers.splice(index, 1);

    // Update default provider if it was removed
    if (this.config.defaultProvider === providerId) {
      this.config.defaultProvider =
        this.config.providers.length > 0 ? this.config.providers[0]?.id || '' : '';
    }

    await this.saveConfiguration();
    this.applyConfiguration();

    logger.info(`Provider ${providerId} removed`);
  }

  /**
   * Get all providers
   */
  getProviders(): ProviderSettings[] {
    return [...this.config.providers];
  }

  /**
   * Get a specific provider
   */
  getProvider(providerId: string): ProviderSettings | undefined {
    return this.config.providers.find(p => p.id === providerId);
  }

  /**
   * Set default provider
   */
  async setDefaultProvider(providerId: string): Promise<void> {
    const provider = this.getProvider(providerId);
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`);
    }

    this.config.defaultProvider = providerId;
    await this.saveConfiguration();

    multiProviderAIClient.setProvider(providerId);
    logger.info(`Default provider set to: ${providerId}`);
  }

  /**
   * Test provider connection
   */
  testProvider(providerId: string): boolean {
    const provider = this.getProvider(providerId);
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`);
    }

    try {
      const apiConfig = this.buildApiConfiguration(provider);
      multiProviderAIClient.configureProvider(provider.type, apiConfig, 0);

      // Perform health check
      const healthResults = multiProviderAIClient.healthCheck();
      return healthResults[`test_${providerId}`] === true;
    } catch (error) {
      logger.error(`Provider test failed for ${providerId}`, error);
      return false;
    }
  }

  /**
   * Create default configuration
   */
  private createDefaultConfig(): AIProviderConfiguration {
    return {
      providers: [],
      defaultProvider: '',
      encryptionEnabled: true,
      version: '1.0.0',
    };
  }

  /**
   * Merge loaded configuration with defaults
   */
  private mergeWithDefaults(
    loadedConfig: Partial<AIProviderConfiguration>
  ): AIProviderConfiguration {
    return {
      providers: loadedConfig.providers || [],
      defaultProvider: loadedConfig.defaultProvider || '',
      encryptionEnabled: loadedConfig.encryptionEnabled !== false,
      version: loadedConfig.version || '1.0.0',
    };
  }

  /**
   * Get or create encryption key
   */
  private getOrCreateEncryptionKey(): Buffer {
    const keyPath = path.join(app.getPath('userData'), '.ai-key');

    try {
      if (fs.existsSync(keyPath)) {
        return fs.readFileSync(keyPath);
      } else {
        const key = crypto.randomBytes(32);
        fs.writeFileSync(keyPath, key, { mode: 0o600 });
        return key;
      }
    } catch (error) {
      logger.error('Failed to handle encryption key', error);
      // Fallback to a deterministic key (less secure)
      return crypto.scryptSync('ai-document-processor', 'salt', 32);
    }
  }

  /**
   * Encrypt credentials
   */
  private encryptCredentials(credentials: ProviderCredentials): any {
    const encrypted: any = {};

    for (const [key, value] of Object.entries(credentials)) {
      if (value && typeof value === 'string') {
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipheriv('aes-256-cbc', this.encryptionKey, iv);
        let encryptedValue = cipher.update(value, 'utf8', 'hex');
        encryptedValue += cipher.final('hex');
        encrypted[key] = `${iv.toString('hex')}:${encryptedValue}`;
      } else {
        encrypted[key] = value;
      }
    }

    return encrypted;
  }

  /**
   * Decrypt credentials
   */
  private decryptCredentials(encryptedCredentials: any): ProviderCredentials {
    const decrypted: ProviderCredentials = {};

    for (const [key, value] of Object.entries(encryptedCredentials)) {
      if (value && typeof value === 'string' && value.includes(':')) {
        try {
          const [ivHex, encryptedValue] = value.split(':');
          if (!ivHex || !encryptedValue) {
            throw new Error('Invalid encrypted value format');
          }
          const iv = Buffer.from(ivHex, 'hex');
          const decipher = crypto.createDecipheriv('aes-256-cbc', this.encryptionKey, iv);
          let decryptedValue = decipher.update(encryptedValue, 'hex', 'utf8');
          decryptedValue += decipher.final('utf8');
          decrypted[key] = decryptedValue;
        } catch (error) {
          logger.warn(`Failed to decrypt credential ${key}`, error);
          decrypted[key] = value;
        }
      } else {
        decrypted[key] = typeof value === 'string' ? value : String(value);
      }
    }

    return decrypted;
  }

  /**
   * Export configuration (without sensitive data)
   */
  exportConfiguration(): any {
    return {
      providers: this.config.providers.map(provider => ({
        id: provider.id,
        name: provider.name,
        type: provider.type,
        enabled: provider.enabled,
        priority: provider.priority,
        models: provider.models,
        settings: provider.settings,
        // Exclude credentials for security
      })),
      defaultProvider: this.config.defaultProvider,
      version: this.config.version,
    };
  }
}

// Export singleton instance
export const aiProviderConfig = new AIProviderConfigManager();

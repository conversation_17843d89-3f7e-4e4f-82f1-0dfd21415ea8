# AI Document Processor Environment Variables
# Copy this file to .env and fill in your actual values

# Application Environment
NODE_ENV=development
LOG_LEVEL=info

# AI Service Configuration
# Azure AI Services
AZURE_AI_ENDPOINT=https://your-resource.cognitiveservices.azure.com/
AZURE_AI_KEY=your-azure-ai-key-here

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_ORGANIZATION=your-openai-org-id-here

# Database Configuration
DATABASE_PATH=./data/ai-document-processor.db
DATABASE_ENCRYPTION_KEY=your-database-encryption-key-here

# ChromaDB Configuration
CHROMA_DB_PATH=./data/chroma_db
CHROMA_DB_HOST=localhost
CHROMA_DB_PORT=8000

# OCR Configuration
TESSERACT_LANG=eng+spa+fra+deu
OCR_CONFIDENCE_THRESHOLD=0.7

# File Processing Configuration
MAX_FILE_SIZE_MB=100
SUPPORTED_FILE_TYPES=pdf,docx,xlsx,csv,png,jpg,jpeg,tiff
TEMP_DIR=./temp
CACHE_DIR=./cache

# Security Configuration
ENCRYPTION_ALGORITHM=aes-256-gcm
SESSION_SECRET=your-session-secret-here
JWT_SECRET=your-jwt-secret-here

# Performance Configuration
MAX_CONCURRENT_PROCESSES=4
MEMORY_LIMIT_MB=2048
CACHE_TTL_SECONDS=3600

# Development Configuration
DEV_SERVER_PORT=4000
HOT_RELOAD=true
ENABLE_DEVTOOLS=true

# Logging Configuration
LOG_FILE_PATH=./logs/app.log
LOG_MAX_SIZE=10MB
LOG_MAX_FILES=5

# Feature Flags
ENABLE_AI_PROCESSING=true
ENABLE_OCR=true
ENABLE_KNOWLEDGE_BASE=true
ENABLE_TIMELINE=true
ENABLE_COLLABORATION=false

# External Services - Removed telemetry endpoints

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30
BACKUP_LOCATION=./backups

# Rate Limiting
API_RATE_LIMIT_PER_MINUTE=60
AI_REQUEST_RATE_LIMIT_PER_MINUTE=10

# Monitoring - Disabled telemetry
HEALTH_CHECK_INTERVAL_SECONDS=30
METRICS_COLLECTION_ENABLED=false
ERROR_REPORTING_ENABLED=false

# Internationalization
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,es,fr,de

# UI Configuration
DEFAULT_THEME=light
ENABLE_ANIMATIONS=true
SIDEBAR_WIDTH=300
TAB_LIMIT=20

# Document Processing
PDF_PROCESSING_TIMEOUT_SECONDS=300
OCR_PROCESSING_TIMEOUT_SECONDS=600
AI_PROCESSING_TIMEOUT_SECONDS=120

# Knowledge Base
VECTOR_DIMENSION=1536
SIMILARITY_THRESHOLD=0.8
MAX_SEARCH_RESULTS=50

# Timeline Configuration
MAX_CHECKPOINTS=1000
CHECKPOINT_COMPRESSION=true
AUTO_CHECKPOINT_INTERVAL_MINUTES=15

import { logger } from '../utils/logger';
import { AIError, ErrorHandlingStrategy, AIRequest } from '../../shared/types/AI';

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  jitter: boolean;
}

export interface CircuitBreakerConfig {
  failureThreshold: number;
  resetTimeout: number;
  monitoringPeriod: number;
}

export interface FallbackConfig {
  enableFallback: boolean;
  fallbackProviders: string[];
  fallbackTimeout: number;
}

export interface ErrorHandlingConfig {
  strategy: ErrorHandlingStrategy;
  retry: RetryConfig;
  circuitBreaker: CircuitBreakerConfig;
  fallback: FallbackConfig;
  enableMetrics: boolean;
}

export enum CircuitBreakerState {
  CLOSED = 'closed',
  OPEN = 'open',
  HALF_OPEN = 'half_open',
}

export interface ErrorMetrics {
  totalErrors: number;
  errorsByType: Map<string, number>;
  errorsByProvider: Map<string, number>;
  lastError?: AIError;
  errorRate: number;
  averageRecoveryTime: number;
}

/**
 * Advanced Error Handler for AI operations
 * Implements retry logic, circuit breaker pattern, and fallback mechanisms
 */
export class AIErrorHandler {
  private readonly config: ErrorHandlingConfig;
  private readonly metrics: ErrorMetrics;
  private circuitBreakerState = CircuitBreakerState.CLOSED;
  private failureCount = 0;
  private lastFailureTime = 0;
  private halfOpenAttempts = 0;

  constructor(config: Partial<ErrorHandlingConfig> = {}) {
    this.config = {
      strategy: ErrorHandlingStrategy.RETRY,
      retry: {
        maxRetries: 3,
        baseDelay: 1000,
        maxDelay: 30000,
        backoffMultiplier: 2,
        jitter: true,
      },
      circuitBreaker: {
        failureThreshold: 5,
        resetTimeout: 60000,
        monitoringPeriod: 300000, // 5 minutes
      },
      fallback: {
        enableFallback: true,
        fallbackProviders: ['openai', 'azure'],
        fallbackTimeout: 10000,
      },
      enableMetrics: true,
      ...config,
    };

    this.metrics = {
      totalErrors: 0,
      errorsByType: new Map(),
      errorsByProvider: new Map(),
      errorRate: 0,
      averageRecoveryTime: 0,
    };
  }

  /**
   * Handle AI operation with error recovery
   */
  async handleOperation<T>(
    operation: () => Promise<T>,
    context: {
      operationType: string;
      provider: string;
      request: AIRequest;
    }
  ): Promise<T> {
    // Check circuit breaker
    if (this.circuitBreakerState === CircuitBreakerState.OPEN) {
      if (Date.now() - this.lastFailureTime > this.config.circuitBreaker.resetTimeout) {
        this.circuitBreakerState = CircuitBreakerState.HALF_OPEN;
        this.halfOpenAttempts = 0;
        logger.info('Circuit breaker transitioning to half-open state');
      } else {
        throw this.createError('CIRCUIT_BREAKER_OPEN', 'Circuit breaker is open', context);
      }
    }

    try {
      const result = await this.executeWithRetry(operation, context);

      // Success - reset circuit breaker if needed
      if (this.circuitBreakerState === CircuitBreakerState.HALF_OPEN) {
        this.circuitBreakerState = CircuitBreakerState.CLOSED;
        this.failureCount = 0;
        logger.info('Circuit breaker reset to closed state');
      }

      return result;
    } catch (error) {
      return this.handleError(error, operation, context);
    }
  }

  /**
   * Execute operation with retry logic
   */
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: { operationType: string; provider: string; request: AIRequest }
  ): Promise<T> {
    let lastError: Error | undefined;

    for (let attempt = 0; attempt <= this.config.retry.maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          const delay = this.calculateDelay(attempt);
          logger.info(`Retrying AI operation, attempt ${attempt}/${this.config.retry.maxRetries}`, {
            delay,
            operationType: context.operationType,
            provider: context.provider,
          });
          await this.sleep(delay);
        }

        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        // Don't retry on certain error types
        if (this.isNonRetryableError(lastError)) {
          throw lastError;
        }

        if (attempt === this.config.retry.maxRetries) {
          break;
        }
      }
    }

    throw lastError || new Error('Operation failed after all retries');
  }

  /**
   * Handle error with fallback mechanisms
   */
  private async handleError<T>(
    error: any,
    operation: () => Promise<T>,
    context: { operationType: string; provider: string; request: AIRequest }
  ): Promise<T> {
    const aiError = this.normalizeError(error, context);
    this.recordError(aiError, context);

    // Update circuit breaker
    this.updateCircuitBreaker();

    // Try fallback if enabled and available
    if (this.config.fallback.enableFallback && this.shouldUseFallback(aiError)) {
      try {
        return await this.executeFallback(operation, context);
      } catch (fallbackError) {
        logger.error('Fallback operation also failed', {
          originalError: aiError,
          fallbackError,
        });
      }
    }

    // Apply error handling strategy
    switch (this.config.strategy) {
      case ErrorHandlingStrategy.FAIL_FAST:
        throw aiError;

      case ErrorHandlingStrategy.SKIP:
        logger.warn('Skipping failed operation', { error: aiError, context });
        return null as T; // Return null for skipped operations

      case ErrorHandlingStrategy.FALLBACK:
        return this.getDefaultResponse(context) as T;

      default:
        throw aiError;
    }
  }

  /**
   * Execute fallback operation with alternative provider
   */
  private async executeFallback<T>(
    operation: () => Promise<T>,
    context: { operationType: string; provider: string; request: AIRequest }
  ): Promise<T> {
    const fallbackProviders = this.config.fallback.fallbackProviders.filter(
      p => p !== context.provider
    );

    for (const fallbackProvider of fallbackProviders) {
      try {
        logger.info(`Attempting fallback with provider: ${fallbackProvider}`);

        // Create fallback operation with timeout
        const fallbackOperation = this.createTimeoutWrapper(
          operation,
          this.config.fallback.fallbackTimeout
        );

        return await fallbackOperation();
      } catch (error) {
        logger.warn(`Fallback provider ${fallbackProvider} failed`, error);
        continue;
      }
    }

    throw new Error('All fallback providers failed');
  }

  /**
   * Create timeout wrapper for operations
   */
  private createTimeoutWrapper<T>(operation: () => Promise<T>, timeout: number): () => Promise<T> {
    return () =>
      Promise.race([
        operation(),
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error('Operation timeout')), timeout)
        ),
      ]);
  }

  /**
   * Calculate retry delay with exponential backoff and jitter
   */
  private calculateDelay(attempt: number): number {
    const exponentialDelay = Math.min(
      this.config.retry.baseDelay * Math.pow(this.config.retry.backoffMultiplier, attempt - 1),
      this.config.retry.maxDelay
    );

    if (this.config.retry.jitter) {
      // Add random jitter (±25%)
      const jitter = exponentialDelay * 0.25 * (Math.random() * 2 - 1);
      return Math.max(0, exponentialDelay + jitter);
    }

    return exponentialDelay;
  }

  /**
   * Update circuit breaker state based on failures
   */
  private updateCircuitBreaker(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.circuitBreakerState === CircuitBreakerState.HALF_OPEN) {
      this.halfOpenAttempts++;
      if (this.halfOpenAttempts >= 3) {
        this.circuitBreakerState = CircuitBreakerState.OPEN;
        logger.warn('Circuit breaker opened due to continued failures');
      }
    } else if (
      this.circuitBreakerState === CircuitBreakerState.CLOSED &&
      this.failureCount >= this.config.circuitBreaker.failureThreshold
    ) {
      this.circuitBreakerState = CircuitBreakerState.OPEN;
      logger.warn(`Circuit breaker opened after ${this.failureCount} failures`);
    }
  }

  /**
   * Check if error is non-retryable
   */
  private isNonRetryableError(error: Error): boolean {
    const nonRetryablePatterns = [
      /authentication/i,
      /authorization/i,
      /invalid.*key/i,
      /quota.*exceeded/i,
      /rate.*limit.*exceeded/i,
      /bad.*request/i,
      /invalid.*input/i,
    ];

    return nonRetryablePatterns.some(pattern => pattern.test(error.message));
  }

  /**
   * Check if fallback should be used for this error
   */
  private shouldUseFallback(error: AIError): boolean {
    const fallbackErrorCodes = [
      'TIMEOUT',
      'NETWORK_ERROR',
      'SERVICE_UNAVAILABLE',
      'INTERNAL_ERROR',
    ];

    return fallbackErrorCodes.includes(error.code);
  }

  /**
   * Normalize error to AIError format
   */
  private normalizeError(
    error: any,
    context: { operationType: string; provider: string }
  ): AIError {
    if (error.code && error.message) {
      return error as AIError;
    }

    let code = 'UNKNOWN_ERROR';
    let retryable = true;

    if (error.message) {
      if (error.message.includes('timeout')) {
        code = 'TIMEOUT';
      } else if (error.message.includes('network')) {
        code = 'NETWORK_ERROR';
      } else if (error.message.includes('rate limit')) {
        code = 'RATE_LIMIT_EXCEEDED';
        retryable = false;
      } else if (error.message.includes('authentication')) {
        code = 'AUTHENTICATION_ERROR';
        retryable = false;
      }
    }

    return {
      code,
      message: error.message || 'Unknown error occurred',
      details: {
        originalError: error,
        context,
        timestamp: new Date().toISOString(),
      },
      retryable,
    };
  }

  /**
   * Record error metrics
   */
  private recordError(error: AIError, context: { operationType: string; provider: string }): void {
    if (!this.config.enableMetrics) {
      return;
    }

    this.metrics.totalErrors++;
    this.metrics.lastError = error;

    // Update error counts by type
    const currentTypeCount = this.metrics.errorsByType.get(error.code) || 0;
    this.metrics.errorsByType.set(error.code, currentTypeCount + 1);

    // Update error counts by provider
    const currentProviderCount = this.metrics.errorsByProvider.get(context.provider) || 0;
    this.metrics.errorsByProvider.set(context.provider, currentProviderCount + 1);

    // Calculate error rate (simplified)
    this.metrics.errorRate = this.metrics.totalErrors / (this.metrics.totalErrors + 100); // Assume 100 successful operations for simplicity
  }

  /**
   * Create standardized AI error
   */
  private createError(
    code: string,
    message: string,
    context: { operationType: string; provider: string }
  ): AIError {
    return {
      code,
      message,
      details: { context, timestamp: new Date().toISOString() },
      retryable: false,
    };
  }

  /**
   * Get default response for fallback strategy
   */
  private getDefaultResponse(context: { operationType: string }): any {
    switch (context.operationType) {
      case 'embedding':
        return { embedding: [], dimensions: 0, model: 'fallback', tokensUsed: 0 };
      case 'reasoning':
        return {
          response: 'Service temporarily unavailable',
          confidence: 0,
          reasoning: '',
          sources: [],
          tokensUsed: 0,
        };
      default:
        return null;
    }
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get error metrics
   */
  getMetrics(): ErrorMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset metrics
   */
  resetMetrics(): void {
    this.metrics.totalErrors = 0;
    this.metrics.errorsByType.clear();
    this.metrics.errorsByProvider.clear();
    this.metrics.errorRate = 0;
    this.metrics.averageRecoveryTime = 0;
    delete this.metrics.lastError;
  }

  /**
   * Get circuit breaker status
   */
  getCircuitBreakerStatus(): {
    state: CircuitBreakerState;
    failureCount: number;
    lastFailureTime: number;
  } {
    return {
      state: this.circuitBreakerState,
      failureCount: this.failureCount,
      lastFailureTime: this.lastFailureTime,
    };
  }
}

// Export singleton instance
export const aiErrorHandler = new AIErrorHandler();

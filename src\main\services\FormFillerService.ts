import { createLogger } from '../utils/logger';
import {
  Document,
  FormField,
  FormFieldType,
  FieldMapping,
  ExtractedData,
  ValidationResult,
} from '../../shared/types/Document';
import { AIModelClient } from './AIModelClient';
import { FormFieldDetector } from './FormFieldDetector';
import { FormFieldValidator } from './FormFieldValidator';
import { PDFProcessor } from './PDFProcessor';
import { DocumentProcessingError } from './DocumentProcessor';
// import { create, all } from 'mathjs';

const logger = createLogger('FormFillerService');
// const math = create(all || {});

export interface FormFillingOptions {
  useAIMapping: boolean;
  confidenceThreshold: number;
  enablePreview: boolean;
  enableValidation: boolean;
  enableCalculations: boolean;
  preserveOriginal: boolean;
  fillMode: FormFillMode;
}

export enum FormFillMode {
  AUTOMATIC = 'automatic',
  MANUAL = 'manual',
  HYBRID = 'hybrid',
  PREVIEW_ONLY = 'preview_only',
}

export interface FormFillingResult {
  success: boolean;
  filledDocument: Document;
  mappings: FieldMapping[];
  validationResults: ValidationResult[];
  confidence: number;
  processingTime: number;
  metadata: {
    totalFields: number;
    filledFields: number;
    skippedFields: number;
    errorFields: number;
    averageConfidence: number;
  };
}

export interface FormFillingPreview {
  mappings: FieldMapping[];
  previewDocument: Document;
  confidence: number;
  warnings: string[];
  suggestions: string[];
}

export interface SemanticMapping {
  sourceData: ExtractedData;
  targetField: FormField;
  similarity: number;
  confidence: number;
  reasoning: string;
}

/**
 * Intelligent form filling service with AI integration
 */
export class FormFillerService {
  private readonly aiClient: AIModelClient;
  private readonly fieldDetector: FormFieldDetector;
  private readonly fieldValidator: FormFieldValidator;
  private readonly pdfProcessor: PDFProcessor;

  constructor(
    aiClient: AIModelClient,
    fieldDetector: FormFieldDetector,
    fieldValidator: FormFieldValidator,
    pdfProcessor: PDFProcessor
  ) {
    this.aiClient = aiClient;
    this.fieldDetector = fieldDetector;
    this.fieldValidator = fieldValidator;
    this.pdfProcessor = pdfProcessor;
  }

  /**
   * Fill form with extracted data using intelligent mapping
   */
  public async fillForm(
    document: Document,
    extractedData: ExtractedData[],
    options: Partial<FormFillingOptions> = {}
  ): Promise<FormFillingResult> {
    const startTime = Date.now();
    const fillOptions = this.mergeDefaultOptions(options);

    logger.info('Starting form filling', {
      documentId: document.id,
      dataCount: extractedData.length,
      options: fillOptions,
    });

    try {
      // Step 1: Detect form fields
      const fieldDetection = await this.fieldDetector.detectFormFields(document);
      const formFields = fieldDetection.fields;

      if (formFields.length === 0) {
        throw new DocumentProcessingError(
          'No form fields detected in document',
          'NO_FORM_FIELDS',
          document.id,
          'form_filling'
        );
      }

      // Step 2: Create field mappings
      const mappings = await this.createFieldMappings(extractedData, formFields, fillOptions);

      // Step 3: Preview mode check
      if (fillOptions.fillMode === FormFillMode.PREVIEW_ONLY || fillOptions.enablePreview) {
        const preview = await this.generatePreview(document, mappings, formFields);
        if (fillOptions.fillMode === FormFillMode.PREVIEW_ONLY) {
          return {
            success: true,
            filledDocument: preview.previewDocument,
            mappings: preview.mappings,
            validationResults: [],
            confidence: preview.confidence,
            processingTime: Date.now() - startTime,
            metadata: this.calculateMetadata(mappings, formFields),
          };
        }
      }

      // Step 4: Validate mappings
      let validationResults: ValidationResult[] = [];
      if (fillOptions.enableValidation) {
        validationResults = await this.validateMappings(mappings, formFields);

        // Filter out invalid mappings if in strict mode
        const validMappings = mappings.filter(mapping => {
          const validation = validationResults.find(v => v.fieldId === mapping.targetField);
          return !validation || validation.isValid;
        });

        if (validMappings.length !== mappings.length) {
          logger.warn('Some mappings failed validation', {
            totalMappings: mappings.length,
            validMappings: validMappings.length,
          });
        }
      }

      // Step 5: Perform calculations if enabled
      if (fillOptions.enableCalculations) {
        await this.performCalculations(mappings, formFields);
      }

      // Step 6: Fill the form
      const filledDocument = await this.applyMappingsToDocument(document, mappings, fillOptions);

      // Step 7: Calculate results
      const confidence = this.calculateOverallConfidence(mappings);
      const metadata = this.calculateMetadata(mappings, formFields);

      const result: FormFillingResult = {
        success: true,
        filledDocument,
        mappings,
        validationResults,
        confidence,
        processingTime: Date.now() - startTime,
        metadata,
      };

      logger.info('Form filling completed', {
        documentId: document.id,
        totalFields: metadata.totalFields,
        filledFields: metadata.filledFields,
        confidence,
        processingTime: result.processingTime,
      });

      return result;
    } catch (error) {
      logger.error('Form filling failed', { error, documentId: document.id });
      throw new DocumentProcessingError(
        'Form filling failed',
        'FORM_FILLING_FAILED',
        document.id,
        'form_filling'
      );
    }
  }

  /**
   * Create intelligent field mappings using semantic similarity
   */
  private async createFieldMappings(
    extractedData: ExtractedData[],
    formFields: FormField[],
    options: FormFillingOptions
  ): Promise<FieldMapping[]> {
    const mappings: FieldMapping[] = [];

    if (options.useAIMapping) {
      // Use AI for semantic mapping
      const semanticMappings = await this.createSemanticMappings(extractedData, formFields);

      for (const semanticMapping of semanticMappings) {
        if (semanticMapping.confidence >= options.confidenceThreshold) {
          mappings.push({
            sourceField: semanticMapping.sourceData.id,
            targetField: semanticMapping.targetField.id,
            confidence: semanticMapping.confidence,
            coordinates: semanticMapping.targetField.bounds,
          });
        }
      }
    } else {
      // Use rule-based mapping
      const ruleMappings = await this.createRuleBasedMappings(extractedData, formFields);
      mappings.push(...ruleMappings);
    }

    logger.info('Field mappings created', {
      totalMappings: mappings.length,
      averageConfidence: mappings.reduce((sum, m) => sum + m.confidence, 0) / mappings.length,
    });

    return mappings;
  }

  /**
   * Create semantic mappings using AI
   */
  private async createSemanticMappings(
    extractedData: ExtractedData[],
    formFields: FormField[]
  ): Promise<SemanticMapping[]> {
    const mappings: SemanticMapping[] = [];

    for (const field of formFields) {
      const bestMatch = await this.findBestSemanticMatch(field, extractedData);
      if (bestMatch) {
        mappings.push(bestMatch);
      }
    }

    return mappings;
  }

  /**
   * Find best semantic match for a form field
   */
  private async findBestSemanticMatch(
    field: FormField,
    extractedData: ExtractedData[]
  ): Promise<SemanticMapping | null> {
    const prompt = `
Given a form field with the following properties:
- Name: ${field.name}
- Type: ${field.type}
- Required: ${field.required}

And the following extracted data items:
${extractedData.map((data, index) => `${index + 1}. ${JSON.stringify(data.content)} (confidence: ${data.confidence})`).join('\n')}

Determine which extracted data item best matches this form field. Consider:
1. Semantic similarity between field name and data content
2. Data type compatibility
3. Context and meaning

Return a JSON response with:
{
  "bestMatchIndex": number (1-based index, or -1 if no good match),
  "confidence": number (0-1),
  "reasoning": "explanation of why this is the best match"
}
`;

    try {
      const response = await this.aiClient.performReasoning({
        context: 'Form field mapping',
        query: prompt,
      });

      const result = JSON.parse(JSON.stringify(response.reasoning) || '{}');

      if (result.bestMatchIndex > 0 && result.bestMatchIndex <= extractedData.length) {
        const matchedData = extractedData[result.bestMatchIndex - 1];
        if (matchedData) {
          return {
            sourceData: matchedData,
            targetField: field,
            similarity: result.confidence,
            confidence: result.confidence * matchedData.confidence,
            reasoning: result.reasoning,
          };
        }
      }
    } catch (error) {
      logger.error('Semantic mapping failed for field', { error, fieldId: field.id });
    }

    return null;
  }

  /**
   * Create rule-based mappings using heuristics
   */
  private async createRuleBasedMappings(
    extractedData: ExtractedData[],
    formFields: FormField[]
  ): Promise<FieldMapping[]> {
    const mappings: FieldMapping[] = [];

    for (const field of formFields) {
      const match = this.findRuleBasedMatch(field, extractedData);
      if (match) {
        mappings.push({
          sourceField: match.id,
          targetField: field.id,
          confidence: match.confidence,
          coordinates: field.bounds,
        });
      }
    }

    return mappings;
  }

  /**
   * Find rule-based match for a form field
   */
  private findRuleBasedMatch(
    field: FormField,
    extractedData: ExtractedData[]
  ): ExtractedData | null {
    const fieldName = field.name.toLowerCase();

    // Define mapping rules
    const rules = [
      { keywords: ['name', 'full name', 'first name', 'last name'], types: [FormFieldType.TEXT] },
      {
        keywords: ['email', 'e-mail', 'email address'],
        types: [FormFieldType.EMAIL, FormFieldType.TEXT],
      },
      {
        keywords: ['phone', 'telephone', 'mobile', 'cell'],
        types: [FormFieldType.PHONE, FormFieldType.TEXT],
      },
      { keywords: ['date', 'birth', 'dob', 'birthday'], types: [FormFieldType.DATE] },
      { keywords: ['amount', 'total', 'sum', 'price', 'cost'], types: [FormFieldType.NUMBER] },
      { keywords: ['address', 'street', 'city', 'state', 'zip'], types: [FormFieldType.TEXT] },
    ];

    for (const rule of rules) {
      if (rule.types.includes(field.type)) {
        for (const keyword of rule.keywords) {
          if (fieldName.includes(keyword)) {
            // Find matching extracted data
            const match = extractedData.find(data => {
              const content = String(data.content).toLowerCase();
              return content.includes(keyword) || this.isDataTypeMatch(data, field.type);
            });

            if (match) {
              return match;
            }
          }
        }
      }
    }

    return null;
  }

  /**
   * Check if data type matches field type
   */
  private isDataTypeMatch(data: ExtractedData, fieldType: FormFieldType): boolean {
    const content = data.content;

    switch (fieldType) {
      case FormFieldType.EMAIL:
        return typeof content === 'string' && /\S+@\S+\.\S+/.test(content);
      case FormFieldType.PHONE:
        return typeof content === 'string' && /[\d\s\-\(\)]{10,}/.test(content);
      case FormFieldType.NUMBER:
        return !isNaN(Number(content));
      case FormFieldType.DATE:
        return !isNaN(Date.parse(String(content)));
      default:
        return true;
    }
  }

  /**
   * Generate preview of form filling
   */
  private async generatePreview(
    document: Document,
    mappings: FieldMapping[],
    formFields: FormField[]
  ): Promise<FormFillingPreview> {
    // Create a copy of the document for preview
    const previewDocument = { ...document };

    // Apply mappings to create preview
    const previewMappings = mappings.map(mapping => ({
      ...mapping,
      confidence: mapping.confidence * 0.9, // Slightly lower confidence for preview
    }));

    const confidence = this.calculateOverallConfidence(previewMappings);
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Generate warnings and suggestions
    const lowConfidenceMappings = previewMappings.filter(m => m.confidence < 0.7);
    if (lowConfidenceMappings.length > 0) {
      warnings.push(`${lowConfidenceMappings.length} field mappings have low confidence`);
      suggestions.push('Review and manually adjust low-confidence mappings');
    }

    const unmappedFields = formFields.filter(
      field => !previewMappings.some(mapping => mapping.targetField === field.id)
    );
    if (unmappedFields.length > 0) {
      warnings.push(`${unmappedFields.length} fields could not be mapped`);
      suggestions.push('Provide additional data or manually map remaining fields');
    }

    return {
      mappings: previewMappings,
      previewDocument,
      confidence,
      warnings,
      suggestions,
    };
  }

  /**
   * Validate field mappings
   */
  private async validateMappings(
    mappings: FieldMapping[],
    formFields: FormField[]
  ): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    for (const mapping of mappings) {
      const field = formFields.find(f => f.id === mapping.targetField);
      if (!field) continue;

      // Create mock form data for validation
      const formData = { [field.id]: 'mock_value' }; // This would be actual mapped value

      const validationResult = await this.fieldValidator.validateField(field, formData[field.id], {
        allFields: formFields,
        dependencies: [],
        formData,
        metadata: {},
      });

      results.push(validationResult);
    }

    return results;
  }

  /**
   * Perform calculations on dependent fields
   */
  private async performCalculations(
    mappings: FieldMapping[],
    formFields: FormField[]
  ): Promise<void> {
    // Find fields that might need calculations
    const calculationFields = formFields.filter(
      field =>
        field.name.toLowerCase().includes('total') ||
        field.name.toLowerCase().includes('sum') ||
        field.name.toLowerCase().includes('tax')
    );

    for (const field of calculationFields) {
      try {
        // Simple calculation example
        if (field.name.toLowerCase().includes('total')) {
          const amountFields = formFields.filter(
            f => f.name.toLowerCase().includes('amount') && f.type === FormFieldType.NUMBER
          );

          if (amountFields.length > 0) {
            // Calculate total from amount fields
            const total = amountFields.reduce((sum, f) => {
              const mapping = mappings.find(m => m.targetField === f.id);
              return sum + (mapping ? Number(mapping.sourceField) || 0 : 0);
            }, 0);

            // Add or update mapping for total field
            const existingMapping = mappings.find(m => m.targetField === field.id);
            if (existingMapping) {
              existingMapping.sourceField = String(total);
              existingMapping.confidence = 1.0;
            } else {
              mappings.push({
                sourceField: String(total),
                targetField: field.id,
                confidence: 1.0,
                coordinates: field.bounds,
              });
            }
          }
        }
      } catch (error) {
        logger.error('Calculation failed for field', { error, fieldId: field.id });
      }
    }
  }

  /**
   * Apply mappings to document
   */
  private async applyMappingsToDocument(
    document: Document,
    mappings: FieldMapping[],
    options: FormFillingOptions
  ): Promise<Document> {
    if (document.type === 'pdf') {
      return this.fillPDFForm(document, mappings, options);
    } else {
      // For other document types, create a new document with filled data
      return {
        ...document,
        content: Buffer.from(JSON.stringify({ mappings, originalDocument: document })),
      };
    }
  }

  /**
   * Fill PDF form with mappings
   */
  private async fillPDFForm(
    document: Document,
    mappings: FieldMapping[],
    _options: FormFillingOptions
  ): Promise<Document> {
    const formData: Record<string, any> = {};

    // Convert mappings to form data
    for (const mapping of mappings) {
      formData[mapping.targetField] = mapping.sourceField;
    }

    // Use PDF processor to fill the form
    if (!document.content) {
      throw new Error('Document content is missing');
    }
    const buffer = Buffer.isBuffer(document.content)
      ? document.content
      : Buffer.from(document.content as any);
    const filledPDFBuffer = await this.pdfProcessor.fillPDFForm(buffer, formData);

    return {
      ...document,
      content: filledPDFBuffer,
    };
  }

  /**
   * Calculate overall confidence score
   */
  private calculateOverallConfidence(mappings: FieldMapping[]): number {
    if (mappings.length === 0) return 0;

    const totalConfidence = mappings.reduce((sum, mapping) => sum + mapping.confidence, 0);
    return totalConfidence / mappings.length;
  }

  /**
   * Calculate metadata for results
   */
  private calculateMetadata(mappings: FieldMapping[], formFields: FormField[]) {
    const filledFields = mappings.length;
    const totalFields = formFields.length;
    const skippedFields = totalFields - filledFields;
    const errorFields = 0; // Would be calculated from validation results
    const averageConfidence = this.calculateOverallConfidence(mappings);

    return {
      totalFields,
      filledFields,
      skippedFields,
      errorFields,
      averageConfidence,
    };
  }

  /**
   * Merge default options with provided options
   */
  private mergeDefaultOptions(options: Partial<FormFillingOptions>): FormFillingOptions {
    return {
      useAIMapping: options.useAIMapping ?? true,
      confidenceThreshold: options.confidenceThreshold ?? 0.7,
      enablePreview: options.enablePreview ?? false,
      enableValidation: options.enableValidation ?? true,
      enableCalculations: options.enableCalculations ?? true,
      preserveOriginal: options.preserveOriginal ?? true,
      fillMode: options.fillMode ?? FormFillMode.AUTOMATIC,
    };
  }
}

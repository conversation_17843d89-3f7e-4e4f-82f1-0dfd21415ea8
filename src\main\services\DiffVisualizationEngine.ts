import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs-extra';
import { VisualDiff, ChangeType } from '../../shared/types/Timeline';

export interface DiffVisualizationOptions {
  colorScheme?: DiffColorScheme;
  highlightStyle?: HighlightStyle;
  exportFormats?: ExportFormat[];
  enableNavigation?: boolean;
  enableCollaboration?: boolean;
  lineNumbers?: boolean;
  contextLines?: number;
}

export interface DiffColorScheme {
  added: string;
  deleted: string;
  modified: string;
  unchanged: string;
  background: string;
  text: string;
  lineNumbers: string;
}

export enum HighlightStyle {
  BACKGROUND = 'background',
  BORDER = 'border',
  UNDERLINE = 'underline',
  STRIKETHROUGH = 'strikethrough',
}

export enum ExportFormat {
  HTML = 'html',
  PDF = 'pdf',
  JSON = 'json',
  MARKDOWN = 'markdown',
  CSV = 'csv',
}

export interface FormattedDiff {
  id: string;
  type: string;
  title: string;
  summary: DiffSummary;
  sections: DiffSection[];
  navigation: NavigationData;
  metadata: DiffMetadata;
}

export interface DiffSummary {
  totalChanges: number;
  addedItems: number;
  deletedItems: number;
  modifiedItems: number;
  similarity: number;
  processingTime: number;
}

export interface DiffSection {
  id: string;
  title: string;
  type: 'text' | 'table' | 'json' | 'xml' | 'form' | 'metadata' | 'image';
  content: DiffContent;
  statistics: SectionStatistics;
}

export interface DiffContent {
  lines?: FormattedLine[];
  table?: FormattedTable;
  json?: FormattedJson;
  xml?: FormattedXml;
  form?: FormattedForm;
  metadata?: FormattedMetadata;
  image?: FormattedImage;
}

export interface FormattedLine {
  id: string;
  lineNumber: number;
  type: ChangeType | 'unchanged';
  content: string;
  beforeLineNumber?: number;
  afterLineNumber?: number;
  highlightRanges?: HighlightRange[];
  annotations?: LineAnnotation[];
}

export interface HighlightRange {
  start: number;
  end: number;
  type: ChangeType;
  tooltip?: string;
}

export interface LineAnnotation {
  id: string;
  type: 'comment' | 'suggestion' | 'error' | 'warning';
  content: string;
  author?: string;
  timestamp?: Date;
}

export interface FormattedTable {
  headers: string[];
  rows: FormattedTableRow[];
  columnChanges: ColumnChangeInfo[];
}

export interface FormattedTableRow {
  id: string;
  type: ChangeType | 'unchanged';
  cells: FormattedTableCell[];
}

export interface FormattedTableCell {
  id: string;
  content: string;
  type: ChangeType | 'unchanged';
  columnName: string;
  tooltip?: string;
}

export interface ColumnChangeInfo {
  columnName: string;
  type: ChangeType;
  description: string;
}

export interface FormattedJson {
  tree: JsonTreeNode[];
  expandedPaths: string[];
}

export interface JsonTreeNode {
  id: string;
  path: string;
  key: string;
  value: any;
  type: ChangeType | 'unchanged';
  valueType: string;
  level: number;
  hasChildren: boolean;
  isExpanded: boolean;
  children?: JsonTreeNode[];
}

export interface FormattedXml {
  elements: XmlElement[];
  expandedPaths: string[];
}

export interface XmlElement {
  id: string;
  xpath: string;
  tagName: string;
  type: ChangeType | 'unchanged';
  attributes: XmlAttribute[];
  content?: string;
  level: number;
  hasChildren: boolean;
  isExpanded: boolean;
  children?: XmlElement[];
}

export interface XmlAttribute {
  name: string;
  value: string;
  type: ChangeType | 'unchanged';
}

export interface FormattedForm {
  fields: FormattedField[];
  sections: FormSection[];
}

export interface FormattedField {
  id: string;
  name: string;
  label: string;
  type: ChangeType | 'unchanged';
  beforeValue?: any;
  afterValue?: any;
  validation?: FieldValidation;
}

export interface FieldValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface FormSection {
  title: string;
  fields: string[];
}

export interface FormattedMetadata {
  properties: MetadataProperty[];
  categories: MetadataCategory[];
}

export interface MetadataProperty {
  id: string;
  name: string;
  type: ChangeType | 'unchanged';
  beforeValue?: any;
  afterValue?: any;
  category: string;
  description?: string;
}

export interface MetadataCategory {
  name: string;
  properties: string[];
  changeCount: number;
}

export interface FormattedImage {
  beforeImage?: ImageInfo;
  afterImage?: ImageInfo;
  diffImage?: ImageInfo;
  annotations: ImageAnnotation[];
  hotspots: ImageHotspot[];
}

export interface ImageInfo {
  url: string;
  width: number;
  height: number;
  format: string;
  size: number;
}

export interface ImageAnnotation {
  id: string;
  type: 'highlight' | 'comment' | 'arrow';
  coordinates: { x: number; y: number; width: number; height: number };
  content: string;
  color: string;
}

export interface ImageHotspot {
  id: string;
  coordinates: { x: number; y: number; width: number; height: number };
  type: ChangeType;
  description: string;
  similarity: number;
}

export interface NavigationData {
  changePoints: ChangePoint[];
  sections: NavigationSection[];
  quickJumps: QuickJump[];
}

export interface ChangePoint {
  id: string;
  type: ChangeType;
  sectionId: string;
  lineNumber?: number;
  description: string;
  severity: 'low' | 'medium' | 'high';
}

export interface NavigationSection {
  id: string;
  title: string;
  changeCount: number;
  startLine?: number;
  endLine?: number;
}

export interface QuickJump {
  id: string;
  label: string;
  target: string;
  icon?: string;
}

export interface SectionStatistics {
  totalItems: number;
  changedItems: number;
  addedItems: number;
  deletedItems: number;
  similarity: number;
}

export interface DiffMetadata {
  createdAt: Date;
  algorithm: string;
  options: DiffVisualizationOptions;
  performance: PerformanceMetrics;
  collaboration?: CollaborationInfo;
}

export interface PerformanceMetrics {
  processingTime: number;
  memoryUsage: number;
  cacheHits: number;
  cacheMisses: number;
}

export interface CollaborationInfo {
  sessionId: string;
  participants: Participant[];
  comments: Comment[];
  suggestions: Suggestion[];
}

export interface Participant {
  id: string;
  name: string;
  role: 'viewer' | 'reviewer' | 'editor';
  joinedAt: Date;
  isActive: boolean;
}

export interface Comment {
  id: string;
  authorId: string;
  content: string;
  target: CommentTarget;
  createdAt: Date;
  updatedAt?: Date;
  replies?: Comment[];
}

export interface CommentTarget {
  sectionId: string;
  lineNumber?: number;
  coordinates?: { x: number; y: number; width: number; height: number };
}

export interface Suggestion {
  id: string;
  authorId: string;
  type: 'change' | 'addition' | 'deletion';
  content: string;
  target: CommentTarget;
  status: 'pending' | 'accepted' | 'rejected';
  createdAt: Date;
}

export class DiffVisualizationEngine {
  private options: Required<DiffVisualizationOptions>;
  private defaultColorScheme: DiffColorScheme = {
    added: '#d4edda',
    deleted: '#f8d7da',
    modified: '#fff3cd',
    unchanged: '#ffffff',
    background: '#f8f9fa',
    text: '#212529',
    lineNumbers: '#6c757d',
  };

  constructor(options: DiffVisualizationOptions = {}) {
    this.options = {
      colorScheme: options.colorScheme || this.defaultColorScheme,
      highlightStyle: options.highlightStyle || HighlightStyle.BACKGROUND,
      exportFormats: options.exportFormats || [ExportFormat.HTML, ExportFormat.JSON],
      enableNavigation: options.enableNavigation ?? true,
      enableCollaboration: options.enableCollaboration ?? false,
      lineNumbers: options.lineNumbers ?? true,
      contextLines: options.contextLines || 3,
    };
  }

  /**
   * Format diff for UI consumption
   */
  public formatDiff(visualDiff: VisualDiff): FormattedDiff {
    const sections: DiffSection[] = [];

    // Format different types of diffs
    if (visualDiff.textDiff) {
      sections.push(this.formatTextDiff(visualDiff.textDiff));
    }

    if (visualDiff.structuralDiff) {
      sections.push(this.formatStructuralDiff(visualDiff.structuralDiff));
    }

    // Create navigation data
    const navigation = this.createNavigationData(sections);

    // Calculate summary
    const summary = this.calculateSummary(sections);

    const formattedDiff: FormattedDiff = {
      id: visualDiff.id,
      type: visualDiff.type,
      title: `${visualDiff.type.toUpperCase()} Diff`,
      summary,
      sections,
      navigation,
      metadata: {
        createdAt: visualDiff.metadata.createdAt,
        algorithm: visualDiff.metadata.algorithm,
        options: this.options,
        performance: {
          processingTime: visualDiff.metadata.processingTime,
          memoryUsage: 0, // Would be calculated in real implementation
          cacheHits: 0,
          cacheMisses: 0,
        },
      },
    };

    return formattedDiff;
  }

  /**
   * Format text diff for display
   */
  private formatTextDiff(textDiff: any): DiffSection {
    const lines: FormattedLine[] = textDiff.lines.map((line: any, index: number) => ({
      id: uuidv4(),
      lineNumber: index + 1,
      type: line.type,
      content: line.content,
      beforeLineNumber: line.beforeLineNumber,
      afterLineNumber: line.afterLineNumber,
      highlightRanges: this.createHighlightRanges(line),
      annotations: [],
    }));

    return {
      id: uuidv4(),
      title: 'Text Changes',
      type: 'text',
      content: { lines },
      statistics: {
        totalItems: textDiff.statistics.totalLines,
        changedItems:
          textDiff.statistics.addedLines +
          textDiff.statistics.deletedLines +
          textDiff.statistics.modifiedLines,
        addedItems: textDiff.statistics.addedLines,
        deletedItems: textDiff.statistics.deletedLines,
        similarity:
          1 -
          (textDiff.statistics.addedLines + textDiff.statistics.deletedLines) /
            textDiff.statistics.totalLines,
      },
    };
  }

  /**
   * Format structural diff for display
   */
  private formatStructuralDiff(structuralDiff: any): DiffSection {
    const jsonTree = this.createJsonTree(structuralDiff.elements);

    return {
      id: uuidv4(),
      title: 'Structural Changes',
      type: 'json',
      content: {
        json: {
          tree: jsonTree,
          expandedPaths: [],
        },
      },
      statistics: {
        totalItems: structuralDiff.statistics.totalElements,
        changedItems:
          structuralDiff.statistics.addedElements +
          structuralDiff.statistics.deletedElements +
          structuralDiff.statistics.modifiedElements,
        addedItems: structuralDiff.statistics.addedElements,
        deletedItems: structuralDiff.statistics.deletedElements,
        similarity:
          1 -
          (structuralDiff.statistics.addedElements + structuralDiff.statistics.deletedElements) /
            structuralDiff.statistics.totalElements,
      },
    };
  }

  /**
   * Create highlight ranges for a line
   */
  private createHighlightRanges(line: any): HighlightRange[] {
    if (line.type === 'unchanged') return [];

    return [
      {
        start: 0,
        end: line.content.length,
        type: line.type,
        tooltip: this.getChangeTooltip(line.type),
      },
    ];
  }

  /**
   * Create JSON tree from structural elements
   */
  private createJsonTree(elements: any[]): JsonTreeNode[] {
    return elements.map((element: any) => ({
      id: uuidv4(),
      path: element.path,
      key: element.path.split('.').pop() || '',
      value: element.afterProperties || element.beforeProperties,
      type: element.changeType,
      valueType: element.type,
      level: element.path.split('.').length - 1,
      hasChildren: false,
      isExpanded: false,
    }));
  }

  /**
   * Create navigation data from sections
   */
  private createNavigationData(sections: DiffSection[]): NavigationData {
    const changePoints: ChangePoint[] = [];
    const navigationSections: NavigationSection[] = [];
    const quickJumps: QuickJump[] = [];

    sections.forEach(section => {
      navigationSections.push({
        id: section.id,
        title: section.title,
        changeCount: section.statistics.changedItems,
      });

      // Add quick jump for sections with many changes
      if (section.statistics.changedItems > 10) {
        quickJumps.push({
          id: uuidv4(),
          label: `${section.title} (${section.statistics.changedItems} changes)`,
          target: section.id,
          icon: this.getSectionIcon(section.type),
        });
      }

      // Create change points for significant changes
      if (section.content.lines) {
        section.content.lines.forEach(line => {
          if (line.type !== 'unchanged') {
            changePoints.push({
              id: uuidv4(),
              type: line.type as ChangeType,
              sectionId: section.id,
              lineNumber: line.lineNumber,
              description: `Line ${line.lineNumber}: ${this.getChangeDescription(line.type as ChangeType)}`,
              severity: this.getChangeSeverity(line.type as ChangeType),
            });
          }
        });
      }
    });

    return {
      changePoints,
      sections: navigationSections,
      quickJumps,
    };
  }

  /**
   * Calculate summary statistics
   */
  private calculateSummary(sections: DiffSection[]): DiffSummary {
    const totalChanges = sections.reduce(
      (sum, section) => sum + section.statistics.changedItems,
      0
    );
    const addedItems = sections.reduce((sum, section) => sum + section.statistics.addedItems, 0);
    const deletedItems = sections.reduce(
      (sum, section) => sum + section.statistics.deletedItems,
      0
    );
    const modifiedItems = totalChanges - addedItems - deletedItems;
    const totalItems = sections.reduce((sum, section) => sum + section.statistics.totalItems, 0);
    const similarity = totalItems > 0 ? 1 - totalChanges / totalItems : 1.0;

    return {
      totalChanges,
      addedItems,
      deletedItems,
      modifiedItems,
      similarity,
      processingTime: 0, // Would be calculated in real implementation
    };
  }

  /**
   * Export diff in specified format
   */
  public async exportDiff(
    formattedDiff: FormattedDiff,
    format: ExportFormat,
    outputPath: string
  ): Promise<void> {
    switch (format) {
      case ExportFormat.HTML:
        await this.exportToHTML(formattedDiff, outputPath);
        break;
      case ExportFormat.PDF:
        await this.exportToPDF(formattedDiff, outputPath);
        break;
      case ExportFormat.JSON:
        await this.exportToJSON(formattedDiff, outputPath);
        break;
      case ExportFormat.MARKDOWN:
        await this.exportToMarkdown(formattedDiff, outputPath);
        break;
      case ExportFormat.CSV:
        await this.exportToCSV(formattedDiff, outputPath);
        break;
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * Export to HTML format
   */
  private async exportToHTML(formattedDiff: FormattedDiff, outputPath: string): Promise<void> {
    const html = this.generateHTML(formattedDiff);
    await fs.writeFile(outputPath, html, 'utf-8');
  }

  /**
   * Export to PDF format
   */
  private async exportToPDF(formattedDiff: FormattedDiff, outputPath: string): Promise<void> {
    // This would use a library like puppeteer to convert HTML to PDF
    const html = this.generateHTML(formattedDiff);
    // Implementation would convert HTML to PDF
    await fs.writeFile(outputPath.replace('.pdf', '.html'), html, 'utf-8');
  }

  /**
   * Export to JSON format
   */
  private async exportToJSON(formattedDiff: FormattedDiff, outputPath: string): Promise<void> {
    const json = JSON.stringify(formattedDiff, null, 2);
    await fs.writeFile(outputPath, json, 'utf-8');
  }

  /**
   * Export to Markdown format
   */
  private async exportToMarkdown(formattedDiff: FormattedDiff, outputPath: string): Promise<void> {
    const markdown = this.generateMarkdown(formattedDiff);
    await fs.writeFile(outputPath, markdown, 'utf-8');
  }

  /**
   * Export to CSV format
   */
  private async exportToCSV(formattedDiff: FormattedDiff, outputPath: string): Promise<void> {
    const csv = this.generateCSV(formattedDiff);
    await fs.writeFile(outputPath, csv, 'utf-8');
  }

  /**
   * Generate HTML representation
   */
  private generateHTML(formattedDiff: FormattedDiff): string {
    const styles = this.generateCSS();
    const content = this.generateHTMLContent(formattedDiff);

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${formattedDiff.title}</title>
    <style>${styles}</style>
</head>
<body>
    <div class="diff-container">
        <header class="diff-header">
            <h1>${formattedDiff.title}</h1>
            <div class="diff-summary">
                <span class="stat">Total Changes: ${formattedDiff.summary.totalChanges}</span>
                <span class="stat">Added: ${formattedDiff.summary.addedItems}</span>
                <span class="stat">Deleted: ${formattedDiff.summary.deletedItems}</span>
                <span class="stat">Modified: ${formattedDiff.summary.modifiedItems}</span>
                <span class="stat">Similarity: ${(formattedDiff.summary.similarity * 100).toFixed(1)}%</span>
            </div>
        </header>
        <nav class="diff-navigation">
            ${this.generateNavigationHTML(formattedDiff.navigation)}
        </nav>
        <main class="diff-content">
            ${content}
        </main>
    </div>
    <script>${this.generateJavaScript()}</script>
</body>
</html>`;
  }

  /**
   * Generate CSS styles
   */
  private generateCSS(): string {
    const colors = this.options.colorScheme;
    return `
      .diff-container { font-family: 'Courier New', monospace; }
      .diff-header { background: ${colors.background}; padding: 1rem; border-bottom: 1px solid #ddd; }
      .diff-summary { display: flex; gap: 1rem; margin-top: 0.5rem; }
      .stat { padding: 0.25rem 0.5rem; background: #f8f9fa; border-radius: 4px; }
      .diff-navigation { background: #f8f9fa; padding: 1rem; }
      .diff-content { padding: 1rem; }
      .line-added { background-color: ${colors.added}; }
      .line-deleted { background-color: ${colors.deleted}; }
      .line-modified { background-color: ${colors.modified}; }
      .line-unchanged { background-color: ${colors.unchanged}; }
      .line-number { color: ${colors.lineNumbers}; margin-right: 1rem; }
      .diff-line { display: flex; padding: 0.25rem; }
      .diff-section { margin-bottom: 2rem; border: 1px solid #ddd; border-radius: 4px; }
      .section-header { background: #f8f9fa; padding: 0.5rem 1rem; font-weight: bold; }
      .section-content { padding: 1rem; }
    `;
  }

  /**
   * Generate HTML content for sections
   */
  private generateHTMLContent(formattedDiff: FormattedDiff): string {
    return formattedDiff.sections
      .map(
        section => `
      <div class="diff-section" id="${section.id}">
        <div class="section-header">${section.title}</div>
        <div class="section-content">
          ${this.generateSectionHTML(section)}
        </div>
      </div>
    `
      )
      .join('');
  }

  /**
   * Generate HTML for a section
   */
  private generateSectionHTML(section: DiffSection): string {
    if (section.content.lines) {
      return section.content.lines
        .map(
          line => `
        <div class="diff-line line-${line.type}">
          ${this.options.lineNumbers ? `<span class="line-number">${line.lineNumber}</span>` : ''}
          <span class="line-content">${this.escapeHTML(line.content)}</span>
        </div>
      `
        )
        .join('');
    }

    return '<div>Content not available for display</div>';
  }

  /**
   * Generate navigation HTML
   */
  private generateNavigationHTML(navigation: NavigationData): string {
    return `
      <div class="quick-jumps">
        ${navigation.quickJumps
          .map(
            jump => `
          <a href="#${jump.target}" class="quick-jump">${jump.label}</a>
        `
          )
          .join('')}
      </div>
    `;
  }

  /**
   * Generate JavaScript for interactivity
   */
  private generateJavaScript(): string {
    return `
      // Add smooth scrolling for navigation
      document.querySelectorAll('.quick-jump').forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) {
            target.scrollIntoView({ behavior: 'smooth' });
          }
        });
      });
    `;
  }

  /**
   * Generate Markdown representation
   */
  private generateMarkdown(formattedDiff: FormattedDiff): string {
    let markdown = `# ${formattedDiff.title}\n\n`;

    // Add summary
    markdown += `## Summary\n\n`;
    markdown += `- **Total Changes:** ${formattedDiff.summary.totalChanges}\n`;
    markdown += `- **Added:** ${formattedDiff.summary.addedItems}\n`;
    markdown += `- **Deleted:** ${formattedDiff.summary.deletedItems}\n`;
    markdown += `- **Modified:** ${formattedDiff.summary.modifiedItems}\n`;
    markdown += `- **Similarity:** ${(formattedDiff.summary.similarity * 100).toFixed(1)}%\n\n`;

    // Add sections
    formattedDiff.sections.forEach(section => {
      markdown += `## ${section.title}\n\n`;

      if (section.content.lines) {
        markdown += '```diff\n';
        section.content.lines.forEach(line => {
          const prefix = line.type === 'added' ? '+' : line.type === 'deleted' ? '-' : ' ';
          markdown += `${prefix} ${line.content}\n`;
        });
        markdown += '```\n\n';
      }
    });

    return markdown;
  }

  /**
   * Generate CSV representation
   */
  private generateCSV(formattedDiff: FormattedDiff): string {
    let csv = 'Section,Line Number,Type,Content\n';

    formattedDiff.sections.forEach(section => {
      if (section.content.lines) {
        section.content.lines.forEach(line => {
          const content = line.content.replace(/"/g, '""'); // Escape quotes
          csv += `"${section.title}",${line.lineNumber},"${line.type}","${content}"\n`;
        });
      }
    });

    return csv;
  }

  /**
   * Escape HTML characters
   */
  private escapeHTML(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  /**
   * Get tooltip text for change type
   */
  private getChangeTooltip(changeType: string): string {
    const tooltips: Record<string, string> = {
      added: 'This content was added',
      deleted: 'This content was deleted',
      modified: 'This content was modified',
    };
    return tooltips[changeType] || '';
  }

  /**
   * Get icon for section type
   */
  private getSectionIcon(sectionType: string): string {
    const icons: Record<string, string> = {
      text: 'text-icon',
      table: 'table-icon',
      json: 'json-icon',
      xml: 'xml-icon',
      form: 'form-icon',
      metadata: 'metadata-icon',
      image: 'image-icon',
    };
    return icons[sectionType] || 'default-icon';
  }

  /**
   * Get change description
   */
  private getChangeDescription(changeType: ChangeType): string {
    const descriptions: Record<ChangeType, string> = {
      [ChangeType.ADDED]: 'Content added',
      [ChangeType.DELETED]: 'Content deleted',
      [ChangeType.MODIFIED]: 'Content modified',
      [ChangeType.MOVED]: 'Content moved',
      [ChangeType.RENAMED]: 'Content renamed',
    };
    return descriptions[changeType] || 'Content changed';
  }

  /**
   * Get change severity
   */
  private getChangeSeverity(changeType: ChangeType): 'low' | 'medium' | 'high' {
    const severities: Record<ChangeType, 'low' | 'medium' | 'high'> = {
      [ChangeType.ADDED]: 'medium',
      [ChangeType.DELETED]: 'high',
      [ChangeType.MODIFIED]: 'medium',
      [ChangeType.MOVED]: 'low',
      [ChangeType.RENAMED]: 'low',
    };
    return severities[changeType] || 'medium';
  }

  /**
   * Create collaboration session
   */
  public createCollaborationSession(_formattedDiff: FormattedDiff): CollaborationInfo {
    return {
      sessionId: uuidv4(),
      participants: [],
      comments: [],
      suggestions: [],
    };
  }

  /**
   * Add comment to diff
   */
  public addComment(
    collaborationInfo: CollaborationInfo,
    authorId: string,
    content: string,
    target: CommentTarget
  ): Comment {
    const comment: Comment = {
      id: uuidv4(),
      authorId,
      content,
      target,
      createdAt: new Date(),
    };

    collaborationInfo.comments.push(comment);
    return comment;
  }

  /**
   * Add suggestion to diff
   */
  public addSuggestion(
    collaborationInfo: CollaborationInfo,
    authorId: string,
    type: 'change' | 'addition' | 'deletion',
    content: string,
    target: CommentTarget
  ): Suggestion {
    const suggestion: Suggestion = {
      id: uuidv4(),
      authorId,
      type,
      content,
      target,
      status: 'pending',
      createdAt: new Date(),
    };

    collaborationInfo.suggestions.push(suggestion);
    return suggestion;
  }

  /**
   * Jump to specific change
   */
  public jumpToChange(formattedDiff: FormattedDiff, changeId: string): ChangePoint | null {
    const changePoint = formattedDiff.navigation.changePoints.find(cp => cp.id === changeId);
    return changePoint || null;
    return null;
  }

  /**
   * Get next change from current position
   */
  public getNextChange(
    formattedDiff: FormattedDiff,
    currentSectionId: string,
    currentLine?: number
  ): ChangePoint | null {
    const changePoints = formattedDiff.navigation.changePoints;

    for (let i = 0; i < changePoints.length; i++) {
      const change = changePoints[i];
      if (change && change.sectionId === currentSectionId && change.lineNumber && currentLine) {
        if (change.lineNumber > currentLine) {
          return change;
        }
      } else if (change && change.sectionId > currentSectionId) {
        return change;
      }
    }

    return null;
  }

  /**
   * Get previous change from current position
   */
  public getPreviousChange(
    formattedDiff: FormattedDiff,
    currentSectionId: string,
    currentLine?: number
  ): ChangePoint | null {
    const changePoints = formattedDiff.navigation.changePoints;

    for (let i = changePoints.length - 1; i >= 0; i--) {
      const change = changePoints[i];
      if (change && change.sectionId === currentSectionId && change.lineNumber && currentLine) {
        if (change.lineNumber < currentLine) {
          return change;
        }
      } else if (change && change.sectionId < currentSectionId) {
        return change;
      }
    }

    return null;
  }
}

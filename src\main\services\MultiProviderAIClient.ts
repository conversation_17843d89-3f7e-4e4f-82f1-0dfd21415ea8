import { logger } from '../utils/logger';
import { build<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>pi<PERSON>and<PERSON> } from '../../shared/ai-config';
import { ApiConfiguration, ApiProvider } from '../../shared/ai-config/api';
import Anthropic from '@anthropic-ai/sdk';

type Mode = 'plan' | 'act';

interface ExtendedApiConfiguration extends ApiConfiguration {
  priority?: number;
}
import { ApiStreamUsageChunk } from '../../shared/ai-config/transform/stream';

export interface TokenUsage {
  inputTokens: number;
  outputTokens: number;
  cacheWriteTokens?: number;
  cacheReadTokens?: number;
  thoughtsTokenCount?: number;
  totalTokens: number;
  cost: number;
  timestamp: Date;
}

export interface AIRequest {
  id: string;
  operation: string;
  systemPrompt?: string;
  messages: Array<{ role: 'user' | 'assistant' | 'system'; content: string }>;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
}

export interface AIResponse {
  id: string;
  content: string;
  usage: TokenUsage;
  model: string;
  provider: string;
  success: boolean;
  error?: string;
  processingTime: number;
  reasoning?: string;
}

export interface EmbeddingRequest {
  text: string;
  model?: string;
  dimensions?: number;
}

export interface EmbeddingResponse {
  embedding: number[];
  dimensions: number;
  model: string;
  usage: TokenUsage;
  provider: string;
}

export interface ProviderConfig {
  provider: string;
  apiKey: string;
  baseUrl?: string;
  model: string;
  maxTokens?: number;
  temperature?: number;
  enabled: boolean;
  priority: number;
}

/**
 * Multi-provider AI client that supports all major AI providers
 * without LangChain dependencies, with comprehensive token tracking
 */
export class MultiProviderAIClient {
  private readonly handlers = new Map<string, ApiHandler>();
  private readonly configurations = new Map<string, ExtendedApiConfiguration>();
  private fallbackOrder: string[] = [];
  private currentProvider: string | null = null;

  constructor() {
    this.initializeProviders();
  }

  /**
   * Initialize available providers
   */
  private initializeProviders(): void {
    // This will be populated by the configuration system
    logger.info('Multi-provider AI client initialized');
  }

  /**
   * Configure a provider
   */
  configureProvider(
    provider: ApiProvider,
    config: Partial<ApiConfiguration>,
    priority: number = 0
  ): void {
    try {
      // Build complete configuration with defaults
      const fullConfig: ExtendedApiConfiguration = {
        planModeApiProvider: provider,
        actModeApiProvider: provider,
        priority,
        ...config,
      };

      this.configurations.set(provider, fullConfig);

      // Create handler for both plan and act modes
      const planHandler = buildApiHandler(fullConfig, 'plan');
      const actHandler = buildApiHandler(fullConfig, 'act');

      this.handlers.set(`${provider}_plan`, planHandler);
      this.handlers.set(`${provider}_act`, actHandler);

      // Update fallback order based on priority
      this.updateFallbackOrder(provider, priority);

      logger.info(`Provider ${provider} configured successfully`);
    } catch (error) {
      logger.error(`Failed to configure provider ${provider}`, error);
      throw error;
    }
  }

  /**
   * Update fallback order based on priority
   */
  private updateFallbackOrder(provider: string, priority: number): void {
    // Remove provider if it already exists
    this.fallbackOrder = this.fallbackOrder.filter(p => p !== provider);

    // Insert at correct position based on priority (higher priority = earlier in list)
    let insertIndex = 0;
    for (let i = 0; i < this.fallbackOrder.length; i++) {
      const providerId = this.fallbackOrder[i];
      if (providerId) {
        const existingConfig = this.configurations.get(providerId);
        if (existingConfig && (existingConfig.priority || 0) < priority) {
          break;
        }
      }
      insertIndex = i + 1;
    }

    this.fallbackOrder.splice(insertIndex, 0, provider);
  }

  /**
   * Set the current provider
   */
  setProvider(provider: string): void {
    if (!this.configurations.has(provider)) {
      throw new Error(`Provider ${provider} is not configured`);
    }
    this.currentProvider = provider;
    logger.info(`Switched to provider: ${provider}`);
  }

  /**
   * Get the current provider or fallback to the first available
   */
  private getCurrentProvider(): string {
    if (this.currentProvider && this.configurations.has(this.currentProvider)) {
      return this.currentProvider;
    }

    if (this.fallbackOrder.length === 0) {
      throw new Error('No AI providers configured');
    }

    return this.fallbackOrder[0] || '';
  }

  /**
   * Create a chat completion
   */
  async createCompletion(request: AIRequest, mode: Mode = 'act'): Promise<AIResponse> {
    const startTime = Date.now();
    const provider = this.getCurrentProvider();
    let lastError: Error | null = null;

    // Try current provider first, then fallbacks
    const providersToTry = [provider, ...this.fallbackOrder.filter(p => p !== provider)];

    for (const tryProvider of providersToTry) {
      const tryHandlerKey = `${tryProvider}_${mode}`;
      const handler = this.handlers.get(tryHandlerKey);

      if (!handler) {
        continue;
      }

      try {
        const modelInfo = handler.getModel();
        const systemPrompt = request.systemPrompt || 'You are a helpful AI assistant.';

        // Filter out system messages and pass only user/assistant messages
        const nonSystemMessages = request.messages.filter(
          msg => msg.role !== 'system'
        ) as Anthropic.Messages.MessageParam[];

        // Create the API stream
        const stream = handler.createMessage(systemPrompt, nonSystemMessages);

        let content = '';
        let reasoning = '';
        let usage: ApiStreamUsageChunk | undefined;

        // Process the stream
        for await (const chunk of stream) {
          switch (chunk.type) {
            case 'text':
              content += chunk.text;
              break;
            case 'reasoning':
              reasoning += chunk.reasoning;
              break;
            case 'usage':
              usage = chunk;
              break;
          }
        }

        const processingTime = Date.now() - startTime;

        // Create token usage object
        const tokenUsage: TokenUsage = {
          inputTokens: usage?.inputTokens || 0,
          outputTokens: usage?.outputTokens || 0,
          cacheWriteTokens: usage?.cacheWriteTokens || 0,
          cacheReadTokens: usage?.cacheReadTokens || 0,
          thoughtsTokenCount: usage?.thoughtsTokenCount || 0,
          totalTokens: (usage?.inputTokens || 0) + (usage?.outputTokens || 0),
          cost: usage?.totalCost || 0,
          timestamp: new Date(),
        };

        // Removed telemetry tracking

        const response: AIResponse = {
          id: request.id,
          content,
          usage: tokenUsage,
          model: modelInfo.id,
          provider: tryProvider,
          success: true,
          processingTime,
          reasoning: reasoning || '',
        };

        logger.info('AI completion successful', {
          provider: tryProvider,
          model: modelInfo.id,
          tokens: tokenUsage.totalTokens,
          cost: tokenUsage.cost,
          processingTime,
        });

        return response;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        logger.warn(`Provider ${tryProvider} failed, trying next`, { error: lastError.message });

        // Removed telemetry tracking

        continue;
      }
    }

    // All providers failed
    const errorMessage = `All providers failed. Last error: ${lastError?.message || 'Unknown error'}`;

    logger.error('All AI providers failed', { error: errorMessage });
    throw new Error(errorMessage);
  }

  /**
   * Generate embeddings (simplified implementation)
   */
  generateEmbeddings(_request: EmbeddingRequest): Promise<EmbeddingResponse> {
    // For now, return a placeholder implementation
    // This would need to be implemented based on provider capabilities
    throw new Error('Embedding generation not yet implemented in multi-provider client');
  }

  /**
   * Get available providers
   */
  getAvailableProviders(): string[] {
    return Array.from(this.configurations.keys());
  }

  /**
   * Get provider configuration
   */
  getProviderConfig(provider: string): ApiConfiguration | undefined {
    return this.configurations.get(provider);
  }

  /**
   * Check if a provider is available
   */
  isProviderAvailable(provider: string): boolean {
    return this.configurations.has(provider) && this.handlers.has(`${provider}_act`);
  }

  /**
   * Get current provider info
   */
  getCurrentProviderInfo(): { provider: string; model: string } | null {
    const provider = this.getCurrentProvider();
    const handler = this.handlers.get(`${provider}_act`);

    if (!handler) {
      return null;
    }

    const modelInfo = handler.getModel();
    return {
      provider,
      model: modelInfo.id,
    };
  }

  /**
   * Health check for all configured providers
   */
  healthCheck(): Record<string, boolean> {
    const results: Record<string, boolean> = {};

    for (const provider of this.getAvailableProviders()) {
      try {
        const handler = this.handlers.get(`${provider}_act`);
        if (handler) {
          // Simple health check - try to get model info
          handler.getModel();
          results[provider] = true;
        } else {
          results[provider] = false;
        }
      } catch (error) {
        results[provider] = false;
        logger.warn(`Health check failed for provider ${provider}`, error);
      }
    }

    return results;
  }

  /**
   * Clear all configurations
   */
  clearProviders(): void {
    this.configurations.clear();
    this.handlers.clear();
    this.fallbackOrder = [];
    this.currentProvider = null;
    logger.info('All provider configurations cleared');
  }
}

// Export singleton instance
export const multiProviderAIClient = new MultiProviderAIClient();

#!/usr/bin/env node

/**
 * Setup Validation Script
 *
 * This script validates that the development environment is properly configured.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function checkFile(filePath, description) {
  if (fs.existsSync(filePath)) {
    logSuccess(`${description} exists`);
    return true;
  } else {
    logError(`${description} is missing`);
    return false;
  }
}

function checkScript(scriptName, description) {
  try {
    execSync(`npm run ${scriptName} --silent`, { stdio: 'pipe' });
    logSuccess(`${description} works`);
    return true;
  } catch (error) {
    logError(`${description} failed`);
    return false;
  }
}

function main() {
  log('🔍 Validating Development Setup', colors.bright + colors.cyan);
  log('');

  let allChecksPass = true;

  // Check essential files
  log('📁 Checking essential files:', colors.bright);
  allChecksPass &= checkFile('package.json', 'package.json');
  allChecksPass &= checkFile('.env.example', '.env.example');
  allChecksPass &= checkFile('.gitignore', '.gitignore');
  allChecksPass &= checkFile('.eslintrc.js', 'ESLint configuration');
  allChecksPass &= checkFile('.prettierrc', 'Prettier configuration');
  allChecksPass &= checkFile('tsconfig.json', 'TypeScript configuration');
  allChecksPass &= checkFile('jest.config.js', 'Jest configuration');
  allChecksPass &= checkFile('tailwind.config.js', 'Tailwind configuration');
  allChecksPass &= checkFile('knexfile.js', 'Knex configuration');
  log('');

  // Check Husky setup
  log('🪝 Checking Git hooks:', colors.bright);
  allChecksPass &= checkFile('.husky/pre-commit', 'Pre-commit hook');
  allChecksPass &= checkFile('.husky/pre-push', 'Pre-push hook');
  allChecksPass &= checkFile('.lintstagedrc.js', 'Lint-staged configuration');
  log('');

  // Check VS Code setup
  log('💻 Checking VS Code configuration:', colors.bright);
  allChecksPass &= checkFile('.vscode/settings.json', 'VS Code settings');
  allChecksPass &= checkFile('.vscode/extensions.json', 'VS Code extensions');
  allChecksPass &= checkFile('.vscode/launch.json', 'VS Code launch configuration');
  allChecksPass &= checkFile('.vscode/tasks.json', 'VS Code tasks');
  allChecksPass &= checkFile('.vscode/ai-document-processor.code-workspace', 'VS Code workspace');
  log('');

  // Check build configuration
  log('⚙️  Checking build configuration:', colors.bright);
  allChecksPass &= checkFile('config/webpack.main.config.js', 'Webpack main config');
  allChecksPass &= checkFile('config/webpack.renderer.config.js', 'Webpack renderer config');
  allChecksPass &= checkFile('config/webpack.preload.config.js', 'Webpack preload config');
  allChecksPass &= checkFile('forge.config.js', 'Electron Forge config');
  log('');

  // Check scripts
  log('🔧 Checking npm scripts:', colors.bright);
  allChecksPass &= checkScript('type-check', 'Type checking');

  // Check if node_modules exists
  if (fs.existsSync('node_modules')) {
    logSuccess('Dependencies are installed');
  } else {
    logError('Dependencies are not installed. Run "npm install"');
    allChecksPass = false;
  }

  log('');

  if (allChecksPass) {
    log('🎉 All checks passed! Your development environment is ready.', colors.green + colors.bright);
    log('');
    log('Next steps:', colors.bright);
    log('• Run "npm run dev" to start development', colors.cyan);
    log('• Open VS Code and install recommended extensions', colors.cyan);
    log('• Update your .env file with actual values', colors.cyan);
  } else {
    log('❌ Some checks failed. Please fix the issues above.', colors.red + colors.bright);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };

import '@testing-library/jest-dom';

// Mock Electron APIs
const mockElectronAPI = {
  openFile: jest.fn(),
  saveFile: jest.fn(),
  processDocument: jest.fn(),
  generateEmbeddings: jest.fn(),
  performReasoning: jest.fn(),
  storeInformation: jest.fn(),
  queryInformation: jest.fn(),
  createCheckpoint: jest.fn(),
  undo: jest.fn(),
  redo: jest.fn(),
  getAppVersion: jest.fn(),
  showMessageBox: jest.fn(),
  onDocumentProcessed: jest.fn(),
  onAIResponse: jest.fn(),
  removeAllListeners: jest.fn(),
};

// Mock window.electronAPI (only in browser-like environments)
if (typeof window !== 'undefined') {
  Object.defineProperty(window, 'electronAPI', {
    value: mockElectronAPI,
    writable: true,
  });

  // Mock window.electronEnv
  Object.defineProperty(window, 'electronEnv', {
    value: {
      NODE_ENV: 'test',
      platform: 'test',
      arch: 'x64',
      versions: {
        node: '18.0.0',
        electron: '37.2.5',
      },
    },
    writable: true,
  });
} else {
  // In Node.js environment, mock as global
  (global as any).electronAPI = mockElectronAPI;
}

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock matchMedia (only in browser-like environments)
if (typeof window !== 'undefined') {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(), // deprecated
      removeListener: jest.fn(), // deprecated
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });

  // Mock scrollTo
  Object.defineProperty(window, 'scrollTo', {
    value: jest.fn(),
    writable: true,
  });
}

// Suppress console errors in tests unless explicitly needed
const originalError = console.error;
beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is no longer supported')
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});

{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false, "quoteProps": "as-needed", "jsxSingleQuote": true, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxBracketSameLine": false, "proseWrap": "preserve", "requirePragma": false, "vueIndentScriptAndStyle": false, "overrides": [{"files": "*.json", "options": {"printWidth": 80, "tabWidth": 2}}, {"files": "*.md", "options": {"printWidth": 80, "proseWrap": "always", "tabWidth": 2}}, {"files": "*.yml", "options": {"tabWidth": 2, "singleQuote": false}}, {"files": "*.yaml", "options": {"tabWidth": 2, "singleQuote": false}}, {"files": "*.css", "options": {"printWidth": 120, "singleQuote": false}}, {"files": "*.scss", "options": {"printWidth": 120, "singleQuote": false}}, {"files": "*.html", "options": {"printWidth": 120, "htmlWhitespaceSensitivity": "ignore"}}]}
import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Define the API that will be exposed to the renderer process
export interface ElectronAPI {
  // File operations
  openFile: () => Promise<string | null>;
  saveFile: (content: string, filePath?: string) => Promise<boolean>;

  // Document processing
  processDocument: (filePath: string) => Promise<any>;

  // AI operations
  generateEmbeddings: (text: string) => Promise<number[]>;
  performReasoning: (context: string, query: string) => Promise<string>;

  // Knowledge base operations
  storeInformation: (data: any) => Promise<void>;
  queryInformation: (query: string) => Promise<any[]>;

  // Timeline operations
  createCheckpoint: (description: string) => Promise<string>;
  undo: () => Promise<void>;
  redo: () => Promise<void>;

  // System operations
  getAppVersion: () => Promise<string>;
  showMessageBox: (options: any) => Promise<any>;

  // Event listeners
  onDocumentProcessed: (callback: (data: any) => void) => void;
  onAIResponse: (callback: (response: string) => void) => void;
  removeAllListeners: (channel: string) => void;

  // Logging operations
  logToMain: (level: string, message: string, data?: any, stack?: string) => Promise<void>;
}

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
const electronAPI: ElectronAPI = {
  // File operations
  openFile: () => ipcRenderer.invoke('file:open'),
  saveFile: (content: string, filePath?: string) =>
    ipcRenderer.invoke('file:save', content, filePath),

  // Document processing
  processDocument: (filePath: string) => ipcRenderer.invoke('document:process', filePath),

  // AI operations
  generateEmbeddings: (text: string) => ipcRenderer.invoke('ai:generate-embeddings', text),
  performReasoning: (context: string, query: string) =>
    ipcRenderer.invoke('ai:perform-reasoning', context, query),

  // Knowledge base operations
  storeInformation: (data: any) => ipcRenderer.invoke('knowledge:store', data),
  queryInformation: (query: string) => ipcRenderer.invoke('knowledge:query', query),

  // Timeline operations
  createCheckpoint: (description: string) =>
    ipcRenderer.invoke('timeline:create-checkpoint', description),
  undo: () => ipcRenderer.invoke('timeline:undo'),
  redo: () => ipcRenderer.invoke('timeline:redo'),

  // System operations
  getAppVersion: () => ipcRenderer.invoke('app:get-version'),
  showMessageBox: (options: any) => ipcRenderer.invoke('app:show-message-box', options),

  // Event listeners
  onDocumentProcessed: (callback: (data: any) => void) => {
    ipcRenderer.on('document:processed', (_event, data) => callback(data));
  },
  onAIResponse: (callback: (response: string) => void) => {
    ipcRenderer.on('ai:response', (_event, response) => callback(response));
  },
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  },

  // Logging operations
  logToMain: (level: string, message: string, data?: any, stack?: string) =>
    ipcRenderer.invoke('log:fromRenderer', level, message, data, stack),
};

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electronAPI', electronAPI);
  } catch (error) {
    console.error('Failed to expose electronAPI:', error);
  }
} else {
  // @ts-ignore - window is available in non-isolated context
  window.electronAPI = electronAPI;
}

// Expose environment information
contextBridge.exposeInMainWorld('electronEnv', {
  NODE_ENV: process.env.NODE_ENV,
  platform: process.platform,
  arch: process.arch,
  versions: process.versions,
});

// Security: Remove Node.js globals from renderer process
// Only attempt to delete these if context isolation is disabled
if (!process.contextIsolated) {
  const globalWindow = globalThis as any;
  delete globalWindow.require;
  delete globalWindow.exports;
  delete globalWindow.module;
}

// Log preload script loaded
console.log('Preload script loaded successfully');

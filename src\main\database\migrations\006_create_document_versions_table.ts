import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create document_versions table for document version tracking
  await knex.schema.createTable('document_versions', (table) => {
    // Primary key
    table.string('id', 36).primary().notNullable().comment('UUID primary key');

    // Document reference
    table.string('document_id', 36).notNullable().comment('Reference to documents table');
    table.foreign('document_id').references('id').inTable('documents').onDelete('CASCADE');

    // Version information
    table.string('version_number', 50).notNullable().comment('Version number (e.g., 1.0, 1.1, 2.0)');
    table.integer('version_sequence').notNullable().comment('Sequential version number');
    table.string('version_type', 50).notNullable().comment('Type of version (major, minor, patch, auto)');

    // Content and changes
    table.string('content_hash', 64).notNullable().comment('SHA-256 hash of version content');
    table.binary('content_diff').comment('Binary diff from previous version (compressed)');
    table.integer('content_size').unsigned().comment('Size of content in bytes');
    table.string('compression_method', 50).defaultTo('lz4').comment('Compression method for diff');

    // Change tracking
    table.text('change_summary').comment('Summary of changes in this version');
    table.json('change_details').comment('Detailed change information');
    table.string('change_type', 100).notNullable().comment('Type of change (create, update, merge, split, etc.)');
    table.json('affected_sections').comment('Document sections affected by changes');

    // Metadata preservation
    table.json('metadata').comment('Document metadata at this version');
    table.json('processing_metadata').comment('Processing metadata for this version');
    table.decimal('processing_confidence', 5, 4).comment('Processing confidence for this version');

    // User and session information
    table.string('created_by', 100).comment('User who created this version');
    table.string('session_id', 36).comment('Session ID when version was created');
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable().comment('Version creation timestamp');

    // Version relationships
    table.string('parent_version_id', 36).comment('Parent version ID');
    table.foreign('parent_version_id').references('id').inTable('document_versions').onDelete('SET NULL');
    table.json('merged_from_versions').comment('Array of version IDs that were merged to create this version');
    table.string('branch_name', 100).comment('Branch name if using branching');

    // Status and lifecycle
    table.boolean('is_current').defaultTo(false).notNullable().comment('Whether this is the current version');
    table.boolean('is_published').defaultTo(false).notNullable().comment('Whether this version is published');
    table.timestamp('published_at').comment('When this version was published');
    table.string('published_by', 100).comment('Who published this version');

    // Quality and validation
    table.boolean('is_validated').defaultTo(false).notNullable().comment('Whether version has been validated');
    table.timestamp('validated_at').comment('When version was validated');
    table.string('validated_by', 100).comment('Who validated the version');
    table.text('validation_notes').comment('Notes from validation process');
    table.decimal('quality_score', 5, 4).comment('Quality score for this version');

    // Backup and recovery
    table.string('backup_location', 500).comment('Location of full backup for this version');
    table.boolean('has_full_backup').defaultTo(false).notNullable().comment('Whether full backup exists');
    table.timestamp('backup_created_at').comment('When backup was created');

    // Tags and categorization
    table.json('tags').comment('Array of tags for this version');
    table.text('notes').comment('User notes for this version');
    table.string('milestone', 100).comment('Milestone name if this is a milestone version');

    // Constraints
    table.unique(['document_id', 'version_sequence'], 'unique_document_version_sequence');
    table.check('version_sequence > 0', [], 'version_sequence_positive');
    table.check('content_size >= 0', [], 'content_size_positive');
    table.check('processing_confidence IS NULL OR (processing_confidence >= 0 AND processing_confidence <= 1)', [], 'processing_confidence_range');
    table.check('quality_score IS NULL OR (quality_score >= 0 AND quality_score <= 1)', [], 'quality_score_range');
    table.check("version_type IN ('major', 'minor', 'patch', 'auto', 'manual', 'merge', 'branch')", [], 'valid_version_type');
    table.check("change_type IN ('create', 'update', 'delete', 'merge', 'split', 'restore', 'import', 'export', 'other')", [], 'valid_change_type');
    table.check("compression_method IN ('lz4', 'gzip', 'brotli', 'none')", [], 'valid_compression_method');

    // JSON validation
    table.check("json_valid(change_details) OR change_details IS NULL", [], 'valid_change_details');
    table.check("json_valid(affected_sections) OR affected_sections IS NULL", [], 'valid_affected_sections');
    table.check("json_valid(metadata) OR metadata IS NULL", [], 'valid_metadata');
    table.check("json_valid(processing_metadata) OR processing_metadata IS NULL", [], 'valid_processing_metadata');
    table.check("json_valid(merged_from_versions) OR merged_from_versions IS NULL", [], 'valid_merged_from_versions');
    table.check("json_valid(tags) OR tags IS NULL", [], 'valid_tags');

    // Indexes for performance
    table.index(['document_id'], 'idx_document_versions_document');
    table.index(['version_sequence'], 'idx_document_versions_sequence');
    table.index(['version_number'], 'idx_document_versions_number');
    table.index(['created_at'], 'idx_document_versions_created_at');
    table.index(['is_current'], 'idx_document_versions_is_current');
    table.index(['is_published'], 'idx_document_versions_is_published');
    table.index(['created_by'], 'idx_document_versions_created_by');
    table.index(['change_type'], 'idx_document_versions_change_type');
    table.index(['version_type'], 'idx_document_versions_version_type');
    table.index(['branch_name'], 'idx_document_versions_branch');
    table.index(['parent_version_id'], 'idx_document_versions_parent');
    table.index(['content_hash'], 'idx_document_versions_content_hash');
    table.index(['is_validated'], 'idx_document_versions_validated');
    table.index(['milestone'], 'idx_document_versions_milestone');

    // Composite indexes for common queries
    table.index(['document_id', 'version_sequence'], 'idx_document_versions_doc_seq');
    table.index(['document_id', 'is_current'], 'idx_document_versions_doc_current');
    table.index(['document_id', 'created_at'], 'idx_document_versions_doc_created');
    table.index(['is_current', 'is_published'], 'idx_document_versions_current_published');
    table.index(['created_by', 'created_at'], 'idx_document_versions_user_created');
    table.index(['branch_name', 'version_sequence'], 'idx_document_versions_branch_seq');
  });

  // Create trigger to ensure only one current version per document
  await knex.raw(`
    CREATE TRIGGER ensure_single_current_version
    BEFORE UPDATE OF is_current ON document_versions
    FOR EACH ROW
    WHEN NEW.is_current = 1 AND OLD.is_current = 0
    BEGIN
      UPDATE document_versions
      SET is_current = 0
      WHERE document_id = NEW.document_id AND id != NEW.id;
    END
  `);

  // Create trigger to auto-increment version sequence
  await knex.raw(`
    CREATE TRIGGER auto_increment_version_sequence
    BEFORE INSERT ON document_versions
    FOR EACH ROW
    WHEN NEW.version_sequence IS NULL
    BEGIN
      SELECT COALESCE(MAX(version_sequence), 0) + 1
      FROM document_versions
      WHERE document_id = NEW.document_id
      INTO NEW.version_sequence;
    END
  `);

  // Create view for current document versions
  await knex.raw(`
    CREATE VIEW current_document_versions AS
    SELECT
      dv.*,
      d.name as document_name,
      d.type as document_type,
      d.path as document_path
    FROM document_versions dv
    JOIN documents d ON dv.document_id = d.id
    WHERE dv.is_current = 1
  `);

  // Create view for version history with change summaries
  await knex.raw(`
    CREATE VIEW version_history AS
    SELECT
      dv.id,
      dv.document_id,
      d.name as document_name,
      dv.version_number,
      dv.version_sequence,
      dv.version_type,
      dv.change_type,
      dv.change_summary,
      dv.created_by,
      dv.created_at,
      dv.is_current,
      dv.is_published,
      dv.quality_score,
      LAG(dv.version_number) OVER (PARTITION BY dv.document_id ORDER BY dv.version_sequence) as previous_version,
      LEAD(dv.version_number) OVER (PARTITION BY dv.document_id ORDER BY dv.version_sequence) as next_version
    FROM document_versions dv
    JOIN documents d ON dv.document_id = d.id
    ORDER BY dv.document_id, dv.version_sequence DESC
  `);
}

export async function down(knex: Knex): Promise<void> {
  // Drop triggers first
  await knex.raw('DROP TRIGGER IF EXISTS auto_increment_version_sequence');
  await knex.raw('DROP TRIGGER IF EXISTS ensure_single_current_version');

  // Drop views
  await knex.raw('DROP VIEW IF EXISTS version_history');
  await knex.raw('DROP VIEW IF EXISTS current_document_versions');

  // Drop main table
  await knex.schema.dropTableIfExists('document_versions');
}

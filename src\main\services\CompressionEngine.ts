import * as zlib from 'zlib';
import { promisify } from 'util';
import { createHash } from 'crypto';
import {
  CompressionOptions,
  CompressionAlgorithm,
  CompressionInfo,
} from '../../shared/types/Timeline';

const gzip = promisify(zlib.gzip);
const gunzip = promisify(zlib.gunzip);
const brotliCompress = promisify(zlib.brotliCompress);
const brotliDecompress = promisify(zlib.brotliDecompress);

export interface CompressionEngineConfig {
  defaultAlgorithm?: CompressionAlgorithm;
  defaultLevel?: number;
  enableDeduplication?: boolean;
  maxCacheSize?: number;
  cacheExpiryMs?: number;
}

export interface CompressionResult {
  data: Buffer;
  originalSize: number;
  compressedSize: number;
  ratio: number;
  algorithm: CompressionAlgorithm;
  checksum: string;
}

export interface DeduplicationEntry {
  hash: string;
  data: Buffer;
  algorithm: CompressionAlgorithm;
  originalSize: number;
  compressedSize: number;
  lastAccessed: Date;
  accessCount: number;
}

export class CompressionEngine {
  private config: Required<CompressionEngineConfig>;
  private deduplicationCache: Map<string, DeduplicationEntry> = new Map();
  private cacheCleanupInterval: NodeJS.Timeout | null = null;

  constructor(config: CompressionEngineConfig = {}) {
    this.config = {
      defaultAlgorithm: config.defaultAlgorithm || CompressionAlgorithm.LZ4,
      defaultLevel: config.defaultLevel || 6,
      enableDeduplication: config.enableDeduplication ?? true,
      maxCacheSize: config.maxCacheSize || 1000,
      cacheExpiryMs: config.cacheExpiryMs || 3600000, // 1 hour
    };

    if (this.config.enableDeduplication) {
      this.setupCacheCleanup();
    }
  }

  async compress(data: string | Buffer, options?: Partial<CompressionOptions>): Promise<Buffer> {
    const inputBuffer = typeof data === 'string' ? Buffer.from(data, 'utf8') : data;
    const originalSize = inputBuffer.length;

    const compressionOptions: CompressionOptions = {
      algorithm: options?.algorithm || this.config.defaultAlgorithm,
      level: options?.level || this.config.defaultLevel,
      ...(options?.dictionary && { dictionary: options.dictionary }),
      chunkSize: options?.chunkSize || 16384,
      parallel: options?.parallel || false,
    };

    // Check deduplication cache first
    if (this.config.enableDeduplication) {
      const hash = this.calculateHash(inputBuffer);
      const cached = this.deduplicationCache.get(hash);

      if (cached && cached.algorithm === compressionOptions.algorithm) {
        // Update cache statistics
        cached.lastAccessed = new Date();
        cached.accessCount++;
        return cached.data;
      }
    }

    let compressedData: Buffer;

    try {
      switch (compressionOptions.algorithm) {
        case CompressionAlgorithm.GZIP:
          compressedData = await this.compressGzip(inputBuffer, compressionOptions);
          break;
        case CompressionAlgorithm.BROTLI:
          compressedData = await this.compressBrotli(inputBuffer, compressionOptions);
          break;
        case CompressionAlgorithm.LZ4:
          compressedData = await this.compressLZ4(inputBuffer, compressionOptions);
          break;
        case CompressionAlgorithm.ZSTD:
          compressedData = await this.compressZstd(inputBuffer, compressionOptions);
          break;
        case CompressionAlgorithm.NONE:
          compressedData = inputBuffer;
          break;
        default:
          throw new Error(`Unsupported compression algorithm: ${compressionOptions.algorithm}`);
      }

      // Store in deduplication cache
      if (
        this.config.enableDeduplication &&
        compressionOptions.algorithm !== CompressionAlgorithm.NONE
      ) {
        const hash = this.calculateHash(inputBuffer);
        this.addToCache(hash, {
          hash,
          data: compressedData,
          algorithm: compressionOptions.algorithm,
          originalSize,
          compressedSize: compressedData.length,
          lastAccessed: new Date(),
          accessCount: 1,
        });
      }

      return compressedData;
    } catch (error) {
      throw new Error(
        `Compression failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async decompress(data: Buffer, options?: Partial<CompressionOptions>): Promise<string> {
    const compressionOptions: CompressionOptions = {
      algorithm: options?.algorithm || this.config.defaultAlgorithm,
      level: options?.level || this.config.defaultLevel,
      ...(options?.dictionary && { dictionary: options.dictionary }),
      chunkSize: options?.chunkSize || 16384,
      parallel: options?.parallel || false,
    };

    try {
      let decompressedData: Buffer;

      switch (compressionOptions.algorithm) {
        case CompressionAlgorithm.GZIP:
          decompressedData = await this.decompressGzip(data);
          break;
        case CompressionAlgorithm.BROTLI:
          decompressedData = await this.decompressBrotli(data);
          break;
        case CompressionAlgorithm.LZ4:
          decompressedData = await this.decompressLZ4(data);
          break;
        case CompressionAlgorithm.ZSTD:
          decompressedData = await this.decompressZstd(data);
          break;
        case CompressionAlgorithm.NONE:
          decompressedData = data;
          break;
        default:
          throw new Error(`Unsupported compression algorithm: ${compressionOptions.algorithm}`);
      }

      return decompressedData.toString('utf8');
    } catch (error) {
      throw new Error(
        `Decompression failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async compressWithInfo(
    data: string | Buffer,
    options?: Partial<CompressionOptions>
  ): Promise<CompressionResult> {
    const inputBuffer = typeof data === 'string' ? Buffer.from(data, 'utf8') : data;
    const originalSize = inputBuffer.length;
    const checksum = this.calculateHash(inputBuffer);

    const compressionOptions: CompressionOptions = {
      algorithm: options?.algorithm || this.config.defaultAlgorithm,
      level: options?.level || this.config.defaultLevel,
      ...(options?.dictionary && { dictionary: options.dictionary }),
      chunkSize: options?.chunkSize || 16384,
      parallel: options?.parallel || false,
    };

    const compressedData = await this.compress(data, options);
    const compressedSize = compressedData.length;
    const ratio = compressedSize / originalSize;

    return {
      data: compressedData,
      originalSize,
      compressedSize,
      ratio,
      algorithm: compressionOptions.algorithm,
      checksum,
    };
  }

  private async compressGzip(data: Buffer, options: CompressionOptions): Promise<Buffer> {
    return await gzip(data, { level: options.level });
  }

  private async decompressGzip(data: Buffer): Promise<Buffer> {
    return await gunzip(data);
  }

  private async compressBrotli(data: Buffer, options: CompressionOptions): Promise<Buffer> {
    return await brotliCompress(data, {
      params: {
        [zlib.constants.BROTLI_PARAM_QUALITY]: options.level,
      },
    });
  }

  private async decompressBrotli(data: Buffer): Promise<Buffer> {
    return await brotliDecompress(data);
  }

  private async compressLZ4(data: Buffer, options: CompressionOptions): Promise<Buffer> {
    // LZ4 compression would require a native module like lz4
    // For now, we'll use gzip as a fallback
    console.warn('LZ4 compression not available, falling back to gzip');
    return await this.compressGzip(data, options);
  }

  private async decompressLZ4(data: Buffer): Promise<Buffer> {
    // LZ4 decompression would require a native module like lz4
    // For now, we'll use gzip as a fallback
    console.warn('LZ4 decompression not available, falling back to gzip');
    return await this.decompressGzip(data);
  }

  private async compressZstd(data: Buffer, options: CompressionOptions): Promise<Buffer> {
    // Zstd compression would require a native module like @mongodb-js/zstd
    // For now, we'll use gzip as a fallback
    console.warn('Zstd compression not available, falling back to gzip');
    return await this.compressGzip(data, options);
  }

  private async decompressZstd(data: Buffer): Promise<Buffer> {
    // Zstd decompression would require a native module like @mongodb-js/zstd
    // For now, we'll use gzip as a fallback
    console.warn('Zstd decompression not available, falling back to gzip');
    return await this.decompressGzip(data);
  }

  private calculateHash(data: Buffer): string {
    return createHash('sha256').update(data).digest('hex');
  }

  private addToCache(hash: string, entry: DeduplicationEntry): void {
    // Check cache size limit
    if (this.deduplicationCache.size >= this.config.maxCacheSize) {
      this.evictOldestEntries();
    }

    this.deduplicationCache.set(hash, entry);
  }

  private evictOldestEntries(): void {
    // Sort by last accessed time and remove oldest entries
    const entries = Array.from(this.deduplicationCache.entries());
    entries.sort((a, b) => a[1].lastAccessed.getTime() - b[1].lastAccessed.getTime());

    // Remove oldest 25% of entries
    const entriesToRemove = Math.floor(entries.length * 0.25);
    for (let i = 0; i < entriesToRemove; i++) {
      const entry = entries[i];
      if (entry) {
        this.deduplicationCache.delete(entry[0]);
      }
    }
  }

  private setupCacheCleanup(): void {
    // Clean up expired entries every 10 minutes
    this.cacheCleanupInterval = setInterval(() => {
      this.cleanupExpiredEntries();
    }, 600000);
  }

  private cleanupExpiredEntries(): void {
    const now = Date.now();
    const expiredEntries: string[] = [];

    for (const [hash, entry] of this.deduplicationCache) {
      if (now - entry.lastAccessed.getTime() > this.config.cacheExpiryMs) {
        expiredEntries.push(hash);
      }
    }

    for (const hash of expiredEntries) {
      this.deduplicationCache.delete(hash);
    }
  }

  getCacheStatistics(): {
    size: number;
    maxSize: number;
    totalOriginalSize: number;
    totalCompressedSize: number;
    totalSavings: number;
    averageCompressionRatio: number;
    hitRate: number;
  } {
    let totalOriginalSize = 0;
    let totalCompressedSize = 0;
    let totalAccessCount = 0;

    for (const entry of this.deduplicationCache.values()) {
      totalOriginalSize += entry.originalSize;
      totalCompressedSize += entry.compressedSize;
      totalAccessCount += entry.accessCount;
    }

    const totalSavings = totalOriginalSize - totalCompressedSize;
    const averageCompressionRatio =
      totalOriginalSize > 0 ? totalCompressedSize / totalOriginalSize : 0;
    const hitRate =
      totalAccessCount > this.deduplicationCache.size
        ? (totalAccessCount - this.deduplicationCache.size) / totalAccessCount
        : 0;

    return {
      size: this.deduplicationCache.size,
      maxSize: this.config.maxCacheSize,
      totalOriginalSize,
      totalCompressedSize,
      totalSavings,
      averageCompressionRatio,
      hitRate,
    };
  }

  clearCache(): void {
    this.deduplicationCache.clear();
  }

  destroy(): void {
    if (this.cacheCleanupInterval) {
      clearInterval(this.cacheCleanupInterval);
      this.cacheCleanupInterval = null;
    }
    this.clearCache();
  }

  // Utility method to get compression info without actually compressing
  getCompressionInfo(
    originalSize: number,
    compressedSize: number,
    algorithm: CompressionAlgorithm
  ): CompressionInfo {
    return {
      algorithm,
      originalSize,
      compressedSize,
      ratio: compressedSize / originalSize,
    };
  }

  // Method to estimate compression ratio for different algorithms
  async estimateCompressionRatio(
    data: string | Buffer,
    algorithm: CompressionAlgorithm
  ): Promise<number> {
    const inputBuffer = typeof data === 'string' ? Buffer.from(data, 'utf8') : data;
    const originalSize = inputBuffer.length;

    // For small data, compression might not be beneficial
    if (originalSize < 100) {
      return 1.0; // No compression benefit
    }

    try {
      const compressed = await this.compress(data, { algorithm, level: 1 }); // Use fast compression for estimation
      return compressed.length / originalSize;
    } catch (error) {
      return 1.0; // Fallback to no compression
    }
  }

  // Method to choose the best compression algorithm for given data
  async chooseBestAlgorithm(data: string | Buffer): Promise<CompressionAlgorithm> {
    const algorithms = [
      CompressionAlgorithm.GZIP,
      CompressionAlgorithm.BROTLI,
      CompressionAlgorithm.LZ4,
    ];

    let bestAlgorithm = CompressionAlgorithm.GZIP;
    let bestRatio = 1.0;

    for (const algorithm of algorithms) {
      try {
        const ratio = await this.estimateCompressionRatio(data, algorithm);
        if (ratio < bestRatio) {
          bestRatio = ratio;
          bestAlgorithm = algorithm;
        }
      } catch (error) {
        // Skip algorithm if it fails
        continue;
      }
    }

    return bestAlgorithm;
  }
}

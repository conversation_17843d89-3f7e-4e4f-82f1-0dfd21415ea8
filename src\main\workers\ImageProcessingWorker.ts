import { Worker, isMainThread, parentPort, workerData } from 'worker_threads';
import { EventEmitter } from 'events';
import { ImageProcessResult, ProcessingOptions, ExtractedData } from '../../shared/types/Document';
import { logger } from '../utils/logger';
import { ImageProcessor } from '../services/ImageProcessor';
import { ComputerVisionService } from '../services/ComputerVisionService';
import { AdvancedImageAnalysisService } from '../services/AdvancedImageAnalysisService';

export interface ImageProcessingTask {
  id: string;
  imageBuffer: Buffer;
  options: ProcessingOptions;
  priority: 'low' | 'normal' | 'high';
  createdAt: Date;
}

export interface ImageProcessingResult {
  taskId: string;
  success: boolean;
  result?: {
    imageProcessing: ImageProcessResult;
    computerVision: any;
    advancedAnalysis: any;
    extractedData: ExtractedData[];
  };
  error?: string;
  processingTime: number;
}

export interface WorkerPoolOptions {
  maxWorkers: number;
  taskTimeout: number;
  retryAttempts: number;
  enableAdvancedAnalysis: boolean;
}

/**
 * Image Processing Worker Pool for handling batch image processing tasks
 */
export class ImageProcessingWorkerPool extends EventEmitter {
  private workers: Worker[] = [];
  private taskQueue: ImageProcessingTask[] = [];
  private activeTasks: Map<string, { worker: Worker; startTime: number }> = new Map();
  private readonly options: WorkerPoolOptions;
  private isShuttingDown = false;

  constructor(options: Partial<WorkerPoolOptions> = {}) {
    super();
    this.options = {
      maxWorkers: options.maxWorkers || Math.max(2, Math.floor(require('os').cpus().length / 2)),
      taskTimeout: options.taskTimeout || 300000, // 5 minutes
      retryAttempts: options.retryAttempts || 2,
      enableAdvancedAnalysis: options.enableAdvancedAnalysis || true,
    };
  }

  /**
   * Initialize the worker pool
   */
  public async initialize(): Promise<void> {
    try {
      for (let i = 0; i < this.options.maxWorkers; i++) {
        await this.createWorker();
      }
      logger.info('Image processing worker pool initialized', {
        workerCount: this.workers.length,
        options: this.options,
      });
    } catch (error) {
      logger.error('Failed to initialize worker pool', { error });
      throw error;
    }
  }

  /**
   * Add a task to the processing queue
   */
  public async addTask(
    imageBuffer: Buffer,
    options: ProcessingOptions,
    priority: 'low' | 'normal' | 'high' = 'normal'
  ): Promise<string> {
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const task: ImageProcessingTask = {
      id: taskId,
      imageBuffer,
      options,
      priority,
      createdAt: new Date(),
    };

    // Insert task based on priority
    if (priority === 'high') {
      this.taskQueue.unshift(task);
    } else if (priority === 'low') {
      this.taskQueue.push(task);
    } else {
      // Insert normal priority tasks after high priority but before low priority
      const highPriorityCount = this.taskQueue.filter(t => t.priority === 'high').length;
      this.taskQueue.splice(highPriorityCount, 0, task);
    }

    logger.debug('Task added to queue', {
      taskId,
      priority,
      queueLength: this.taskQueue.length,
    });

    // Try to process the task immediately if workers are available
    this.processNextTask();

    return taskId;
  }

  /**
   * Get the status of a task
   */
  public getTaskStatus(taskId: string): 'queued' | 'processing' | 'completed' | 'not_found' {
    if (this.activeTasks.has(taskId)) {
      return 'processing';
    }
    if (this.taskQueue.some(task => task.id === taskId)) {
      return 'queued';
    }
    return 'not_found';
  }

  /**
   * Get queue statistics
   */
  public getQueueStats(): {
    queuedTasks: number;
    activeTasks: number;
    availableWorkers: number;
    totalWorkers: number;
  } {
    return {
      queuedTasks: this.taskQueue.length,
      activeTasks: this.activeTasks.size,
      availableWorkers: this.workers.length - this.activeTasks.size,
      totalWorkers: this.workers.length,
    };
  }

  /**
   * Process the next task in the queue
   */
  private async processNextTask(): Promise<void> {
    if (this.isShuttingDown || this.taskQueue.length === 0) {
      return;
    }

    // Find an available worker
    const availableWorker = this.workers.find(
      worker => !Array.from(this.activeTasks.values()).some(task => task.worker === worker)
    );

    if (!availableWorker) {
      return; // No available workers
    }

    const task = this.taskQueue.shift();
    if (!task) {
      return;
    }

    try {
      await this.executeTask(availableWorker, task);
    } catch (error) {
      logger.error('Task execution failed', { taskId: task.id, error });
      this.emit('taskError', {
        taskId: task.id,
        error: error instanceof Error ? error.message : String(error),
        processingTime: 0,
      });
    }
  }

  /**
   * Execute a task on a specific worker
   */
  private async executeTask(worker: Worker, task: ImageProcessingTask): Promise<void> {
    const startTime = Date.now();
    this.activeTasks.set(task.id, { worker, startTime });

    logger.debug('Starting task execution', {
      taskId: task.id,
      workerId: worker.threadId,
    });

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.activeTasks.delete(task.id);
        worker.terminate();
        this.createWorker(); // Replace the terminated worker
        reject(new Error(`Task ${task.id} timed out`));
      }, this.options.taskTimeout);

      const messageHandler = (result: ImageProcessingResult) => {
        clearTimeout(timeout);
        this.activeTasks.delete(task.id);
        worker.off('message', messageHandler);
        worker.off('error', errorHandler);

        if (result.success) {
          this.emit('taskCompleted', result);
        } else {
          this.emit('taskError', result);
        }

        // Process next task
        this.processNextTask();
        resolve();
      };

      const errorHandler = (error: Error) => {
        clearTimeout(timeout);
        this.activeTasks.delete(task.id);
        worker.off('message', messageHandler);
        worker.off('error', errorHandler);

        logger.error('Worker error during task execution', {
          taskId: task.id,
          error: error instanceof Error ? error.message : String(error),
        });

        reject(error);
      };

      worker.on('message', messageHandler);
      worker.on('error', errorHandler);

      // Send task to worker
      worker.postMessage({
        taskId: task.id,
        imageBuffer: task.imageBuffer,
        options: task.options,
        enableAdvancedAnalysis: this.options.enableAdvancedAnalysis,
      });
    });
  }

  /**
   * Create a new worker
   */
  private async createWorker(): Promise<void> {
    return new Promise((resolve, reject) => {
      const worker = new Worker(__filename, {
        workerData: { isWorker: true },
      });

      worker.on('online', () => {
        this.workers.push(worker);
        logger.debug('Worker created', { workerId: worker.threadId });
        resolve();
      });

      worker.on('error', error => {
        logger.error('Worker error', { error });
        reject(error);
      });

      worker.on('exit', code => {
        if (code !== 0) {
          logger.warn('Worker exited with non-zero code', { code });
        }
        // Remove worker from pool
        this.workers = this.workers.filter(w => w !== worker);
      });
    });
  }

  /**
   * Shutdown the worker pool
   */
  public async shutdown(): Promise<void> {
    this.isShuttingDown = true;

    logger.info('Shutting down image processing worker pool');

    // Wait for active tasks to complete or timeout
    const shutdownTimeout = 30000; // 30 seconds
    const startTime = Date.now();

    while (this.activeTasks.size > 0 && Date.now() - startTime < shutdownTimeout) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Terminate all workers
    await Promise.all(
      this.workers.map(worker => {
        return new Promise<void>(resolve => {
          worker
            .terminate()
            .then(() => resolve())
            .catch(() => resolve());
        });
      })
    );

    this.workers = [];
    this.activeTasks.clear();
    this.taskQueue = [];

    logger.info('Image processing worker pool shut down');
  }
}

// Worker thread code
if (!isMainThread && workerData?.isWorker) {
  let imageProcessor: ImageProcessor;
  let computerVisionService: ComputerVisionService;
  let advancedAnalysisService: AdvancedImageAnalysisService;

  // Initialize services
  (async () => {
    try {
      imageProcessor = new ImageProcessor();
      computerVisionService = new ComputerVisionService();
      advancedAnalysisService = new AdvancedImageAnalysisService();
      await advancedAnalysisService.initialize();
    } catch (error) {
      logger.error('Failed to initialize worker services', { error });
    }
  })();

  parentPort?.on(
    'message',
    async (data: {
      taskId: string;
      imageBuffer: Buffer;
      options: ProcessingOptions;
      enableAdvancedAnalysis: boolean;
    }) => {
      const startTime = Date.now();

      try {
        const { taskId, imageBuffer, enableAdvancedAnalysis } = data;

        // Perform image processing
        const imageProcessing = await imageProcessor.processImage(
          imageBuffer,
          imageProcessor.getOCROptimizedOptions()
        );

        // Perform computer vision analysis
        const computerVision = await computerVisionService.analyzeDocument(imageBuffer);

        // Perform advanced analysis if enabled
        let advancedAnalysis;
        if (enableAdvancedAnalysis) {
          advancedAnalysis = await advancedAnalysisService.analyzeImage(imageBuffer);
        }

        const result: ImageProcessingResult = {
          taskId,
          success: true,
          result: {
            imageProcessing,
            computerVision,
            advancedAnalysis,
            extractedData: advancedAnalysis?.extractedData || [],
          },
          processingTime: Date.now() - startTime,
        };

        parentPort?.postMessage(result);
      } catch (error) {
        const result: ImageProcessingResult = {
          taskId: data.taskId,
          success: false,
          error: error instanceof Error ? error.message : String(error),
          processingTime: Date.now() - startTime,
        };

        parentPort?.postMessage(result);
      }
    }
  );
}

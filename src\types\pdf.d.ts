// PDF.js type declarations for missing types

declare module 'pdfjs-dist' {
  export interface PDFTextItem {
    str: string;
    dir: string;
    width: number;
    height: number;
    transform: number[];
    fontName: string;
    hasEOL?: boolean;
  }

  export interface PDFTextContent {
    items: PDFTextItem[];
    styles: Record<
      string,
      {
        fontFamily?: string;
        fontSize?: number;
        ascent?: number;
        descent?: number;
        vertical?: boolean;
      }
    >;
  }

  export interface PDFViewport {
    width: number;
    height: number;
    scale: number;
    rotation: number;
    offsetX: number;
    offsetY: number;
    transform: number[];
    viewBox: number[];
  }

  export interface PDFPage {
    pageNumber: number;
    getViewport(params: { scale: number; rotation?: number }): PDFViewport;
    getTextContent(): Promise<PDFTextContent>;
    render(params: {
      canvasContext: CanvasRenderingContext2D;
      canvas?: HTMLCanvasElement | NodeCanvasLike;
      viewport: PDFViewport;
      intent?: string;
      enableWebGL?: boolean;
      renderInteractiveForms?: boolean;
    }): { promise: Promise<void>; cancel(): void };
    cleanup(): void;
  }

  export interface PDFFormField {
    fieldName: string;
    fieldType: string;
    fieldValue: string | number | boolean | null;
    options?: string[];
    exportValues?: string[];
    required?: boolean;
    readonly?: boolean;
    rect?: number[];
    multiline?: boolean;
    password?: boolean;
    fileSelect?: boolean;
    doNotSpellCheck?: boolean;
    doNotScroll?: boolean;
    comb?: boolean;
    richText?: boolean;
    maxLen?: number;
    buttonValue?: string | number | boolean;
    radiosInUnison?: boolean;
    page?: number;
  }

  export interface PDFDocumentProxy {
    numPages: number;
    fingerprints: string[];
    isEncrypted?: boolean;
    getPage(pageNumber: number): Promise<PDFPage>;
    getMetadata(): Promise<{ info: PDFInfo; metadata: Record<string, unknown> | null }>;
    getDestinations(): Promise<Record<string, unknown> | null>;
    getOutline(): Promise<PDFOutlineNode[] | null>;
    getPermissions(): Promise<number[] | null>;
    getJavaScript(): Promise<string[]>;
    getForm?(): Promise<PDFFormField[]>;
    destroy(): void;
  }

  export interface PDFOutlineNode {
    title: string;
    bold?: boolean;
    italic?: boolean;
    color?: number[];
    dest?: string | unknown[];
    url?: string;
    items?: PDFOutlineNode[];
  }

  export interface PDFInfo {
    Title?: string;
    Author?: string;
    Subject?: string;
    Keywords?: string;
    Creator?: string;
    Producer?: string;
    CreationDate?: string;
    ModDate?: string;
    Language?: string;
  }
}

export interface PDFTextWithCoordinates {
  text: string;
  x: number;
  y: number;
  width: number;
  height: number;
  page: number;
  fontSize: number;
  fontFamily: string;
}

// Additional types for PDF processing
export interface FontInfo {
  family: string;
  size: number;
  weight: string;
  style: string;
}

export interface TextStyling {
  bold: boolean;
  italic: boolean;
  underline: boolean;
  fontSize: number;
  fontFamily: string;
  color?: string;
}

export interface TextBlock {
  text: string;
  x: number;
  y: number;
  width: number;
  height: number;
  fontSize: number;
  fontFamily: string;
  confidence: number;
}

// PDF manipulation types
export interface PDFCreationOptions {
  title?: string;
  author?: string;
  subject?: string;
  keywords?: string[];
  creator?: string;
  producer?: string;
  pageOptions?: {
    size?: [number, number];
    margins?: {
      top?: number;
      bottom?: number;
      left?: number;
      right?: number;
    };
  };
  metadata?: {
    title?: string;
    author?: string;
    subject?: string;
    keywords?: string[];
  };
  initialContent?: string;
}

export interface PDFFormFillOptions {
  flatten?: boolean;
  appearance?: boolean;
  incremental?: boolean;
  flattenForm?: boolean;
  addValidationInfo?: boolean;
}

export interface TextOverlay {
  text: string;
  x: number;
  y: number;
  pageNumber?: number;
  position?: {
    x: number;
    y: number;
  };
  style?: {
    fontSize?: number;
    fontFamily?: string;
    color?: { r: number; g: number; b: number };
    bold?: boolean;
    opacity?: number;
    rotation?: number;
  };
  fontSize?: number;
  fontFamily?: string;
  color?: string;
}

export interface TextOverlayOptions {
  font?: string;
  fontSize?: number;
  color?: string;
  opacity?: number;
  fontName?: string;
  boldFontName?: string;
}

export interface PDFAnnotation {
  type:
    | 'text'
    | 'highlight'
    | 'underline'
    | 'strikeout'
    | 'squiggly'
    | 'note'
    | 'stamp'
    | 'drawing';
  page: number;
  pageNumber?: number;
  rect: number[];
  bounds?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  contents?: string;
  content?: string;
  color?: number[] | { r: number; g: number; b: number };
}

export interface AnnotationOptions {
  author?: string;
  subject?: string;
  opacity?: number;
}

export interface PDFMergeOptions {
  bookmarks?: boolean;
  metadata?:
    | boolean
    | {
        title?: string;
        author?: string;
        subject?: string;
        keywords?: string[];
      };
  forms?: boolean;
  pageRanges?: string[];
  continueOnError?: boolean;
  addMergeInfo?: boolean;
}

export interface PDFMergeSourceInfo {
  source: string;
  sourceIndex?: number;
  originalPageCount?: number;
  copiedPageCount?: number;
  startPageInMerged?: number;
  endPageInMerged?: number;
  pages?: number[];
  bookmarkTitle?: string;
}

export interface PDFSplitOptions {
  method?: 'ranges' | 'pages' | 'size';
  ranges?: Array<{ start: number; end: number }>;
  pageRanges?: Array<{ start: number; end: number }>;
  pageNumbers?: number[];
  pagesPerDocument?: number;
  outputPattern?: string;
  addMetadata?: boolean;
}

export interface PageOperation {
  type:
    | 'rotate'
    | 'scale'
    | 'crop'
    | 'delete'
    | 'duplicate'
    | 'reorder'
    | 'add'
    | 'copy'
    | 'remove';
  pages: number[];
  pageNumber?: number;
  sourcePageNumber?: number;
  newPosition?: number;
  pageSize?: number[];
  content?: string;
  parameters?: Record<string, unknown>;
}

export interface PageOperationResult {
  operation: string;
  type?: string;
  pages: number[];
  pageNumber: number | undefined;
  message?: string;
  success: boolean;
  error?: string;
}

export interface DocumentTable {
  id: string;
  pageNumber: number;
  bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
    pageNumber: number;
  };
  rows: TableRow[];
  headers: string[];
}

export interface TableRow {
  cells: TableCell[];
}

export interface TableCell {
  value: string;
  type: 'text' | 'number' | 'date' | 'boolean';
  bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
    pageNumber: number;
  };
}

// PDF to Image conversion types
export interface PDFToImageOptions {
  format: 'png' | 'jpeg' | 'webp';
  scale?: number;
  quality?: number;
  backgroundColor?: string;
  pageNumbers?: number[];
}

// Canvas types for Node.js environment
export interface NodeCanvasLike {
  width: number;
  height: number;
  getContext(contextId: '2d'): NodeCanvasRenderingContext2D;
  toBuffer(format?: string, quality?: number): Buffer;
}

export interface NodeCanvasRenderingContext2D {
  canvas: NodeCanvasLike;
  fillStyle: string | CanvasGradient | CanvasPattern;
  strokeStyle: string | CanvasGradient | CanvasPattern;
  lineWidth: number;
  font: string;
  textAlign: CanvasTextAlign;
  textBaseline: CanvasTextBaseline;
  globalAlpha: number;
  globalCompositeOperation: GlobalCompositeOperation;

  // Drawing methods
  fillRect(x: number, y: number, w: number, h: number): void;
  strokeRect(x: number, y: number, w: number, h: number): void;
  clearRect(x: number, y: number, w: number, h: number): void;
  fillText(text: string, x: number, y: number, maxWidth?: number): void;
  strokeText(text: string, x: number, y: number, maxWidth?: number): void;
  measureText(text: string): TextMetrics;

  // Path methods
  beginPath(): void;
  closePath(): void;
  moveTo(x: number, y: number): void;
  lineTo(x: number, y: number): void;
  arc(
    x: number,
    y: number,
    radius: number,
    startAngle: number,
    endAngle: number,
    counterclockwise?: boolean
  ): void;
  rect(x: number, y: number, w: number, h: number): void;
  fill(): void;
  stroke(): void;

  // Transform methods
  scale(x: number, y: number): void;
  rotate(angle: number): void;
  translate(x: number, y: number): void;
  transform(a: number, b: number, c: number, d: number, e: number, f: number): void;
  setTransform(a: number, b: number, c: number, d: number, e: number, f: number): void;
  resetTransform(): void;

  // State methods
  save(): void;
  restore(): void;
}

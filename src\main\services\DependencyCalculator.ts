import { createLogger } from '../utils/logger';
import { CalculationEngine } from './CalculationEngine';

const logger = createLogger('DependencyCalculator');

export interface FieldDependency {
  id: string;
  targetField: string;
  sourceFields: string[];
  expression: string;
  condition?: string;
  priority: number;
  description?: string;
  isActive: boolean;
}

export interface DependencyGraph {
  nodes: DependencyNode[];
  edges: DependencyEdge[];
  cycles: string[][];
  calculationOrder: string[];
  metadata: DependencyGraphMetadata;
}

export interface DependencyNode {
  id: string;
  fieldId: string;
  fieldName: string;
  fieldType: string;
  value?: any;
  isCalculated: boolean;
  dependencies: string[];
  dependents: string[];
  level: number; // Topological level
}

export interface DependencyEdge {
  id: string;
  source: string;
  target: string;
  dependency: FieldDependency;
  weight: number;
}

export interface DependencyGraphMetadata {
  totalNodes: number;
  totalEdges: number;
  maxDepth: number;
  cycleCount: number;
  lastUpdated: Date;
  version: number;
}

export interface CalculationUpdate {
  fieldId: string;
  oldValue: any;
  newValue: any;
  timestamp: Date;
  triggeredBy: string;
  affectedFields: string[];
  calculationTime: number;
}

export interface DependencyCalculatorOptions {
  enableRealTimeUpdates: boolean;
  enableCaching: boolean;
  maxCalculationDepth: number;
  cycleDetectionEnabled: boolean;
  updateThrottleMs: number;
  enableLogging: boolean;
}

/**
 * Dependency Calculator for managing field relationships and calculations
 * Handles dependency graph construction, cycle detection, and real-time updates
 */
export class DependencyCalculator {
  private calculationEngine: CalculationEngine;
  private dependencies: Map<string, FieldDependency> = new Map();
  private dependencyGraph: DependencyGraph | null = null;
  private fieldValues: Map<string, any> = new Map();
  private calculationCache: Map<string, any> = new Map();
  private updateListeners: Map<string, Function[]> = new Map();
  private options: Required<DependencyCalculatorOptions>;
  private isCalculating: boolean = false;

  constructor(
    calculationEngine: CalculationEngine,
    options: Partial<DependencyCalculatorOptions> = {}
  ) {
    this.calculationEngine = calculationEngine;
    this.options = {
      enableRealTimeUpdates: options.enableRealTimeUpdates ?? true,
      enableCaching: options.enableCaching ?? true,
      maxCalculationDepth: options.maxCalculationDepth ?? 10,
      cycleDetectionEnabled: options.cycleDetectionEnabled ?? true,
      updateThrottleMs: options.updateThrottleMs ?? 100,
      enableLogging: options.enableLogging ?? true,
    };

    logger.info('DependencyCalculator initialized', {
      options: this.options,
    });
  }

  /**
   * Add field dependency
   */
  public addDependency(dependency: FieldDependency): void {
    logger.debug('Adding field dependency', {
      id: dependency.id,
      target: dependency.targetField,
      sources: dependency.sourceFields,
    });

    // Validate dependency
    this.validateDependency(dependency);

    // Store dependency
    this.dependencies.set(dependency.id, dependency);

    // Rebuild dependency graph
    this.rebuildDependencyGraph();

    // Check for cycles if enabled
    if (this.options.cycleDetectionEnabled) {
      const cycles = this.detectCycles();
      if (cycles.length > 0) {
        logger.warn('Circular dependencies detected', { cycles });
        throw new Error(
          `Circular dependencies detected: ${cycles.map(c => c.join(' -> ')).join(', ')}`
        );
      }
    }

    logger.info('Field dependency added successfully', {
      id: dependency.id,
      target: dependency.targetField,
    });
  }

  /**
   * Remove field dependency
   */
  public removeDependency(dependencyId: string): boolean {
    const removed = this.dependencies.delete(dependencyId);
    if (removed) {
      this.rebuildDependencyGraph();
      logger.info('Field dependency removed', { dependencyId });
    }
    return removed;
  }

  /**
   * Update field value and trigger calculations
   */
  public async updateFieldValue(
    fieldId: string,
    value: any,
    triggeredBy?: string
  ): Promise<CalculationUpdate[]> {
    const startTime = Date.now();
    const oldValue = this.fieldValues.get(fieldId);

    // Update field value
    this.fieldValues.set(fieldId, value);

    // Clear cache for affected calculations
    if (this.options.enableCaching) {
      this.clearCacheForField(fieldId);
    }

    // Get affected fields
    const affectedFields = this.getAffectedFields(fieldId);

    // Perform calculations
    const updates: CalculationUpdate[] = [];

    if (this.options.enableRealTimeUpdates && affectedFields.length > 0) {
      const calculationUpdates = await this.calculateDependentFields(affectedFields);
      updates.push(...calculationUpdates);
    }

    // Create update record
    const update: CalculationUpdate = {
      fieldId,
      oldValue,
      newValue: value,
      timestamp: new Date(),
      triggeredBy: triggeredBy || 'manual',
      affectedFields,
      calculationTime: Date.now() - startTime,
    };

    updates.unshift(update);

    // Notify listeners
    this.notifyUpdateListeners(fieldId, update);

    logger.debug('Field value updated', {
      fieldId,
      oldValue,
      newValue: value,
      affectedFields: affectedFields.length,
      calculationTime: update.calculationTime,
    });

    return updates;
  }

  /**
   * Calculate dependent fields
   */
  public async calculateDependentFields(fieldIds?: string[]): Promise<CalculationUpdate[]> {
    if (this.isCalculating) {
      logger.warn('Calculation already in progress, skipping');
      return [];
    }

    this.isCalculating = true;
    const updates: CalculationUpdate[] = [];

    try {
      const fieldsToCalculate = fieldIds || this.getCalculationOrder();

      for (const fieldId of fieldsToCalculate) {
        const fieldDependencies = this.getDependenciesForField(fieldId);

        for (const dependency of fieldDependencies) {
          if (!dependency.isActive) continue;

          try {
            const update = await this.calculateSingleField(dependency);
            if (update) {
              updates.push(update);
            }
          } catch (error) {
            logger.error('Failed to calculate field', {
              fieldId,
              dependency: dependency.id,
              error: error instanceof Error ? error.message : 'Unknown error',
            });
          }
        }
      }

      logger.info('Dependent fields calculated', {
        fieldsCalculated: updates.length,
        totalFields: fieldsToCalculate.length,
      });

      return updates;
    } finally {
      this.isCalculating = false;
    }
  }

  /**
   * Get dependency graph
   */
  public getDependencyGraph(): DependencyGraph | null {
    return this.dependencyGraph;
  }

  /**
   * Get calculation order using topological sort
   */
  public getCalculationOrder(): string[] {
    if (!this.dependencyGraph) {
      this.rebuildDependencyGraph();
    }
    return this.dependencyGraph?.calculationOrder || [];
  }

  /**
   * Detect circular dependencies
   */
  public detectCycles(): string[][] {
    if (!this.dependencyGraph) {
      this.rebuildDependencyGraph();
    }
    return this.dependencyGraph?.cycles || [];
  }

  /**
   * Add update listener for field changes
   */
  public addUpdateListener(fieldId: string, listener: Function): void {
    if (!this.updateListeners.has(fieldId)) {
      this.updateListeners.set(fieldId, []);
    }
    this.updateListeners.get(fieldId)!.push(listener);
  }

  /**
   * Remove update listener
   */
  public removeUpdateListener(fieldId: string, listener: Function): boolean {
    const listeners = this.updateListeners.get(fieldId);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index !== -1) {
        listeners.splice(index, 1);
        return true;
      }
    }
    return false;
  }

  /**
   * Get field value
   */
  public getFieldValue(fieldId: string): any {
    return this.fieldValues.get(fieldId);
  }

  /**
   * Set multiple field values
   */
  public async setFieldValues(
    values: Record<string, any>,
    _triggeredBy?: string
  ): Promise<CalculationUpdate[]> {
    const allUpdates: CalculationUpdate[] = [];

    // Update all values first
    for (const [fieldId, value] of Object.entries(values)) {
      this.fieldValues.set(fieldId, value);
    }

    // Then calculate dependencies
    const affectedFields = new Set<string>();
    for (const fieldId of Object.keys(values)) {
      const affected = this.getAffectedFields(fieldId);
      affected.forEach(f => affectedFields.add(f));
    }

    if (affectedFields.size > 0) {
      const updates = await this.calculateDependentFields(Array.from(affectedFields));
      allUpdates.push(...updates);
    }

    return allUpdates;
  }

  /**
   * Clear all cached calculations
   */
  public clearCache(): void {
    this.calculationCache.clear();
    logger.info('Calculation cache cleared');
  }

  /**
   * Get dependencies for a specific field
   */
  public getDependenciesForField(fieldId: string): FieldDependency[] {
    return Array.from(this.dependencies.values()).filter(dep => dep.targetField === fieldId);
  }

  /**
   * Get fields that depend on the given field
   */
  public getDependentsForField(fieldId: string): FieldDependency[] {
    return Array.from(this.dependencies.values()).filter(dep => dep.sourceFields.includes(fieldId));
  }

  /**
   * Validate field dependency
   */
  private validateDependency(dependency: FieldDependency): void {
    if (!dependency.id || dependency.id.trim() === '') {
      throw new Error('Dependency ID cannot be empty');
    }

    if (!dependency.targetField || dependency.targetField.trim() === '') {
      throw new Error('Target field cannot be empty');
    }

    if (!dependency.sourceFields || dependency.sourceFields.length === 0) {
      throw new Error('Source fields cannot be empty');
    }

    if (!dependency.expression || dependency.expression.trim() === '') {
      throw new Error('Dependency expression cannot be empty');
    }

    // Check for self-dependency
    if (dependency.sourceFields.includes(dependency.targetField)) {
      throw new Error('Field cannot depend on itself');
    }

    // Validate expression syntax
    try {
      this.calculationEngine.validateExpression(dependency.expression);
    } catch (error) {
      throw new Error(`Invalid expression syntax: ${dependency.expression}`);
    }
  }

  /**
   * Rebuild dependency graph
   */
  private rebuildDependencyGraph(): void {
    const nodes: DependencyNode[] = [];
    const edges: DependencyEdge[] = [];
    const fieldIds = new Set<string>();

    // Collect all field IDs
    for (const dependency of this.dependencies.values()) {
      fieldIds.add(dependency.targetField);
      dependency.sourceFields.forEach(field => fieldIds.add(field));
    }

    // Create nodes
    for (const fieldId of fieldIds) {
      const dependencies = this.getDependenciesForField(fieldId);
      const dependents = this.getDependentsForField(fieldId);

      nodes.push({
        id: fieldId,
        fieldId,
        fieldName: fieldId, // Could be enhanced with actual field names
        fieldType: 'unknown', // Could be enhanced with actual field types
        value: this.fieldValues.get(fieldId),
        isCalculated: dependencies.length > 0,
        dependencies: dependencies.flatMap(d => d.sourceFields),
        dependents: dependents.map(d => d.targetField),
        level: 0, // Will be calculated during topological sort
      });
    }

    // Create edges
    for (const dependency of this.dependencies.values()) {
      for (const sourceField of dependency.sourceFields) {
        edges.push({
          id: `${sourceField}->${dependency.targetField}`,
          source: sourceField,
          target: dependency.targetField,
          dependency,
          weight: dependency.priority,
        });
      }
    }

    // Perform topological sort and cycle detection
    const { calculationOrder, cycles } = this.performTopologicalSort(nodes, edges);

    // Update node levels
    this.updateNodeLevels(nodes, calculationOrder);

    // Create graph metadata
    const metadata: DependencyGraphMetadata = {
      totalNodes: nodes.length,
      totalEdges: edges.length,
      maxDepth: Math.max(...nodes.map(n => n.level), 0),
      cycleCount: cycles.length,
      lastUpdated: new Date(),
      version: 1,
    };

    this.dependencyGraph = {
      nodes,
      edges,
      cycles,
      calculationOrder,
      metadata,
    };

    logger.debug('Dependency graph rebuilt', {
      nodes: nodes.length,
      edges: edges.length,
      cycles: cycles.length,
      maxDepth: metadata.maxDepth,
    });
  }

  /**
   * Perform topological sort with cycle detection
   */
  private performTopologicalSort(
    nodes: DependencyNode[],
    edges: DependencyEdge[]
  ): { calculationOrder: string[]; cycles: string[][] } {
    const inDegree = new Map<string, number>();
    const adjacencyList = new Map<string, string[]>();
    const cycles: string[][] = [];

    // Initialize
    for (const node of nodes) {
      inDegree.set(node.id, 0);
      adjacencyList.set(node.id, []);
    }

    // Build adjacency list and calculate in-degrees
    for (const edge of edges) {
      adjacencyList.get(edge.source)!.push(edge.target);
      inDegree.set(edge.target, (inDegree.get(edge.target) || 0) + 1);
    }

    // Kahn's algorithm for topological sorting
    const queue: string[] = [];
    const result: string[] = [];

    // Find nodes with no incoming edges
    for (const [nodeId, degree] of inDegree) {
      if (degree === 0) {
        queue.push(nodeId);
      }
    }

    while (queue.length > 0) {
      const current = queue.shift()!;
      result.push(current);

      // Process neighbors
      for (const neighbor of adjacencyList.get(current) || []) {
        const newDegree = inDegree.get(neighbor)! - 1;
        inDegree.set(neighbor, newDegree);

        if (newDegree === 0) {
          queue.push(neighbor);
        }
      }
    }

    // Check for cycles
    if (result.length !== nodes.length) {
      // There are cycles - find them using DFS
      const visited = new Set<string>();
      const recursionStack = new Set<string>();

      for (const node of nodes) {
        if (!visited.has(node.id)) {
          const cycle = this.findCycleDFS(node.id, adjacencyList, visited, recursionStack, []);
          if (cycle.length > 0) {
            cycles.push(cycle);
          }
        }
      }
    }

    return { calculationOrder: result, cycles };
  }

  /**
   * Find cycles using DFS
   */
  private findCycleDFS(
    nodeId: string,
    adjacencyList: Map<string, string[]>,
    visited: Set<string>,
    recursionStack: Set<string>,
    path: string[]
  ): string[] {
    visited.add(nodeId);
    recursionStack.add(nodeId);
    path.push(nodeId);

    for (const neighbor of adjacencyList.get(nodeId) || []) {
      if (!visited.has(neighbor)) {
        const cycle = this.findCycleDFS(neighbor, adjacencyList, visited, recursionStack, [
          ...path,
        ]);
        if (cycle.length > 0) {
          return cycle;
        }
      } else if (recursionStack.has(neighbor)) {
        // Found a cycle
        const cycleStart = path.indexOf(neighbor);
        return path.slice(cycleStart).concat([neighbor]);
      }
    }

    recursionStack.delete(nodeId);
    return [];
  }

  /**
   * Update node levels based on calculation order
   */
  private updateNodeLevels(nodes: DependencyNode[], calculationOrder: string[]): void {
    const levelMap = new Map<string, number>();

    for (let i = 0; i < calculationOrder.length; i++) {
      const fieldId = calculationOrder[i];
      if (fieldId) {
        levelMap.set(fieldId, i);
      }
    }

    for (const node of nodes) {
      node.level = levelMap.get(node.id) || 0;
    }
  }

  /**
   * Calculate single field
   */
  private async calculateSingleField(
    dependency: FieldDependency
  ): Promise<CalculationUpdate | null> {
    const startTime = Date.now();

    try {
      // Check if all source fields have values
      const sourceValues: Record<string, any> = {};
      let hasAllValues = true;

      for (const sourceField of dependency.sourceFields) {
        const value = this.fieldValues.get(sourceField);
        if (value === undefined || value === null) {
          hasAllValues = false;
          break;
        }
        sourceValues[sourceField] = value;
      }

      if (!hasAllValues) {
        logger.debug('Skipping calculation - missing source values', {
          dependency: dependency.id,
          sourceFields: dependency.sourceFields,
        });
        return null;
      }

      // Check condition if present
      if (dependency.condition) {
        const conditionResult = await this.calculationEngine.evaluate(
          dependency.condition,
          sourceValues
        );
        if (!conditionResult.result) {
          logger.debug('Skipping calculation - condition not met', {
            dependency: dependency.id,
            condition: dependency.condition,
          });
          return null;
        }
      }

      // Check cache
      const cacheKey = this.generateCacheKey(dependency, sourceValues);
      if (this.options.enableCaching && this.calculationCache.has(cacheKey)) {
        const cachedValue = this.calculationCache.get(cacheKey);
        logger.debug('Using cached calculation result', {
          dependency: dependency.id,
          cacheKey,
        });

        const oldValue = this.fieldValues.get(dependency.targetField);
        this.fieldValues.set(dependency.targetField, cachedValue);

        return {
          fieldId: dependency.targetField,
          oldValue,
          newValue: cachedValue,
          timestamp: new Date(),
          triggeredBy: 'calculation',
          affectedFields: [],
          calculationTime: Date.now() - startTime,
        };
      }

      // Perform calculation
      const calculationResult = await this.calculationEngine.evaluate(
        dependency.expression,
        sourceValues
      );
      const newValue = calculationResult.result;

      // Cache result
      if (this.options.enableCaching) {
        this.calculationCache.set(cacheKey, newValue);
      }

      // Update field value
      const oldValue = this.fieldValues.get(dependency.targetField);
      this.fieldValues.set(dependency.targetField, newValue);

      const update: CalculationUpdate = {
        fieldId: dependency.targetField,
        oldValue,
        newValue,
        timestamp: new Date(),
        triggeredBy: 'calculation',
        affectedFields: [],
        calculationTime: Date.now() - startTime,
      };

      logger.debug('Field calculated successfully', {
        dependency: dependency.id,
        targetField: dependency.targetField,
        oldValue,
        newValue,
        calculationTime: update.calculationTime,
      });

      return update;
    } catch (error) {
      logger.error('Field calculation failed', {
        dependency: dependency.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Get affected fields for a given field
   */
  private getAffectedFields(fieldId: string): string[] {
    const affected: string[] = [];
    const visited = new Set<string>();

    const traverse = (currentFieldId: string) => {
      if (visited.has(currentFieldId)) return;
      visited.add(currentFieldId);

      const dependents = this.getDependentsForField(currentFieldId);
      for (const dependent of dependents) {
        if (!affected.includes(dependent.targetField)) {
          affected.push(dependent.targetField);
          traverse(dependent.targetField);
        }
      }
    };

    traverse(fieldId);
    return affected;
  }

  /**
   * Clear cache for field and its dependents
   */
  private clearCacheForField(fieldId: string): void {
    const keysToDelete: string[] = [];

    for (const [key] of this.calculationCache) {
      if (key.includes(fieldId)) {
        keysToDelete.push(key);
      }
    }

    for (const key of keysToDelete) {
      this.calculationCache.delete(key);
    }
  }

  /**
   * Generate cache key for dependency calculation
   */
  private generateCacheKey(dependency: FieldDependency, sourceValues: Record<string, any>): string {
    const valuesStr = JSON.stringify(sourceValues, Object.keys(sourceValues).sort());
    return `${dependency.id}:${valuesStr}`;
  }

  /**
   * Notify update listeners
   */
  private notifyUpdateListeners(fieldId: string, update: CalculationUpdate): void {
    const listeners = this.updateListeners.get(fieldId);
    if (listeners) {
      for (const listener of listeners) {
        try {
          listener(update);
        } catch (error) {
          logger.error('Update listener failed', {
            fieldId,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }
    }
  }
}

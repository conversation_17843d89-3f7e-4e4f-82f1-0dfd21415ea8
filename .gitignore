# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# vuepress build output
.vuepress/dist

# vuepress v2.x temp and cache directory
.temp
.cache

# Docusaurus cache and generated files
.docusaurus

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Electron-Forge
out/

# Build outputs
dist/
build/
release/
app/
packages/

# TypeScript build info
*.tsbuildinfo

# Webpack build outputs
*.bundle.js
*.chunk.js
*.hot-update.js
*.hot-update.json

# Test coverage
coverage/
.nyc_output/
junit.xml

# Jest cache
.jest/

# Playwright
test-results/
playwright-report/
playwright/.cache/

# Database files
*.db
*.sqlite
*.sqlite3
*.db-journal
*.db-wal
*.db-shm

# AI model cache and data
*.model
*.bin
*.onnx
chroma_db/
vector_cache/
embeddings_cache/

# User data and preferences
user_data/
app_data/
preferences/
sessions/
temp_files/

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/
.metadata/
.kiro/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Windows image file caches
Thumbs.db
ehthumbs.db

# Folder config file
Desktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Windows Installer files
*.cab
*.msi
*.msm
*.msp

# Windows shortcuts
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~

# temporary files which can be created if a process still has a handle open of a deleted file
.fuse_hidden*

# KDE directory preferences
.directory

# Linux trash folder which might appear on any partition or disk
.Trash-*

# .nfs files are created when an open file is removed but is still being accessed
.nfs*

# Electron specific
electron-builder.env

# Husky
.husky/_

# Temporary directories
tmp/
temp/
.tmp/

# Lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# Environment specific files
.env.*.local
.env.development
.env.staging
.env.production

# Backup files
*.backup
*.bak
*.orig

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Large media files (should use Git LFS if needed)
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm
*.mkv
*.m4v
*.3gp
*.3g2
*.mp3
*.wav
*.flac
*.aac
*.ogg
*.wma
*.m4a
*.aiff
*.au
*.ra

# Document processing temporary files
*.tmp
*.temp
processing_cache/
ocr_cache/
pdf_cache/
image_cache/

# Security and sensitive files
*.key
*.pem
*.p12
*.pfx
secrets/
credentials/

# Performance and profiling
*.cpuprofile
*.heapprofile
*.heapsnapshot

# Storybook
.storybook-out/
storybook-static/

# Webpack
.webpack/

# Vite
.vite/

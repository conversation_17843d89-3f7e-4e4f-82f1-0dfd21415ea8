import { create, all } from 'mathjs';
import { createLogger } from '../utils/logger';
import {
  TemplateVariable,
  VariableType,
  VariableConstraints,
  ValidationError,
} from '../../shared/types/Document';

const logger = createLogger('TemplateVariableManager');
const math = create(all || {});

export interface VariableAssignment {
  variableName: string;
  value: any;
  source: VariableSource;
  confidence: number;
  timestamp: Date;
}

export enum VariableSource {
  USER_INPUT = 'user_input',
  EXTRACTED_DATA = 'extracted_data',
  CALCULATED = 'calculated',
  DEFAULT_VALUE = 'default_value',
  TEMPLATE_INHERITANCE = 'template_inheritance',
}

export interface VariableDependency {
  dependentVariable: string;
  sourceVariables: string[];
  expression: string;
  condition?: string;
  priority: number;
}

export interface VariableSubstitutionResult {
  success: boolean;
  substitutedContent: string;
  variablesUsed: string[];
  errors: ValidationError[];
  warnings: ValidationError[];
}

export interface VariableCalculationChain {
  variables: string[];
  dependencies: VariableDependency[];
  calculationOrder: string[];
  cycles: string[][];
}

export interface ExpressionContext {
  variables: Map<string, any>;
  functions: Map<string, Function>;
  constants: Map<string, any>;
}

/**
 * Template variable management system
 */
export class TemplateVariableManager {
  private readonly variableAssignments: Map<string, VariableAssignment> = new Map();
  private readonly variableDependencies: Map<string, VariableDependency[]> = new Map();
  private readonly expressionCache: Map<string, any> = new Map();

  /**
   * Define a new template variable
   */
  public defineVariable(variable: TemplateVariable): void {
    logger.debug('Defining variable', { name: variable.name, type: variable.type });

    // Validate variable definition
    this.validateVariableDefinition(variable);

    // Set default value if provided
    if (variable.defaultValue !== undefined) {
      this.assignVariable(variable.name, variable.defaultValue, VariableSource.DEFAULT_VALUE);
    }

    logger.info('Variable defined', { name: variable.name, type: variable.type });
  }

  /**
   * Assign value to a variable
   */
  public assignVariable(
    variableName: string,
    value: any,
    source: VariableSource = VariableSource.USER_INPUT,
    confidence: number = 1.0
  ): void {
    logger.debug('Assigning variable', { variableName, value, source });

    const assignment: VariableAssignment = {
      variableName,
      value,
      source,
      confidence,
      timestamp: new Date(),
    };

    this.variableAssignments.set(variableName, assignment);

    // Trigger dependent variable calculations
    this.calculateDependentVariables(variableName);

    logger.debug('Variable assigned', { variableName, source });
  }

  /**
   * Get variable value
   */
  public getVariableValue(variableName: string): any {
    const assignment = this.variableAssignments.get(variableName);
    return assignment ? assignment.value : undefined;
  }

  /**
   * Get all variable assignments
   */
  public getAllVariables(): Map<string, VariableAssignment> {
    return new Map(this.variableAssignments);
  }

  /**
   * Add variable dependency
   */
  public addDependency(dependency: VariableDependency): void {
    logger.debug('Adding variable dependency', {
      dependent: dependency.dependentVariable,
      sources: dependency.sourceVariables,
    });

    // Validate dependency
    this.validateDependency(dependency);

    const dependencies = this.variableDependencies.get(dependency.dependentVariable) || [];
    dependencies.push(dependency);
    this.variableDependencies.set(dependency.dependentVariable, dependencies);

    // Check for circular dependencies
    const cycles = this.detectCircularDependencies();
    if (cycles.length > 0) {
      logger.warn('Circular dependencies detected', { cycles });
      throw new Error(
        `Circular dependencies detected: ${cycles.map(c => c.join(' -> ')).join(', ')}`
      );
    }

    logger.info('Variable dependency added', {
      dependent: dependency.dependentVariable,
      sources: dependency.sourceVariables.length,
    });
  }

  /**
   * Calculate dependent variables
   */
  private calculateDependentVariables(changedVariable: string): void {
    const dependentVariables = this.findDependentVariables(changedVariable);

    for (const dependentVar of dependentVariables) {
      const dependencies = this.variableDependencies.get(dependentVar) || [];

      for (const dependency of dependencies) {
        try {
          // Check if all source variables are available
          const allSourcesAvailable = dependency.sourceVariables.every(sourceVar =>
            this.variableAssignments.has(sourceVar)
          );

          if (allSourcesAvailable) {
            // Evaluate condition if present
            if (dependency.condition) {
              const conditionResult = this.evaluateExpression(dependency.condition);
              if (!conditionResult) {
                continue; // Skip this dependency
              }
            }

            // Calculate new value
            const newValue = this.evaluateExpression(dependency.expression);
            this.assignVariable(dependentVar, newValue, VariableSource.CALCULATED, 0.95);
          }
        } catch (error) {
          logger.error('Failed to calculate dependent variable', {
            error,
            dependentVar,
            expression: dependency.expression,
          });
        }
      }
    }
  }

  /**
   * Find variables that depend on the given variable
   */
  private findDependentVariables(variableName: string): string[] {
    const dependents: string[] = [];

    for (const [dependentVar, dependencies] of this.variableDependencies) {
      for (const dependency of dependencies) {
        if (dependency.sourceVariables.includes(variableName)) {
          dependents.push(dependentVar);
          break;
        }
      }
    }

    return dependents;
  }

  /**
   * Substitute variables in template content
   */
  public substituteVariables(
    content: string,
    variables: TemplateVariable[]
  ): VariableSubstitutionResult {
    logger.debug('Substituting variables in content', {
      contentLength: content.length,
      variableCount: variables.length,
    });

    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];
    const variablesUsed: string[] = [];
    let substitutedContent = content;

    try {
      // Find all variable references in the content
      const variablePattern = /\{\{([^}]+)\}\}/g;
      let match;

      while ((match = variablePattern.exec(content)) !== null) {
        const variableExpression = match[1]?.trim() || '';
        const fullMatch = match[0];

        try {
          // Parse variable expression (could be simple variable or complex expression)
          const result = this.evaluateVariableExpression(variableExpression, variables);

          if (result.success) {
            substitutedContent = substitutedContent.replace(fullMatch, String(result.value));
            variablesUsed.push(...result.variablesUsed);
          } else {
            errors.push({
              code: 'VARIABLE_SUBSTITUTION_FAILED',
              message: `Failed to substitute variable: ${variableExpression}`,
              severity: 'error',
            });
          }
        } catch (error) {
          errors.push({
            code: 'VARIABLE_EXPRESSION_ERROR',
            message: `Error in variable expression: ${variableExpression}`,
            severity: 'error',
          });
        }
      }

      // Check for undefined variables
      const undefinedVariables = variablesUsed.filter(
        varName => !this.variableAssignments.has(varName)
      );

      if (undefinedVariables.length > 0) {
        warnings.push({
          code: 'UNDEFINED_VARIABLES',
          message: `Undefined variables: ${undefinedVariables.join(', ')}`,
          severity: 'warning',
        });
      }

      return {
        success: errors.length === 0,
        substitutedContent,
        variablesUsed: [...new Set(variablesUsed)],
        errors,
        warnings,
      };
    } catch (error) {
      logger.error('Variable substitution failed', { error });
      return {
        success: false,
        substitutedContent: content,
        variablesUsed: [],
        errors: [
          {
            code: 'SUBSTITUTION_ERROR',
            message: `Variable substitution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            severity: 'error',
          },
        ],
        warnings: [],
      };
    }
  }

  /**
   * Evaluate variable expression
   */
  private evaluateVariableExpression(
    expression: string,
    _variables: TemplateVariable[]
  ): { success: boolean; value: any; variablesUsed: string[] } {
    const variablesUsed: string[] = [];

    try {
      // Simple variable reference
      if (/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(expression)) {
        const value = this.getVariableValue(expression);
        if (value !== undefined) {
          variablesUsed.push(expression);
          return { success: true, value, variablesUsed };
        } else {
          return { success: false, value: undefined, variablesUsed: [expression] };
        }
      }

      // Complex expression - evaluate with math.js
      const result = this.evaluateExpression(expression);

      // Extract variable names from expression
      const variableNames = this.extractVariableNames(expression);
      variablesUsed.push(...variableNames);

      return { success: true, value: result, variablesUsed };
    } catch (error) {
      logger.error('Failed to evaluate variable expression', { error, expression });
      return { success: false, value: undefined, variablesUsed };
    }
  }

  /**
   * Evaluate mathematical expression
   */
  private evaluateExpression(expression: string): any {
    const cacheKey = `${expression}_${this.getVariableHash()}`;
    const cached = this.expressionCache.get(cacheKey);
    if (cached !== undefined) {
      return cached;
    }

    try {
      // Create expression context
      const context = this.createExpressionContext();

      // Evaluate expression
      const result = math.evaluate(expression, context.variables);

      // Cache result
      this.expressionCache.set(cacheKey, result);

      return result;
    } catch (error) {
      logger.error('Expression evaluation failed', { error, expression });
      throw error;
    }
  }

  /**
   * Create expression context for evaluation
   */
  private createExpressionContext(): ExpressionContext {
    const variables = new Map<string, any>();
    const functions = new Map<string, Function>();
    const constants = new Map<string, any>();

    // Add variable values
    for (const [name, assignment] of this.variableAssignments) {
      variables.set(name, assignment.value);
    }

    // Add common constants
    constants.set('PI', Math.PI);
    constants.set('E', Math.E);

    // Add custom functions
    functions.set('round', (value: number, decimals: number = 0) => {
      const factor = Math.pow(10, decimals);
      return Math.round(value * factor) / factor;
    });

    functions.set('max', Math.max);
    functions.set('min', Math.min);
    functions.set('abs', Math.abs);

    return { variables, functions, constants };
  }

  /**
   * Extract variable names from expression
   */
  private extractVariableNames(expression: string): string[] {
    const variablePattern = /\b[a-zA-Z_][a-zA-Z0-9_]*\b/g;
    const matches = expression.match(variablePattern) || [];

    // Filter out math.js functions and constants
    const mathFunctions = ['sin', 'cos', 'tan', 'log', 'sqrt', 'abs', 'round', 'max', 'min'];
    const mathConstants = ['PI', 'E'];

    return matches.filter(
      match =>
        !mathFunctions.includes(match) &&
        !mathConstants.includes(match) &&
        this.variableAssignments.has(match)
    );
  }

  /**
   * Get hash of current variable values for caching
   */
  private getVariableHash(): string {
    const values = Array.from(this.variableAssignments.entries())
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([name, assignment]) => `${name}:${assignment.value}`)
      .join('|');

    return Buffer.from(values).toString('base64').slice(0, 16);
  }

  /**
   * Build calculation chain for variables
   */
  public buildCalculationChain(): VariableCalculationChain {
    const variables = Array.from(this.variableAssignments.keys());
    const dependencies = Array.from(this.variableDependencies.values()).flat();

    // Topological sort to determine calculation order
    const calculationOrder = this.topologicalSort(variables, dependencies);

    // Detect cycles
    const cycles = this.detectCircularDependencies();

    return {
      variables,
      dependencies,
      calculationOrder,
      cycles,
    };
  }

  /**
   * Topological sort for dependency resolution
   */
  private topologicalSort(variables: string[], dependencies: VariableDependency[]): string[] {
    const graph = new Map<string, string[]>();
    const inDegree = new Map<string, number>();

    // Initialize graph
    for (const variable of variables) {
      graph.set(variable, []);
      inDegree.set(variable, 0);
    }

    // Build graph
    for (const dependency of dependencies) {
      for (const sourceVar of dependency.sourceVariables) {
        if (graph.has(sourceVar)) {
          graph.get(sourceVar)!.push(dependency.dependentVariable);
          inDegree.set(
            dependency.dependentVariable,
            (inDegree.get(dependency.dependentVariable) || 0) + 1
          );
        }
      }
    }

    // Kahn's algorithm
    const queue: string[] = [];
    const result: string[] = [];

    // Find variables with no dependencies
    for (const [variable, degree] of inDegree) {
      if (degree === 0) {
        queue.push(variable);
      }
    }

    while (queue.length > 0) {
      const current = queue.shift()!;
      result.push(current);

      for (const neighbor of graph.get(current) || []) {
        inDegree.set(neighbor, inDegree.get(neighbor)! - 1);
        if (inDegree.get(neighbor) === 0) {
          queue.push(neighbor);
        }
      }
    }

    return result;
  }

  /**
   * Detect circular dependencies
   */
  private detectCircularDependencies(): string[][] {
    const cycles: string[][] = [];
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    for (const variable of this.variableAssignments.keys()) {
      if (!visited.has(variable)) {
        const cycle = this.dfsDetectCycle(variable, visited, recursionStack, []);
        if (cycle.length > 0) {
          cycles.push(cycle);
        }
      }
    }

    return cycles;
  }

  /**
   * DFS for cycle detection
   */
  private dfsDetectCycle(
    variable: string,
    visited: Set<string>,
    recursionStack: Set<string>,
    path: string[]
  ): string[] {
    visited.add(variable);
    recursionStack.add(variable);
    path.push(variable);

    const dependencies = this.variableDependencies.get(variable) || [];

    for (const dependency of dependencies) {
      for (const sourceVar of dependency.sourceVariables) {
        if (!visited.has(sourceVar)) {
          const cycle = this.dfsDetectCycle(sourceVar, visited, recursionStack, [...path]);
          if (cycle.length > 0) {
            return cycle;
          }
        } else if (recursionStack.has(sourceVar)) {
          // Found cycle
          const cycleStart = path.indexOf(sourceVar);
          return path.slice(cycleStart).concat([sourceVar]);
        }
      }
    }

    recursionStack.delete(variable);
    return [];
  }

  /**
   * Validate variable definition
   */
  private validateVariableDefinition(variable: TemplateVariable): void {
    if (!variable.name || variable.name.trim() === '') {
      throw new Error('Variable name cannot be empty');
    }

    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(variable.name)) {
      throw new Error('Variable name must be a valid identifier');
    }

    if (variable.defaultValue !== undefined) {
      this.validateVariableValue(variable.defaultValue, variable.type, variable.constraints);
    }
  }

  /**
   * Validate variable value against type and constraints
   */
  private validateVariableValue(
    value: any,
    type: VariableType,
    constraints?: VariableConstraints
  ): void {
    // Type validation
    switch (type) {
      case VariableType.STRING:
        if (typeof value !== 'string') {
          throw new Error('Value must be a string');
        }
        break;
      case VariableType.NUMBER:
        if (typeof value !== 'number' || isNaN(value)) {
          throw new Error('Value must be a number');
        }
        break;
      case VariableType.BOOLEAN:
        if (typeof value !== 'boolean') {
          throw new Error('Value must be a boolean');
        }
        break;
      case VariableType.DATE:
        if (!(value instanceof Date) && isNaN(Date.parse(value))) {
          throw new Error('Value must be a valid date');
        }
        break;
    }

    // Constraint validation
    if (constraints) {
      if (type === VariableType.STRING && typeof value === 'string') {
        if (constraints.minLength && value.length < constraints.minLength) {
          throw new Error(`String must be at least ${constraints.minLength} characters`);
        }
        if (constraints.maxLength && value.length > constraints.maxLength) {
          throw new Error(`String must be at most ${constraints.maxLength} characters`);
        }
        if (constraints.pattern && !new RegExp(constraints.pattern).test(value)) {
          throw new Error('String does not match required pattern');
        }
      }

      if (type === VariableType.NUMBER && typeof value === 'number') {
        if (constraints.minValue !== undefined && value < constraints.minValue) {
          throw new Error(`Number must be at least ${constraints.minValue}`);
        }
        if (constraints.maxValue !== undefined && value > constraints.maxValue) {
          throw new Error(`Number must be at most ${constraints.maxValue}`);
        }
      }
    }
  }

  /**
   * Validate dependency
   */
  private validateDependency(dependency: VariableDependency): void {
    if (!dependency.dependentVariable || dependency.dependentVariable.trim() === '') {
      throw new Error('Dependent variable name cannot be empty');
    }

    if (!dependency.sourceVariables || dependency.sourceVariables.length === 0) {
      throw new Error('Source variables cannot be empty');
    }

    if (!dependency.expression || dependency.expression.trim() === '') {
      throw new Error('Dependency expression cannot be empty');
    }

    // Validate expression syntax
    try {
      math.parse(dependency.expression);
    } catch (error) {
      throw new Error(`Invalid expression syntax: ${dependency.expression}`);
    }
  }

  /**
   * Clear all variables and dependencies
   */
  public clear(): void {
    this.variableAssignments.clear();
    this.variableDependencies.clear();
    this.expressionCache.clear();
    logger.info('Variable manager cleared');
  }

  /**
   * Export variables to JSON
   */
  public exportVariables(): string {
    const data = {
      assignments: Array.from(this.variableAssignments.entries()),
      dependencies: Array.from(this.variableDependencies.entries()),
    };
    return JSON.stringify(data, null, 2);
  }

  /**
   * Import variables from JSON
   */
  public importVariables(jsonData: string): void {
    try {
      const data = JSON.parse(jsonData);

      // Clear existing data
      this.clear();

      // Import assignments
      if (data.assignments) {
        for (const [name, assignment] of data.assignments) {
          this.variableAssignments.set(name, assignment);
        }
      }

      // Import dependencies
      if (data.dependencies) {
        for (const [name, dependencies] of data.dependencies) {
          this.variableDependencies.set(name, dependencies);
        }
      }

      logger.info('Variables imported successfully');
    } catch (error) {
      logger.error('Failed to import variables', { error });
      throw new Error('Failed to import variables: Invalid JSON format');
    }
  }
}

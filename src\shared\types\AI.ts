// AI service type definitions

export interface AIProvider {
  id: string;
  name: string;
  type: AIProviderType;
  endpoint: string;
  apiKey: string;
  models: AIModel[];
  isActive: boolean;
}

export enum AIProviderType {
  AZURE = 'azure',
  OPENAI = 'openai',
  LANGCHAIN = 'langchain',
  LOCAL = 'local',
}

export interface AIModel {
  id: string;
  name: string;
  type: AIModelType;
  maxTokens: number;
  costPerToken: number;
  capabilities: AICapability[];
}

export enum AIModelType {
  COMPLETION = 'completion',
  CHAT = 'chat',
  EMBEDDING = 'embedding',
  VISION = 'vision',
  REASONING = 'reasoning',
}

export enum AICapability {
  TEXT_GENERATION = 'text_generation',
  TEXT_ANALYSIS = 'text_analysis',
  DOCUMENT_UNDERSTANDING = 'document_understanding',
  FORM_FILLING = 'form_filling',
  ENTITY_EXTRACTION = 'entity_extraction',
  SUMMARIZATION = 'summarization',
  TRANSLATION = 'translation',
  QUESTION_ANSWERING = 'question_answering',
  CODE_GENERATION = 'code_generation',
  IMAGE_ANALYSIS = 'image_analysis',
}

export interface AIRequest {
  id: string;
  type: AIRequestType;
  prompt: string;
  context?: string;
  model: string;
  parameters: AIParameters;
  metadata: RequestMetadata;
}

export enum AIRequestType {
  COMPLETION = 'completion',
  CHAT = 'chat',
  EMBEDDING = 'embedding',
  ANALYSIS = 'analysis',
  EXTRACTION = 'extraction',
  REASONING = 'reasoning',
}

export interface AIParameters {
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stopSequences?: string[];
  stream?: boolean;
}

export interface RequestMetadata {
  userId?: string;
  sessionId?: string;
  documentId?: string;
  timestamp: Date;
  priority: RequestPriority;
}

export enum RequestPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
}

export interface AIResponse {
  id: string;
  requestId: string;
  success: boolean;
  content: string;
  confidence: number;
  tokensUsed: number;
  processingTime: number;
  model: string;
  error?: AIError;
  metadata: ResponseMetadata;
}

export interface AIError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  retryable: boolean;
}

export interface ResponseMetadata {
  timestamp: Date;
  provider: string;
  model: string;
  cached: boolean;
  cost: number;
}

export interface EmbeddingRequest {
  text: string;
  model: string;
  dimensions?: number;
}

export interface EmbeddingResponse {
  embedding: number[];
  dimensions: number;
  model: string;
  tokensUsed: number;
}

export interface ChatMessage {
  role: ChatRole;
  content: string;
  timestamp: Date;
  metadata?: MessageMetadata;
}

export enum ChatRole {
  SYSTEM = 'system',
  USER = 'user',
  ASSISTANT = 'assistant',
  FUNCTION = 'function',
}

export interface MessageMetadata {
  documentId?: string;
  confidence?: number;
  sources?: string[];
  reasoning?: string;
}

export interface ChatSession {
  id: string;
  userId: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  context?: SessionContext;
}

export interface SessionContext {
  documentIds: string[];
  knowledgeBaseId?: string;
  preferences: UserPreferences;
}

export interface UserPreferences {
  preferredModel: string;
  temperature: number;
  maxTokens: number;
  language: string;
  responseStyle: ResponseStyle;
}

export enum ResponseStyle {
  CONCISE = 'concise',
  DETAILED = 'detailed',
  TECHNICAL = 'technical',
  CASUAL = 'casual',
}

export interface AIAgent {
  id: string;
  name: string;
  description: string;
  type: AgentType;
  capabilities: AICapability[];
  tools: AgentTool[];
  configuration: AgentConfiguration;
  isActive: boolean;
}

export enum AgentType {
  DOCUMENT_PROCESSOR = 'document_processor',
  FORM_FILLER = 'form_filler',
  KNOWLEDGE_MANAGER = 'knowledge_manager',
  ANALYST = 'analyst',
  ASSISTANT = 'assistant',
}

export interface AgentTool {
  id: string;
  name: string;
  description: string;
  parameters: ToolParameter[];
}

export interface ToolParameter {
  name: string;
  type: string;
  description: string;
  required: boolean;
  default?: unknown;
}

export interface AgentConfiguration {
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  tools: string[];
  memorySize: number;
  timeout: number;
}

// Additional interfaces for task 2.2

export interface AIModelClient {
  id: string;
  provider: AIProviderType;
  models: string[];
  isConnected: boolean;
  lastHealthCheck: Date;

  // Core AI operations
  generateEmbeddings(text: string, options?: EmbeddingOptions): Promise<EmbeddingVector>;
  performReasoning(
    context: string,
    query: string,
    options?: ReasoningOptions
  ): Promise<ReasoningResult>;
  generateResponse(
    prompt: string,
    context?: string,
    options?: GenerationOptions
  ): Promise<AIResponse>;
  validateResponse(response: string, criteria: ValidationCriteria): Promise<boolean>;

  // Health and monitoring
  checkHealth(): Promise<HealthStatus>;
  getUsageStats(): Promise<UsageStats>;
}

export interface EmbeddingOptions {
  model?: string;
  dimensions?: number;
  normalize?: boolean;
  batchSize?: number;
}

export interface ReasoningOptions {
  model?: string;
  temperature?: number;
  maxSteps?: number;
  includeReasoning?: boolean;
  timeout?: number;
}

export interface GenerationOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  stopSequences?: string[];
}

export interface ReasoningResult {
  conclusion: string;
  confidence: number;
  reasoning: ReasoningStep[];
  sources: string[];
  processingTime: number;
  model: string;
  metadata: ReasoningMetadata;
}

export interface ReasoningStep {
  step: number;
  description: string;
  input: string;
  output: string;
  confidence: number;
  reasoning: string;
}

export interface ReasoningMetadata {
  totalSteps: number;
  averageConfidence: number;
  tokensUsed: number;
  cost: number;
  timestamp: Date;
}

export interface ValidationCriteria {
  accuracy: AccuracyCriteria;
  completeness: CompletenessCriteria;
  consistency: ConsistencyCriteria;
  relevance: RelevanceCriteria;
  format: FormatCriteria;
}

export interface AccuracyCriteria {
  minimumConfidence: number;
  requireSourceVerification: boolean;
  allowedErrorRate: number;
  factCheckRequired: boolean;
}

export interface CompletenessCriteria {
  requiredFields: string[];
  minimumContentLength: number;
  mustIncludeReferences: boolean;
}

export interface ConsistencyCriteria {
  checkAgainstKnowledgeBase: boolean;
  validateInternalConsistency: boolean;
  compareWithPreviousResponses: boolean;
}

export interface RelevanceCriteria {
  topicRelevanceThreshold: number;
  contextRelevanceThreshold: number;
  excludeOffTopicContent: boolean;
}

export interface FormatCriteria {
  expectedFormat: ResponseFormat;
  structureValidation: boolean;
  schemaValidation?: Record<string, unknown>;
}

export enum ResponseFormat {
  TEXT = 'text',
  JSON = 'json',
  MARKDOWN = 'markdown',
  HTML = 'html',
  STRUCTURED = 'structured',
}

export interface HealthStatus {
  isHealthy: boolean;
  latency: number;
  errorRate: number;
  lastError?: string;
  uptime: number;
  timestamp: Date;
}

export interface UsageStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageLatency: number;
  totalTokensUsed: number;
  totalCost: number;
  period: StatsPeriod;
}

export interface StatsPeriod {
  start: Date;
  end: Date;
  duration: number;
}

export interface KnowledgeResult {
  id: string;
  content: string;
  source: string;
  confidence: number;
  relevanceScore: number;
  metadata: KnowledgeMetadata;
  relationships: Relationship[];
  embeddings?: EmbeddingVector;
}

export interface KnowledgeMetadata {
  category: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  author?: string;
  version: number;
  language: string;
  documentId?: string;
}

export interface KnowledgeEntry {
  id: string;
  category: string;
  keyName: string;
  value: KnowledgeValue;
  sourceDocument: string;
  confidence: number;
  embeddings: EmbeddingVector;
  relationships: Relationship[];
  metadata: KnowledgeMetadata;
  createdAt: Date;
  updatedAt: Date;
}

export interface KnowledgeValue {
  type: KnowledgeValueType;
  data: unknown;
  format?: string;
  unit?: string;
  precision?: number;
}

export enum KnowledgeValueType {
  TEXT = 'text',
  NUMBER = 'number',
  DATE = 'date',
  BOOLEAN = 'boolean',
  ARRAY = 'array',
  OBJECT = 'object',
  REFERENCE = 'reference',
}

export interface Relationship {
  id: string;
  type: RelationshipType;
  sourceId: string;
  targetId: string;
  strength: number;
  confidence: number;
  properties: RelationshipProperties;
  createdAt: Date;
}

export enum RelationshipType {
  IS_A = 'is_a',
  PART_OF = 'part_of',
  RELATED_TO = 'related_to',
  DEPENDS_ON = 'depends_on',
  CONTAINS = 'contains',
  REFERENCES = 'references',
  SIMILAR_TO = 'similar_to',
  OPPOSITE_OF = 'opposite_of',
}

export interface RelationshipProperties {
  description?: string;
  weight: number;
  bidirectional: boolean;
  temporal?: TemporalInfo;
  context?: string;
}

export interface TemporalInfo {
  startDate?: Date;
  endDate?: Date;
  duration?: number;
  frequency?: string;
}

export interface KnowledgeGraph {
  id: string;
  name: string;
  description: string;
  nodes: GraphNode[];
  edges: GraphEdge[];
  metadata: GraphMetadata;
  statistics: GraphStatistics;
}

export interface GraphNode {
  id: string;
  label: string;
  type: NodeType;
  properties: NodeProperties;
  position?: GraphPosition;
}

export enum NodeType {
  ENTITY = 'entity',
  CONCEPT = 'concept',
  DOCUMENT = 'document',
  PERSON = 'person',
  ORGANIZATION = 'organization',
  LOCATION = 'location',
  EVENT = 'event',
}

export interface NodeProperties {
  name: string;
  description?: string;
  category: string;
  confidence: number;
  attributes: Record<string, unknown>;
}

export interface GraphPosition {
  x: number;
  y: number;
  z?: number;
}

export interface GraphEdge {
  id: string;
  source: string;
  target: string;
  relationship: Relationship;
  weight: number;
  style?: EdgeStyle;
}

export interface EdgeStyle {
  color?: string;
  width?: number;
  style?: 'solid' | 'dashed' | 'dotted';
  arrow?: boolean;
}

export interface GraphMetadata {
  createdAt: Date;
  updatedAt: Date;
  version: number;
  author: string;
  tags: string[];
}

export interface GraphStatistics {
  nodeCount: number;
  edgeCount: number;
  averageDegree: number;
  density: number;
  components: number;
  diameter?: number;
  clustering: number;
  centrality: CentralityMeasures;
}

export interface CentralityMeasures {
  betweenness: Record<string, number>;
  closeness: Record<string, number>;
  degree: Record<string, number>;
  eigenvector: Record<string, number>;
}

// Graph traversal and path finding types
export interface GraphPath {
  nodes: string[];
  edges: string[];
  length: number;
  weight: number;
  type: PathType;
}

export enum PathType {
  SHORTEST = 'shortest',
  LONGEST = 'longest',
  WEIGHTED = 'weighted',
  UNWEIGHTED = 'unweighted',
}

export interface TraversalResult {
  visitedNodes: string[];
  visitedEdges: string[];
  order: TraversalOrder;
  depth: number;
  components: string[][];
}

export enum TraversalOrder {
  DEPTH_FIRST = 'depth_first',
  BREADTH_FIRST = 'breadth_first',
  TOPOLOGICAL = 'topological',
}

export interface GraphVisualizationData {
  format: VisualizationFormat;
  data: string;
  metadata: VisualizationMetadata;
}

export enum VisualizationFormat {
  CYTOSCAPE = 'cytoscape',
  D3 = 'd3',
  GEPHI = 'gephi',
  GRAPHML = 'graphml',
  JSON_LD = 'json_ld',
  DOT = 'dot',
}

export interface VisualizationMetadata {
  layout: string;
  styling: Record<string, unknown>;
  interactions: string[];
  filters: Record<string, unknown>;
}

export interface Entity {
  id: string;
  name: string;
  type: EntityType;
  confidence: number;
  mentions: EntityMention[];
  attributes: EntityAttributes;
  relationships: Relationship[];
  canonicalForm: string;
}

export interface EntityType {
  category: EntityCategory;
  subtype?: string;
  description: string;
}

export enum EntityCategory {
  PERSON = 'person',
  ORGANIZATION = 'organization',
  LOCATION = 'location',
  DATE = 'date',
  TIME = 'time',
  MONEY = 'money',
  PERCENTAGE = 'percentage',
  PHONE = 'phone',
  EMAIL = 'email',
  URL = 'url',
  CUSTOM = 'custom',
}

export interface EntityMention {
  text: string;
  startOffset: number;
  endOffset: number;
  confidence: number;
  context: string;
  documentId: string;
}

export interface EntityAttributes {
  aliases: string[];
  description?: string;
  properties: Record<string, unknown>;
  metadata: Record<string, unknown>;
}

export interface NLPResult {
  text: string;
  language: string;
  confidence: number;
  entities: Entity[];
  relationships: Relationship[];
  sentiment: SentimentResult;
  topics: Topic[];
  keywords: Keyword[];
  summary?: string;
  processingTime: number;
}

export interface SentimentResult {
  polarity: number; // -1 to 1
  subjectivity: number; // 0 to 1
  confidence: number;
  label: SentimentLabel;
}

export enum SentimentLabel {
  POSITIVE = 'positive',
  NEGATIVE = 'negative',
  NEUTRAL = 'neutral',
  MIXED = 'mixed',
}

export interface Topic {
  id: string;
  name: string;
  confidence: number;
  keywords: string[];
  description?: string;
}

export interface Keyword {
  text: string;
  frequency: number;
  importance: number;
  partOfSpeech?: string;
}

// Document similarity and clustering types
export interface DocumentSimilarity {
  documentId1: string;
  documentId2: string;
  similarity: number;
  method: SimilarityMethod;
  features: SimilarityFeatures;
}

export enum SimilarityMethod {
  COSINE = 'cosine',
  JACCARD = 'jaccard',
  EUCLIDEAN = 'euclidean',
  SEMANTIC = 'semantic',
  HYBRID = 'hybrid',
}

export interface SimilarityFeatures {
  textual: number;
  semantic: number;
  structural: number;
  topical: number;
}

export interface ClusterResult {
  clusterId: string;
  documents: string[];
  centroid: number[];
  coherence: number;
  topics: Topic[];
  representative: string;
}

export interface ClusteringOptions {
  method: ClusteringMethod;
  numberOfClusters?: number;
  minClusterSize?: number;
  maxIterations?: number;
  threshold?: number;
}

export enum ClusteringMethod {
  KMEANS = 'kmeans',
  HIERARCHICAL = 'hierarchical',
  DBSCAN = 'dbscan',
  TOPIC_BASED = 'topic_based',
}

// Readability and text quality types
export interface ReadabilityScore {
  overall: number;
  fleschKincaid: number;
  fleschReadingEase: number;
  gunningFog: number;
  smog: number;
  automatedReadabilityIndex: number;
  colemanLiau: number;
  gradeLevel: string;
  complexity: ComplexityLevel;
}

export enum ComplexityLevel {
  VERY_EASY = 'very_easy',
  EASY = 'easy',
  FAIRLY_EASY = 'fairly_easy',
  STANDARD = 'standard',
  FAIRLY_DIFFICULT = 'fairly_difficult',
  DIFFICULT = 'difficult',
  VERY_DIFFICULT = 'very_difficult',
}

export interface TextQualityAssessment {
  overall: number;
  readability: ReadabilityScore;
  clarity: number;
  coherence: number;
  conciseness: number;
  grammar: GrammarAssessment;
  vocabulary: VocabularyAssessment;
  structure: StructureAssessment;
  suggestions: QualityImprovement[];
}

export interface GrammarAssessment {
  score: number;
  errors: GrammarError[];
  suggestions: string[];
}

export interface GrammarError {
  type: string;
  message: string;
  startOffset: number;
  endOffset: number;
  severity: ErrorSeverity;
  suggestion?: string;
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface VocabularyAssessment {
  diversity: number;
  complexity: number;
  appropriateness: number;
  repetition: number;
  suggestions: string[];
}

export interface StructureAssessment {
  organization: number;
  flow: number;
  transitions: number;
  paragraphStructure: number;
  suggestions: string[];
}

export interface QualityImprovement {
  type: ImprovementType;
  description: string;
  priority: ImprovementPriority;
  suggestion: string;
  example?: string;
}

export enum ImprovementType {
  READABILITY = 'readability',
  CLARITY = 'clarity',
  CONCISENESS = 'conciseness',
  GRAMMAR = 'grammar',
  VOCABULARY = 'vocabulary',
  STRUCTURE = 'structure',
  STYLE = 'style',
}

export enum ImprovementPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface EmbeddingVector {
  vector: number[];
  dimensions: number;
  model: string;
  createdAt: Date;
  metadata?: EmbeddingMetadata;
}

export interface EmbeddingMetadata {
  sourceText: string;
  sourceId?: string;
  chunkIndex?: number;
  totalChunks?: number;
  processingTime: number;
}

export interface VectorSearchResult {
  id: string;
  content: string;
  similarity: number;
  distance: number;
  metadata: Record<string, unknown>;
  embedding?: EmbeddingVector;
}

export interface SemanticSearchOptions {
  query: string;
  limit?: number;
  threshold?: number;
  includeMetadata?: boolean;
  includeEmbeddings?: boolean;
  filters?: SearchFilter[];
  sortBy?: SortOption[];
}

export interface SearchFilter {
  field: string;
  operator: FilterOperator;
  value: unknown;
}

export enum FilterOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  CONTAINS = 'contains',
  IN = 'in',
  NOT_IN = 'not_in',
}

export interface SortOption {
  field: string;
  direction: SortDirection;
}

export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc',
}

export interface LangChainAgent {
  id: string;
  name: string;
  description: string;
  llm: AIModelClient;
  tools: AITool[];
  memory: AgentMemory;
  executor: AgentExecutor;
  configuration: LangChainConfiguration;
}

export interface AITool {
  id: string;
  name: string;
  description: string;
  parameters: ToolParameter[];
  execute: (input: ToolInput) => Promise<ToolOutput>;
  metadata: ToolMetadata;
}

export interface ToolInput {
  parameters: Record<string, unknown>;
  context?: AIContext;
}

export interface ToolOutput {
  result: unknown;
  success: boolean;
  error?: string;
  metadata?: Record<string, unknown>;
}

export interface ToolMetadata {
  category: ToolCategory;
  version: string;
  author: string;
  tags: string[];
  requirements: string[];
}

export enum ToolCategory {
  DOCUMENT_PROCESSING = 'document_processing',
  CALCULATION = 'calculation',
  KNOWLEDGE_BASE = 'knowledge_base',
  FORM_FILLING = 'form_filling',
  ANALYSIS = 'analysis',
  UTILITY = 'utility',
}

export interface AgentMemory {
  type: MemoryType;
  capacity: number;
  persistence: boolean;
  entries: MemoryEntry[];
}

export enum MemoryType {
  CONVERSATION = 'conversation',
  ENTITY = 'entity',
  SUMMARY = 'summary',
  VECTOR = 'vector',
  KNOWLEDGE = 'knowledge',
}

export interface MemoryEntry {
  id: string;
  type: MemoryEntryType;
  content: string;
  importance: number;
  timestamp: Date;
  metadata: Record<string, unknown>;
}

export enum MemoryEntryType {
  FACT = 'fact',
  OBSERVATION = 'observation',
  PLAN = 'plan',
  REFLECTION = 'reflection',
  CONVERSATION = 'conversation',
}

export interface AgentExecutor {
  maxIterations: number;
  timeout: number;
  errorHandling: ErrorHandlingStrategy;
  logging: LoggingConfiguration;
}

export enum ErrorHandlingStrategy {
  FAIL_FAST = 'fail_fast',
  RETRY = 'retry',
  SKIP = 'skip',
  FALLBACK = 'fallback',
}

export interface LoggingConfiguration {
  level: LogLevel;
  includeContext: boolean;
  includeTimestamps: boolean;
  maxLogSize: number;
}

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
}

export interface LangChainConfiguration {
  verbose: boolean;
  temperature: number;
  maxTokens: number;
  callbackHandlers: string[];
  tags: string[];
  metadata: Record<string, unknown>;
}

export interface AIContext {
  sessionId: string;
  userId?: string;
  documentIds: string[];
  knowledgeBaseId?: string;
  currentTask?: string;
  preferences: UserPreferences;
  history: ContextEntry[];
}

export interface ContextEntry {
  timestamp: Date;
  type: ContextEntryType;
  content: string;
  metadata: Record<string, unknown>;
}

export enum ContextEntryType {
  USER_INPUT = 'user_input',
  AI_RESPONSE = 'ai_response',
  TOOL_EXECUTION = 'tool_execution',
  SYSTEM_EVENT = 'system_event',
  ERROR = 'error',
}

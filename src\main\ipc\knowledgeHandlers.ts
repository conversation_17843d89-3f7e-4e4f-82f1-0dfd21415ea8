import { ipcMain } from 'electron';
import { chromaKnowledgeBase } from '../services/ChromaKnowledgeBase';
import { aiModelClient } from '../services/AIModelClient';
import { logger } from '../utils/logger';
import { 
  KnowledgeStoreRequest, 
  KnowledgeQueryRequest, 
  KnowledgeQueryResponse,
  AIGenerateEmbeddingsRequest 
} from '../../shared/types/IPC';

export function setupKnowledgeHandlers(): void {
  // Store information in ChromaDB
  ipcMain.handle('knowledge:store', async (_event, request: KnowledgeStoreRequest) => {
    try {
      logger.info('Storing knowledge item', { id: request.data.id });
      
      // Generate embedding if not provided
      if (!request.data.embedding && request.data.content) {
        const embeddingResponse = await aiModelClient.generateEmbeddings({
          text: request.data.content
        });
        request.data.embedding = embeddingResponse.embedding;
      }

      await chromaKnowledgeBase.storeInformation(
        request.data, 
        request.collection || 'knowledge_base'
      );

      logger.info('Knowledge item stored successfully', { id: request.data.id });
      return { success: true };
    } catch (error) {
      logger.error('Failed to store knowledge item', error);
      throw error;
    }
  });

  // Query ChromaDB with semantic search
  ipcMain.handle('knowledge:query', async (_event, request: KnowledgeQueryRequest): Promise<KnowledgeQueryResponse> => {
    try {
      logger.info('Querying knowledge base', { 
        query: request.query.substring(0, 100),
        collection: request.collection 
      });

      const startTime = Date.now();
      const results = await chromaKnowledgeBase.semanticSearch(
        request.query,
        request.collection || 'knowledge_base',
        request.limit || 10,
        request.threshold || 0.7
      );
      const processingTime = Date.now() - startTime;

      const response: KnowledgeQueryResponse = {
        results,
        totalCount: results.length,
        processingTime
      };

      logger.info('Knowledge query completed', { 
        resultsCount: results.length,
        processingTime 
      });

      return response;
    } catch (error) {
      logger.error('Knowledge query failed', error);
      throw error;
    }
  });

  // Generate embeddings (ensures ChromaDB storage)
  ipcMain.handle('ai:generate-embeddings', async (_event, request: AIGenerateEmbeddingsRequest): Promise<number[]> => {
    try {
      logger.info('Generating embeddings', { textLength: request.text.length });

      const embeddingRequest: any = { text: request.text };
      if (request.model) {
        embeddingRequest.model = request.model;
      }
      const response = await aiModelClient.generateEmbeddings(embeddingRequest);

      logger.info('Embeddings generated successfully', { 
        dimensions: response.dimensions,
        tokensUsed: response.tokensUsed 
      });

      return response.embedding;
    } catch (error) {
      logger.error('Failed to generate embeddings', error);
      throw error;
    }
  });

  // Get collection statistics
  ipcMain.handle('knowledge:stats', async (_event, collectionName: string) => {
    try {
      const stats = await chromaKnowledgeBase.getCollectionStats(collectionName);
      logger.info('Retrieved collection stats', stats);
      return stats;
    } catch (error) {
      logger.error('Failed to get collection stats', error);
      throw error;
    }
  });

  logger.info('Knowledge base IPC handlers registered');
}
import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create timeline sequence table for managing sequence numbers
  await knex.schema.createTable('timeline_sequence', table => {
    // Primary key
    table.integer('id').primary().notNullable().comment('Sequence ID (always 1)');

    // Sequence value
    table
      .bigInteger('next_value')
      .defaultTo(1)
      .notNullable()
      .comment('Next sequence number to use');

    // Metadata
    table
      .timestamp('created_at')
      .defaultTo(knex.fn.now())
      .notNullable()
      .comment('Creation timestamp');
    table
      .timestamp('updated_at')
      .defaultTo(knex.fn.now())
      .notNullable()
      .comment('Last update timestamp');

    // Constraints
    table.check('next_value > 0', [], 'next_value_positive');
    table.check('id = 1', [], 'single_row_constraint');
  });

  // Create trigger to update the updated_at timestamp
  await knex.raw(`
    CREATE TRIGGER update_timeline_sequence_timestamp
    AFTER UPDATE ON timeline_sequence
    FOR EACH ROW
    BEGIN
      UPDATE timeline_sequence SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END
  `);

  // Insert the initial sequence record
  await knex('timeline_sequence').insert({
    id: 1,
    next_value: 1,
  });

  // Create function to get next sequence number (SQLite doesn't have sequences)
  await knex.raw(`
    CREATE TRIGGER get_next_timeline_sequence
    BEFORE INSERT ON timeline
    FOR EACH ROW
    WHEN NEW.sequence_number IS NULL
    BEGIN
      UPDATE timeline_sequence SET next_value = next_value + 1 WHERE id = 1;
      UPDATE timeline SET sequence_number = (SELECT next_value - 1 FROM timeline_sequence WHERE id = 1) WHERE rowid = NEW.rowid;
    END
  `);
}

export async function down(knex: Knex): Promise<void> {
  // Drop triggers first
  await knex.raw('DROP TRIGGER IF EXISTS get_next_timeline_sequence');
  await knex.raw('DROP TRIGGER IF EXISTS update_timeline_sequence_timestamp');

  // Drop the table
  await knex.schema.dropTableIfExists('timeline_sequence');
}

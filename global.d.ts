// Global type definitions for AI Document Processor

declare global {
  // Electron Forge environment variables
  const MAIN_WINDOW_VITE_DEV_SERVER_URL: string;
  const MAIN_WINDOW_VITE_NAME: string;

  // Window API extensions
  interface Window {
    electronAPI: {
      // File operations
      openFile: () => Promise<string | null>;
      saveFile: (content: string, filePath?: string) => Promise<boolean>;

      // Document processing
      processDocument: (filePath: string) => Promise<{
        success: boolean;
        content?: string;
        metadata?: Record<string, unknown>;
        error?: string;
      }>;

      // AI operations (embeddings stored in ChromaDB)
      generateEmbeddings: (text: string) => Promise<number[]>;
      performReasoning: (context: string, query: string) => Promise<string>;

      // Knowledge base operations
      storeInformation: (data: Record<string, unknown>) => Promise<void>;
      queryInformation: (query: string) => Promise<Array<Record<string, unknown>>>;

      // Timeline operations
      createCheckpoint: (description: string) => Promise<string>;
      undo: () => Promise<void>;
      redo: () => Promise<void>;

      // System operations
      getAppVersion: () => Promise<string>;
      showMessageBox: (options: {
        type?: 'none' | 'info' | 'error' | 'question' | 'warning';
        buttons?: string[];
        defaultId?: number;
        title?: string;
        message: string;
        detail?: string;
      }) => Promise<{
        response: number;
        checkboxChecked?: boolean;
      }>;

      // Event listeners
      onDocumentProcessed: (callback: (data: Record<string, unknown>) => void) => void;
      onAIResponse: (callback: (response: string) => void) => void;
      removeAllListeners: (channel: string) => void;

      // Logging operations
      logToMain: (
        level: string,
        message: string,
        data?: Record<string, unknown>,
        stack?: string
      ) => Promise<void>;
    };
    electronEnv: {
      NODE_ENV: string;
      platform: string;
      arch: string;
      versions: NodeJS.ProcessVersions;
    };
  }

  // Module hot replacement for development
  interface Module {
    hot?: {
      accept(path?: string, callback?: () => void): void;
    };
  }

  // Environment variables
  namespace NodeJS {
    interface ProcessEnv {
      NODE_ENV: 'development' | 'production' | 'test';
      AZURE_AI_ENDPOINT?: string;
      AZURE_AI_KEY?: string;
      OPENAI_API_KEY?: string;
      DATABASE_PATH?: string;
      CHROMA_DB_PATH?: string; // ChromaDB vector database path
    }
  }
}

export {};

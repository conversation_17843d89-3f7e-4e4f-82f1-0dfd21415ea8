import { v4 as uuidv4 } from 'uuid';
import * as crypto from 'crypto';
import { ChangeType } from '../../shared/types/Timeline';
import { DocumentMetadata } from '../../shared/types/Document';

export interface StructuredDataDiffOptions {
  enableTableDiff?: boolean;
  enableJsonDiff?: boolean;
  enableXmlDiff?: boolean;
  enableFormDataDiff?: boolean;
  enableMetadataDiff?: boolean;
  ignoreOrder?: boolean;
  ignoreCase?: boolean;
  threshold?: number;
}

export interface TableDiff {
  id: string;
  type: 'table';
  beforeTable: TableData;
  afterTable: TableData;
  cellChanges: CellChange[];
  rowChanges: RowChange[];
  columnChanges: ColumnChange[];
  statistics: TableDiffStatistics;
}

export interface TableData {
  headers: string[];
  rows: string[][];
  metadata?: {
    name?: string;
    sheet?: string;
    range?: string;
  };
}

export interface CellChange {
  id: string;
  row: number;
  column: number;
  columnName: string;
  changeType: ChangeType;
  beforeValue?: string;
  afterValue?: string;
  confidence: number;
}

export interface RowChange {
  id: string;
  rowIndex: number;
  changeType: ChangeType;
  beforeRow?: string[];
  afterRow?: string[];
  confidence: number;
}

export interface ColumnChange {
  id: string;
  columnIndex: number;
  columnName: string;
  changeType: ChangeType;
  beforeColumn?: string[];
  afterColumn?: string[];
  confidence: number;
}

export interface TableDiffStatistics {
  totalCells: number;
  changedCells: number;
  addedRows: number;
  deletedRows: number;
  addedColumns: number;
  deletedColumns: number;
  similarity: number;
}

export interface JsonDiff {
  id: string;
  type: 'json';
  beforeJson: any;
  afterJson: any;
  changes: JsonChange[];
  statistics: JsonDiffStatistics;
}

export interface JsonChange {
  id: string;
  path: string;
  changeType: ChangeType;
  beforeValue?: any;
  afterValue?: any;
  valueType: string;
  confidence: number;
}

export interface JsonDiffStatistics {
  totalProperties: number;
  changedProperties: number;
  addedProperties: number;
  deletedProperties: number;
  similarity: number;
}

export interface XmlDiff {
  id: string;
  type: 'xml';
  beforeXml: string;
  afterXml: string;
  changes: XmlChange[];
  statistics: XmlDiffStatistics;
}

export interface XmlChange {
  id: string;
  xpath: string;
  changeType: ChangeType;
  elementType: 'element' | 'attribute' | 'text';
  beforeValue?: string;
  afterValue?: string;
  confidence: number;
}

export interface XmlDiffStatistics {
  totalElements: number;
  changedElements: number;
  addedElements: number;
  deletedElements: number;
  similarity: number;
}

export interface FormDataDiff {
  id: string;
  type: 'form';
  beforeFormData: Record<string, any>;
  afterFormData: Record<string, any>;
  fieldChanges: FormFieldChange[];
  statistics: FormDataDiffStatistics;
}

export interface FormFieldChange {
  id: string;
  fieldName: string;
  fieldType: string;
  changeType: ChangeType;
  beforeValue?: any;
  afterValue?: any;
  validation?: {
    beforeValid: boolean;
    afterValid: boolean;
    errors: string[];
  };
  confidence: number;
}

export interface FormDataDiffStatistics {
  totalFields: number;
  changedFields: number;
  addedFields: number;
  deletedFields: number;
  validationChanges: number;
  similarity: number;
}

export interface MetadataDiff {
  id: string;
  type: 'metadata';
  beforeMetadata: DocumentMetadata;
  afterMetadata: DocumentMetadata;
  changes: MetadataChange[];
  statistics: MetadataDiffStatistics;
}

export interface MetadataChange {
  id: string;
  property: string;
  changeType: ChangeType;
  beforeValue?: any;
  afterValue?: any;
  confidence: number;
}

export interface MetadataDiffStatistics {
  totalProperties: number;
  changedProperties: number;
  addedProperties: number;
  deletedProperties: number;
  similarity: number;
}

export class StructuredDataDiffEngine {
  private cache: Map<string, any>;

  constructor(_options: StructuredDataDiffOptions = {}) {
    this.cache = new Map();
  }

  /**
   * Create table diff with cell-level change detection
   */
  public createTableDiff(beforeTable: TableData, afterTable: TableData): TableDiff {
    const cacheKey = this.generateCacheKey(beforeTable, afterTable, 'table');
    const cached = this.cache.get(cacheKey);
    if (cached) return cached;

    const cellChanges: CellChange[] = [];
    const rowChanges: RowChange[] = [];
    const columnChanges: ColumnChange[] = [];

    // Compare headers (columns)
    this.compareColumns(beforeTable, afterTable, columnChanges);

    // Compare rows
    this.compareRows(beforeTable, afterTable, rowChanges);

    // Compare individual cells
    this.compareCells(beforeTable, afterTable, cellChanges);

    const statistics = this.calculateTableStatistics(
      beforeTable,
      afterTable,
      cellChanges,
      rowChanges,
      columnChanges
    );

    const result: TableDiff = {
      id: uuidv4(),
      type: 'table',
      beforeTable,
      afterTable,
      cellChanges,
      rowChanges,
      columnChanges,
      statistics,
    };

    this.cache.set(cacheKey, result);
    return result;
  }

  /**
   * Create JSON diff with hierarchical change visualization
   */
  public createJsonDiff(beforeJson: any, afterJson: any): JsonDiff {
    const cacheKey = this.generateCacheKey(beforeJson, afterJson, 'json');
    const cached = this.cache.get(cacheKey);
    if (cached) return cached;

    const changes: JsonChange[] = [];
    this.compareJsonObjects(beforeJson, afterJson, '', changes);

    const statistics = this.calculateJsonStatistics(beforeJson, afterJson, changes);

    const result: JsonDiff = {
      id: uuidv4(),
      type: 'json',
      beforeJson,
      afterJson,
      changes,
      statistics,
    };

    this.cache.set(cacheKey, result);
    return result;
  }

  /**
   * Create XML diff with hierarchical change visualization
   */
  public createXmlDiff(beforeXml: string, afterXml: string): XmlDiff {
    const cacheKey = this.generateCacheKey(beforeXml, afterXml, 'xml');
    const cached = this.cache.get(cacheKey);
    if (cached) return cached;

    const changes: XmlChange[] = [];

    try {
      // Parse XML and compare structure
      const beforeDoc = this.parseXml(beforeXml);
      const afterDoc = this.parseXml(afterXml);

      this.compareXmlElements(beforeDoc, afterDoc, '', changes);
    } catch (error) {
      console.error('Error parsing XML for diff:', error);
    }

    const statistics = this.calculateXmlStatistics(beforeXml, afterXml, changes);

    const result: XmlDiff = {
      id: uuidv4(),
      type: 'xml',
      beforeXml,
      afterXml,
      changes,
      statistics,
    };

    this.cache.set(cacheKey, result);
    return result;
  }

  /**
   * Create form data diff with field-level comparison
   */
  public createFormDataDiff(
    beforeFormData: Record<string, any>,
    afterFormData: Record<string, any>
  ): FormDataDiff {
    const cacheKey = this.generateCacheKey(beforeFormData, afterFormData, 'form');
    const cached = this.cache.get(cacheKey);
    if (cached) return cached;

    const fieldChanges: FormFieldChange[] = [];

    // Get all field names from both forms
    const allFieldNames = new Set([...Object.keys(beforeFormData), ...Object.keys(afterFormData)]);

    for (const fieldName of allFieldNames) {
      const beforeValue = beforeFormData[fieldName];
      const afterValue = afterFormData[fieldName];

      if (beforeValue === undefined && afterValue !== undefined) {
        fieldChanges.push({
          id: uuidv4(),
          fieldName,
          fieldType: typeof afterValue,
          changeType: ChangeType.ADDED,
          afterValue,
          confidence: 1.0,
        });
      } else if (beforeValue !== undefined && afterValue === undefined) {
        fieldChanges.push({
          id: uuidv4(),
          fieldName,
          fieldType: typeof beforeValue,
          changeType: ChangeType.DELETED,
          beforeValue,
          confidence: 1.0,
        });
      } else if (beforeValue !== afterValue) {
        fieldChanges.push({
          id: uuidv4(),
          fieldName,
          fieldType: typeof afterValue,
          changeType: ChangeType.MODIFIED,
          beforeValue,
          afterValue,
          confidence: 1.0,
        });
      }
    }

    const statistics = this.calculateFormDataStatistics(
      beforeFormData,
      afterFormData,
      fieldChanges
    );

    const result: FormDataDiff = {
      id: uuidv4(),
      type: 'form',
      beforeFormData,
      afterFormData,
      fieldChanges,
      statistics,
    };

    this.cache.set(cacheKey, result);
    return result;
  }

  /**
   * Create metadata diff for document properties
   */
  public createMetadataDiff(
    beforeMetadata: DocumentMetadata,
    afterMetadata: DocumentMetadata
  ): MetadataDiff {
    const cacheKey = this.generateCacheKey(beforeMetadata, afterMetadata, 'metadata');
    const cached = this.cache.get(cacheKey);
    if (cached) return cached;

    const changes: MetadataChange[] = [];

    // Compare all metadata properties
    const allProperties = new Set([...Object.keys(beforeMetadata), ...Object.keys(afterMetadata)]);

    for (const property of allProperties) {
      const beforeValue = (beforeMetadata as any)[property];
      const afterValue = (afterMetadata as any)[property];

      if (beforeValue === undefined && afterValue !== undefined) {
        changes.push({
          id: uuidv4(),
          property,
          changeType: ChangeType.ADDED,
          afterValue,
          confidence: 1.0,
        });
      } else if (beforeValue !== undefined && afterValue === undefined) {
        changes.push({
          id: uuidv4(),
          property,
          changeType: ChangeType.DELETED,
          beforeValue,
          confidence: 1.0,
        });
      } else if (!this.deepEqual(beforeValue, afterValue)) {
        changes.push({
          id: uuidv4(),
          property,
          changeType: ChangeType.MODIFIED,
          beforeValue,
          afterValue,
          confidence: 1.0,
        });
      }
    }

    const statistics = this.calculateMetadataStatistics(beforeMetadata, afterMetadata, changes);

    const result: MetadataDiff = {
      id: uuidv4(),
      type: 'metadata',
      beforeMetadata,
      afterMetadata,
      changes,
      statistics,
    };

    this.cache.set(cacheKey, result);
    return result;
  }

  /**
   * Compare columns between two tables
   */
  private compareColumns(
    beforeTable: TableData,
    afterTable: TableData,
    columnChanges: ColumnChange[]
  ): void {
    const beforeHeaders = beforeTable.headers;
    const afterHeaders = afterTable.headers;

    // Find added columns
    for (let i = 0; i < afterHeaders.length; i++) {
      const header = afterHeaders[i];
      if (header && !beforeHeaders.includes(header)) {
        columnChanges.push({
          id: uuidv4(),
          columnIndex: i,
          columnName: header,
          changeType: ChangeType.ADDED,
          afterColumn: afterTable.rows.map(row => row[i] || ''),
          confidence: 1.0,
        });
      }
    }

    // Find deleted columns
    for (let i = 0; i < beforeHeaders.length; i++) {
      const header = beforeHeaders[i];
      if (header && !afterHeaders.includes(header)) {
        columnChanges.push({
          id: uuidv4(),
          columnIndex: i,
          columnName: header,
          changeType: ChangeType.DELETED,
          beforeColumn: beforeTable.rows.map(row => row[i] || ''),
          confidence: 1.0,
        });
      }
    }
  }

  /**
   * Compare rows between two tables
   */
  private compareRows(
    beforeTable: TableData,
    afterTable: TableData,
    rowChanges: RowChange[]
  ): void {
    const maxRows = Math.max(beforeTable.rows.length, afterTable.rows.length);

    for (let i = 0; i < maxRows; i++) {
      const beforeRow = beforeTable.rows[i];
      const afterRow = afterTable.rows[i];

      if (!beforeRow && afterRow) {
        rowChanges.push({
          id: uuidv4(),
          rowIndex: i,
          changeType: ChangeType.ADDED,
          afterRow,
          confidence: 1.0,
        });
      } else if (beforeRow && !afterRow) {
        rowChanges.push({
          id: uuidv4(),
          rowIndex: i,
          changeType: ChangeType.DELETED,
          beforeRow,
          confidence: 1.0,
        });
      } else if (beforeRow && afterRow && !this.arraysEqual(beforeRow, afterRow)) {
        rowChanges.push({
          id: uuidv4(),
          rowIndex: i,
          changeType: ChangeType.MODIFIED,
          beforeRow,
          afterRow,
          confidence: 1.0,
        });
      }
    }
  }

  /**
   * Compare individual cells between two tables
   */
  private compareCells(
    beforeTable: TableData,
    afterTable: TableData,
    cellChanges: CellChange[]
  ): void {
    const maxRows = Math.max(beforeTable.rows.length, afterTable.rows.length);
    const maxCols = Math.max(beforeTable.headers.length, afterTable.headers.length);

    for (let row = 0; row < maxRows; row++) {
      for (let col = 0; col < maxCols; col++) {
        const beforeValue = beforeTable.rows[row]?.[col];
        const afterValue = afterTable.rows[row]?.[col];
        const columnName = afterTable.headers[col] || beforeTable.headers[col] || `Column ${col}`;

        if (beforeValue !== afterValue) {
          let changeType: ChangeType;
          if (beforeValue === undefined) {
            changeType = ChangeType.ADDED;
          } else if (afterValue === undefined) {
            changeType = ChangeType.DELETED;
          } else {
            changeType = ChangeType.MODIFIED;
          }

          cellChanges.push({
            id: uuidv4(),
            row,
            column: col,
            columnName,
            changeType,
            beforeValue: beforeValue || undefined,
            afterValue: afterValue || undefined,
            confidence: 1.0,
          });
        }
      }
    }
  }

  /**
   * Compare JSON objects recursively
   */
  private compareJsonObjects(
    beforeObj: any,
    afterObj: any,
    path: string,
    changes: JsonChange[]
  ): void {
    if (typeof beforeObj !== typeof afterObj) {
      changes.push({
        id: uuidv4(),
        path,
        changeType: ChangeType.MODIFIED,
        beforeValue: beforeObj,
        afterValue: afterObj,
        valueType: typeof afterObj,
        confidence: 1.0,
      });
      return;
    }

    if (beforeObj === null || afterObj === null) {
      if (beforeObj !== afterObj) {
        changes.push({
          id: uuidv4(),
          path,
          changeType: ChangeType.MODIFIED,
          beforeValue: beforeObj,
          afterValue: afterObj,
          valueType: typeof afterObj,
          confidence: 1.0,
        });
      }
      return;
    }

    if (typeof beforeObj !== 'object') {
      if (beforeObj !== afterObj) {
        changes.push({
          id: uuidv4(),
          path,
          changeType: ChangeType.MODIFIED,
          beforeValue: beforeObj,
          afterValue: afterObj,
          valueType: typeof afterObj,
          confidence: 1.0,
        });
      }
      return;
    }

    if (Array.isArray(beforeObj) && Array.isArray(afterObj)) {
      this.compareJsonArrays(beforeObj, afterObj, path, changes);
    } else if (!Array.isArray(beforeObj) && !Array.isArray(afterObj)) {
      this.compareJsonObjectProperties(beforeObj, afterObj, path, changes);
    } else {
      changes.push({
        id: uuidv4(),
        path,
        changeType: ChangeType.MODIFIED,
        beforeValue: beforeObj,
        afterValue: afterObj,
        valueType: Array.isArray(afterObj) ? 'array' : 'object',
        confidence: 1.0,
      });
    }
  }

  /**
   * Compare JSON arrays
   */
  private compareJsonArrays(
    beforeArray: any[],
    afterArray: any[],
    path: string,
    changes: JsonChange[]
  ): void {
    const maxLength = Math.max(beforeArray.length, afterArray.length);

    for (let i = 0; i < maxLength; i++) {
      const beforeItem = beforeArray[i];
      const afterItem = afterArray[i];
      const itemPath = `${path}[${i}]`;

      if (beforeItem === undefined && afterItem !== undefined) {
        changes.push({
          id: uuidv4(),
          path: itemPath,
          changeType: ChangeType.ADDED,
          afterValue: afterItem,
          valueType: typeof afterItem,
          confidence: 1.0,
        });
      } else if (beforeItem !== undefined && afterItem === undefined) {
        changes.push({
          id: uuidv4(),
          path: itemPath,
          changeType: ChangeType.DELETED,
          beforeValue: beforeItem,
          valueType: typeof beforeItem,
          confidence: 1.0,
        });
      } else if (beforeItem !== undefined && afterItem !== undefined) {
        this.compareJsonObjects(beforeItem, afterItem, itemPath, changes);
      }
    }
  }

  /**
   * Compare JSON object properties
   */
  private compareJsonObjectProperties(
    beforeObj: any,
    afterObj: any,
    path: string,
    changes: JsonChange[]
  ): void {
    const allKeys = new Set([...Object.keys(beforeObj), ...Object.keys(afterObj)]);

    for (const key of allKeys) {
      const beforeValue = beforeObj[key];
      const afterValue = afterObj[key];
      const keyPath = path ? `${path}.${key}` : key;

      if (beforeValue === undefined && afterValue !== undefined) {
        changes.push({
          id: uuidv4(),
          path: keyPath,
          changeType: ChangeType.ADDED,
          afterValue,
          valueType: typeof afterValue,
          confidence: 1.0,
        });
      } else if (beforeValue !== undefined && afterValue === undefined) {
        changes.push({
          id: uuidv4(),
          path: keyPath,
          changeType: ChangeType.DELETED,
          beforeValue,
          valueType: typeof beforeValue,
          confidence: 1.0,
        });
      } else if (beforeValue !== undefined && afterValue !== undefined) {
        this.compareJsonObjects(beforeValue, afterValue, keyPath, changes);
      }
    }
  }

  /**
   * Parse XML string (simplified implementation)
   */
  private parseXml(xmlString: string): any {
    // This is a simplified XML parser for demonstration
    // In a real implementation, you'd use a proper XML parser like xml2js
    try {
      return { content: xmlString }; // Placeholder
    } catch (error) {
      throw new Error(`Failed to parse XML: ${error}`);
    }
  }

  /**
   * Compare XML elements
   */
  private compareXmlElements(
    beforeDoc: any,
    afterDoc: any,
    xpath: string,
    changes: XmlChange[]
  ): void {
    // Simplified XML comparison - in a real implementation, you'd traverse the XML tree
    if (beforeDoc.content !== afterDoc.content) {
      changes.push({
        id: uuidv4(),
        xpath: xpath || '/',
        changeType: ChangeType.MODIFIED,
        elementType: 'text',
        beforeValue: beforeDoc.content,
        afterValue: afterDoc.content,
        confidence: 1.0,
      });
    }
  }

  /**
   * Calculate table diff statistics
   */
  private calculateTableStatistics(
    beforeTable: TableData,
    afterTable: TableData,
    cellChanges: CellChange[],
    rowChanges: RowChange[],
    columnChanges: ColumnChange[]
  ): TableDiffStatistics {
    const totalCells = Math.max(
      beforeTable.rows.length * beforeTable.headers.length,
      afterTable.rows.length * afterTable.headers.length
    );
    const changedCells = cellChanges.length;
    const addedRows = rowChanges.filter(r => r.changeType === ChangeType.ADDED).length;
    const deletedRows = rowChanges.filter(r => r.changeType === ChangeType.DELETED).length;
    const addedColumns = columnChanges.filter(c => c.changeType === ChangeType.ADDED).length;
    const deletedColumns = columnChanges.filter(c => c.changeType === ChangeType.DELETED).length;
    const similarity = totalCells > 0 ? Math.max(0, 1 - changedCells / totalCells) : 1.0;

    return {
      totalCells,
      changedCells,
      addedRows,
      deletedRows,
      addedColumns,
      deletedColumns,
      similarity,
    };
  }

  /**
   * Calculate JSON diff statistics
   */
  private calculateJsonStatistics(
    beforeJson: any,
    afterJson: any,
    changes: JsonChange[]
  ): JsonDiffStatistics {
    const beforeProps = this.countJsonProperties(beforeJson);
    const afterProps = this.countJsonProperties(afterJson);
    const totalProperties = Math.max(beforeProps, afterProps);
    const changedProperties = changes.filter(c => c.changeType === ChangeType.MODIFIED).length;
    const addedProperties = changes.filter(c => c.changeType === ChangeType.ADDED).length;
    const deletedProperties = changes.filter(c => c.changeType === ChangeType.DELETED).length;
    const similarity =
      totalProperties > 0 ? Math.max(0, 1 - changes.length / totalProperties) : 1.0;

    return {
      totalProperties,
      changedProperties,
      addedProperties,
      deletedProperties,
      similarity,
    };
  }

  /**
   * Calculate XML diff statistics
   */
  private calculateXmlStatistics(
    beforeXml: string,
    afterXml: string,
    changes: XmlChange[]
  ): XmlDiffStatistics {
    // Simplified calculation - in a real implementation, you'd count actual XML elements
    const totalElements = Math.max(beforeXml.length, afterXml.length) / 100; // Rough estimate
    const changedElements = changes.filter(c => c.changeType === ChangeType.MODIFIED).length;
    const addedElements = changes.filter(c => c.changeType === ChangeType.ADDED).length;
    const deletedElements = changes.filter(c => c.changeType === ChangeType.DELETED).length;
    const similarity = totalElements > 0 ? Math.max(0, 1 - changes.length / totalElements) : 1.0;

    return {
      totalElements,
      changedElements,
      addedElements,
      deletedElements,
      similarity,
    };
  }

  /**
   * Calculate form data diff statistics
   */
  private calculateFormDataStatistics(
    beforeFormData: Record<string, any>,
    afterFormData: Record<string, any>,
    fieldChanges: FormFieldChange[]
  ): FormDataDiffStatistics {
    const beforeFields = Object.keys(beforeFormData).length;
    const afterFields = Object.keys(afterFormData).length;
    const totalFields = Math.max(beforeFields, afterFields);
    const changedFields = fieldChanges.filter(f => f.changeType === ChangeType.MODIFIED).length;
    const addedFields = fieldChanges.filter(f => f.changeType === ChangeType.ADDED).length;
    const deletedFields = fieldChanges.filter(f => f.changeType === ChangeType.DELETED).length;
    const validationChanges = fieldChanges.filter(f => f.validation).length;
    const similarity = totalFields > 0 ? Math.max(0, 1 - fieldChanges.length / totalFields) : 1.0;

    return {
      totalFields,
      changedFields,
      addedFields,
      deletedFields,
      validationChanges,
      similarity,
    };
  }

  /**
   * Calculate metadata diff statistics
   */
  private calculateMetadataStatistics(
    beforeMetadata: DocumentMetadata,
    afterMetadata: DocumentMetadata,
    changes: MetadataChange[]
  ): MetadataDiffStatistics {
    const beforeProps = Object.keys(beforeMetadata).length;
    const afterProps = Object.keys(afterMetadata).length;
    const totalProperties = Math.max(beforeProps, afterProps);
    const changedProperties = changes.filter(c => c.changeType === ChangeType.MODIFIED).length;
    const addedProperties = changes.filter(c => c.changeType === ChangeType.ADDED).length;
    const deletedProperties = changes.filter(c => c.changeType === ChangeType.DELETED).length;
    const similarity =
      totalProperties > 0 ? Math.max(0, 1 - changes.length / totalProperties) : 1.0;

    return {
      totalProperties,
      changedProperties,
      addedProperties,
      deletedProperties,
      similarity,
    };
  }

  /**
   * Count JSON properties recursively
   */
  private countJsonProperties(obj: any): number {
    if (obj === null || typeof obj !== 'object') {
      return 1;
    }

    if (Array.isArray(obj)) {
      return obj.reduce((count, item) => count + this.countJsonProperties(item), 0);
    }

    return Object.keys(obj).reduce((count, key) => count + this.countJsonProperties(obj[key]), 0);
  }

  /**
   * Deep equality check
   */
  private deepEqual(obj1: any, obj2: any): boolean {
    if (obj1 === obj2) return true;
    if (obj1 == null || obj2 == null) return obj1 === obj2;
    if (typeof obj1 !== typeof obj2) return false;

    if (typeof obj1 !== 'object') return obj1 === obj2;

    if (Array.isArray(obj1) !== Array.isArray(obj2)) return false;

    if (Array.isArray(obj1)) {
      if (obj1.length !== obj2.length) return false;
      for (let i = 0; i < obj1.length; i++) {
        if (!this.deepEqual(obj1[i], obj2[i])) return false;
      }
      return true;
    }

    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    if (keys1.length !== keys2.length) return false;

    for (const key of keys1) {
      if (!keys2.includes(key)) return false;
      if (!this.deepEqual(obj1[key], obj2[key])) return false;
    }

    return true;
  }

  /**
   * Check if two arrays are equal
   */
  private arraysEqual(arr1: any[], arr2: any[]): boolean {
    if (arr1.length !== arr2.length) return false;
    for (let i = 0; i < arr1.length; i++) {
      if (arr1[i] !== arr2[i]) return false;
    }
    return true;
  }

  /**
   * Generate cache key
   */
  private generateCacheKey(before: any, after: any, type: string): string {
    const content = `${JSON.stringify(before)}|${JSON.stringify(after)}|${type}`;
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  /**
   * Clear cache
   */
  public clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

// This script must be loaded first to ensure global is available
// It prevents "global is not defined" errors in webpack bundles
(function() {
  'use strict';

  // Define global immediately before any other code runs
  if (typeof global === 'undefined') {
    if (typeof globalThis !== 'undefined') {
      globalThis.global = globalThis;
    } else if (typeof window !== 'undefined') {
      window.global = window;
    } else if (typeof self !== 'undefined') {
      self.global = self;
    }
  }

  // Ensure it's also available on window for webpack compatibility
  if (typeof window !== 'undefined') {
    if (typeof window.global === 'undefined') {
      window.global = globalThis || window;
    }

    // Also ensure process is available for webpack polyfills
    if (typeof window.process === 'undefined') {
      window.process = { env: {} };
    }
  }

  // Debug log to confirm polyfill loaded
  console.log('Global polyfill loaded, global is now:', typeof global);
})();

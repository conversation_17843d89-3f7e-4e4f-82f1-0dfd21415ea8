import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create templates table for form templates and coordinate mappings
  await knex.schema.createTable('templates', table => {
    // Primary key
    table.string('id', 36).primary().notNullable().comment('UUID primary key');

    // Template identification
    table.string('name', 255).notNullable().comment('Template name');
    table
      .string('document_type', 100)
      .notNullable()
      .comment('Type of document this template applies to');
    table.text('description').comment('Description of the template');
    table.string('version', 50).defaultTo('1.0').notNullable().comment('Template version');

    // Template configuration
    table.json('field_mappings').notNullable().comment('Field mappings configuration');
    table.json('coordinate_mappings').notNullable().comment('Coordinate mappings for form fields');
    table.json('variables').comment('Template variables and their configurations');
    table.json('validation_rules').comment('Validation rules for template fields');

    // Template metadata
    table.json('metadata').comment('Additional template metadata');
    table.json('tags').comment('Array of tags for categorization');
    table.string('category', 100).comment('Template category (tax, legal, financial, etc.)');
    table.string('subcategory', 100).comment('Template subcategory');

    // Usage and performance tracking
    table
      .integer('usage_count')
      .defaultTo(0)
      .notNullable()
      .comment('Number of times template has been used');
    table.timestamp('last_used').comment('When template was last used');
    table.decimal('success_rate', 5, 4).comment('Success rate of template usage (0-1)');
    table.json('usage_statistics').comment('Detailed usage statistics');

    // Template source and creation
    table.string('created_by', 100).comment('User who created the template');
    table.string('source_document_id', 36).comment('Source document used to create template');
    table.foreign('source_document_id').references('id').inTable('documents').onDelete('SET NULL');
    table.json('creation_metadata').comment('Metadata from template creation process');

    // Temporal information
    table
      .timestamp('created_at')
      .defaultTo(knex.fn.now())
      .notNullable()
      .comment('Template creation timestamp');
    table
      .timestamp('updated_at')
      .defaultTo(knex.fn.now())
      .notNullable()
      .comment('Last update timestamp');
    table.boolean('is_active').defaultTo(true).notNullable().comment('Whether template is active');
    table.timestamp('deactivated_at').comment('When template was deactivated');
    table.string('deactivated_by', 100).comment('Who deactivated the template');

    // Versioning and relationships
    table.string('parent_template_id', 36).comment('Parent template if this is a version');
    table.foreign('parent_template_id').references('id').inTable('templates').onDelete('SET NULL');
    table.json('child_templates').comment('Array of child template IDs');
    table
      .boolean('is_master_template')
      .defaultTo(false)
      .notNullable()
      .comment('Whether this is a master template');

    // Quality and validation
    table.decimal('accuracy_score', 5, 4).comment('Template accuracy score (0-1)');
    table.json('quality_metrics').comment('Detailed quality metrics');
    table
      .boolean('is_validated')
      .defaultTo(false)
      .notNullable()
      .comment('Whether template has been validated');
    table.timestamp('validated_at').comment('When template was validated');
    table.string('validated_by', 100).comment('Who validated the template');
    table.text('validation_notes').comment('Notes from validation process');

    // Configuration and settings
    table.json('processing_options').comment('Processing options for this template');
    table.json('ai_settings').comment('AI-specific settings for template processing');
    table
      .boolean('auto_apply')
      .defaultTo(false)
      .notNullable()
      .comment('Whether to auto-apply this template');
    table
      .decimal('confidence_threshold', 5, 4)
      .defaultTo(0.8)
      .comment('Minimum confidence for auto-application');

    // Compatibility and requirements
    table.json('compatible_document_types').comment('Array of compatible document types');
    table.json('required_fields').comment('Array of required field names');
    table.json('optional_fields').comment('Array of optional field names');
    table.string('min_app_version', 50).comment('Minimum app version required');
    table.json('dependencies').comment('Template dependencies');

    // Constraints
    table.unique(['name', 'document_type'], 'unique_template_name_type');
    table.check('usage_count >= 0', [], 'usage_count_positive');
    table.check(
      'success_rate IS NULL OR (success_rate >= 0 AND success_rate <= 1)',
      [],
      'success_rate_range'
    );
    table.check(
      'accuracy_score IS NULL OR (accuracy_score >= 0 AND accuracy_score <= 1)',
      [],
      'accuracy_score_range'
    );
    table.check(
      'confidence_threshold >= 0 AND confidence_threshold <= 1',
      [],
      'confidence_threshold_range'
    );
    table.check(
      "document_type IN ('pdf', 'excel', 'word', 'csv', 'image', 'form', 'other')",
      [],
      'valid_document_type'
    );

    // JSON validation
    table.check('json_valid(field_mappings)', [], 'valid_field_mappings');
    table.check('json_valid(coordinate_mappings)', [], 'valid_coordinate_mappings');
    table.check('json_valid(variables) OR variables IS NULL', [], 'valid_variables');
    table.check(
      'json_valid(validation_rules) OR validation_rules IS NULL',
      [],
      'valid_validation_rules'
    );
    table.check('json_valid(metadata) OR metadata IS NULL', [], 'valid_metadata');
    table.check('json_valid(tags) OR tags IS NULL', [], 'valid_tags');
    table.check(
      'json_valid(usage_statistics) OR usage_statistics IS NULL',
      [],
      'valid_usage_statistics'
    );
    table.check(
      'json_valid(creation_metadata) OR creation_metadata IS NULL',
      [],
      'valid_creation_metadata'
    );
    table.check(
      'json_valid(child_templates) OR child_templates IS NULL',
      [],
      'valid_child_templates'
    );
    table.check(
      'json_valid(quality_metrics) OR quality_metrics IS NULL',
      [],
      'valid_quality_metrics'
    );
    table.check(
      'json_valid(processing_options) OR processing_options IS NULL',
      [],
      'valid_processing_options'
    );
    table.check('json_valid(ai_settings) OR ai_settings IS NULL', [], 'valid_ai_settings');
    table.check(
      'json_valid(compatible_document_types) OR compatible_document_types IS NULL',
      [],
      'valid_compatible_types'
    );
    table.check(
      'json_valid(required_fields) OR required_fields IS NULL',
      [],
      'valid_required_fields'
    );
    table.check(
      'json_valid(optional_fields) OR optional_fields IS NULL',
      [],
      'valid_optional_fields'
    );
    table.check('json_valid(dependencies) OR dependencies IS NULL', [], 'valid_dependencies');

    // Indexes for performance
    table.index(['document_type'], 'idx_templates_document_type');
    table.index(['last_used'], 'idx_templates_last_used');
    table.index(['name'], 'idx_templates_name');
    table.index(['created_at'], 'idx_templates_created_at');
    table.index(['updated_at'], 'idx_templates_updated_at');
    table.index(['is_active'], 'idx_templates_is_active');
    table.index(['category'], 'idx_templates_category');
    table.index(['subcategory'], 'idx_templates_subcategory');
    table.index(['usage_count'], 'idx_templates_usage_count');
    table.index(['success_rate'], 'idx_templates_success_rate');
    table.index(['accuracy_score'], 'idx_templates_accuracy_score');
    table.index(['is_validated'], 'idx_templates_is_validated');
    table.index(['is_master_template'], 'idx_templates_is_master');
    table.index(['auto_apply'], 'idx_templates_auto_apply');
    table.index(['created_by'], 'idx_templates_created_by');
    table.index(['parent_template_id'], 'idx_templates_parent');
    table.index(['source_document_id'], 'idx_templates_source_document');

    // Composite indexes for common queries
    table.index(['document_type', 'is_active'], 'idx_templates_type_active');
    table.index(['category', 'subcategory'], 'idx_templates_category_sub');
    table.index(['is_active', 'auto_apply'], 'idx_templates_active_auto');
    table.index(['usage_count', 'last_used'], 'idx_templates_usage_last_used');
    table.index(['success_rate', 'accuracy_score'], 'idx_templates_success_accuracy');
    table.index(['document_type', 'confidence_threshold'], 'idx_templates_type_confidence');
    table.index(['created_at', 'document_type'], 'idx_templates_created_type');
    table.index(['is_validated', 'is_active'], 'idx_templates_validated_active');
  });

  // Create full-text search virtual table for templates
  await knex.raw(`
    CREATE VIRTUAL TABLE IF NOT EXISTS templates_fts USING fts5(
      id UNINDEXED,
      name,
      description,
      document_type,
      category,
      subcategory,
      tags,
      metadata,
      content='templates',
      content_rowid='rowid'
    )
  `);

  // Create triggers to keep FTS table in sync
  await knex.raw(`
    CREATE TRIGGER templates_fts_insert AFTER INSERT ON templates BEGIN
      INSERT INTO templates_fts(id, name, description, document_type, category, subcategory, tags, metadata)
      VALUES (new.id, new.name, new.description, new.document_type, new.category, new.subcategory, new.tags, new.metadata);
    END
  `);

  await knex.raw(`
    CREATE TRIGGER templates_fts_delete AFTER DELETE ON templates BEGIN
      DELETE FROM templates_fts WHERE id = old.id;
    END
  `);

  await knex.raw(`
    CREATE TRIGGER templates_fts_update AFTER UPDATE ON templates BEGIN
      DELETE FROM templates_fts WHERE id = old.id;
      INSERT INTO templates_fts(id, name, description, document_type, category, subcategory, tags, metadata)
      VALUES (new.id, new.name, new.description, new.document_type, new.category, new.subcategory, new.tags, new.metadata);
    END
  `);

  // Create updated_at trigger
  await knex.raw(`
    CREATE TRIGGER update_templates_updated_at
    AFTER UPDATE ON templates
    FOR EACH ROW
    BEGIN
      UPDATE templates SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END
  `);

  // Create trigger to update last_used when usage_count increases
  await knex.raw(`
    CREATE TRIGGER update_template_last_used
    AFTER UPDATE OF usage_count ON templates
    FOR EACH ROW
    WHEN NEW.usage_count > OLD.usage_count
    BEGIN
      UPDATE templates SET last_used = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END
  `);

  // Create view for active templates
  await knex.raw(`
    CREATE VIEW active_templates AS
    SELECT *
    FROM templates
    WHERE is_active = 1
    ORDER BY usage_count DESC, last_used DESC
  `);

  // Create view for auto-applicable templates
  await knex.raw(`
    CREATE VIEW auto_apply_templates AS
    SELECT
      t.*,
      d.name as source_document_name
    FROM templates t
    LEFT JOIN documents d ON t.source_document_id = d.id
    WHERE t.is_active = 1
      AND t.auto_apply = 1
      AND t.is_validated = 1
    ORDER BY t.confidence_threshold DESC, t.success_rate DESC
  `);

  // Create view for template performance metrics
  await knex.raw(`
    CREATE VIEW template_performance AS
    SELECT
      id,
      name,
      document_type,
      category,
      usage_count,
      success_rate,
      accuracy_score,
      last_used,
      CASE
        WHEN usage_count = 0 THEN 'Unused'
        WHEN success_rate IS NULL THEN 'No Data'
        WHEN success_rate >= 0.9 THEN 'Excellent'
        WHEN success_rate >= 0.7 THEN 'Good'
        WHEN success_rate >= 0.5 THEN 'Fair'
        ELSE 'Poor'
      END as performance_rating,
      CASE
        WHEN last_used IS NULL THEN 'Never Used'
        WHEN last_used >= datetime('now', '-7 days') THEN 'Recent'
        WHEN last_used >= datetime('now', '-30 days') THEN 'Active'
        WHEN last_used >= datetime('now', '-90 days') THEN 'Inactive'
        ELSE 'Stale'
      END as usage_status,
      created_at,
      updated_at
    FROM templates
    WHERE is_active = 1
  `);

  // Create view for template statistics by document type
  await knex.raw(`
    CREATE VIEW template_type_stats AS
    SELECT
      document_type,
      category,
      COUNT(*) as total_templates,
      COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_templates,
      COUNT(CASE WHEN auto_apply = 1 THEN 1 END) as auto_apply_templates,
      COUNT(CASE WHEN is_validated = 1 THEN 1 END) as validated_templates,
      SUM(usage_count) as total_usage,
      AVG(success_rate) as avg_success_rate,
      AVG(accuracy_score) as avg_accuracy_score,
      MAX(last_used) as last_category_usage,
      COUNT(CASE WHEN is_master_template = 1 THEN 1 END) as master_templates
    FROM templates
    GROUP BY document_type, category
    ORDER BY total_usage DESC
  `);

  // Create view for template relationships (parent-child)
  await knex.raw(`
    CREATE VIEW template_relationships AS
    SELECT
      p.id as parent_id,
      p.name as parent_name,
      p.version as parent_version,
      c.id as child_id,
      c.name as child_name,
      c.version as child_version,
      c.created_at as child_created_at,
      p.usage_count as parent_usage,
      c.usage_count as child_usage
    FROM templates p
    JOIN templates c ON c.parent_template_id = p.id
    WHERE p.is_active = 1
  `);

  // Create view for templates requiring attention
  await knex.raw(`
    CREATE VIEW templates_requiring_attention AS
    SELECT
      *,
      CASE
        WHEN is_validated = 0 THEN 'Needs Validation'
        WHEN success_rate IS NOT NULL AND success_rate < 0.5 THEN 'Poor Performance'
        WHEN accuracy_score IS NOT NULL AND accuracy_score < 0.6 THEN 'Low Accuracy'
        WHEN usage_count = 0 AND created_at < datetime('now', '-30 days') THEN 'Unused Template'
        WHEN last_used < datetime('now', '-180 days') AND usage_count > 0 THEN 'Stale Template'
        ELSE 'Other'
      END as attention_reason
    FROM templates
    WHERE is_active = 1
      AND (
        is_validated = 0
        OR (success_rate IS NOT NULL AND success_rate < 0.5)
        OR (accuracy_score IS NOT NULL AND accuracy_score < 0.6)
        OR (usage_count = 0 AND created_at < datetime('now', '-30 days'))
        OR (last_used < datetime('now', '-180 days') AND usage_count > 0)
      )
    ORDER BY
      CASE attention_reason
        WHEN 'Poor Performance' THEN 1
        WHEN 'Low Accuracy' THEN 2
        WHEN 'Needs Validation' THEN 3
        WHEN 'Unused Template' THEN 4
        WHEN 'Stale Template' THEN 5
        ELSE 6
      END,
      created_at DESC
  `);
}

export async function down(knex: Knex): Promise<void> {
  // Drop triggers first
  await knex.raw('DROP TRIGGER IF EXISTS update_template_last_used');
  await knex.raw('DROP TRIGGER IF EXISTS update_templates_updated_at');
  await knex.raw('DROP TRIGGER IF EXISTS templates_fts_update');
  await knex.raw('DROP TRIGGER IF EXISTS templates_fts_delete');
  await knex.raw('DROP TRIGGER IF EXISTS templates_fts_insert');

  // Drop FTS table
  await knex.raw('DROP TABLE IF EXISTS templates_fts');

  // Drop views
  await knex.raw('DROP VIEW IF EXISTS templates_requiring_attention');
  await knex.raw('DROP VIEW IF EXISTS template_relationships');
  await knex.raw('DROP VIEW IF EXISTS template_type_stats');
  await knex.raw('DROP VIEW IF EXISTS template_performance');
  await knex.raw('DROP VIEW IF EXISTS auto_apply_templates');
  await knex.raw('DROP VIEW IF EXISTS active_templates');

  // Drop main table (this will also drop all indexes and foreign keys)
  await knex.schema.dropTableIfExists('templates');
}

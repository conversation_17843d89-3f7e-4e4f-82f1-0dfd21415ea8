import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create audit_log table for security and compliance tracking
  await knex.schema.createTable('audit_log', (table) => {
    // Primary key
    table.string('id', 36).primary().notNullable().comment('UUID primary key');

    // Event identification
    table.string('event_type', 100).notNullable().comment('Type of audited event');
    table.string('event_category', 50).notNullable().comment('Event category (security, data, system, user)');
    table.string('event_action', 100).notNullable().comment('Specific action performed');
    table.text('event_description').notNullable().comment('Human-readable description of the event');

    // Actor information
    table.string('user_id', 100).comment('User who performed the action');
    table.string('session_id', 36).comment('Session ID when event occurred');
    table.string('ip_address', 45).comment('IP address of the actor');
    table.json('user_agent').comment('User agent information');
    table.string('actor_type', 50).defaultTo('user').notNullable().comment('Type of actor (user, system, api, etc.)');

    // Target information
    table.string('target_type', 100).comment('Type of target entity');
    table.string('target_id', 100).comment('ID of the target entity');
    table.text('target_description').comment('Description of the target');
    table.json('target_metadata').comment('Additional target metadata');

    // Event details
    table.json('event_data').comment('Detailed event data');
    table.json('before_state').comment('State before the event (for changes)');
    table.json('after_state').comment('State after the event (for changes)');
    table.json('context').comment('Additional context information');

    // Temporal information
    table.timestamp('event_timestamp').defaultTo(knex.fn.now()).notNullable().comment('When the event occurred');
    table.bigInteger('sequence_number').notNullable().comment('Sequential number for ordering');
    table.string('correlation_id', 36).comment('Correlation ID for related events');
    table.string('trace_id', 36).comment('Trace ID for distributed tracing');

    // Risk and severity
    table.string('severity_level', 20).notNullable().comment('Severity level of the event');
    table.string('risk_level', 20).comment('Risk level associated with the event');
    table.decimal('risk_score', 5, 4).comment('Calculated risk score (0-1)');
    table.boolean('is_suspicious').defaultTo(false).notNullable().comment('Whether event is flagged as suspicious');

    // Compliance and regulatory
    table.json('compliance_tags').comment('Compliance framework tags (GDPR, SOX, etc.)');
    table.boolean('requires_retention').defaultTo(true).notNullable().comment('Whether event must be retained for compliance');
    table.timestamp('retention_until').comment('Retention deadline for this event');
    table.string('data_classification', 50).comment('Data classification level');

    // Technical details
    table.string('application_version', 50).comment('Application version when event occurred');
    table.string('platform', 50).comment('Platform information');
    table.json('system_info').comment('System intion at time of event');
    table.integer('processing_time_ms').unsigned().comment('Time taken to process the event');

    // Status and workflow
    table.string('status', 50).defaultTo('logged').notNullable().comment('Status of the audit event');
    table.boolean('is_reviewed').defaultTo(false).notNullable().comment('Whether event has been reviewed');
    table.timestamp('reviewed_at').comment('When event was reviewed');
    table.string('reviewed_by', 100).comment('Who reviewed the event');
    table.text('review_notes').comment('Notes from the review');

    // Error and exception handling
    table.boolean('is_error_event').defaultTo(false).notNullable().comment('Whether this logs an error');
    table.text('error_message').comment('Error message if applicable');
    table.text('stack_trace').comment('Stack trace for errors');
    table.string('error_code', 50).comment('Error code if applicable');

    // Data integrity and verification
    table.string('event_hash', 64).notNullable().comment('Hash of event data for integrity');
    table.string('previous_hash', 64).comment('Hash of previous event for chain integrity');
    table.boolean('is_verified').defaultTo(false).notNullable().comment('Whether event integrity has been verified');
    table.timestamp('verified_at').comment('When event was verified');

    // Alerting and notifications
    table.boolean('alert_triggered').defaultTo(false).notNullable().comment('Whether event triggered an alert');
    table.json('alert_recipients').comment('Recipients of alerts for this event');
    table.timestamp('alert_sent_at').comment('When alert was sent');
    table.string('alert_level', 20).comment('Level of alert triggered');

    // Constraints
    table.check('sequence_number > 0', [], 'sequence_number_positive');
    table.check('processing_time_ms >= 0', [], 'processing_time_positive');
    table.check('risk_score IS NULL OR (risk_score >= 0 AND risk_score <= 1)', [], 'risk_score_range');
    table.check("event_category IN ('security', 'data', 'system', 'user', 'compliance', 'performance', 'other')", [], 'valid_event_category');
    table.check("actor_type IN ('user', 'system', 'api', 'service', 'admin', 'other')", [], 'valid_actor_type');
    table.check("severity_level IN ('critical', 'high', 'medium', 'low', 'info')", [], 'valid_severity_level');
    table.check("risk_level IN ('critical', 'high', 'medium', 'low', 'none') OR risk_level IS NULL", [], 'valid_risk_level');
    table.check("status IN ('logged', 'processing', 'reviewed', 'archived', 'escalated')", [], 'valid_status');
    table.check("data_classification IN ('public', 'internal', 'confidential', 'restricted') OR data_classification IS NULL", [], 'valid_data_classification');
    table.check("alert_level IN ('critical', 'high', 'medium', 'low') OR alert_level IS NULL", [], 'valid_alert_level');

    // JSON validation
    table.check("json_valid(user_agent) OR user_agent IS NULL", [], 'valid_user_agent');
    table.check("json_valid(target_metadata) OR target_metadata IS NULL", [], 'valid_target_metadata');
    table.check("json_valid(event_data) OR event_data IS NULL", [], 'valid_event_data');
    table.check("json_valid(before_state) OR before_state IS NULL", [], 'valid_before_state');
    table.check("json_valid(after_state) OR after_state IS NULL", [], 'valid_after_state');
    table.check("json_valid(context) OR context IS NULL", [], 'valid_context');
    table.check("json_valid(compliance_tags) OR compliance_tags IS NULL", [], 'valid_compliance_tags');
    table.check("json_valid(system_info) OR system_info IS NULL", [], 'valid_system_info');
    table.check("json_valid(alert_recipients) OR alert_recipients IS NULL", [], 'valid_alert_recipients');

    // Indexes for performance and compliance queries
    table.index(['event_timestamp'], 'idx_audit_log_timestamp');
    table.index(['event_type'], 'idx_audit_log_event_type');
    table.index(['event_category'], 'idx_audit_log_category');
    table.index(['event_action'], 'idx_audit_log_action');
    table.index(['user_id'], 'idx_audit_log_user');
    table.index(['session_id'], 'idx_audit_log_session');
    table.index(['target_type'], 'idx_audit_log_target_type');
    table.index(['target_id'], 'idx_audit_log_target_id');
    table.index(['severity_level'], 'idx_audit_log_severity');
    table.index(['risk_level'], 'idx_audit_log_risk');
    table.index(['is_suspicious'], 'idx_audit_log_suspicious');
    table.index(['status'], 'idx_audit_log_status');
    table.index(['is_reviewed'], 'idx_audit_log_reviewed');
    table.index(['is_error_event'], 'idx_audit_log_error');
    table.index(['correlation_id'], 'idx_audit_log_correlation');
    table.index(['trace_id'], 'idx_audit_log_trace');
    table.index(['sequence_number'], 'idx_audit_log_sequence');
    table.index(['event_hash'], 'idx_audit_log_hash');
    table.index(['alert_triggered'], 'idx_audit_log_alert');
    table.index(['requires_retention'], 'idx_audit_log_retention');
    table.index(['retention_until'], 'idx_audit_log_retention_until');

    // Composite indexes for common audit queries
    table.index(['event_category', 'event_timestamp'], 'idx_audit_log_category_time');
    table.index(['user_id', 'event_timestamp'], 'idx_audit_log_user_time');
    table.index(['target_type', 'target_id'], 'idx_audit_log_target');
    table.index(['severity_level', 'event_timestamp'], 'idx_audit_log_severity_time');
    table.index(['is_suspicious', 'risk_level'], 'idx_audit_log_suspicious_risk');
    table.index(['event_type', 'event_action'], 'idx_audit_log_type_action');
    table.index(['correlation_id', 'sequence_number'], 'idx_audit_log_correlation_seq');
    table.index(['is_reviewed', 'severity_level'], 'idx_audit_log_reviewed_severity');
    table.index(['alert_triggered', 'alert_level'], 'idx_audit_log_alert_level');
  });

  // Create sequence for audit log entries
  await knex.raw(`
    CREATE TABLE audit_log_sequence (
      id INTEGER PRIMARY KEY,
      next_value BIGINT NOT NULL DEFAULT 1
    )
  `);

  // Insert initial sequence value
  await knex.raw(`INSERT INTO audit_log_sequence (id, next_value) VALUES (1, 1)`);

  // Create trigger to auto-increment sequence number and calculate hash
  await knex.raw(`
    CREATE TRIGGER audit_log_sequence_trigger
    BEFORE INSERT ON audit_log
    FOR EACH ROW
    BEGIN
      -- Update sequence number
      UPDATE audit_log_sequence SET next_value = next_value + 1 WHERE id = 1;
      SELECT next_value - 1 FROM audit_log_sequence WHERE id = 1 INTO NEW.sequence_number;

      -- Calculate event hash for integrity
      SELECT
        hex(
          hash(
            COALESCE(NEW.event_type, '') || '|' ||
            COALESCE(NEW.event_action, '') || '|' ||
            COALESCE(NEW.user_id, '') || '|' ||
            COALESCE(NEW.target_id, '') || '|' ||
            COALESCE(NEW.event_timestamp, '') || '|' ||
            COALESCE(NEW.sequence_number, 0)
          )
        ) INTO NEW.event_hash;

      -- Get previous hash for chain integrity
      SELECT event_hash FROM audit_log
      WHERE sequence_number = NEW.sequence_number - 1
      INTO NEW.previous_hash;
    END
  `);

  // Create full-text search virtual table for audit log
  await knex.raw(`
    CREATE VIRTUAL TABLE IF NOT EXISTS audit_log_fts USING fts5(
      id UNINDEXED,
      event_description,
      target_description,
      error_message,
      review_notes,
      event_data,
      content='audit_log',
      content_rowid='rowid'
    )
  `);

  // Create triggers to keep FTS table in sync
  await knex.raw(`
    CREATE TRIGGER audit_log_fts_insert AFTER INSERT ON audit_log BEGIN
      INSERT INTO audit_log_fts(id, event_description, target_description, error_message, review_notes, event_data)
      VALUES (new.id, new.event_description, new.target_description, new.error_message, new.review_notes, new.event_data);
    END
  `);

  await knex.raw(`
    CREATE TRIGGER audit_log_fts_delete AFTER DELETE ON audit_log BEGIN
      DELETE FROM audit_log_fts WHERE id = old.id;
    END
  `);

  await knex.raw(`
    CREATE TRIGGER audit_log_fts_update AFTER UPDATE ON audit_log BEGIN
      DELETE FROM audit_log_fts WHERE id = old.id;
      INSERT INTO audit_log_fts(id, event_description, target_description, error_message, review_notes, event_data)
      VALUES (new.id, new.event_description, new.target_description, new.error_message, new.review_notes, new.event_data);
    END
  `);

  // Create view for recent high-severity events
  await knex.raw(`
    CREATE VIEW recent_high_severity_events AS
    SELECT
      id,
      event_type,
      event_action,
      event_description,
      user_id,
      target_type,
      target_id,
      severity_level,
      risk_level,
      is_suspicious,
      event_timestamp,
      is_reviewed
    FROM audit_log
    WHERE severity_level IN ('critical', 'high')
      AND event_timestamp >= datetime('now', '-7 days')
    ORDER BY event_timestamp DESC
  `);

  // Create view for suspicious activities
  await knex.raw(`
    CREATE VIEW suspicious_activities AS
    SELECT
      al.*,
      COUNT(*) OVER (
        PARTITION BY user_id, event_type
        ORDER BY event_timestamp
        RANGE BETWEEN INTERVAL 1 HOUR PRECEDING AND CURRENT ROW
      ) as similar_events_last_hour
    FROM audit_log al
    WHERE is_suspicious = 1
      OR risk_level IN ('critical', 'high')
      OR severity_level = 'critical'
    ORDER BY event_timestamp DESC
  `);

  // Create view for compliance reporting
  await knex.raw(`
    CREATE VIEW compliance_events AS
    SELECT
      event_category,
      event_type,
      data_classification,
      COUNT(*) as event_count,
      COUNT(CASE WHEN severity_level IN ('critical', 'high') THEN 1 END) as high_severity_count,
      COUNT(CASE WHEN is_reviewed = 0 THEN 1 END) as unreviewed_count,
      MIN(event_timestamp) as first_event,
      MAX(event_timestamp) as last_event,
      compliance_tags
    FROM audit_log
    WHERE requires_retention = 1
      AND (retention_until IS NULL OR retention_until > CURRENT_TIMESTAMP)
    GROUP BY event_category, event_type, data_classification, compliance_tags
    ORDER BY event_count DESC
  `);

  // Create view for user activity summary
  await knex.raw(`
    CREATE VIEW user_activity_summary AS
    SELECT
      user_id,
      COUNT(*) as total_events,
      COUNT(DISTINCT event_type) as unique_event_types,
      COUNT(CASE WHEN severity_level IN ('critical', 'high') THEN 1 END) as high_severity_events,
      COUNT(CASE WHEN is_suspicious = 1 THEN 1 END) as suspicious_events,
      COUNT(CASE WHEN is_error_event = 1 THEN 1 END) as error_events,
      MIN(event_timestamp) as first_activity,
      MAX(event_timestamp) as last_activity,
      COUNT(CASE WHEN event_timestamp >= datetime('now', '-1 day') THEN 1 END) as events_last_24h,
      COUNT(CASE WHEN event_timestamp >= datetime('now', '-7 days') THEN 1 END) as events_last_week,
      COUNT(DISTINCT ip_address) as unique_ip_addresses,
      COUNT(DISTINCT session_id) as unique_sessions
    FROM audit_log
    WHERE user_id IS NOT NULL
    GROUP BY user_id
    ORDER BY total_events DESC
  `);

  // Create view for system health monitoring
  await knex.raw(`
    CREATE VIEW system_health_events AS
    SELECT
      DATE(event_timestamp) as event_date,
      event_category,
      severity_level,
      COUNT(*) as event_count,
      COUNT(CASE WHEN is_error_event = 1 THEN 1 END) as error_count,
      AVG(processing_time_ms) as avg_processing_time,
      COUNT(CASE WHEN alert_triggered = 1 THEN 1 END) as alerts_triggered
    FROM audit_log
    WHERE event_category IN ('system', 'performance')
      AND event_timestamp >= datetime('now', '-30 days')
    GROUP BY DATE(event_timestamp), event_category, severity_level
    ORDER BY event_date DESC, event_count DESC
  `);

  // Create view for events requiring review
  await knex.raw(`
    CREATE VIEW events_requiring_review AS
    SELECT
      *,
      CASE
        WHEN severity_level = 'critical' THEN 'Critical Event'
        WHEN is_suspicious = 1 THEN 'Suspicious Activity'
        WHEN risk_level IN ('critical', 'high') THEN 'High Risk Event'
        WHEN is_error_event = 1 AND severity_level = 'high' THEN 'High Severity Error'
        WHEN alert_triggered = 1 AND is_reviewed = 0 THEN 'Unreviewed Alert'
        ELSE 'Other'
      END as review_reason
    FROM audit_log
    WHERE is_reviewed = 0
      AND (
        severity_level = 'critical'
        OR is_suspicious = 1
        OR risk_level IN ('critical', 'high')
        OR (is_error_event = 1 AND severity_level = 'high')
        OR alert_triggered = 1
      )
    ORDER BY
      CASE severity_level
        WHEN 'critical' THEN 1
        WHEN 'high' THEN 2
        WHEN 'medium' THEN 3
        ELSE 4
      END,
      event_timestamp DESC
  `);
}

export async function down(knex: Knex): Promise<void> {
  // Drop triggers first
  await knex.raw('DROP TRIGGER IF EXISTS audit_log_fts_update');
  await knex.raw('DROP TRIGGER IF EXISTS audit_log_fts_delete');
  await knex.raw('DROP TRIGGER IF EXISTS audit_log_fts_insert');
  await knex.raw('DROP TRIGGER IF EXISTS audit_log_sequence_trigger');

  // Drop FTS table
  await knex.raw('DROP TABLE IF EXISTS audit_log_fts');

  // Drop views
  await knex.raw('DROP VIEW IF EXISTS events_requiring_review');
  await knex.raw('DROP VIEW IF EXISTS system_health_events');
  await knex.raw('DROP VIEW IF EXISTS user_activity_summary');
  await knex.raw('DROP VIEW IF EXISTS compliance_events');
  await knex.raw('DROP VIEW IF EXISTS suspicious_activities');
  await knex.raw('DROP VIEW IF EXISTS recent_high_severity_events');

  // Drop sequence table
  await knex.raw('DROP TABLE IF EXISTS audit_log_sequence');

  // Drop main table
  await knex.schema.dropTableIfExists('audit_log');
}

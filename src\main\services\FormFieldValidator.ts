// import Jo<PERSON> from 'joi';
import { createLogger } from '../utils/logger';
import {
  FormField,
  FormFieldType,
  ValidationRule,
  ValidationRuleType,
  ValidationResult,
  ValidationError,
  ValidationCondition,
} from '../../shared/types/Document';

const logger = createLogger('FormFieldValidator');

export interface ValidationOptions {
  enableCrossFieldValidation: boolean;
  enableDependencyValidation: boolean;
  strictMode: boolean;
  customValidators: Map<string, CustomValidator>;
  locale: string;
}

export interface CustomValidator {
  name: string;
  validate: (value: any, field: FormField, allFields: FormField[]) => Promise<ValidationResult>;
  description: string;
}

export interface FieldDependency {
  sourceField: string;
  targetField: string;
  condition: ValidationCondition;
  action: DependencyAction;
}

export enum DependencyAction {
  REQUIRE = 'require',
  HIDE = 'hide',
  DISABLE = 'disable',
  CALCULATE = 'calculate',
  VALIDATE = 'validate',
}

export interface ValidationContext {
  allFields: FormField[];
  dependencies: FieldDependency[];
  formData: Record<string, any>;
  metadata: Record<string, any>;
}

/**
 * Comprehensive form field validation service
 */
export class FormFieldValidator {
  private readonly options: ValidationOptions;
  private readonly builtInValidators: Map<
    ValidationRuleType,
    (value: any, rule: ValidationRule, context: ValidationContext) => Promise<ValidationResult>
  >;

  constructor(options: Partial<ValidationOptions> = {}) {
    this.options = this.mergeDefaultOptions(options);
    this.builtInValidators = this.initializeBuiltInValidators();
  }

  /**
   * Validate a single form field
   */
  public async validateField(
    field: FormField,
    value: any,
    context: ValidationContext
  ): Promise<ValidationResult> {
    logger.debug('Validating field', { fieldId: field.id, fieldType: field.type });

    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    try {
      // Basic type validation
      const typeValidation = await this.validateFieldType(field, value);
      if (!typeValidation.isValid) {
        errors.push(...typeValidation.errors);
      }
      warnings.push(
        ...typeValidation.warnings.map(w => ({
          ...w,
          severity: w.severity || ('warning' as const),
        }))
      );

      // Required field validation
      if (field.required && this.isEmpty(value)) {
        errors.push({
          code: 'FIELD_REQUIRED',
          message: `${field.name} is required`,
          severity: 'error',
        });
      }

      // Custom validation rules
      if (field.bounds && 'validationRules' in field.bounds) {
        const customValidation = await this.validateCustomRules(
          field,
          value,
          (field.bounds as any).validationRules || [],
          context
        );
        errors.push(...customValidation.errors);
        warnings.push(
          ...customValidation.warnings.map(w => ({
            ...w,
            severity: w.severity || ('warning' as const),
          }))
        );
      }

      // Cross-field validation
      if (this.options.enableCrossFieldValidation) {
        const crossFieldValidation = await this.validateCrossFieldRules(field, value, context);
        errors.push(...crossFieldValidation.errors);
        warnings.push(
          ...crossFieldValidation.warnings.map(w => ({
            ...w,
            severity: w.severity || ('warning' as const),
          }))
        );
      }

      // Dependency validation
      if (this.options.enableDependencyValidation) {
        const dependencyValidation = await this.validateDependencies(field, value, context);
        errors.push(...dependencyValidation.errors);
        warnings.push(
          ...dependencyValidation.warnings.map(w => ({
            ...w,
            severity: w.severity || ('warning' as const),
          }))
        );
      }

      return {
        fieldId: field.id,
        isValid: errors.length === 0,
        errors,
        warnings,
      };
    } catch (error) {
      logger.error('Field validation failed', { error, fieldId: field.id });
      return {
        fieldId: field.id,
        isValid: false,
        errors: [
          {
            code: 'VALIDATION_ERROR',
            message: `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            severity: 'error',
          },
        ],
        warnings: [],
      };
    }
  }

  /**
   * Validate multiple form fields
   */
  public async validateFields(
    fields: FormField[],
    formData: Record<string, any>,
    dependencies: FieldDependency[] = []
  ): Promise<ValidationResult[]> {
    logger.info('Validating form fields', { fieldCount: fields.length });

    const context: ValidationContext = {
      allFields: fields,
      dependencies,
      formData,
      metadata: {},
    };

    const results: ValidationResult[] = [];

    for (const field of fields) {
      const value = formData[field.id] || formData[field.name] || field.value;
      const result = await this.validateField(field, value, context);
      results.push(result);
    }

    // Perform form-level validation
    const formLevelValidation = await this.validateFormLevel(fields, formData, context);
    if (formLevelValidation.length > 0) {
      results.push(...formLevelValidation);
    }

    logger.info('Form validation completed', {
      totalFields: fields.length,
      validFields: results.filter(r => r.isValid).length,
      invalidFields: results.filter(r => !r.isValid).length,
    });

    return results;
  }

  /**
   * Validate field type-specific constraints
   */
  private async validateFieldType(field: FormField, value: any): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    if (this.isEmpty(value)) {
      return { fieldId: field.id, isValid: true, errors, warnings };
    }

    switch (field.type) {
      case FormFieldType.EMAIL:
        if (!this.isValidEmail(value)) {
          errors.push({
            code: 'INVALID_EMAIL',
            message: 'Please enter a valid email address',
            severity: 'error',
          });
        }
        break;

      case FormFieldType.PHONE:
        if (!this.isValidPhone(value)) {
          errors.push({
            code: 'INVALID_PHONE',
            message: 'Please enter a valid phone number',
            severity: 'error',
          });
        }
        break;

      case FormFieldType.NUMBER:
        if (!this.isValidNumber(value)) {
          errors.push({
            code: 'INVALID_NUMBER',
            message: 'Please enter a valid number',
            severity: 'error',
          });
        }
        break;

      case FormFieldType.DATE:
        if (!this.isValidDate(value)) {
          errors.push({
            code: 'INVALID_DATE',
            message: 'Please enter a valid date',
            severity: 'error',
          });
        }
        break;

      case FormFieldType.TEXT:
        // Text fields can have length constraints
        if (typeof value === 'string') {
          if (value.length > 1000) {
            warnings.push({
              code: 'TEXT_TOO_LONG',
              message: 'Text is very long and may be truncated',
              severity: 'warning',
            });
          }
        }
        break;
    }

    return {
      fieldId: field.id,
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Validate custom validation rules
   */
  private async validateCustomRules(
    field: FormField,
    value: any,
    rules: ValidationRule[],
    context: ValidationContext
  ): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    for (const rule of rules) {
      if (!rule.enabled) continue;

      try {
        const validator = this.builtInValidators.get(rule.type);
        if (validator) {
          const result = await validator(value, rule, context);
          if (!result.isValid) {
            if (rule.severity === 'error') {
              errors.push(...result.errors);
            } else {
              warnings.push(...result.errors.map(e => ({ ...e, severity: rule.severity as any })));
            }
          }
        } else {
          // Check custom validators
          const customValidator = this.options.customValidators.get(rule.type);
          if (customValidator) {
            const result = await customValidator.validate(value, field, context.allFields);
            if (!result.isValid) {
              if (rule.severity === 'error') {
                errors.push(...result.errors);
              } else {
                warnings.push(
                  ...result.errors.map(e => ({ ...e, severity: rule.severity as any }))
                );
              }
            }
          }
        }
      } catch (error) {
        logger.error('Custom validation rule failed', { error, ruleId: rule.id });
        errors.push({
          code: 'VALIDATION_RULE_ERROR',
          message: `Validation rule "${rule.name}" failed to execute`,
          severity: 'error',
        });
      }
    }

    return {
      fieldId: field.id,
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Validate cross-field rules
   */
  private async validateCrossFieldRules(
    field: FormField,
    value: any,
    context: ValidationContext
  ): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    // Example: Password confirmation validation
    if (
      field.name.toLowerCase().includes('confirm') &&
      field.name.toLowerCase().includes('password')
    ) {
      const passwordField = context.allFields.find(
        f => f.name.toLowerCase().includes('password') && !f.name.toLowerCase().includes('confirm')
      );

      if (passwordField) {
        const passwordValue =
          context.formData[passwordField.id] || context.formData[passwordField.name];
        if (value !== passwordValue) {
          errors.push({
            code: 'PASSWORD_MISMATCH',
            message: 'Passwords do not match',
            severity: 'error',
          });
        }
      }
    }

    // Example: Date range validation
    if (field.type === FormFieldType.DATE) {
      if (field.name.toLowerCase().includes('end') || field.name.toLowerCase().includes('to')) {
        const startField = context.allFields.find(
          f =>
            f.type === FormFieldType.DATE &&
            (f.name.toLowerCase().includes('start') || f.name.toLowerCase().includes('from'))
        );

        if (startField) {
          const startValue = context.formData[startField.id] || context.formData[startField.name];
          if (startValue && value && new Date(value) < new Date(startValue)) {
            errors.push({
              code: 'INVALID_DATE_RANGE',
              message: 'End date must be after start date',
              severity: 'error',
            });
          }
        }
      }
    }

    return {
      fieldId: field.id,
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Validate field dependencies
   */
  private async validateDependencies(
    field: FormField,
    value: any,
    context: ValidationContext
  ): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    const dependencies = context.dependencies.filter(dep => dep.targetField === field.id);

    for (const dependency of dependencies) {
      const sourceField = context.allFields.find(f => f.id === dependency.sourceField);
      if (!sourceField) continue;

      const sourceValue = context.formData[sourceField.id] || context.formData[sourceField.name];
      const conditionMet = this.evaluateCondition(dependency.condition, sourceValue);

      switch (dependency.action) {
        case DependencyAction.REQUIRE:
          if (conditionMet && this.isEmpty(value)) {
            errors.push({
              code: 'CONDITIONAL_REQUIRED',
              message: `${field.name} is required when ${sourceField.name} is ${sourceValue}`,
              severity: 'error',
            });
          }
          break;

        case DependencyAction.VALIDATE:
          if (conditionMet && !this.isEmpty(value)) {
            // Additional validation when condition is met
            // This could be extended with more complex validation logic
          }
          break;
      }
    }

    return {
      fieldId: field.id,
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Perform form-level validation
   */
  private async validateFormLevel(
    fields: FormField[],
    formData: Record<string, any>,
    _context: ValidationContext
  ): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    // Example: Check for duplicate values in unique fields
    const uniqueFields = fields.filter(f => f.name.toLowerCase().includes('unique'));
    for (const field of uniqueFields) {
      const value = formData[field.id] || formData[field.name];
      if (!this.isEmpty(value)) {
        const duplicates = fields.filter(
          f => f.id !== field.id && (formData[f.id] === value || formData[f.name] === value)
        );

        if (duplicates.length > 0) {
          results.push({
            fieldId: field.id,
            isValid: false,
            errors: [
              {
                code: 'DUPLICATE_VALUE',
                message: `${field.name} must be unique`,
                severity: 'error',
              },
            ],
            warnings: [],
          });
        }
      }
    }

    return results;
  }

  /**
   * Initialize built-in validators
   */
  private initializeBuiltInValidators(): Map<
    ValidationRuleType,
    (value: any, rule: ValidationRule, context: ValidationContext) => Promise<ValidationResult>
  > {
    const validators = new Map();

    validators.set(ValidationRuleType.REQUIRED, async (value: any, rule: ValidationRule) => {
      const isValid = !this.isEmpty(value);
      return {
        fieldId: '',
        isValid,
        errors: isValid
          ? []
          : [
              {
                code: 'REQUIRED_FIELD',
                message: rule.message || 'This field is required',
                severity: 'error' as const,
              },
            ],
        warnings: [],
      };
    });

    validators.set(ValidationRuleType.PATTERN, async (value: any, rule: ValidationRule) => {
      if (this.isEmpty(value)) return { fieldId: '', isValid: true, errors: [], warnings: [] };

      const pattern = new RegExp(rule.condition.value);
      const isValid = pattern.test(String(value));
      return {
        fieldId: '',
        isValid,
        errors: isValid
          ? []
          : [
              {
                code: 'PATTERN_MISMATCH',
                message: rule.message || 'Value does not match required pattern',
                severity: 'error' as const,
              },
            ],
        warnings: [],
      };
    });

    validators.set(ValidationRuleType.RANGE, async (value: any, rule: ValidationRule) => {
      if (this.isEmpty(value)) return { fieldId: '', isValid: true, errors: [], warnings: [] };

      const numValue = Number(value);
      if (isNaN(numValue)) {
        return {
          fieldId: '',
          isValid: false,
          errors: [
            {
              code: 'INVALID_NUMBER',
              message: 'Value must be a number for range validation',
              severity: 'error' as const,
            },
          ],
          warnings: [],
        };
      }

      const min = rule.condition.min;
      const max = rule.condition.max;
      const isValid =
        (min === undefined || numValue >= min) && (max === undefined || numValue <= max);

      return {
        fieldId: '',
        isValid,
        errors: isValid
          ? []
          : [
              {
                code: 'OUT_OF_RANGE',
                message:
                  rule.message || `Value must be between ${min || 'any'} and ${max || 'any'}`,
                severity: 'error' as const,
              },
            ],
        warnings: [],
      };
    });

    return validators;
  }

  /**
   * Utility methods
   */
  private isEmpty(value: any): boolean {
    return (
      value === null ||
      value === undefined ||
      value === '' ||
      (Array.isArray(value) && value.length === 0)
    );
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  }

  private isValidNumber(value: any): boolean {
    return !isNaN(Number(value)) && isFinite(Number(value));
  }

  private isValidDate(value: any): boolean {
    const date = new Date(value);
    return date instanceof Date && !isNaN(date.getTime());
  }

  private evaluateCondition(condition: ValidationCondition, value: any): boolean {
    switch (condition.operator) {
      case 'equals':
        return value === condition.value;
      case 'not_equals':
        return value !== condition.value;
      case 'greater_than':
        return Number(value) > Number(condition.value);
      case 'less_than':
        return Number(value) < Number(condition.value);
      case 'contains':
        return String(value).includes(String(condition.value));
      case 'not_empty':
        return !this.isEmpty(value);
      default:
        return false;
    }
  }

  private mergeDefaultOptions(options: Partial<ValidationOptions>): ValidationOptions {
    return {
      enableCrossFieldValidation: options.enableCrossFieldValidation ?? true,
      enableDependencyValidation: options.enableDependencyValidation ?? true,
      strictMode: options.strictMode ?? false,
      customValidators: options.customValidators ?? new Map(),
      locale: options.locale ?? 'en',
    };
  }
}

import { LogEntry, LogLevel } from './LoggingService';

export interface ErrorPattern {
  id: string;
  pattern: RegExp;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  suggestions: string[];
}

export interface ErrorAnalysis {
  totalErrors: number;
  errorsByLevel: Record<LogLevel, number>;
  errorsByComponent: Record<string, number>;
  errorsByCategory: Record<string, number>;
  commonPatterns: Array<{
    pattern: ErrorPattern;
    count: number;
    examples: LogEntry[];
  }>;
  trends: {
    hourly: number[];
    daily: number[];
  };
  recommendations: string[];
}

/**
 * Error analytics service for analyzing error patterns and providing insights
 */
export class ErrorAnalyticsService {
  private errorPatterns: ErrorPattern[] = [
    {
      id: 'network-error',
      pattern: /network|fetch|connection|timeout/i,
      description: 'Network connectivity issues',
      severity: 'high',
      category: 'Network',
      suggestions: [
        'Check internet connection',
        'Verify API endpoints are accessible',
        'Implement retry logic with exponential backoff',
        'Add offline mode support',
      ],
    },
    {
      id: 'memory-error',
      pattern: /memory|heap|allocation/i,
      description: 'Memory-related issues',
      severity: 'critical',
      category: 'Performance',
      suggestions: [
        'Optimize memory usage',
        'Implement lazy loading',
        'Add memory monitoring',
        'Review large object allocations',
      ],
    },
    {
      id: 'permission-error',
      pattern: /permission|unauthorized|forbidden|access denied/i,
      description: 'Permission and authorization issues',
      severity: 'medium',
      category: 'Security',
      suggestions: [
        'Check user permissions',
        'Verify authentication tokens',
        'Review access control policies',
        'Implement proper error handling for auth failures',
      ],
    },
    {
      id: 'file-error',
      pattern: /file|read|write|path|directory/i,
      description: 'File system operations',
      severity: 'medium',
      category: 'File System',
      suggestions: [
        'Verify file paths exist',
        'Check file permissions',
        'Implement proper error handling for file operations',
        'Add file validation',
      ],
    },
    {
      id: 'ai-error',
      pattern: /ai|model|embedding|completion|inference/i,
      description: 'AI service issues',
      severity: 'high',
      category: 'AI Services',
      suggestions: [
        'Check AI service availability',
        'Verify API keys and quotas',
        'Implement fallback mechanisms',
        'Add rate limiting',
      ],
    },
    {
      id: 'database-error',
      pattern: /database|sql|query|connection/i,
      description: 'Database connectivity and query issues',
      severity: 'high',
      category: 'Database',
      suggestions: [
        'Check database connection',
        'Optimize database queries',
        'Implement connection pooling',
        'Add database health monitoring',
      ],
    },
    {
      id: 'ui-error',
      pattern: /react|component|render|hook|state/i,
      description: 'React UI component issues',
      severity: 'medium',
      category: 'UI',
      suggestions: [
        'Review component lifecycle',
        'Check prop types and validation',
        'Implement proper error boundaries',
        'Add component testing',
      ],
    },
    {
      id: 'validation-error',
      pattern: /validation|invalid|required|format/i,
      description: 'Data validation issues',
      severity: 'low',
      category: 'Validation',
      suggestions: [
        'Improve input validation',
        'Add client-side validation',
        'Provide better error messages',
        'Implement schema validation',
      ],
    },
  ];

  /**
   * Analyze error logs and provide insights
   */
  public analyzeErrors(logs: LogEntry[]): ErrorAnalysis {
    const errorLogs = logs.filter(log => log.level >= LogLevel.WARN);
    
    const analysis: ErrorAnalysis = {
      totalErrors: errorLogs.length,
      errorsByLevel: this.analyzeByLevel(errorLogs),
      errorsByComponent: this.analyzeByComponent(errorLogs),
      errorsByCategory: {},
      commonPatterns: this.analyzePatterns(errorLogs),
      trends: this.analyzeTrends(errorLogs),
      recommendations: [],
    };

    // Calculate errors by category
    analysis.errorsByCategory = this.calculateErrorsByCategory(analysis.commonPatterns);
    
    // Generate recommendations
    analysis.recommendations = this.generateRecommendations(analysis);

    return analysis;
  }

  private analyzeByLevel(logs: LogEntry[]): Record<LogLevel, number> {
    const result: Record<LogLevel, number> = {
      [LogLevel.DEBUG]: 0,
      [LogLevel.INFO]: 0,
      [LogLevel.WARN]: 0,
      [LogLevel.ERROR]: 0,
      [LogLevel.FATAL]: 0,
    };

    logs.forEach(log => {
      result[log.level]++;
    });

    return result;
  }

  private analyzeByComponent(logs: LogEntry[]): Record<string, number> {
    const result: Record<string, number> = {};

    logs.forEach(log => {
      const component = log.component || 'Unknown';
      result[component] = (result[component] || 0) + 1;
    });

    return result;
  }

  private analyzePatterns(logs: LogEntry[]): Array<{
    pattern: ErrorPattern;
    count: number;
    examples: LogEntry[];
  }> {
    const patternMatches = this.errorPatterns.map(pattern => {
      const matches = logs.filter(log => 
        pattern.pattern.test(log.message) || 
        (log.data && pattern.pattern.test(JSON.stringify(log.data)))
      );

      return {
        pattern,
        count: matches.length,
        examples: matches.slice(0, 3), // Keep only first 3 examples
      };
    });

    // Sort by count and filter out patterns with no matches
    return patternMatches
      .filter(match => match.count > 0)
      .sort((a, b) => b.count - a.count);
  }

  private analyzeTrends(logs: LogEntry[]): { hourly: number[]; daily: number[] } {
    const now = new Date();
    const hourly = new Array(24).fill(0);
    const daily = new Array(7).fill(0);

    logs.forEach(log => {
      const logDate = new Date(log.timestamp);
      const hoursDiff = Math.floor((now.getTime() - logDate.getTime()) / (1000 * 60 * 60));
      const daysDiff = Math.floor((now.getTime() - logDate.getTime()) / (1000 * 60 * 60 * 24));

      if (hoursDiff < 24) {
        hourly[23 - hoursDiff]++;
      }

      if (daysDiff < 7) {
        daily[6 - daysDiff]++;
      }
    });

    return { hourly, daily };
  }

  private calculateErrorsByCategory(patterns: Array<{
    pattern: ErrorPattern;
    count: number;
    examples: LogEntry[];
  }>): Record<string, number> {
    const result: Record<string, number> = {};

    patterns.forEach(({ pattern, count }) => {
      result[pattern.category] = (result[pattern.category] || 0) + count;
    });

    return result;
  }

  private generateRecommendations(analysis: ErrorAnalysis): string[] {
    const recommendations: string[] = [];

    // High error count recommendations
    if (analysis.totalErrors > 100) {
      recommendations.push('High error count detected. Consider implementing better error prevention strategies.');
    }

    // Critical errors recommendations
    if (analysis.errorsByLevel[LogLevel.FATAL] > 0) {
      recommendations.push('Fatal errors detected. Immediate attention required to prevent application crashes.');
    }

    // Pattern-based recommendations
    analysis.commonPatterns.forEach(({ pattern, count }) => {
      if (count > 5 && pattern.severity === 'critical') {
        recommendations.push(`Critical pattern "${pattern.description}" detected ${count} times. ${pattern.suggestions[0]}`);
      } else if (count > 10) {
        recommendations.push(`Frequent pattern "${pattern.description}" detected ${count} times. ${pattern.suggestions[0]}`);
      }
    });

    // Component-specific recommendations
    const topErrorComponent = Object.entries(analysis.errorsByComponent)
      .sort(([, a], [, b]) => b - a)[0];
    
    if (topErrorComponent && topErrorComponent[1] > 10) {
      recommendations.push(`Component "${topErrorComponent[0]}" has ${topErrorComponent[1]} errors. Consider reviewing its implementation.`);
    }

    // Trend-based recommendations
    const recentHourlyErrors = analysis.trends.hourly.slice(-3).reduce((sum, count) => sum + count, 0);
    if (recentHourlyErrors > analysis.totalErrors * 0.3) {
      recommendations.push('Error rate has increased significantly in recent hours. Monitor system health closely.');
    }

    return recommendations.slice(0, 10); // Limit to top 10 recommendations
  }

  /**
   * Get error pattern by ID
   */
  public getPattern(id: string): ErrorPattern | undefined {
    return this.errorPatterns.find(pattern => pattern.id === id);
  }

  /**
   * Add custom error pattern
   */
  public addPattern(pattern: ErrorPattern): void {
    this.errorPatterns.push(pattern);
  }

  /**
   * Generate error report
   */
  public generateReport(analysis: ErrorAnalysis): string {
    let report = '# Error Analysis Report\n\n';
    report += `Generated: ${new Date().toISOString()}\n\n`;
    
    report += `## Summary\n`;
    report += `- Total Errors: ${analysis.totalErrors}\n`;
    report += `- Fatal Errors: ${analysis.errorsByLevel[LogLevel.FATAL]}\n`;
    report += `- Error Rate: ${analysis.errorsByLevel[LogLevel.ERROR]}\n`;
    report += `- Warnings: ${analysis.errorsByLevel[LogLevel.WARN]}\n\n`;

    report += `## Top Error Categories\n`;
    Object.entries(analysis.errorsByCategory)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .forEach(([category, count]) => {
        report += `- ${category}: ${count} errors\n`;
      });
    report += '\n';

    report += `## Common Patterns\n`;
    analysis.commonPatterns.slice(0, 5).forEach(({ pattern, count }) => {
      report += `- ${pattern.description}: ${count} occurrences (${pattern.severity} severity)\n`;
    });
    report += '\n';

    report += `## Recommendations\n`;
    analysis.recommendations.forEach((rec, index) => {
      report += `${index + 1}. ${rec}\n`;
    });

    return report;
  }
}

// Create global error analytics service instance
export const errorAnalytics = new ErrorAnalyticsService();

export default ErrorAnalyticsService;

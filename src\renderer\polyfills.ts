// Global polyfills for Electron renderer process
declare global {
  // eslint-disable-next-line no-var
  var global: typeof globalThis;
}

// Additional polyfills for Electron renderer process
(function() {
  // Double-check global is available (should be set by global-polyfill.js)
  if (typeof global === 'undefined') {
    console.warn('Global polyfill not loaded properly, setting up fallback');
    (globalThis as any).global = globalThis;
  }

  if (typeof window !== 'undefined') {
    // Ensure global is also on window
    if (typeof (window as any).global === 'undefined') {
      (window as any).global = globalThis;
    }

    // Ensure process is available
    if (typeof (window as any).process === 'undefined') {
      (window as any).process = { env: {} };
    }

    // Provide a basic Buffer polyfill if needed
    if (typeof (window as any).Buffer === 'undefined') {
      (window as any).Buffer = {};
    }
  }

  console.log('TypeScript polyfills loaded, global is:', typeof global);
})();

export { };

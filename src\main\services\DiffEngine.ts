import { v4 as uuidv4 } from 'uuid';
import * as crypto from 'crypto';
import {
  DiffResult,
  VisualDiff,
  ApplicationState,
  ChangeSet,
  ChangeType,
  DiffStatistics,
  VisualDiffType,
  TextDiff,
  TextDiffLine,
  TextDiffLineType,
  TextDiffStatistics,
  StructuralDiff,
  StructuralDiffElement,
  StructuralDiffStatistics,
  ImageData,
} from '../../shared/types/Timeline';

export interface DiffEngineConfig {
  textDiffAlgorithm?: 'myers' | 'patience' | 'histogram';
  contextLines?: number;
  ignoreWhitespace?: boolean;
  ignoreCase?: boolean;
  maxDiffSize?: number;
  enableVisualDiff?: boolean;
  visualDiffThreshold?: number;
  enableCaching?: boolean;
  cacheSize?: number;
  enableWordDiff?: boolean;
  enableCharDiff?: boolean;
}

export interface DiffOperation {
  type: 'insert' | 'delete' | 'equal';
  text: string;
  startIndex?: number;
  endIndex?: number;
}

export interface MyersDiffResult {
  operations: DiffOperation[];
  editDistance: number;
  similarity: number;
}

export interface WordDiffResult {
  words: WordDiffOperation[];
  statistics: WordDiffStatistics;
}

export interface WordDiffOperation {
  type: 'insert' | 'delete' | 'equal';
  word: string;
  position: number;
}

export interface WordDiffStatistics {
  totalWords: number;
  addedWords: number;
  deletedWords: number;
  unchangedWords: number;
  similarity: number;
}

export interface CharDiffResult {
  chars: CharDiffOperation[];
  statistics: CharDiffStatistics;
}

export interface CharDiffOperation {
  type: 'insert' | 'delete' | 'equal';
  char: string;
  position: number;
}

export interface CharDiffStatistics {
  totalChars: number;
  addedChars: number;
  deletedChars: number;
  unchangedChars: number;
  similarity: number;
}

interface DiffCacheEntry {
  key: string;
  result: any;
  timestamp: number;
  accessCount: number;
}

export class DiffEngine {
  private config: Required<DiffEngineConfig>;
  private diffCache: Map<string, DiffCacheEntry>;
  private readonly maxCacheSize: number;

  constructor(config: DiffEngineConfig = {}) {
    this.config = {
      textDiffAlgorithm: config.textDiffAlgorithm || 'myers',
      contextLines: config.contextLines || 3,
      ignoreWhitespace: config.ignoreWhitespace || false,
      ignoreCase: config.ignoreCase || false,
      maxDiffSize: config.maxDiffSize || 10000000, // 10MB
      enableVisualDiff: config.enableVisualDiff ?? true,
      visualDiffThreshold: config.visualDiffThreshold || 0.1,
      enableCaching: config.enableCaching ?? true,
      cacheSize: config.cacheSize || 1000,
      enableWordDiff: config.enableWordDiff ?? true,
      enableCharDiff: config.enableCharDiff ?? false,
    };

    this.maxCacheSize = this.config.cacheSize;
    this.diffCache = new Map();
  }

  /**
   * Generate a cache key for diff operations
   */
  private generateCacheKey(before: string, after: string, algorithm: string): string {
    const content = `${before}|${after}|${algorithm}`;
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  /**
   * Get cached diff result
   */
  private getCachedResult(key: string): any | null {
    if (!this.config.enableCaching) return null;

    const entry = this.diffCache.get(key);
    if (entry) {
      entry.accessCount++;
      entry.timestamp = Date.now();
      return entry.result;
    }
    return null;
  }

  /**
   * Cache diff result
   */
  private setCachedResult(key: string, result: any): void {
    if (!this.config.enableCaching) return;

    // Implement LRU cache eviction
    if (this.diffCache.size >= this.maxCacheSize) {
      this.evictLeastRecentlyUsed();
    }

    this.diffCache.set(key, {
      key,
      result,
      timestamp: Date.now(),
      accessCount: 1,
    });
  }

  /**
   * Evict least recently used cache entries
   */
  private evictLeastRecentlyUsed(): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, entry] of this.diffCache) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.diffCache.delete(oldestKey);
    }
  }

  /**
   * Myers diff algorithm implementation
   * Based on "An O(ND) Difference Algorithm and Its Variations" by Eugene W. Myers
   */
  public myersDiff(textA: string, textB: string): MyersDiffResult {
    const cacheKey = this.generateCacheKey(textA, textB, 'myers');
    const cached = this.getCachedResult(cacheKey);
    if (cached) return cached;

    const linesA = this.preprocessText(textA).split('\n');
    const linesB = this.preprocessText(textB).split('\n');

    const n = linesA.length;
    const m = linesB.length;
    const max = n + m;

    // V array for storing furthest reaching D-paths
    const v: number[] = new Array(2 * max + 1).fill(0);
    v[1] = 0;

    // Trace array for backtracking
    const trace: number[][] = [];

    let d = 0;
    for (d = 0; d <= max; d++) {
      trace[d] = [...v];

      for (let k = -d; k <= d; k += 2) {
        let x: number;

        if (k === -d || (k !== d && (v[k - 1] || 0) < (v[k + 1] || 0))) {
          x = v[k + 1] || 0;
        } else {
          x = (v[k - 1] || 0) + 1;
        }

        let y = x - k;

        // Extend diagonal as far as possible
        while (x < n && y < m && linesA[x] === linesB[y]) {
          x++;
          y++;
        }

        v[k] = x;

        if (x >= n && y >= m) {
          // Found the shortest edit script
          const operations = this.backtrackMyersDiff(linesA, linesB, trace, d);
          const similarity = this.calculateSimilarity(operations, n + m);

          const result: MyersDiffResult = {
            operations,
            editDistance: d,
            similarity,
          };

          this.setCachedResult(cacheKey, result);
          return result;
        }
      }
    }

    // Fallback - should not reach here with valid input
    const result: MyersDiffResult = {
      operations: [],
      editDistance: max,
      similarity: 0,
    };

    this.setCachedResult(cacheKey, result);
    return result;
  }

  /**
   * Backtrack through the Myers diff trace to construct the edit operations
   */
  private backtrackMyersDiff(
    linesA: string[],
    linesB: string[],
    trace: number[][],
    d: number
  ): DiffOperation[] {
    const operations: DiffOperation[] = [];
    let x = linesA.length;
    let y = linesB.length;

    for (let depth = d; depth > 0; depth--) {
      const v = trace[depth];
      const k = x - y;

      let prevK: number;
      if (k === -depth || (k !== depth && (v[k - 1] || 0) < (v[k + 1] || 0))) {
        prevK = k + 1;
      } else {
        prevK = k - 1;
      }

      const prevX = v[prevK] || 0;
      const prevY = prevX - prevK;

      // Add diagonal moves (equal lines)
      while (x > prevX && y > prevY) {
        x--;
        y--;
        operations.unshift({
          type: 'equal',
          text: linesA[x] || '',
        });
      }

      if (depth > 0) {
        if (x > prevX) {
          // Deletion
          x--;
          operations.unshift({
            type: 'delete',
            text: linesA[x] || '',
          });
        } else {
          // Insertion
          y--;
          operations.unshift({
            type: 'insert',
            text: linesB[y] || '',
          });
        }
      }
    }

    return operations;
  }

  /**
   * Word-level diff implementation
   */
  public wordDiff(textA: string, textB: string): WordDiffResult {
    const cacheKey = this.generateCacheKey(textA, textB, 'word');
    const cached = this.getCachedResult(cacheKey);
    if (cached) return cached;

    const wordsA = this.tokenizeWords(textA);
    const wordsB = this.tokenizeWords(textB);

    const myersResult = this.myersDiff(wordsA.join('\n'), wordsB.join('\n'));

    const words: WordDiffOperation[] = [];
    let positionA = 0;
    let positionB = 0;

    for (const op of myersResult.operations) {
      switch (op.type) {
        case 'equal':
          words.push({
            type: 'equal',
            word: op.text,
            position: positionA,
          });
          positionA++;
          positionB++;
          break;
        case 'delete':
          words.push({
            type: 'delete',
            word: op.text,
            position: positionA,
          });
          positionA++;
          break;
        case 'insert':
          words.push({
            type: 'insert',
            word: op.text,
            position: positionB,
          });
          positionB++;
          break;
      }
    }

    const statistics: WordDiffStatistics = {
      totalWords: words.length,
      addedWords: words.filter(w => w.type === 'insert').length,
      deletedWords: words.filter(w => w.type === 'delete').length,
      unchangedWords: words.filter(w => w.type === 'equal').length,
      similarity: myersResult.similarity,
    };

    const result: WordDiffResult = { words, statistics };
    this.setCachedResult(cacheKey, result);
    return result;
  }

  /**
   * Character-level diff implementation
   */
  public charDiff(textA: string, textB: string): CharDiffResult {
    const cacheKey = this.generateCacheKey(textA, textB, 'char');
    const cached = this.getCachedResult(cacheKey);
    if (cached) return cached;

    const charsA = textA.split('');
    const charsB = textB.split('');

    const myersResult = this.myersDiff(charsA.join('\n'), charsB.join('\n'));

    const chars: CharDiffOperation[] = [];
    let positionA = 0;
    let positionB = 0;

    for (const op of myersResult.operations) {
      switch (op.type) {
        case 'equal':
          chars.push({
            type: 'equal',
            char: op.text,
            position: positionA,
          });
          positionA++;
          positionB++;
          break;
        case 'delete':
          chars.push({
            type: 'delete',
            char: op.text,
            position: positionA,
          });
          positionA++;
          break;
        case 'insert':
          chars.push({
            type: 'insert',
            char: op.text,
            position: positionB,
          });
          positionB++;
          break;
      }
    }

    const statistics: CharDiffStatistics = {
      totalChars: chars.length,
      addedChars: chars.filter(c => c.type === 'insert').length,
      deletedChars: chars.filter(c => c.type === 'delete').length,
      unchangedChars: chars.filter(c => c.type === 'equal').length,
      similarity: myersResult.similarity,
    };

    const result: CharDiffResult = { chars, statistics };
    this.setCachedResult(cacheKey, result);
    return result;
  }

  /**
   * Tokenize text into words
   */
  private tokenizeWords(text: string): string[] {
    // Split on whitespace and punctuation, but keep the delimiters
    return text
      .split(/(\s+|[.,;:!?()[\]{}'"<>\/\\|`~@#$%^&*+=_-])/)
      .filter(word => word.length > 0);
  }

  /**
   * Preprocess text based on configuration
   */
  private preprocessText(text: string): string {
    let processed = text;

    if (this.config.ignoreCase) {
      processed = processed.toLowerCase();
    }

    if (this.config.ignoreWhitespace) {
      processed = processed.replace(/\s+/g, ' ').trim();
    }

    return processed;
  }

  /**
   * Calculate similarity percentage from diff operations
   */
  private calculateSimilarity(operations: DiffOperation[], totalLength: number): number {
    if (totalLength === 0) return 1.0;

    const equalLength = operations
      .filter(op => op.type === 'equal')
      .reduce((sum, op) => sum + op.text.length, 0);

    return equalLength / totalLength;
  }

  async createDiff(before: ApplicationState, after: ApplicationState): Promise<DiffResult> {
    const beforeCheckpoint = before.id;
    const afterCheckpoint = after.id;

    try {
      const changes: ChangeSet[] = [];

      // Compare documents
      const documentChanges = await this.compareDocuments(before.documents, after.documents);
      changes.push(...documentChanges);

      // Compare knowledge base
      const knowledgeChanges = await this.compareKnowledgeBase(
        before.knowledgeBase,
        after.knowledgeBase
      );
      changes.push(...knowledgeChanges);

      // Compare UI state
      const uiChanges = await this.compareUIState(before.userInterface, after.userInterface);
      changes.push(...uiChanges);

      // Compare AI context
      const aiChanges = await this.compareAIContext(before.aiContext, after.aiContext);
      changes.push(...aiChanges);

      // Compare configuration
      const configChanges = await this.compareConfiguration(
        before.configuration,
        after.configuration
      );
      changes.push(...configChanges);

      const statistics = this.calculateDiffStatistics(changes);

      const diffResult: DiffResult = {
        id: uuidv4(),
        beforeCheckpoint,
        afterCheckpoint,
        changes,
        statistics,
        createdAt: new Date(),
      };

      return diffResult;
    } catch (error) {
      throw new Error(
        `Failed to create diff: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async createVisualDiff(before: any, after: any): Promise<VisualDiff> {
    const startTime = Date.now();

    try {
      // Determine the type of visual diff based on input
      let diffType: VisualDiffType;
      let textDiff: TextDiff | undefined;
      let structuralDiff: StructuralDiff | undefined;

      if (typeof before === 'string' && typeof after === 'string') {
        diffType = VisualDiffType.TEXT;
        textDiff = await this.createTextDiff(before, after);
      } else if (this.isImageData(before) && this.isImageData(after)) {
        diffType = VisualDiffType.IMAGE;
        // Image diff would be implemented here
      } else if (this.isPDFData(before) && this.isPDFData(after)) {
        diffType = VisualDiffType.PDF;
        // PDF diff would be implemented here
      } else {
        diffType = VisualDiffType.STRUCTURAL;
        structuralDiff = await this.createStructuralDiff(before, after);
      }

      const processingTime = Date.now() - startTime;

      const visualDiff: VisualDiff = {
        id: uuidv4(),
        type: diffType,
        ...(textDiff && { textDiff }),
        ...(structuralDiff && { structuralDiff }),
        metadata: {
          algorithm: this.config.textDiffAlgorithm,
          threshold: this.config.visualDiffThreshold,
          processingTime,
          accuracy: 0.95, // This would be calculated based on the diff algorithm
          createdAt: new Date(),
        },
      };

      return visualDiff;
    } catch (error) {
      throw new Error(
        `Failed to create visual diff: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private async createTextDiff(before: string, after: string): Promise<TextDiff> {
    // Use Myers algorithm for optimal text comparison
    const myersResult = this.myersDiff(before, after);

    const diffLines: TextDiffLine[] = [];
    let beforeLineNumber = 1;
    let afterLineNumber = 1;
    let lineNumber = 1;

    for (const operation of myersResult.operations) {
      switch (operation.type) {
        case 'equal':
          diffLines.push({
            lineNumber: lineNumber++,
            type: TextDiffLineType.UNCHANGED,
            content: operation.text,
            beforeLineNumber: beforeLineNumber++,
            afterLineNumber: afterLineNumber++,
          });
          break;
        case 'delete':
          diffLines.push({
            lineNumber: lineNumber++,
            type: TextDiffLineType.DELETED,
            content: operation.text,
            beforeLineNumber: beforeLineNumber++,
          });
          break;
        case 'insert':
          diffLines.push({
            lineNumber: lineNumber++,
            type: TextDiffLineType.ADDED,
            content: operation.text,
            afterLineNumber: afterLineNumber++,
          });
          break;
      }
    }

    // Add context lines if configured
    if (this.config.contextLines > 0) {
      this.addContextLines(diffLines);
    }

    const statistics: TextDiffStatistics = {
      totalLines: diffLines.length,
      addedLines: diffLines.filter(line => line.type === TextDiffLineType.ADDED).length,
      deletedLines: diffLines.filter(line => line.type === TextDiffLineType.DELETED).length,
      modifiedLines: diffLines.filter(line => line.type === TextDiffLineType.MODIFIED).length,
      unchangedLines: diffLines.filter(line => line.type === TextDiffLineType.UNCHANGED).length,
    };

    return {
      lines: diffLines,
      statistics,
    };
  }

  /**
   * Add context lines around changes for better readability
   */
  private addContextLines(diffLines: TextDiffLine[]): void {
    const contextLines = this.config.contextLines;
    const result: TextDiffLine[] = [];

    for (let i = 0; i < diffLines.length; i++) {
      const line = diffLines[i];

      if (line && line.type !== TextDiffLineType.UNCHANGED) {
        // Add context before
        const startContext = Math.max(0, i - contextLines);
        for (let j = startContext; j < i; j++) {
          const contextLine = diffLines[j];
          if (
            contextLine &&
            contextLine.type === TextDiffLineType.UNCHANGED &&
            !result.includes(contextLine)
          ) {
            result.push(contextLine);
          }
        }

        // Add the changed line
        result.push(line);

        // Add context after
        const endContext = Math.min(diffLines.length, i + contextLines + 1);
        for (let j = i + 1; j < endContext; j++) {
          const contextLine = diffLines[j];
          if (
            contextLine &&
            contextLine.type === TextDiffLineType.UNCHANGED &&
            !result.includes(contextLine)
          ) {
            result.push(contextLine);
          }
        }
      }
    }

    // Replace original array with context-enhanced version
    diffLines.splice(0, diffLines.length, ...result);
  }

  private async createStructuralDiff(before: any, after: any): Promise<StructuralDiff> {
    const elements: StructuralDiffElement[] = [];

    // Deep comparison of object structures
    const beforeKeys = Object.keys(before || {});
    const afterKeys = Object.keys(after || {});
    const allKeys = new Set([...beforeKeys, ...afterKeys]);

    for (const key of allKeys) {
      const beforeValue = before?.[key];
      const afterValue = after?.[key];

      if (beforeValue === undefined && afterValue !== undefined) {
        elements.push({
          id: uuidv4(),
          type: typeof afterValue,
          changeType: ChangeType.ADDED,
          path: key,
          afterProperties: { [key]: afterValue },
        });
      } else if (beforeValue !== undefined && afterValue === undefined) {
        elements.push({
          id: uuidv4(),
          type: typeof beforeValue,
          changeType: ChangeType.DELETED,
          path: key,
          beforeProperties: { [key]: beforeValue },
        });
      } else if (beforeValue !== afterValue) {
        elements.push({
          id: uuidv4(),
          type: typeof afterValue,
          changeType: ChangeType.MODIFIED,
          path: key,
          beforeProperties: { [key]: beforeValue },
          afterProperties: { [key]: afterValue },
        });
      }
    }

    const statistics: StructuralDiffStatistics = {
      totalElements: elements.length,
      addedElements: elements.filter(el => el.changeType === ChangeType.ADDED).length,
      modifiedElements: elements.filter(el => el.changeType === ChangeType.MODIFIED).length,
      deletedElements: elements.filter(el => el.changeType === ChangeType.DELETED).length,
      movedElements: elements.filter(el => el.changeType === ChangeType.MOVED).length,
    };

    return {
      elements,
      statistics,
    };
  }

  private async compareDocuments(before: any[], after: any[]): Promise<ChangeSet[]> {
    const changes: ChangeSet[] = [];

    // Create maps for efficient lookup
    const beforeMap = new Map(before.map(doc => [doc.documentId, doc]));
    const afterMap = new Map(after.map(doc => [doc.documentId, doc]));

    // Find added documents
    for (const [id, doc] of afterMap) {
      if (!beforeMap.has(id)) {
        changes.push({
          type: ChangeType.ADDED,
          path: `documents.${id}`,
          beforeValue: undefined,
          afterValue: doc,
          confidence: 1.0,
          description: `Document ${id} was added`,
        });
      }
    }

    // Find deleted documents
    for (const [id, doc] of beforeMap) {
      if (!afterMap.has(id)) {
        changes.push({
          type: ChangeType.DELETED,
          path: `documents.${id}`,
          beforeValue: doc,
          afterValue: undefined,
          confidence: 1.0,
          description: `Document ${id} was deleted`,
        });
      }
    }

    // Find modified documents
    for (const [id, beforeDoc] of beforeMap) {
      const afterDoc = afterMap.get(id);
      if (afterDoc && JSON.stringify(beforeDoc) !== JSON.stringify(afterDoc)) {
        changes.push({
          type: ChangeType.MODIFIED,
          path: `documents.${id}`,
          beforeValue: beforeDoc,
          afterValue: afterDoc,
          confidence: 1.0,
          description: `Document ${id} was modified`,
        });
      }
    }

    return changes;
  }

  private async compareKnowledgeBase(before: any, after: any): Promise<ChangeSet[]> {
    const changes: ChangeSet[] = [];

    if (JSON.stringify(before) !== JSON.stringify(after)) {
      changes.push({
        type: ChangeType.MODIFIED,
        path: 'knowledgeBase',
        beforeValue: before,
        afterValue: after,
        confidence: 0.9,
        description: 'Knowledge base was modified',
      });
    }

    return changes;
  }

  private async compareUIState(before: any, after: any): Promise<ChangeSet[]> {
    const changes: ChangeSet[] = [];

    if (JSON.stringify(before) !== JSON.stringify(after)) {
      changes.push({
        type: ChangeType.MODIFIED,
        path: 'userInterface',
        beforeValue: before,
        afterValue: after,
        confidence: 0.8,
        description: 'UI state was modified',
      });
    }

    return changes;
  }

  private async compareAIContext(before: any, after: any): Promise<ChangeSet[]> {
    const changes: ChangeSet[] = [];

    if (JSON.stringify(before) !== JSON.stringify(after)) {
      changes.push({
        type: ChangeType.MODIFIED,
        path: 'aiContext',
        beforeValue: before,
        afterValue: after,
        confidence: 0.9,
        description: 'AI context was modified',
      });
    }

    return changes;
  }

  private async compareConfiguration(before: any, after: any): Promise<ChangeSet[]> {
    const changes: ChangeSet[] = [];

    if (JSON.stringify(before) !== JSON.stringify(after)) {
      changes.push({
        type: ChangeType.MODIFIED,
        path: 'configuration',
        beforeValue: before,
        afterValue: after,
        confidence: 1.0,
        description: 'Configuration was modified',
      });
    }

    return changes;
  }

  private calculateDiffStatistics(changes: ChangeSet[]): DiffStatistics {
    return {
      totalChanges: changes.length,
      addedItems: changes.filter(c => c.type === ChangeType.ADDED).length,
      modifiedItems: changes.filter(c => c.type === ChangeType.MODIFIED).length,
      deletedItems: changes.filter(c => c.type === ChangeType.DELETED).length,
      movedItems: changes.filter(c => c.type === ChangeType.MOVED).length,
      renamedItems: changes.filter(c => c.type === ChangeType.RENAMED).length,
    };
  }

  private isImageData(data: any): data is ImageData {
    return (
      data && typeof data === 'object' && 'width' in data && 'height' in data && 'data' in data
    );
  }

  private isPDFData(data: any): boolean {
    return data && typeof data === 'object' && data.type === 'pdf';
  }
}

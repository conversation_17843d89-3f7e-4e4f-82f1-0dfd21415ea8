import { beforeEach, describe, expect, it, jest } from '@jest/globals';
import { PDFProcessor } from '../../main/services/PDFProcessor';
import { ProcessingOptions } from '../../shared/types/Document';

// Mock PDF.js
jest.mock('pdfjs-dist', () => ({
  GlobalWorkerOptions: { workerSrc: '' },
  getDocument: jest.fn(() => ({
    promise: Promise.resolve({
      numPages: 2,
      getPage: jest.fn(pageNum =>
        Promise.resolve({
          getTextContent: jest.fn(() =>
            Promise.resolve({
              items: [
                { str: 'Sample text', transform: [1, 0, 0, 1, 100, 200], width: 50, height: 12 },
                { str: 'More text', transform: [1, 0, 0, 1, 150, 180], width: 40, height: 12 },
              ],
            })
          ),
          getViewport: jest.fn(({ scale }) => ({
            width: 612 * scale,
            height: 792 * scale,
            rotation: 0,
          })),
          render: jest.fn(() => ({ promise: Promise.resolve() })),
          getAnnotations: jest.fn(() =>
            Promise.resolve([
              {
                subtype: 'Widget',
                fieldName: 'testField',
                fieldType: 'Tx',
                fieldValue: 'test value',
                rect: [100, 200, 200, 220],
              },
            ])
          ),
        })
      ),
      getMetadata: jest.fn(() =>
        Promise.resolve({
          info: {
            Title: 'Test PDF',
            Author: 'Test Author',
            CreationDate: new Date('2023-01-01'),
          },
        })
      ),
      destroy: jest.fn(),
    }),
  })),
}));

// Mock pdf-lib
jest.mock('pdf-lib', () => ({
  PDFDocument: {
    load: jest.fn(() =>
      Promise.resolve({
        getPages: jest.fn(() => [
          {
            node: { Annots: [] },
            drawText: jest.fn(),
            drawRectangle: jest.fn(),
            setRotation: jest.fn(),
          },
        ]),
        getForm: jest.fn(() => ({
          getFields: jest.fn(() => []),
          flatten: jest.fn(),
        })),
        save: jest.fn(() => Promise.resolve(new Uint8Array([1, 2, 3, 4]))),
        getPageCount: jest.fn(() => 2),
        getPageIndices: jest.fn(() => [0, 1]),
        embedFont: jest.fn(() => Promise.resolve({})),
      })
    ),
    create: jest.fn(() =>
      Promise.resolve({
        addPage: jest.fn(),
        copyPages: jest.fn(() => Promise.resolve([])),
        save: jest.fn(() => Promise.resolve(new Uint8Array([1, 2, 3, 4]))),
      })
    ),
  },
  rgb: jest.fn((r, g, b) => ({ r, g, b })),
  StandardFonts: {
    Helvetica: 'Helvetica',
    HelveticaBold: 'Helvetica-Bold',
  },
}));

// Mock canvas
jest.mock('canvas', () => ({
  createCanvas: jest.fn((width, height) => ({
    width,
    height,
    getContext: jest.fn(() => ({
      fillStyle: '',
      fillRect: jest.fn(),
      drawImage: jest.fn(),
    })),
    toBuffer: jest.fn((format, options) => Buffer.from('mock-image-data')),
  })),
}));

describe('PDFProcessor', () => {
  let pdfProcessor: PDFProcessor;
  let mockPDFBuffer: Buffer;

  beforeEach(() => {
    pdfProcessor = new PDFProcessor();
    mockPDFBuffer = Buffer.from('mock-pdf-data');
    jest.clearAllMocks();
  });

  describe('Constructor', () => {
    it('should create PDFProcessor with correct capabilities', () => {
      expect(pdfProcessor).toBeInstanceOf(PDFProcessor);
      // Test that it extends DocumentProcessor correctly
    });
  });

  describe('Text Extraction', () => {
    it('should extract text from PDF', async () => {
      const options: ProcessingOptions = {
        preserveFormatting: false,
        detectFormFields: false,
      };

      const text = await pdfProcessor.extractText(mockPDFBuffer, options);

      expect(text).toBe('Sample text More text');
      expect(text).toContain('Sample text');
      expect(text).toContain('More text');
    });

    it('should extract text with coordinates', async () => {
      const textWithCoords = await pdfProcessor.extractTextWithCoordinates(mockPDFBuffer);

      expect(textWithCoords).toHaveLength(2);
      expect(textWithCoords[0]).toEqual({
        text: 'Sample text',
        x: 100,
        y: 200,
        width: 50,
        height: 12,
        fontSize: 1,
        fontFamily: 'Unknown',
        pageNumber: 1,
      });
    });

    it('should handle empty PDF gracefully', async () => {
      // Mock empty PDF
      const mockEmptyPDF = {
        numPages: 0,
        destroy: jest.fn(),
      };

      jest.mocked(require('pdfjs-dist').getDocument).mockReturnValueOnce({
        promise: Promise.resolve(mockEmptyPDF),
      });

      const text = await pdfProcessor.extractText(mockPDFBuffer);
      expect(text).toBe('');
    });
  });

  describe('Structured Data Extraction', () => {
    it('should extract structured data with metadata', async () => {
      const options: ProcessingOptions = {
        preserveFormatting: true,
        detectFormFields: true,
      };

      const extractedData = await pdfProcessor.extractStructuredData(mockPDFBuffer, options);

      expect(extractedData).toHaveLength(3); // metadata, form_fields, text_coordinates

      const metadataEntry = extractedData.find(data => data.id === 'metadata');
      expect(metadataEntry).toBeDefined();
      expect(metadataEntry?.type).toBe('TEXT');

      const parsedMetadata = JSON.parse(metadataEntry?.content as string);
      expect(parsedMetadata.title).toBe('Test PDF');
      expect(parsedMetadata.author).toBe('Test Author');
      expect(parsedMetadata.pageCount).toBe(2);
    });

    it('should extract form fields when requested', async () => {
      const options: ProcessingOptions = {
        detectFormFields: true,
      };

      const extractedData = await pdfProcessor.extractStructuredData(mockPDFBuffer, options);

      const formFieldsEntry = extractedData.find(data => data.id === 'form_fields');
      expect(formFieldsEntry).toBeDefined();
      expect(formFieldsEntry?.type).toBe('FORM_FIELD');
    });
  });

  describe('Form Field Processing', () => {
    it('should get detailed form field information', async () => {
      const formFields = await pdfProcessor.getFormFieldDetails(mockPDFBuffer);

      expect(formFields).toHaveLength(1);
      expect(formFields[0]).toEqual({
        name: 'testField',
        type: 'text',
        value: 'test value',
        required: false,
        readOnly: false,
        bounds: {
          x: 100,
          y: expect.any(Number), // Y coordinate is flipped
          width: 100,
          height: 20,
          pageNumber: 1,
        },
      });
    });

    it('should validate form fields', async () => {
      const formFields = await pdfProcessor.getFormFieldDetails(mockPDFBuffer);
      const validation = await pdfProcessor.validateFormFields(formFields);

      expect(validation.valid).toHaveLength(1);
      expect(validation.invalid).toHaveLength(0);
    });

    it('should detect invalid form fields', async () => {
      const invalidFormFields = [
        {
          name: '',
          type: 'text' as const,
          value: '',
          required: false,
          readOnly: false,
          bounds: {
            x: 0,
            y: 0,
            width: -10, // Invalid width
            height: 0,
            pageNumber: 0, // Invalid page
          },
        },
      ];

      const validation = await pdfProcessor.validateFormFields(invalidFormFields);

      expect(validation.valid).toHaveLength(0);
      expect(validation.invalid).toHaveLength(1);
      expect(validation.invalid[0].errors).toContain('Field name is required');
      expect(validation.invalid[0].errors).toContain('Invalid field bounds');
      expect(validation.invalid[0].errors).toContain('Invalid page number');
    });

    it('should extract form field coordinates', async () => {
      const coordinates = await pdfProcessor.extractFormFieldCoordinates(mockPDFBuffer);

      expect(coordinates).toHaveLength(1);
      expect(coordinates[0]).toEqual({
        name: 'testField',
        type: 'text',
        coordinates: {
          x: 100,
          y: expect.any(Number),
          width: 100,
          height: 20,
          pageNumber: 1,
        },
        properties: {
          required: false,
          readOnly: false,
        },
      });
    });

    it('should detect form field types using heuristics', async () => {
      const detections = await pdfProcessor.detectFormFieldTypes(mockPDFBuffer);

      expect(detections).toHaveLength(1);
      expect(detections[0]).toEqual({
        name: 'testField',
        detectedType: 'text',
        confidence: expect.any(Number),
        reasoning: expect.any(String),
      });
    });
  });

  describe('Validation', () => {
    it('should validate extracted data', async () => {
      const extractedData = await pdfProcessor.extractStructuredData(mockPDFBuffer, {});
      const validationResults = await pdfProcessor.validateExtraction(extractedData);

      expect(validationResults).toHaveLength(extractedData.length);
      validationResults.forEach(result => {
        expect(result).toHaveProperty('fieldId');
        expect(result).toHaveProperty('isValid');
        expect(result).toHaveProperty('errors');
        expect(result).toHaveProperty('warnings');
      });
    });

    it('should detect validation errors in metadata', async () => {
      // Mock invalid metadata
      const invalidExtractedData = [
        {
          id: 'metadata',
          documentId: '',
          type: 'TEXT' as const,
          content: JSON.stringify({ pageCount: 0 }), // Invalid page count
          confidence: 1.0,
          extractionMethod: 'PDF_PARSER' as const,
          createdAt: new Date(),
        },
      ];

      const validationResults = await pdfProcessor.validateExtraction(invalidExtractedData);

      expect(validationResults[0].isValid).toBe(false);
      expect(validationResults[0].errors).toHaveLength(1);
      expect(validationResults[0].errors[0].code).toBe('INVALID_PAGE_COUNT');
    });
  });

  describe('PDF Generation with PDFKit', () => {
    it('should create a new PDF document', () => {
      const doc = pdfProcessor.createPDF({
        title: 'Test Document',
        author: 'Test Author',
        pageSize: 'A4',
      });

      expect(doc).toBeDefined();
    });

    it('should add text to PDF document', () => {
      const doc = pdfProcessor.createPDF();
      const result = pdfProcessor.addTextToPDF(doc, 'Hello World', 100, 100, {
        fontSize: 12,
        color: 'black',
      });

      expect(result).toBe(doc);
    });

    it('should add form fields to PDF document', () => {
      const doc = pdfProcessor.createPDF();
      const result = pdfProcessor.addFormFieldToPDF(doc, {
        name: 'testField',
        type: 'text',
        x: 100,
        y: 100,
        width: 200,
        height: 20,
        value: 'test value',
      });

      expect(result).toBe(doc);
    });

    it('should fill PDF form with data', async () => {
      const formData = {
        testField: 'filled value',
        checkboxField: true,
      };

      const result = await pdfProcessor.fillPDFForm(mockPDFBuffer, formData);
      expect(result).toBeInstanceOf(Buffer);
    });

    it('should add annotations to PDF', () => {
      const doc = pdfProcessor.createPDF();
      const result = pdfProcessor.addAnnotationToPDF(doc, {
        type: 'highlight',
        x: 100,
        y: 100,
        width: 200,
        height: 20,
        color: '#FFFF00',
        opacity: 0.5,
      });

      expect(result).toBe(doc);
    });

    it('should merge multiple PDFs', async () => {
      const buffers = [mockPDFBuffer, mockPDFBuffer];
      const result = await pdfProcessor.mergePDFs(buffers);

      expect(result).toBeInstanceOf(Buffer);
    });

    it('should split PDF into pages', async () => {
      const result = await pdfProcessor.splitPDF(mockPDFBuffer);

      expect(result).toHaveLength(2); // Mock PDF has 2 pages
      result.forEach(pageBuffer => {
        expect(pageBuffer).toBeInstanceOf(Buffer);
      });
    });
  });

  describe('Advanced PDF Operations with pdf-lib', () => {
    it('should extract annotations from PDF', async () => {
      const annotations = await pdfProcessor.extractAnnotations(mockPDFBuffer);

      expect(annotations).toBeInstanceOf(Array);
      // Note: Mock returns empty annotations, but structure should be correct
    });

    it('should add advanced annotations', async () => {
      const result = await pdfProcessor.addAdvancedAnnotation(mockPDFBuffer, {
        type: 'text',
        page: 1,
        x: 100,
        y: 100,
        width: 200,
        height: 20,
        content: 'Test annotation',
        color: { r: 255, g: 255, b: 0 },
      });

      expect(result).toBeInstanceOf(Buffer);
    });

    it('should fill PDF form with advanced method', async () => {
      const formData = {
        textField: 'test value',
        checkboxField: true,
        numberField: 42,
      };

      const result = await pdfProcessor.fillPDFFormAdvanced(mockPDFBuffer, formData);
      expect(result).toBeInstanceOf(Buffer);
    });

    it('should add signature placeholder', async () => {
      const result = await pdfProcessor.addSignaturePlaceholder(mockPDFBuffer, {
        page: 1,
        x: 100,
        y: 100,
        width: 200,
        height: 80,
        signerName: 'John Doe',
        reason: 'Document approval',
        location: 'New York',
      });

      expect(result).toBeInstanceOf(Buffer);
    });

    it('should merge PDFs with advanced method', async () => {
      const buffers = [mockPDFBuffer, mockPDFBuffer];
      const result = await pdfProcessor.mergePDFsAdvanced(buffers);

      expect(result).toBeInstanceOf(Buffer);
    });

    it('should extract specific pages', async () => {
      const result = await pdfProcessor.extractPages(mockPDFBuffer, [1, 2]);

      expect(result).toBeInstanceOf(Buffer);
    });

    it('should rotate pages', async () => {
      const result = await pdfProcessor.rotatePages(mockPDFBuffer, [
        { page: 1, degrees: 90 },
        { page: 2, degrees: 180 },
      ]);

      expect(result).toBeInstanceOf(Buffer);
    });

    it('should handle invalid page numbers in advanced operations', async () => {
      await expect(
        pdfProcessor.addAdvancedAnnotation(mockPDFBuffer, {
          type: 'text',
          page: 999,
          x: 100,
          y: 100,
          width: 200,
          height: 20,
          content: 'Test',
        })
      ).rejects.toThrow('Invalid page number: 999');
    });
  });

  describe('PDF to Image Conversion', () => {
    it('should convert PDF page to image', async () => {
      const result = await pdfProcessor.convertPageToImage(mockPDFBuffer, 1, {
        scale: 2.0,
        format: 'png',
      });

      expect(result).toBeInstanceOf(Buffer);
    });

    it('should convert all pages to images', async () => {
      const result = await pdfProcessor.convertAllPagesToImages(mockPDFBuffer, {
        scale: 1.5,
        format: 'jpeg',
        maxPages: 5,
      });

      expect(result).toHaveLength(2); // Mock PDF has 2 pages
      result.forEach(image => {
        expect(image).toHaveProperty('pageNumber');
        expect(image).toHaveProperty('imageBuffer');
        expect(image).toHaveProperty('width');
        expect(image).toHaveProperty('height');
        expect(image.imageBuffer).toBeInstanceOf(Buffer);
      });
    });

    it('should generate thumbnail', async () => {
      const result = await pdfProcessor.generateThumbnail(mockPDFBuffer, 1, {
        maxWidth: 150,
        maxHeight: 200,
        format: 'jpeg',
      });

      expect(result).toBeInstanceOf(Buffer);
    });

    it('should extract images for OCR', async () => {
      const result = await pdfProcessor.extractImagesForOCR(mockPDFBuffer, {
        scale: 3.0,
        pageRange: { start: 1, end: 2 },
      });

      expect(result).toHaveLength(2);
      result.forEach(image => {
        expect(image).toHaveProperty('pageNumber');
        expect(image).toHaveProperty('imageBuffer');
        expect(image).toHaveProperty('textRegions');
        expect(image.textRegions).toBeInstanceOf(Array);
      });
    });

    it('should get page dimensions', async () => {
      const dimensions = await pdfProcessor.getPageDimensions(mockPDFBuffer);

      expect(dimensions).toHaveLength(2);
      dimensions.forEach(dim => {
        expect(dim).toHaveProperty('pageNumber');
        expect(dim).toHaveProperty('width');
        expect(dim).toHaveProperty('height');
        expect(dim).toHaveProperty('rotation');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle PDF loading errors', async () => {
      jest.mocked(require('pdfjs-dist').getDocument).mockReturnValueOnce({
        promise: Promise.reject(new Error('Invalid PDF')),
      });

      await expect(pdfProcessor.extractText(mockPDFBuffer)).rejects.toThrow(
        'Failed to extract text from PDF'
      );
    });

    it('should handle invalid page numbers', async () => {
      await expect(pdfProcessor.convertPageToImage(mockPDFBuffer, 999)).rejects.toThrow(
        'Invalid page number: 999'
      );
    });

    it('should handle canvas creation errors gracefully', async () => {
      // Mock canvas creation failure
      jest.mocked(require('canvas').createCanvas).mockImplementationOnce(() => {
        throw new Error('Canvas not available');
      });

      // Should fall back to mock canvas
      const result = await pdfProcessor.convertPageToImage(mockPDFBuffer, 1);
      expect(result).toBeInstanceOf(Buffer);
    });

    it('should handle pdf-lib loading errors', async () => {
      jest
        .mocked(require('pdf-lib').PDFDocument.load)
        .mockRejectedValueOnce(new Error('Invalid PDF format'));

      await expect(pdfProcessor.extractAnnotations(mockPDFBuffer)).rejects.toThrow(
        'Failed to extract annotations'
      );
    });

    it('should handle form filling errors gracefully', async () => {
      const mockForm = {
        getFields: jest.fn(() => [
          {
            getName: jest.fn(() => 'testField'),
            constructor: { name: 'PDFTextField' },
            setText: jest.fn(() => {
              throw new Error('Field error');
            }),
          },
        ]),
        flatten: jest.fn(),
      };

      jest.mocked(require('pdf-lib').PDFDocument.load).mockResolvedValueOnce({
        getForm: jest.fn(() => mockForm),
        save: jest.fn(() => Promise.resolve(new Uint8Array([1, 2, 3]))),
      });

      // Should not throw, but log warning
      const result = await pdfProcessor.fillPDFFormAdvanced(mockPDFBuffer, {
        testField: 'value',
      });

      expect(result).toBeInstanceOf(Buffer);
    });
  });
});

import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create knowledge_base table
  await knex.schema.createTable('knowledge_base', (table) => {
    // Primary key
    table.string('id', 36).primary().notNullable().comment('UUID primary key');

    // Knowledge categorization
    table.string('category', 100).notNullable().comment('Knowledge category (person, organization, date, amount, etc.)');
    table.string('key_name', 255).notNullable().comment('Unique key name within category');

    // Knowledge content and structure
    table.json('value').notNullable().comment('The knowledge value (can be simple value or complex object)');
    table.string('value_type', 50).notNullable().comment('Type of value (string, number, date, object, array)');
    table.text('description').comment('Human-readable description of this knowledge');

    // Source and provenance
    table.string('source_document', 36).comment('Reference to source document');
    table.foreign('source_document').references('id').inTable('documents').onDelete('SET NULL');
    table.string('source_extraction', 36).comment('Reference to source extraction');
    table.foreign('source_extraction').references('id').inTable('extracted_data').onDelete('SET NULL');
    table.json('source_metadata').comment('Additional source information');

    // Confidence and validation
    table.decimal('confidence', 5, 4).notNullable().comment('Confidence in this knowledge (0-1)');
    table.boolean('is_validated').defaultTo(false).notNullable().comment('Whether knowledge has been manually validated');
    table.timestamp('validated_at').comment('When knowledge was validated');
    table.string('validated_by', 100).comment('Who validated the knowledge');

    // Vector embeddings for semantic search
    table.binary('embeddings').comment('Vector embeddings for semantic search (compressed)');
    table.integer('embedding_dimension').unsigned().comment('Dimension of the embedding vector');
    table.string('embedding_model', 100).comment('Model used to generate embeddings');

    // Relationships and context
    table.json('related_knowledge').comment('Array of related knowledge IDs');
    table.json('context').comment('Contextual information about this knowledge');
    table.string('domain', 100).comment('Domain or subject area (tax, legal, financial, etc.)');
    table.json('tags').comment('Array of tags for categorization');

    // Temporal information
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable().comment('Knowledge creation timestamp');
    table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable().comment('Last update timestamp');
    table.timestamp('valid_from').comment('When this knowledge becomes valid');
    table.timestamp('valid_until').comment('When this knowledge expires');
    table.boolean('is_current').defaultTo(true).notNullable().comment('Whether this is the current version');

    // Usage and quality metrics
    table.integer('usage_count').defaultTo(0).notNullable().comment('How many times this knowledge has been used');
    table.timestamp('last_used_at').comment('When this knowledge was last used');
    table.decimal('quality_score', 5, 4).comment('Overall quality score (0-1)');
    table.json('quality_metrics').comment('Detailed quality metrics');

    // Versioning and history
    table.string('version', 50).defaultTo('1.0').notNullable().comment('Version of this knowledge entry');
    table.string('previous_version_id', 36).comment('Reference to previous version');
    table.foreign('previous_version_id').references('id').inTable('knowledge_base').onDelete('SET NULL');
    table.json('change_log').comment('Log of changes made to this knowledge');

    // Constraints
    table.unique(['category', 'key_name'], 'unique_category_key');
    table.check('confidence >= 0 AND confidence <= 1', [], 'confidence_range');
    table.check('quality_score IS NULL OR (quality_score >= 0 AND quality_score <= 1)', [], 'quality_score_range');
    table.check('usage_count >= 0', [], 'usage_count_positive');
    table.check('embedding_dimension > 0', [], 'embedding_dimension_positive');
    table.check("value_type IN ('string', 'number', 'boolean', 'date', 'object', 'array', 'null')", [], 'valid_value_type');
    table.check('valid_from IS NULL OR valid_until IS NULL OR valid_from <= valid_until', [], 'valid_date_range');

    // JSON validation for structured values
    table.check("json_valid(value)", [], 'valid_json_value');
    table.check("json_valid(source_metadata) OR source_metadata IS NULL", [], 'valid_source_metadata');
    table.check("json_valid(related_knowledge) OR related_knowledge IS NULL", [], 'valid_related_knowledge');
    table.check("json_valid(context) OR context IS NULL", [], 'valid_context');
    table.check("json_valid(tags) OR tags IS NULL", [], 'valid_tags');
    table.check("json_valid(quality_metrics) OR quality_metrics IS NULL", [], 'valid_quality_metrics');
    table.check("json_valid(change_log) OR change_log IS NULL", [], 'valid_change_log');

    // Indexes for performance
    table.index(['category'], 'idx_knowledge_category');
    table.index(['key_name'], 'idx_knowledge_key_name');
    table.index(['source_document'], 'idx_knowledge_source');
    table.index(['confidence'], 'idx_knowledge_confidence');
    table.index(['is_validated'], 'idx_knowledge_validated');
    table.index(['created_at'], 'idx_knowledge_created_at');
    table.index(['updated_at'], 'idx_knowledge_updated_at');
    table.index(['domain'], 'idx_knowledge_domain');
    table.index(['is_current'], 'idx_knowledge_current');
    table.index(['usage_count'], 'idx_knowledge_usage');
    table.index(['last_used_at'], 'idx_knowledge_last_used');
    table.index(['valid_from'], 'idx_knowledge_valid_from');
    table.index(['valid_until'], 'idx_knowledge_valid_until');
    table.index(['value_type'], 'idx_knowledge_value_type');

    // Composite indexes for common queries
    table.index(['category', 'confidence'], 'idx_knowledge_category_confidence');
    table.index(['domain', 'category'], 'idx_knowledge_domain_category');
    table.index(['is_current', 'category'], 'idx_knowledge_current_category');
    table.index(['source_document', 'category'], 'idx_knowledge_source_category');
    table.index(['confidence', 'is_validated'], 'idx_knowledge_confidence_validated');
    table.index(['created_at', 'category'], 'idx_knowledge_created_category');
    table.index(['usage_count', 'last_used_at'], 'idx_knowledge_usage_last_used');
  });

  // Create full-text search virtual table for knowledge content
  await knex.raw(`
    CREATE VIRTUAL TABLE IF NOT EXISTS knowledge_base_fts USING fts5(
      id UNINDEXED,
      category,
      key_name,
      value,
      description,
      domain,
      tags,
      context,
      content='knowledge_base',
      content_rowid='rowid'
    )
  `);

  // Create triggers to keep FTS table in sync
  await knex.raw(`
    CREATE TRIGGER knowledge_base_fts_insert AFTER INSERT ON knowledge_base BEGIN
      INSERT INTO knowledge_base_fts(id, category, key_name, value, description, domain, tags, context)
      VALUES (new.id, new.category, new.key_name, new.value, new.description, new.domain, new.tags, new.context);
    END
  `);

  await knex.raw(`
    CREATE TRIGGER knowledge_base_fts_delete AFTER DELETE ON knowledge_base BEGIN
      DELETE FROM knowledge_base_fts WHERE id = old.id;
    END
  `);

  await knex.raw(`
    CREATE TRIGGER knowledge_base_fts_update AFTER UPDATE ON knowledge_base BEGIN
      DELETE FROM knowledge_base_fts WHERE id = old.id;
      INSERT INTO knowledge_base_fts(id, category, key_name, value, description, domain, tags, context)
      VALUES (new.id, new.category, new.key_name, new.value, new.description, new.domain, new.tags, new.context);
    END
  `);

  // Create updated_at trigger
  await knex.raw(`
    CREATE TRIGGER update_knowledge_base_updated_at
    AFTER UPDATE ON knowledge_base
    FOR EACH ROW
    BEGIN
      UPDATE knowledge_base SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END
  `);

  // Create trigger to update usage statistics
  await knex.raw(`
    CREATE TRIGGER update_knowledge_usage
    AFTER UPDATE OF usage_count ON knowledge_base
    FOR EACH ROW
    WHEN NEW.usage_count > OLD.usage_count
    BEGIN
      UPDATE knowledge_base SET last_used_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END
  `);

  // Create view for current knowledge (non-expired, current versions)
  await knex.raw(`
    CREATE VIEW current_knowledge AS
    SELECT *
    FROM knowledge_base
    WHERE is_current = 1
      AND (valid_until IS NULL OR valid_until > CURRENT_TIMESTAMP)
      AND (valid_from IS NULL OR valid_from <= CURRENT_TIMESTAMP)
  `);

  // Create view for high-confidence knowledge
  await knex.raw(`
    CREATE VIEW high_confidence_knowledge AS
    SELECT
      kb.*,
      d.name as source_document_name,
      d.type as source_document_type
    FROM knowledge_base kb
    LEFT JOIN documents d ON kb.source_document = d.id
    WHERE kb.confidence >= 0.8 AND kb.is_current = 1
  `);

  // Create view for knowledge requiring validation
  await knex.raw(`
    CREATE VIEW knowledge_for_validation AS
    SELECT
      kb.*,
      d.name as source_document_name,
      CASE
        WHEN kb.confidence < 0.5 THEN 'Low Confidence'
        WHEN kb.is_validated = 0 AND kb.confidence < 0.8 THEN 'Needs Validation'
        WHEN kb.quality_score IS NOT NULL AND kb.quality_score < 0.6 THEN 'Low Quality'
        ELSE 'Other'
      END as validation_reason
    FROM knowledge_base kb
    LEFT JOIN documents d ON kb.source_document = d.id
    WHERE kb.is_validated = 0 OR kb.confidence < 0.8 OR (kb.quality_score IS NOT NULL AND kb.quality_score < 0.6)
    ORDER BY kb.confidence ASC, kb.created_at DESC
  `);

  // Create view for knowledge statistics by category
  await knex.raw(`
    CREATE VIEW knowledge_category_stats AS
    SELECT
      category,
      domain,
      COUNT(*) as total_entries,
      COUNT(CASE WHEN is_validated = 1 THEN 1 END) as validated_entries,
      COUNT(CASE WHEN is_current = 1 THEN 1 END) as current_entries,
      AVG(confidence) as avg_confidence,
      MIN(confidence) as min_confidence,
      MAX(confidence) as max_confidence,
      SUM(usage_count) as total_usage,
      MAX(last_used_at) as last_category_usage,
      COUNT(DISTINCT source_document) as unique_source_documents
    FROM knowledge_base
    GROUP BY category, domain
    ORDER BY total_entries DESC
  `);

  // Create view for knowledge relationships
  await knex.raw(`
    CREATE VIEW knowledge_relationships AS
    SELECT
      kb1.id as source_id,
      kb1.category as source_category,
      kb1.key_name as source_key,
      kb2.id as related_id,
      kb2.category as related_category,
      kb2.key_name as related_key,
      kb1.confidence as source_confidence,
      kb2.confidence as related_confidence
    FROM knowledge_base kb1
    JOIN json_each(kb1.related_knowledge) je ON 1=1
    JOIN knowledge_base kb2 ON kb2.id = je.value
    WHERE kb1.related_knowledge IS NOT NULL
  `);
}

export async function down(knex: Knex): Promise<void> {
  // Drop triggers first
  await knex.raw('DROP TRIGGER IF EXISTS update_knowledge_usage');
  await knex.raw('DROP TRIGGER IF EXISTS update_knowledge_base_updated_at');
  await knex.raw('DROP TRIGGER IF EXISTS knowledge_base_fts_update');
  await knex.raw('DROP TRIGGER IF EXISTS knowledge_base_fts_delete');
  await knex.raw('DROP TRIGGER IF EXISTS knowledge_base_fts_insert');

  // Drop FTS table
  await knex.raw('DROP TABLE IF EXISTS knowledge_base_fts');

  // Drop views
  await knex.raw('DROP VIEW IF EXISTS knowledge_relationships');
  await knex.raw('DROP VIEW IF EXISTS knowledge_category_stats');
  await knex.raw('DROP VIEW IF EXISTS knowledge_for_validation');
  await knex.raw('DROP VIEW IF EXISTS high_confidence_knowledge');
  await knex.raw('DROP VIEW IF EXISTS current_knowledge');

  // Drop main table (this will also drop all indexes and foreign keys)
  await knex.schema.dropTableIfExists('knowledge_base');
}

import React from 'react';
import { useAppNavigation } from '../router/RouterProvider';

const Dashboard: React.FC = () => {
  const { navigate } = useAppNavigation();

  return (
    <div className="min-h-screen bg-base-100 text-base-content">
      {/* Title Bar */}
      <div className="h-8 bg-base-200 flex items-center justify-between px-4 drag-region">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 rounded-full bg-error"></div>
          <div className="w-3 h-3 rounded-full bg-warning"></div>
          <div className="w-3 h-3 rounded-full bg-success"></div>
        </div>
        <div className="text-sm font-medium">AI Document Processor</div>
        <div className="w-16"></div>
      </div>

      {/* Main Application */}
      <div className="flex h-[calc(100vh-2rem)]">
        {/* Sidebar */}
        <div className="w-64 bg-base-200 border-r border-base-300">
          <div className="p-4">
            <h2 className="text-lg font-semibold mb-4">Explorer</h2>
            <div className="space-y-2">
              <button
                className="w-full text-left p-2 rounded hover:bg-base-300 transition-colors"
                onClick={() => navigate('/documents')}
              >
                📄 Documents
              </button>
              <button
                className="w-full text-left p-2 rounded hover:bg-base-300 transition-colors"
                onClick={() => navigate('/timeline')}
              >
                ⏱️ Timeline
              </button>
              <button
                className="w-full text-left p-2 rounded hover:bg-base-300 transition-colors"
                onClick={() => navigate('/knowledge')}
              >
                📚 Knowledge Base
              </button>
              <button
                className="w-full text-left p-2 rounded hover:bg-base-300 transition-colors"
                onClick={() => navigate('/settings')}
              >
                ⚙️ Settings
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Tab Bar */}
          <div className="h-10 bg-base-200 border-b border-base-300 flex items-center px-4">
            <div className="text-sm text-base-content/70">Dashboard</div>
          </div>

          {/* Content Area */}
          <div className="flex-1 flex items-center justify-center bg-base-100">
            <div className="text-center max-w-md">
              <div className="mb-8">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    className="w-8 h-8 text-primary"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <h1 className="text-2xl font-bold text-base-content mb-2">AI Document Processor</h1>
                <p className="text-base-content/70 mb-6">
                  Enterprise-grade AI-powered document processing and intelligent paperwork
                  management system
                </p>
              </div>

              <div className="space-y-3">
                <button
                  className="btn btn-primary btn-block"
                  onClick={() => {
                    console.log('Open Document clicked');
                    if (window.electronAPI?.logToMain) {
                      window.electronAPI.logToMain('info', 'User clicked Open Document button', {
                        component: 'Dashboard',
                        action: 'openDocument',
                      });
                    }
                  }}
                >
                  Open Document
                </button>
                <button
                  className="btn btn-outline btn-block"
                  onClick={() => {
                    console.log('Create New Project clicked');
                    if (window.electronAPI?.logToMain) {
                      window.electronAPI.logToMain('info', 'User clicked Create New Project button', {
                        component: 'Dashboard',
                        action: 'createProject',
                      });
                    }
                  }}
                >
                  Create New Project
                </button>
              </div>

              <div className="mt-8 text-xs text-base-content/50">
                Version 1.0.0 • Built with Electron & React
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Status Bar */}
      <div className="h-6 bg-base-200 border-t border-base-300 flex items-center justify-between px-4 text-xs text-base-content/70">
        <div>Ready</div>
        <div>AI Document Processor</div>
      </div>
    </div>
  );
};

export default Dashboard;

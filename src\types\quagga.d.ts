declare module 'quagga' {
  export interface QuaggaConfig {
    src?: string;
    numOfWorkers?: number;
    inputStream?: {
      name?: string;
      type?: string;
      target?: HTMLElement | string;
      constraints?: MediaStreamConstraints;
      area?: {
        top: string;
        right: string;
        left: string;
        bottom: string;
      };
      singleChannel?: boolean;
      size?: number;
    };
    locator?: {
      patchSize?: string;
      halfSample?: boolean;
    };
    decoder?: {
      readers?: string[];
      debug?: {
        drawBoundingBox?: boolean;
        showFrequency?: boolean;
        drawScanline?: boolean;
        showPattern?: boolean;
      };
      multiple?: boolean;
    };
    locate?: boolean;
    debug?: boolean;
  }

  export interface QuaggaResult {
    codeResult?: {
      code: string;
      format: string;
      start: number;
      end: number;
      codeset: number;
      startInfo: {
        error: number;
        code: number;
        start: number;
        end: number;
      };
      decodedCodes: Array<{
        code: number;
        start: number;
        end: number;
        error?: number;
      }>;
      endInfo: {
        error: number;
        code: number;
        start: number;
        end: number;
      };
      direction: number;
    };
    line?: Array<{
      x: number;
      y: number;
    }>;
    angle?: number;
    pattern?: number[];
    box?: Array<Array<number>>;
  }

  export interface QuaggaJSResultObject {
    codeResult: {
      code: string;
      format: string;
    };
    line: Array<{
      x: number;
      y: number;
    }>;
  }

  export interface QuaggaJSResultCallbackFunction {
    (data: QuaggaJSResultObject): void;
  }

  export interface QuaggaJSErrorCallbackFunction {
    (error: string | Error): void;
  }

  export function init(
    config: QuaggaConfig,
    callback?: (error?: string | Error) => void
  ): void;

  export function start(): void;

  export function stop(): void;

  export function pause(): void;

  export function onProcessed(callback: (result: QuaggaResult) => void): void;

  export function offProcessed(callback: (result: QuaggaResult) => void): void;

  export function onDetected(callback: QuaggaJSResultCallbackFunction): void;

  export function offDetected(callback: QuaggaJSResultCallbackFunction): void;

  export function decodeSingle(
    config: QuaggaConfig,
    callback: (result: QuaggaResult | null) => void
  ): void;

  export const ImageWrapper: {
    load(src: string): Promise<any>;
  };

  export const ImageDebug: {
    drawPath(path: any, options: any, canvas: any): void;
    drawRect(rect: any, options: any, canvas: any): void;
  };

  export const ResultCollector: {
    create(config: any): any;
    addResult(result: any, imageData: any): void;
    getResults(): any[];
  };

  export default {
    init,
    start,
    stop,
    pause,
    onProcessed,
    offProcessed,
    onDetected,
    offDetected,
    decodeSingle,
    ImageWrapper,
    ImageDebug,
    ResultCollector,
  };
}

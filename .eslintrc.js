module.exports = {
  root: true,
  env: {
    browser: true,
    es2022: true,
    node: true,
    jest: true,
  },
  parser: '@typescript-eslint/parser',
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
  ],
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
  },
  plugins: ['@typescript-eslint', 'react', 'react-hooks'],
  settings: {
    react: {
      version: 'detect',
    },
  },
  rules: {
    // TypeScript specific rules
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-non-null-assertion': 'warn',
    '@typescript-eslint/no-var-requires': 'error',
    '@typescript-eslint/ban-ts-comment': 'warn',

    // React specific rules
    'react/react-in-jsx-scope': 'off', // Not needed with React 17+
    'react/prop-types': 'off', // Using TypeScript for prop validation
    'react/jsx-uses-react': 'off', // Not needed with React 17+

    // React Hooks rules
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',

    // General rules
    'no-console': 'warn',
    'no-debugger': 'error',
    'no-var': 'error',
    'prefer-const': 'error',
  },
  overrides: [
    // TypeScript files with project references
    {
      files: ['src/**/*.{ts,tsx}'],
      parser: '@typescript-eslint/parser',
      parserOptions: {
        project: ['./tsconfig.main.json', './tsconfig.renderer.json'],
        tsconfigRootDir: __dirname,
      },
      rules: {
        '@typescript-eslint/no-floating-promises': 'error',
        '@typescript-eslint/await-thenable': 'error',
        '@typescript-eslint/require-await': 'error',
        '@typescript-eslint/no-misused-promises': 'error',
      },
    },
    {
      files: ['src/main/**/*.ts'],
      env: {
        browser: false,
        node: true,
      },
      rules: {
        'no-console': 'off', // Allow console in main process
        '@typescript-eslint/no-var-requires': 'off', // Allow require in main process
      },
    },
    {
      files: ['src/renderer/**/*.{ts,tsx}'],
      env: {
        browser: true,
        node: false,
      },
      rules: {
        'no-console': 'warn', // Warn about console in renderer
      },
    },
    {
      files: ['src/preload/**/*.ts'],
      env: {
        browser: true,
        node: true,
      },
      rules: {
        'no-console': 'off', // Allow console in preload
      },
    },
    {
      files: ['tests/**/*.{ts,tsx}', '**/*.test.{ts,tsx}', '**/*.spec.{ts,tsx}'],
      env: {
        jest: true,
      },
      rules: {
        '@typescript-eslint/no-explicit-any': 'off',
        '@typescript-eslint/no-non-null-assertion': 'off',
        '@typescript-eslint/no-empty-function': 'off',
        'no-console': 'off',
      },
    },
    // Configuration files (JavaScript only, no TypeScript parsing)
    {
      files: [
        '*.config.js',
        '*.config.ts',
        'forge.config.js',
        'jest.config.js',
        'knexfile.js',
        'tailwind.config.js',
        'postcss.config.js',
        '.eslintrc.js',
        '.lintstagedrc.js',
      ],
      parser: 'espree', // Use default JavaScript parser
      parserOptions: {
        ecmaVersion: 2022,
        sourceType: 'module',
      },
      env: {
        node: true,
      },
      rules: {
        '@typescript-eslint/no-var-requires': 'off',
        'no-console': 'off',
        '@typescript-eslint/no-unused-vars': 'off', // Disable for config files
      },
    },
  ],
  ignorePatterns: [
    'dist/',
    'build/',
    'public/',
    'coverage/',
    'node_modules/',
    '*.min.js',
    '*.bundle.js',
    'src/index.js',
    'src/preload.js',
    'src/renderer/global-polyfill.js',
  ],
};

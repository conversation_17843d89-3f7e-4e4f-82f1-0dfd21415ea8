import { NLPProcessor } from '../../../src/main/services/NLPProcessor';
import {
  KnowledgeGraph,
  GraphNode,
  GraphEdge,
  NodeType,
  EntityCategory,
  RelationshipType,
} from '../../../src/shared/types/AI';

describe('NLPProcessor Knowledge Graph Construction', () => {
  let nlpProcessor: NLPProcessor;

  beforeEach(() => {
    nlpProcessor = new NLPProcessor();
  });

  describe('constructKnowledgeGraph', () => {
    it('should construct a basic knowledge graph from text', async () => {
      const text =
        '<PERSON> works at Microsoft Corporation in Seattle. He manages the development team.';
      const documentId = 'test-doc-1';

      const graph = await nlpProcessor.constructKnowledgeGraph(text, documentId);

      expect(graph).toBeDefined();
      expect(graph.id).toContain(documentId);
      expect(graph.name).toContain(documentId);
      expect(graph.nodes).toBeInstanceOf(Array);
      expect(graph.edges).toBeInstanceOf(Array);
      expect(graph.metadata).toBeDefined();
      expect(graph.statistics).toBeDefined();
    });

    it('should create nodes with correct types', async () => {
      const text = 'Apple Inc. is located in Cupertino, California. Tim Cook is the CEO.';
      const documentId = 'test-doc-2';

      const graph = await nlpProcessor.constructKnowledgeGraph(text, documentId);

      const personNodes = graph.nodes.filter(node => node.type === NodeType.PERSON);
      const orgNodes = graph.nodes.filter(node => node.type === NodeType.ORGANIZATION);
      const locationNodes = graph.nodes.filter(node => node.type === NodeType.LOCATION);

      expect(personNodes.length).toBeGreaterThan(0);
      expect(orgNodes.length).toBeGreaterThan(0);
      expect(locationNodes.length).toBeGreaterThan(0);
    });

    it('should create edges with relationships', async () => {
      const text = 'Google was founded by Larry Page and Sergey Brin in Mountain View.';
      const documentId = 'test-doc-3';

      const graph = await nlpProcessor.constructKnowledgeGraph(text, documentId);

      expect(graph.edges.length).toBeGreaterThan(0);
      graph.edges.forEach(edge => {
        expect(edge.source).toBeDefined();
        expect(edge.target).toBeDefined();
        expect(edge.relationship).toBeDefined();
        expect(edge.weight).toBeGreaterThan(0);
      });
    });

    it('should calculate graph statistics correctly', async () => {
      const text = 'Amazon is headquartered in Seattle. Jeff Bezos founded Amazon.';
      const documentId = 'test-doc-4';

      const graph = await nlpProcessor.constructKnowledgeGraph(text, documentId);

      expect(graph.statistics.nodeCount).toBe(graph.nodes.length);
      expect(graph.statistics.edgeCount).toBe(graph.edges.length);
      expect(graph.statistics.averageDegree).toBeGreaterThanOrEqual(0);
      expect(graph.statistics.density).toBeGreaterThanOrEqual(0);
      expect(graph.statistics.density).toBeLessThanOrEqual(1);
      expect(graph.statistics.components).toBeGreaterThan(0);
    });
  });

  describe('enhanceKnowledgeGraph', () => {
    it('should enhance graph with semantic relationships', async () => {
      const text = 'Tesla is an electric vehicle company. Elon Musk is the CEO of Tesla.';
      const documentId = 'test-doc-5';

      const initialGraph = await nlpProcessor.constructKnowledgeGraph(text, documentId);
      const enhancedGraph = await nlpProcessor.enhanceKnowledgeGraph(initialGraph, text);

      expect(enhancedGraph.edges.length).toBeGreaterThanOrEqual(initialGraph.edges.length);
      expect(enhancedGraph.metadata.version).toBe(initialGraph.metadata.version + 1);
      expect(enhancedGraph.metadata.updatedAt.getTime()).toBeGreaterThan(
        initialGraph.metadata.updatedAt.getTime()
      );
    });

    it('should deduplicate edges correctly', async () => {
      const text = 'Microsoft is a technology company. Microsoft develops software.';
      const documentId = 'test-doc-6';

      const initialGraph = await nlpProcessor.constructKnowledgeGraph(text, documentId);
      const enhancedGraph = await nlpProcessor.enhanceKnowledgeGraph(initialGraph, text);

      // Check that duplicate edges are merged
      const edgeKeys = new Set();
      enhancedGraph.edges.forEach(edge => {
        const key = `${edge.source}_${edge.target}_${edge.relationship.type}`;
        expect(edgeKeys.has(key)).toBe(false);
        edgeKeys.add(key);
      });
    });
  });

  describe('findShortestPath', () => {
    it('should find shortest path between connected nodes', async () => {
      const text =
        'Alice works at Company A. Company A is located in City B. City B is in Country C.';
      const documentId = 'test-doc-7';

      const graph = await nlpProcessor.constructKnowledgeGraph(text, documentId);

      if (graph.nodes.length >= 2) {
        const sourceNode = graph.nodes[0];
        const targetNode = graph.nodes[1];

        const path = nlpProcessor.findShortestPath(graph, sourceNode.id, targetNode.id);

        if (path) {
          expect(path.length).toBeGreaterThan(0);
          expect(path[0].id).toBe(sourceNode.id);
          expect(path[path.length - 1].id).toBe(targetNode.id);
        }
      }
    });

    it('should return null for disconnected nodes', async () => {
      const graph: KnowledgeGraph = {
        id: 'test-graph',
        name: 'Test Graph',
        description: 'Test',
        nodes: [
          {
            id: 'node1',
            label: 'Node 1',
            type: NodeType.ENTITY,
            properties: {
              name: 'Node 1',
              category: 'test',
              confidence: 1,
              attributes: {},
            },
          },
          {
            id: 'node2',
            label: 'Node 2',
            type: NodeType.ENTITY,
            properties: {
              name: 'Node 2',
              category: 'test',
              confidence: 1,
              attributes: {},
            },
          },
        ],
        edges: [], // No edges - disconnected
        metadata: {
          createdAt: new Date(),
          updatedAt: new Date(),
          version: 1,
          author: 'test',
          tags: [],
        },
        statistics: {
          nodeCount: 2,
          edgeCount: 0,
          averageDegree: 0,
          density: 0,
          components: 2,
        },
      };

      const path = nlpProcessor.findShortestPath(graph, 'node1', 'node2');
      expect(path).toBeNull();
    });
  });

  describe('getNodeNeighbors', () => {
    it('should return correct neighbors for a node', async () => {
      const text = 'Facebook connects people. Mark Zuckerberg founded Facebook.';
      const documentId = 'test-doc-8';

      const graph = await nlpProcessor.constructKnowledgeGraph(text, documentId);

      if (graph.nodes.length > 0) {
        const node = graph.nodes[0];
        const neighbors = nlpProcessor.getNodeNeighbors(graph, node.id);

        expect(neighbors).toBeInstanceOf(Array);
        neighbors.forEach(neighbor => {
          expect(neighbor.id).not.toBe(node.id);
        });
      }
    });

    it('should return empty array for isolated node', async () => {
      const graph: KnowledgeGraph = {
        id: 'test-graph',
        name: 'Test Graph',
        description: 'Test',
        nodes: [
          {
            id: 'isolated-node',
            label: 'Isolated Node',
            type: NodeType.ENTITY,
            properties: {
              name: 'Isolated Node',
              category: 'test',
              confidence: 1,
              attributes: {},
            },
          },
        ],
        edges: [],
        metadata: {
          createdAt: new Date(),
          updatedAt: new Date(),
          version: 1,
          author: 'test',
          tags: [],
        },
        statistics: {
          nodeCount: 1,
          edgeCount: 0,
          averageDegree: 0,
          density: 0,
          components: 1,
        },
      };

      const neighbors = nlpProcessor.getNodeNeighbors(graph, 'isolated-node');
      expect(neighbors).toEqual([]);
    });
  });

  describe('calculateNodeCentrality', () => {
    it('should calculate centrality measures for all nodes', async () => {
      const text =
        'Netflix streams movies. Reed Hastings founded Netflix. Netflix is based in California.';
      const documentId = 'test-doc-9';

      const graph = await nlpProcessor.constructKnowledgeGraph(text, documentId);
      const centrality = nlpProcessor.calculateNodeCentrality(graph);

      graph.nodes.forEach(node => {
        expect(centrality[node.id]).toBeDefined();
        expect(centrality[node.id].degree).toBeGreaterThanOrEqual(0);
        expect(centrality[node.id].betweenness).toBeGreaterThanOrEqual(0);
        expect(centrality[node.id].closeness).toBeGreaterThanOrEqual(0);
      });
    });

    it('should assign higher centrality to more connected nodes', async () => {
      const graph: KnowledgeGraph = {
        id: 'test-graph',
        name: 'Test Graph',
        description: 'Test',
        nodes: [
          {
            id: 'central-node',
            label: 'Central Node',
            type: NodeType.ENTITY,
            properties: {
              name: 'Central Node',
              category: 'test',
              confidence: 1,
              attributes: {},
            },
          },
          {
            id: 'peripheral-node',
            label: 'Peripheral Node',
            type: NodeType.ENTITY,
            properties: {
              name: 'Peripheral Node',
              category: 'test',
              confidence: 1,
              attributes: {},
            },
          },
        ],
        edges: [
          {
            id: 'edge1',
            source: 'central-node',
            target: 'peripheral-node',
            relationship: {
              id: 'rel1',
              type: RelationshipType.RELATED_TO,
              sourceId: 'central-node',
              targetId: 'peripheral-node',
              strength: 1,
              confidence: 1,
              properties: {
                description: 'test',
                weight: 1,
                bidirectional: true,
              },
              createdAt: new Date(),
            },
            weight: 1,
          },
        ],
        metadata: {
          createdAt: new Date(),
          updatedAt: new Date(),
          version: 1,
          author: 'test',
          tags: [],
        },
        statistics: {
          nodeCount: 2,
          edgeCount: 1,
          averageDegree: 1,
          density: 1,
          components: 1,
        },
      };

      const centrality = nlpProcessor.calculateNodeCentrality(graph);

      expect(centrality['central-node'].degree).toBeGreaterThanOrEqual(
        centrality['peripheral-node'].degree
      );
    });
  });

  describe('exportKnowledgeGraph', () => {
    let testGraph: KnowledgeGraph;

    beforeEach(async () => {
      const text = 'Spotify plays music. Daniel Ek founded Spotify.';
      const documentId = 'test-doc-10';
      testGraph = await nlpProcessor.constructKnowledgeGraph(text, documentId);
    });

    it('should export graph as JSON', () => {
      const exported = nlpProcessor.exportKnowledgeGraph(testGraph, 'json');
      const parsed = JSON.parse(exported);

      expect(parsed.id).toBe(testGraph.id);
      expect(parsed.nodes).toEqual(testGraph.nodes);
      expect(parsed.edges).toEqual(testGraph.edges);
    });

    it('should export graph as Cytoscape format', () => {
      const exported = nlpProcessor.exportKnowledgeGraph(testGraph, 'cytoscape');
      const parsed = JSON.parse(exported);

      expect(parsed.elements).toBeDefined();
      expect(parsed.elements.nodes).toBeInstanceOf(Array);
      expect(parsed.elements.edges).toBeInstanceOf(Array);
    });

    it('should export graph as GraphML', () => {
      const exported = nlpProcessor.exportKnowledgeGraph(testGraph, 'graphml');

      expect(exported).toContain('<?xml version="1.0" encoding="UTF-8"?>');
      expect(exported).toContain('<graphml');
      expect(exported).toContain('<graph');
      expect(exported).toContain('<node');
      expect(exported).toContain('</graphml>');
    });

    it('should export graph as Gephi format', () => {
      const exported = nlpProcessor.exportKnowledgeGraph(testGraph, 'gephi');
      const parsed = JSON.parse(exported);

      expect(parsed.nodes).toBeInstanceOf(Array);
      expect(parsed.edges).toBeInstanceOf(Array);

      if (parsed.nodes.length > 0) {
        expect(parsed.nodes[0]).toHaveProperty('id');
        expect(parsed.nodes[0]).toHaveProperty('label');
        expect(parsed.nodes[0]).toHaveProperty('attributes');
      }
    });

    it('should handle export errors gracefully', () => {
      const invalidGraph = { ...testGraph, nodes: null } as any;
      const exported = nlpProcessor.exportKnowledgeGraph(invalidGraph, 'json');

      expect(exported).toContain('error');
    });
  });

  describe('entity deduplication', () => {
    it('should deduplicate similar entities', async () => {
      const text = 'Apple Inc. is a company. Apple Corporation makes phones. Apple is innovative.';
      const documentId = 'test-doc-11';

      const graph = await nlpProcessor.constructKnowledgeGraph(text, documentId);

      // Check that similar entities are merged
      const appleNodes = graph.nodes.filter(node => node.label.toLowerCase().includes('apple'));

      // Should have fewer Apple nodes than mentions due to deduplication
      expect(appleNodes.length).toBeLessThan(3);
    });
  });

  describe('error handling', () => {
    it('should handle empty text gracefully', async () => {
      const text = '';
      const documentId = 'test-doc-12';

      const graph = await nlpProcessor.constructKnowledgeGraph(text, documentId);

      expect(graph).toBeDefined();
      expect(graph.nodes).toEqual([]);
      expect(graph.edges).toEqual([]);
      expect(graph.statistics.nodeCount).toBe(0);
      expect(graph.statistics.edgeCount).toBe(0);
    });

    it('should handle malformed text gracefully', async () => {
      const text = '!@#$%^&*()_+{}|:"<>?[]\\;\',./ *********';
      const documentId = 'test-doc-13';

      const graph = await nlpProcessor.constructKnowledgeGraph(text, documentId);

      expect(graph).toBeDefined();
      expect(graph.nodes).toBeInstanceOf(Array);
      expect(graph.edges).toBeInstanceOf(Array);
    });

    it('should handle very long text efficiently', async () => {
      const longText = 'Microsoft Corporation '.repeat(1000) + 'is a technology company.';
      const documentId = 'test-doc-14';

      const startTime = Date.now();
      const graph = await nlpProcessor.constructKnowledgeGraph(longText, documentId);
      const endTime = Date.now();

      expect(graph).toBeDefined();
      expect(endTime - startTime).toBeLessThan(10000); // Should complete within 10 seconds
    });
  });
});

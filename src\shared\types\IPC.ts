// IPC message type definitions

// Processing options interface
export interface ProcessingOptions {
  extractText?: boolean;
  extractImages?: boolean;
  performOCR?: boolean;
  detectForms?: boolean;
  extractTables?: boolean;
  aiModel?: string;
}

// Document content interface
export interface DocumentContent {
  text?: string;
  images?: string[];
  tables?: any[];
  forms?: any[];
  metadata?: Record<string, any>;
}

// Document metadata interface
export interface DocumentMetadata {
  fileName: string;
  fileSize: number;
  mimeType: string;
  pageCount?: number;
  createdAt: string;
  modifiedAt: string;
  checksum?: string;
}

// AI parameters interface
export interface AIParameters {
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  model?: string;
}

// Knowledge base item interface
export interface KnowledgeItem {
  id: string;
  content: string;
  metadata: Record<string, any>;
  embedding?: number[];
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

// Knowledge search result interface
export interface KnowledgeSearchResult {
  item: KnowledgeItem;
  score: number;
  relevance: number;
  snippet?: string;
}

export interface IPCRequest<T = any> {
  id: string;
  channel: string;
  data: T;
  timestamp: Date;
  sender: IPCSender;
}

export interface IPCResponse<T = any> {
  id: string;
  requestId: string;
  success: boolean;
  data?: T;
  error?: IPCError;
  timestamp: Date;
}

export interface IPCError {
  code: string;
  message: string;
  stack?: string;
  details?: any;
}

export enum IPCSender {
  MAIN = 'main',
  RENDERER = 'renderer',
  PRELOAD = 'preload',
  WORKER = 'worker'
}

// Document IPC Messages
export interface DocumentOpenRequest {
  filePath?: string;
  filters?: FileFilter[];
}

export interface DocumentSaveRequest {
  content: string;
  filePath?: string;
  format?: string;
}

export interface DocumentProcessRequest {
  filePath: string;
  options: ProcessingOptions;
}

export interface DocumentProcessResponse {
  documentId: string;
  content: DocumentContent;
  metadata: DocumentMetadata;
  processingTime: number;
}

// AI IPC Messages
export interface AIGenerateEmbeddingsRequest {
  text: string;
  model?: string;
}

export interface AIPerformReasoningRequest {
  context: string;
  query: string;
  model?: string;
  parameters?: AIParameters;
}

export interface AIPerformReasoningResponse {
  response: string;
  confidence: number;
  reasoning: string;
  sources: string[];
}

// Knowledge Base IPC Messages
export interface KnowledgeStoreRequest {
  data: KnowledgeItem;
  collection?: string;
}

export interface KnowledgeQueryRequest {
  query: string;
  collection?: string;
  limit?: number;
  threshold?: number;
}

export interface KnowledgeQueryResponse {
  results: KnowledgeSearchResult[];
  totalCount: number;
  processingTime: number;
}

// Timeline IPC Messages
export interface TimelineCreateCheckpointRequest {
  description: string;
  data: any;
  tags?: string[];
}

export interface TimelineCreateCheckpointResponse {
  checkpointId: string;
  timestamp: Date;
}

export interface TimelineUndoRequest {
  steps?: number;
}

export interface TimelineRedoRequest {
  steps?: number;
}

// File System IPC Messages
export interface FileFilter {
  name: string;
  extensions: string[];
}

export interface FileOpenResult {
  filePath: string;
  content: Buffer;
  metadata: FileMetadata;
}

export interface FileMetadata {
  name: string;
  size: number;
  type: string;
  lastModified: Date;
  permissions: FilePermissions;
}

export interface FilePermissions {
  readable: boolean;
  writable: boolean;
  executable: boolean;
}

// System IPC Messages
export interface SystemInfoRequest {
  includeHardware?: boolean;
  includeNetwork?: boolean;
}

export interface SystemInfoResponse {
  platform: string;
  arch: string;
  version: string;
  memory: MemoryInfo;
  cpu: CPUInfo;
  disk: DiskInfo[];
}

export interface MemoryInfo {
  total: number;
  free: number;
  used: number;
  percentage: number;
}

export interface CPUInfo {
  model: string;
  cores: number;
  speed: number;
  usage: number;
}

export interface DiskInfo {
  filesystem: string;
  size: number;
  used: number;
  available: number;
  percentage: number;
  mountpoint: string;
}

// Event Messages
export interface IPCEvent<T = any> {
  type: string;
  data: T;
  timestamp: Date;
  source: IPCSender;
}

export interface DocumentProcessedEvent {
  documentId: string;
  success: boolean;
  processingTime: number;
  error?: string;
}

export interface AIResponseEvent {
  requestId: string;
  response: string;
  confidence: number;
  model: string;
}

export interface ProgressEvent {
  taskId: string;
  progress: number;
  message: string;
  stage: string;
}

export interface ErrorEvent {
  error: IPCError;
  context: string;
  recoverable: boolean;
}

// Channel Constants
export const IPC_CHANNELS = {
  // Document channels
  DOCUMENT_OPEN: 'document:open',
  DOCUMENT_SAVE: 'document:save',
  DOCUMENT_PROCESS: 'document:process',
  DOCUMENT_PROCESSED: 'document:processed',
  
  // AI channels
  AI_GENERATE_EMBEDDINGS: 'ai:generate-embeddings',
  AI_PERFORM_REASONING: 'ai:perform-reasoning',
  AI_RESPONSE: 'ai:response',
  
  // Knowledge base channels
  KNOWLEDGE_STORE: 'knowledge:store',
  KNOWLEDGE_QUERY: 'knowledge:query',
  
  // Timeline channels
  TIMELINE_CREATE_CHECKPOINT: 'timeline:create-checkpoint',
  TIMELINE_UNDO: 'timeline:undo',
  TIMELINE_REDO: 'timeline:redo',
  
  // File system channels
  FILE_OPEN: 'file:open',
  FILE_SAVE: 'file:save',
  FILE_WATCH: 'file:watch',
  
  // System channels
  SYSTEM_INFO: 'system:info',
  SYSTEM_SHUTDOWN: 'system:shutdown',
  
  // Event channels
  PROGRESS: 'progress',
  ERROR: 'error',
  LOG: 'log'
} as const;

export type IPCChannel = typeof IPC_CHANNELS[keyof typeof IPC_CHANNELS];
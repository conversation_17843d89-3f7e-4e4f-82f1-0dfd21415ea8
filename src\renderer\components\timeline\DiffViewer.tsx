import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  DiffResult,
  VisualDiff,
  ChangeSet,
  ChangeType,
  TextDiff,
  TextDiffLine,
  TextDiffLineType,
  StructuralDiff,
  VisualDiffType,
} from '../../../shared/types/Timeline';

export interface DiffViewerProps {
  diff?: DiffResult;
  visualDiff?: VisualDiff;
  isLoading?: boolean;
  showSideBySide?: boolean;
  showLineNumbers?: boolean;
  highlightChanges?: boolean;
  onClose?: () => void;
  className?: string;
}

export interface DiffViewerState {
  viewMode: 'unified' | 'split';
  showWhitespace: boolean;
  contextLines: number;
  selectedChange: ChangeSet | null;
}

const DiffViewer: React.FC<DiffViewerProps> = ({
  diff,
  visualDiff,
  isLoading = false,
  showSideBySide = true,
  showLineNumbers = true,
  highlightChanges = true,
  onClose,
  className = '',
}) => {
  const [state, setState] = useState<DiffViewerState>({
    viewMode: showSideBySide ? 'split' : 'unified',
    showWhitespace: false,
    contextLines: 3,
    selectedChange: null,
  });

  const changesByType = useMemo(() => {
    if (!diff) return {};

    return diff.changes.reduce(
      (acc, change) => {
        if (!acc[change.type]) {
          acc[change.type] = [];
        }
        acc[change.type].push(change);
        return acc;
      },
      {} as Record<ChangeType, ChangeSet[]>
    );
  }, [diff]);

  const getChangeTypeColor = (type: ChangeType): string => {
    switch (type) {
      case ChangeType.ADDED:
        return 'bg-green-100 border-green-300 text-green-800';
      case ChangeType.DELETED:
        return 'bg-red-100 border-red-300 text-red-800';
      case ChangeType.MODIFIED:
        return 'bg-yellow-100 border-yellow-300 text-yellow-800';
      case ChangeType.MOVED:
        return 'bg-blue-100 border-blue-300 text-blue-800';
      case ChangeType.RENAMED:
        return 'bg-purple-100 border-purple-300 text-purple-800';
      default:
        return 'bg-gray-100 border-gray-300 text-gray-800';
    }
  };

  const getChangeTypeIcon = (type: ChangeType): string => {
    switch (type) {
      case ChangeType.ADDED:
        return '+';
      case ChangeType.DELETED:
        return '-';
      case ChangeType.MODIFIED:
        return '~';
      case ChangeType.MOVED:
        return '→';
      case ChangeType.RENAMED:
        return '✎';
      default:
        return '?';
    }
  };

  const renderTextDiff = (textDiff: TextDiff) => {
    return (
      <div className='text-diff'>
        <div className='diff-header bg-gray-100 p-2 border-b'>
          <h4 className='font-medium'>Text Changes</h4>
          <div className='text-sm text-gray-600 mt-1'>
            {textDiff.statistics.addedLines} additions, {textDiff.statistics.deletedLines}{' '}
            deletions, {textDiff.statistics.modifiedLines} modifications
          </div>
        </div>

        <div className='diff-content'>
          {state.viewMode === 'split' ? (
            <div className='grid grid-cols-2 gap-0'>
              <div className='before-content border-r'>
                <div className='bg-red-50 p-2 text-sm font-medium border-b'>Before</div>
                <div className='diff-lines'>
                  {textDiff.lines.map((line, index) => (
                    <div
                      key={index}
                      className={`diff-line flex ${
                        line.type === TextDiffLineType.DELETED ||
                        line.type === TextDiffLineType.MODIFIED
                          ? 'bg-red-50'
                          : line.type === TextDiffLineType.UNCHANGED
                            ? 'bg-white'
                            : 'bg-gray-100'
                      }`}
                    >
                      {showLineNumbers && (
                        <div className='line-number w-12 text-xs text-gray-500 p-1 border-r bg-gray-50'>
                          {line.beforeLineNumber || ''}
                        </div>
                      )}
                      <div className='line-content flex-1 p-1 font-mono text-sm'>
                        {line.type === TextDiffLineType.DELETED ||
                        line.type === TextDiffLineType.MODIFIED
                          ? line.content
                          : line.type === TextDiffLineType.UNCHANGED
                            ? line.content
                            : ''}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className='after-content'>
                <div className='bg-green-50 p-2 text-sm font-medium border-b'>After</div>
                <div className='diff-lines'>
                  {textDiff.lines.map((line, index) => (
                    <div
                      key={index}
                      className={`diff-line flex ${
                        line.type === TextDiffLineType.ADDED ||
                        line.type === TextDiffLineType.MODIFIED
                          ? 'bg-green-50'
                          : line.type === TextDiffLineType.UNCHANGED
                            ? 'bg-white'
                            : 'bg-gray-100'
                      }`}
                    >
                      {showLineNumbers && (
                        <div className='line-number w-12 text-xs text-gray-500 p-1 border-r bg-gray-50'>
                          {line.afterLineNumber || ''}
                        </div>
                      )}
                      <div className='line-content flex-1 p-1 font-mono text-sm'>
                        {line.type === TextDiffLineType.ADDED ||
                        line.type === TextDiffLineType.MODIFIED
                          ? line.content
                          : line.type === TextDiffLineType.UNCHANGED
                            ? line.content
                            : ''}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className='unified-diff'>
              {textDiff.lines.map((line, index) => (
                <div
                  key={index}
                  className={`diff-line flex ${
                    line.type === TextDiffLineType.ADDED
                      ? 'bg-green-50'
                      : line.type === TextDiffLineType.DELETED
                        ? 'bg-red-50'
                        : line.type === TextDiffLineType.MODIFIED
                          ? 'bg-yellow-50'
                          : 'bg-white'
                  }`}
                >
                  <div className='change-indicator w-6 text-center text-sm font-mono'>
                    {line.type === TextDiffLineType.ADDED
                      ? '+'
                      : line.type === TextDiffLineType.DELETED
                        ? '-'
                        : line.type === TextDiffLineType.MODIFIED
                          ? '~'
                          : ' '}
                  </div>
                  {showLineNumbers && (
                    <div className='line-number w-12 text-xs text-gray-500 p-1 border-r bg-gray-50'>
                      {line.lineNumber}
                    </div>
                  )}
                  <div className='line-content flex-1 p-1 font-mono text-sm'>{line.content}</div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderStructuralDiff = (structuralDiff: StructuralDiff) => {
    return (
      <div className='structural-diff'>
        <div className='diff-header bg-gray-100 p-2 border-b'>
          <h4 className='font-medium'>Structural Changes</h4>
          <div className='text-sm text-gray-600 mt-1'>
            {structuralDiff.statistics.totalElements} elements changed
          </div>
        </div>

        <div className='diff-content p-4'>
          <div className='space-y-2'>
            {structuralDiff.elements.map((element, index) => (
              <div
                key={element.id}
                className={`border rounded p-3 ${getChangeTypeColor(element.changeType)}`}
              >
                <div className='flex items-center justify-between'>
                  <div className='flex items-center space-x-2'>
                    <span className='font-mono text-sm'>
                      {getChangeTypeIcon(element.changeType)}
                    </span>
                    <span className='font-medium'>{element.path}</span>
                    <span className='text-xs bg-white px-2 py-1 rounded'>{element.type}</span>
                  </div>
                  <span className='text-xs'>{element.changeType}</span>
                </div>

                {element.beforeProperties && (
                  <div className='mt-2'>
                    <div className='text-xs font-medium text-gray-600'>Before:</div>
                    <pre className='text-xs bg-white p-2 rounded mt-1 overflow-auto'>
                      {JSON.stringify(element.beforeProperties, null, 2)}
                    </pre>
                  </div>
                )}

                {element.afterProperties && (
                  <div className='mt-2'>
                    <div className='text-xs font-medium text-gray-600'>After:</div>
                    <pre className='text-xs bg-white p-2 rounded mt-1 overflow-auto'>
                      {JSON.stringify(element.afterProperties, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderChangesSummary = () => {
    if (!diff) return null;

    return (
      <div className='changes-summary bg-white border-b p-4'>
        <h3 className='font-medium mb-3'>Changes Summary</h3>

        <div className='grid grid-cols-2 md:grid-cols-5 gap-4 mb-4'>
          {Object.entries(changesByType).map(([type, changes]) => (
            <div
              key={type}
              className={`text-center p-2 rounded border ${getChangeTypeColor(type as ChangeType)}`}
            >
              <div className='text-lg font-bold'>{changes.length}</div>
              <div className='text-xs'>{type}</div>
            </div>
          ))}
        </div>

        <div className='text-sm text-gray-600'>
          Total changes: {diff.statistics.totalChanges} | Created: {diff.createdAt.toLocaleString()}
        </div>
      </div>
    );
  };

  const renderChangesList = () => {
    if (!diff) return null;

    return (
      <div className='changes-list'>
        <div className='changes-header bg-gray-100 p-2 border-b'>
          <h4 className='font-medium'>All Changes</h4>
        </div>

        <div className='changes-content max-h-96 overflow-auto'>
          {diff.changes.map((change, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05 }}
              className={`change-item p-3 border-b cursor-pointer hover:bg-gray-50 ${
                state.selectedChange === change ? 'bg-blue-50 border-blue-200' : ''
              }`}
              onClick={() => setState(prev => ({ ...prev, selectedChange: change }))}
            >
              <div className='flex items-center justify-between'>
                <div className='flex items-center space-x-2'>
                  <span
                    className={`w-6 h-6 rounded text-center text-xs font-bold flex items-center justify-center ${getChangeTypeColor(change.type)}`}
                  >
                    {getChangeTypeIcon(change.type)}
                  </span>
                  <span className='font-medium'>{change.path}</span>
                </div>
                <div className='flex items-center space-x-2'>
                  <span className='text-xs bg-gray-200 px-2 py-1 rounded'>
                    {(change.confidence * 100).toFixed(0)}%
                  </span>
                </div>
              </div>

              <div className='mt-1 text-sm text-gray-600'>{change.description}</div>
            </motion.div>
          ))}
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className={`diff-viewer flex items-center justify-center h-64 ${className}`}>
        <div className='loading loading-spinner loading-lg'></div>
      </div>
    );
  }

  if (!diff && !visualDiff) {
    return (
      <div
        className={`diff-viewer flex items-center justify-center h-64 text-gray-500 ${className}`}
      >
        <p>No diff data available</p>
      </div>
    );
  }

  return (
    <div className={`diff-viewer bg-white border rounded-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className='diff-header bg-gray-50 border-b p-4'>
        <div className='flex items-center justify-between'>
          <h2 className='text-lg font-semibold'>Diff Viewer</h2>

          <div className='flex items-center space-x-2'>
            {/* View mode toggle */}
            <div className='btn-group'>
              <button
                className={`btn btn-sm ${state.viewMode === 'unified' ? 'btn-active' : 'btn-outline'}`}
                onClick={() => setState(prev => ({ ...prev, viewMode: 'unified' }))}
              >
                Unified
              </button>
              <button
                className={`btn btn-sm ${state.viewMode === 'split' ? 'btn-active' : 'btn-outline'}`}
                onClick={() => setState(prev => ({ ...prev, viewMode: 'split' }))}
              >
                Split
              </button>
            </div>

            {/* Options */}
            <div className='dropdown dropdown-end'>
              <label tabIndex={0} className='btn btn-sm btn-outline'>
                Options
              </label>
              <div
                tabIndex={0}
                className='dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52'
              >
                <label className='label cursor-pointer'>
                  <span className='label-text'>Show whitespace</span>
                  <input
                    type='checkbox'
                    className='checkbox checkbox-sm'
                    checked={state.showWhitespace}
                    onChange={e =>
                      setState(prev => ({ ...prev, showWhitespace: e.target.checked }))
                    }
                  />
                </label>
                <div className='form-control'>
                  <label className='label'>
                    <span className='label-text'>Context lines</span>
                  </label>
                  <input
                    type='range'
                    min='0'
                    max='10'
                    value={state.contextLines}
                    className='range range-sm'
                    onChange={e =>
                      setState(prev => ({ ...prev, contextLines: parseInt(e.target.value) }))
                    }
                  />
                  <div className='text-xs text-center'>{state.contextLines}</div>
                </div>
              </div>
            </div>

            {onClose && (
              <button onClick={onClose} className='btn btn-sm btn-outline'>
                ✕
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className='diff-content'>
        {diff && renderChangesSummary()}

        {visualDiff?.textDiff && renderTextDiff(visualDiff.textDiff)}

        {visualDiff?.structuralDiff && renderStructuralDiff(visualDiff.structuralDiff)}

        {diff && renderChangesList()}
      </div>

      {/* Selected change details */}
      {state.selectedChange && (
        <div className='selected-change-details bg-gray-50 border-t p-4'>
          <h4 className='font-medium mb-2'>Change Details</h4>

          <div className='grid grid-cols-2 gap-4'>
            <div>
              <label className='label'>
                <span className='label-text font-medium'>Path</span>
              </label>
              <p className='text-sm font-mono'>{state.selectedChange.path}</p>
            </div>

            <div>
              <label className='label'>
                <span className='label-text font-medium'>Type</span>
              </label>
              <span className={`badge ${getChangeTypeColor(state.selectedChange.type)}`}>
                {state.selectedChange.type}
              </span>
            </div>
          </div>

          <div className='mt-4'>
            <label className='label'>
              <span className='label-text font-medium'>Description</span>
            </label>
            <p className='text-sm'>{state.selectedChange.description}</p>
          </div>

          {state.selectedChange.beforeValue && (
            <div className='mt-4'>
              <label className='label'>
                <span className='label-text font-medium'>Before</span>
              </label>
              <pre className='text-xs bg-white p-2 rounded border overflow-auto max-h-32'>
                {typeof state.selectedChange.beforeValue === 'string'
                  ? state.selectedChange.beforeValue
                  : JSON.stringify(state.selectedChange.beforeValue, null, 2)}
              </pre>
            </div>
          )}

          {state.selectedChange.afterValue && (
            <div className='mt-4'>
              <label className='label'>
                <span className='label-text font-medium'>After</span>
              </label>
              <pre className='text-xs bg-white p-2 rounded border overflow-auto max-h-32'>
                {typeof state.selectedChange.afterValue === 'string'
                  ? state.selectedChange.afterValue
                  : JSON.stringify(state.selectedChange.afterValue, null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DiffViewer;

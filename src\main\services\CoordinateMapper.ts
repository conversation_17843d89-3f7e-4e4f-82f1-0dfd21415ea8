import { createLogger } from '../utils/logger';
import { DocumentCoordinates, ValidationError } from '../../shared/types/Document';

const logger = createLogger('CoordinateMapper');

export interface CoordinateTransformOptions {
  sourceWidth: number;
  sourceHeight: number;
  targetWidth: number;
  targetHeight: number;
  preserveAspectRatio: boolean;
  scaleMode: ScaleMode;
}

export enum ScaleMode {
  FIT = 'fit', // Scale to fit within target dimensions
  FILL = 'fill', // Scale to fill target dimensions (may crop)
  STRETCH = 'stretch', // Stretch to exact target dimensions
  NONE = 'none', // No scaling
}

export interface CoordinateValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
  suggestions: string[];
}

export interface CoordinateAdjustment {
  coordinateId: string;
  originalCoordinates: DocumentCoordinates;
  adjustedCoordinates: DocumentCoordinates;
  adjustmentType: AdjustmentType;
  confidence: number;
  reason: string;
}

export enum AdjustmentType {
  MANUAL = 'manual',
  AUTOMATIC = 'automatic',
  AI_SUGGESTED = 'ai_suggested',
  TEMPLATE_BASED = 'template_based',
}

export interface CoordinateVisualization {
  coordinates: DocumentCoordinates[];
  overlays: VisualizationOverlay[];
  debugInfo: DebugInfo;
}

export interface VisualizationOverlay {
  id: string;
  type: OverlayType;
  coordinates: DocumentCoordinates;
  color: string;
  label: string;
  opacity: number;
}

export enum OverlayType {
  FIELD_BOUNDARY = 'field_boundary',
  DETECTION_AREA = 'detection_area',
  VALIDATION_ERROR = 'validation_error',
  SUGGESTION = 'suggestion',
  GRID_LINE = 'grid_line',
}

export interface DebugInfo {
  originalDetection: DocumentCoordinates[];
  transformedCoordinates: DocumentCoordinates[];
  validationResults: CoordinateValidationResult[];
  performanceMetrics: {
    detectionTime: number;
    transformationTime: number;
    validationTime: number;
  };
}

/**
 * Coordinate mapping system for precise field positioning
 */
export class CoordinateMapper {
  private readonly adjustmentHistory: Map<string, CoordinateAdjustment[]> = new Map();
  private readonly validationCache: Map<string, CoordinateValidationResult> = new Map();

  /**
   * Transform coordinates from source to target dimensions
   */
  public transformCoordinates(
    sourceCoordinates: DocumentCoordinates,
    options: CoordinateTransformOptions
  ): DocumentCoordinates {
    logger.debug('Transforming coordinates', {
      source: sourceCoordinates,
      options,
    });

    const { sourceWidth, sourceHeight, targetWidth, targetHeight } = options;

    let scaleX: number;
    let scaleY: number;

    switch (options.scaleMode) {
      case ScaleMode.FIT:
        const scale = Math.min(targetWidth / sourceWidth, targetHeight / sourceHeight);
        scaleX = scaleY = scale;
        break;

      case ScaleMode.FILL:
        const fillScale = Math.max(targetWidth / sourceWidth, targetHeight / sourceHeight);
        scaleX = scaleY = fillScale;
        break;

      case ScaleMode.STRETCH:
        scaleX = targetWidth / sourceWidth;
        scaleY = targetHeight / sourceHeight;
        break;

      case ScaleMode.NONE:
      default:
        scaleX = scaleY = 1;
        break;
    }

    // Apply aspect ratio preservation if requested
    if (options.preserveAspectRatio && options.scaleMode === ScaleMode.STRETCH) {
      const aspectScale = Math.min(scaleX, scaleY);
      scaleX = scaleY = aspectScale;
    }

    const transformedCoordinates: DocumentCoordinates = {
      x: sourceCoordinates.x * scaleX,
      y: sourceCoordinates.y * scaleY,
      width: sourceCoordinates.width * scaleX,
      height: sourceCoordinates.height * scaleY,
      pageNumber: sourceCoordinates.pageNumber,
      rotation: sourceCoordinates.rotation || 0,
      scale: (sourceCoordinates.scale || 1) * Math.min(scaleX, scaleY),
    };

    logger.debug('Coordinates transformed', {
      original: sourceCoordinates,
      transformed: transformedCoordinates,
      scaleX,
      scaleY,
    });

    return transformedCoordinates;
  }

  /**
   * Validate coordinate boundaries and constraints
   */
  public validateCoordinates(
    coordinates: DocumentCoordinates,
    documentDimensions: { width: number; height: number; pageCount: number }
  ): CoordinateValidationResult {
    const cacheKey = `${JSON.stringify(coordinates)}_${JSON.stringify(documentDimensions)}`;
    const cached = this.validationCache.get(cacheKey);
    if (cached) {
      return cached;
    }

    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];
    const suggestions: string[] = [];

    // Validate basic coordinate values
    if (coordinates.x < 0 || coordinates.y < 0) {
      errors.push({
        code: 'NEGATIVE_COORDINATES',
        message: 'Coordinates cannot be negative',
        severity: 'error',
      });
    }

    if (coordinates.width <= 0 || coordinates.height <= 0) {
      errors.push({
        code: 'INVALID_DIMENSIONS',
        message: 'Width and height must be positive',
        severity: 'error',
      });
    }

    // Validate boundaries
    if (coordinates.x + coordinates.width > documentDimensions.width) {
      errors.push({
        code: 'EXCEEDS_WIDTH_BOUNDARY',
        message: 'Field extends beyond document width',
        severity: 'error',
      });
      suggestions.push('Reduce field width or adjust x position');
    }

    if (coordinates.y + coordinates.height > documentDimensions.height) {
      errors.push({
        code: 'EXCEEDS_HEIGHT_BOUNDARY',
        message: 'Field extends beyond document height',
        severity: 'error',
      });
      suggestions.push('Reduce field height or adjust y position');
    }

    // Validate page number
    if (coordinates.pageNumber < 1 || coordinates.pageNumber > documentDimensions.pageCount) {
      errors.push({
        code: 'INVALID_PAGE_NUMBER',
        message: `Page number must be between 1 and ${documentDimensions.pageCount}`,
        severity: 'error',
      });
    }

    // Check for very small fields
    if (coordinates.width < 10 || coordinates.height < 10) {
      warnings.push({
        code: 'VERY_SMALL_FIELD',
        message: 'Field is very small and may be difficult to interact with',
        severity: 'warning',
      });
      suggestions.push('Consider increasing field size for better usability');
    }

    // Check for very large fields
    const fieldArea = coordinates.width * coordinates.height;
    const documentArea = documentDimensions.width * documentDimensions.height;
    if (fieldArea > documentArea * 0.5) {
      warnings.push({
        code: 'VERY_LARGE_FIELD',
        message: 'Field covers more than 50% of the document',
        severity: 'warning',
      });
      suggestions.push('Consider reducing field size');
    }

    const result: CoordinateValidationResult = {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
    };

    // Cache the result
    this.validationCache.set(cacheKey, result);

    return result;
  }

  /**
   * Manually adjust coordinates with validation
   */
  public adjustCoordinates(
    coordinateId: string,
    originalCoordinates: DocumentCoordinates,
    adjustedCoordinates: DocumentCoordinates,
    adjustmentType: AdjustmentType = AdjustmentType.MANUAL,
    reason: string = 'Manual adjustment'
  ): CoordinateAdjustment {
    logger.info('Adjusting coordinates', {
      coordinateId,
      adjustmentType,
      reason,
    });

    const adjustment: CoordinateAdjustment = {
      coordinateId,
      originalCoordinates,
      adjustedCoordinates,
      adjustmentType,
      confidence: adjustmentType === AdjustmentType.MANUAL ? 1.0 : 0.8,
      reason,
    };

    // Store adjustment in history
    const history = this.adjustmentHistory.get(coordinateId) || [];
    history.push(adjustment);
    this.adjustmentHistory.set(coordinateId, history);

    // Clear validation cache for this coordinate
    this.clearValidationCache(coordinateId);

    logger.debug('Coordinate adjustment recorded', {
      coordinateId,
      historyLength: history.length,
    });

    return adjustment;
  }

  /**
   * Auto-adjust coordinates based on content analysis
   */
  public async autoAdjustCoordinates(
    coordinates: DocumentCoordinates,
    documentBuffer: Buffer,
    fieldType: string
  ): Promise<DocumentCoordinates> {
    logger.debug('Auto-adjusting coordinates', {
      coordinates,
      fieldType,
    });

    try {
      // Analyze the area around the coordinates to find optimal positioning
      const analysisResult = await this.analyzeCoordinateArea(
        coordinates,
        documentBuffer,
        fieldType
      );

      if (analysisResult.suggestedCoordinates) {
        return analysisResult.suggestedCoordinates;
      }

      // If no better coordinates found, return original with minor adjustments
      return this.applyMinorAdjustments(coordinates, fieldType);
    } catch (error) {
      logger.error('Auto-adjustment failed', { error, coordinates });
      return coordinates;
    }
  }

  /**
   * Create coordinate mapping visualization
   */
  public createVisualization(
    coordinates: DocumentCoordinates[],
    documentDimensions: { width: number; height: number },
    options: { showGrid?: boolean; showLabels?: boolean; highlightErrors?: boolean } = {}
  ): CoordinateVisualization {
    const overlays: VisualizationOverlay[] = [];
    const debugInfo: DebugInfo = {
      originalDetection: coordinates,
      transformedCoordinates: coordinates,
      validationResults: [],
      performanceMetrics: {
        detectionTime: 0,
        transformationTime: 0,
        validationTime: 0,
      },
    };

    // Create field boundary overlays
    coordinates.forEach((coord, index) => {
      overlays.push({
        id: `field_${index}`,
        type: OverlayType.FIELD_BOUNDARY,
        coordinates: coord,
        color: '#007bff',
        label: options.showLabels ? `Field ${index + 1}` : '',
        opacity: 0.3,
      });

      // Validate and add error overlays if needed
      if (options.highlightErrors) {
        const validation = this.validateCoordinates(coord, {
          width: documentDimensions.width,
          height: documentDimensions.height,
          pageCount: 1,
        });

        debugInfo.validationResults.push(validation);

        if (!validation.isValid) {
          overlays.push({
            id: `error_${index}`,
            type: OverlayType.VALIDATION_ERROR,
            coordinates: coord,
            color: '#dc3545',
            label: 'Error',
            opacity: 0.5,
          });
        }
      }
    });

    // Add grid lines if requested
    if (options.showGrid) {
      const gridSpacing = 50;
      for (let x = 0; x < documentDimensions.width; x += gridSpacing) {
        overlays.push({
          id: `grid_v_${x}`,
          type: OverlayType.GRID_LINE,
          coordinates: {
            x,
            y: 0,
            width: 1,
            height: documentDimensions.height,
            pageNumber: 1,
          },
          color: '#e9ecef',
          label: '',
          opacity: 0.2,
        });
      }

      for (let y = 0; y < documentDimensions.height; y += gridSpacing) {
        overlays.push({
          id: `grid_h_${y}`,
          type: OverlayType.GRID_LINE,
          coordinates: {
            x: 0,
            y,
            width: documentDimensions.width,
            height: 1,
            pageNumber: 1,
          },
          color: '#e9ecef',
          label: '',
          opacity: 0.2,
        });
      }
    }

    return {
      coordinates,
      overlays,
      debugInfo,
    };
  }

  /**
   * Get coordinate adjustment history
   */
  public getAdjustmentHistory(coordinateId: string): CoordinateAdjustment[] {
    return this.adjustmentHistory.get(coordinateId) || [];
  }

  /**
   * Undo last coordinate adjustment
   */
  public undoLastAdjustment(coordinateId: string): DocumentCoordinates | null {
    const history = this.adjustmentHistory.get(coordinateId);
    if (!history || history.length === 0) {
      return null;
    }

    const lastAdjustment = history.pop();
    if (lastAdjustment) {
      this.clearValidationCache(coordinateId);
      return lastAdjustment.originalCoordinates;
    }

    return null;
  }

  /**
   * Clear validation cache
   */
  public clearValidationCache(coordinateId?: string): void {
    if (coordinateId) {
      // Clear cache entries related to specific coordinate
      for (const key of this.validationCache.keys()) {
        if (key.includes(coordinateId)) {
          this.validationCache.delete(key);
        }
      }
    } else {
      // Clear entire cache
      this.validationCache.clear();
    }
  }

  /**
   * Analyze coordinate area for optimal positioning
   */
  private async analyzeCoordinateArea(
    coordinates: DocumentCoordinates,
    _documentBuffer: Buffer,
    _fieldType: string
  ): Promise<{ suggestedCoordinates?: DocumentCoordinates; confidence: number }> {
    // This would typically use image analysis to find optimal field positioning
    // For now, return a simple analysis

    const expandedArea = {
      x: Math.max(0, coordinates.x - 10),
      y: Math.max(0, coordinates.y - 10),
      width: coordinates.width + 20,
      height: coordinates.height + 20,
      pageNumber: coordinates.pageNumber,
    };

    // Simulate analysis result
    return {
      suggestedCoordinates: expandedArea,
      confidence: 0.8,
    };
  }

  /**
   * Apply minor adjustments based on field type
   */
  private applyMinorAdjustments(
    coordinates: DocumentCoordinates,
    fieldType: string
  ): DocumentCoordinates {
    const adjusted = { ...coordinates };

    // Apply field-type specific adjustments
    switch (fieldType) {
      case 'text':
        // Ensure minimum height for text fields
        adjusted.height = Math.max(adjusted.height, 20);
        break;

      case 'checkbox':
        // Make checkboxes square
        const size = Math.max(adjusted.width, adjusted.height);
        adjusted.width = adjusted.height = Math.min(size, 20);
        break;

      case 'signature':
        // Ensure adequate size for signatures
        adjusted.width = Math.max(adjusted.width, 150);
        adjusted.height = Math.max(adjusted.height, 50);
        break;
    }

    return adjusted;
  }

  /**
   * Snap coordinates to grid
   */
  public snapToGrid(coordinates: DocumentCoordinates, gridSize: number = 10): DocumentCoordinates {
    return {
      ...coordinates,
      x: Math.round(coordinates.x / gridSize) * gridSize,
      y: Math.round(coordinates.y / gridSize) * gridSize,
      width: Math.round(coordinates.width / gridSize) * gridSize,
      height: Math.round(coordinates.height / gridSize) * gridSize,
    };
  }

  /**
   * Calculate distance between two coordinates
   */
  public calculateDistance(coord1: DocumentCoordinates, coord2: DocumentCoordinates): number {
    const centerX1 = coord1.x + coord1.width / 2;
    const centerY1 = coord1.y + coord1.height / 2;
    const centerX2 = coord2.x + coord2.width / 2;
    const centerY2 = coord2.y + coord2.height / 2;

    return Math.sqrt(Math.pow(centerX2 - centerX1, 2) + Math.pow(centerY2 - centerY1, 2));
  }

  /**
   * Check if two coordinates overlap
   */
  public checkOverlap(coord1: DocumentCoordinates, coord2: DocumentCoordinates): boolean {
    return !(
      coord1.x + coord1.width <= coord2.x ||
      coord2.x + coord2.width <= coord1.x ||
      coord1.y + coord1.height <= coord2.y ||
      coord2.y + coord2.height <= coord1.y ||
      coord1.pageNumber !== coord2.pageNumber
    );
  }

  /**
   * Find optimal positioning to avoid overlaps
   */
  public resolveOverlaps(
    coordinates: DocumentCoordinates[],
    documentDimensions: { width: number; height: number }
  ): DocumentCoordinates[] {
    const resolved = [...coordinates];

    for (let i = 0; i < resolved.length; i++) {
      for (let j = i + 1; j < resolved.length; j++) {
        const coord1 = resolved[i];
        const coord2 = resolved[j];
        if (coord1 && coord2 && this.checkOverlap(coord1, coord2)) {
          // Move the second coordinate to avoid overlap
          resolved[j] = this.findNonOverlappingPosition(
            coord2,
            resolved.slice(0, j),
            documentDimensions
          );
        }
      }
    }

    return resolved;
  }

  /**
   * Find a non-overlapping position for a coordinate
   */
  private findNonOverlappingPosition(
    coordinate: DocumentCoordinates,
    existingCoordinates: DocumentCoordinates[],
    documentDimensions: { width: number; height: number }
  ): DocumentCoordinates {
    const step = 10;
    const newCoord = { ...coordinate };

    // Try moving right first
    while (newCoord.x + newCoord.width < documentDimensions.width) {
      newCoord.x += step;
      if (!existingCoordinates.some(coord => this.checkOverlap(newCoord, coord))) {
        return newCoord;
      }
    }

    // Reset x and try moving down
    newCoord.x = coordinate.x;
    while (newCoord.y + newCoord.height < documentDimensions.height) {
      newCoord.y += step;
      if (!existingCoordinates.some(coord => this.checkOverlap(newCoord, coord))) {
        return newCoord;
      }
    }

    // If no position found, return original
    return coordinate;
  }
}

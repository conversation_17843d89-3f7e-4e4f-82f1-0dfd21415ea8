module.exports = {
  // TypeScript and JavaScript files
  '*.{ts,tsx,js,jsx}': [
    'eslint --fix --max-warnings=0',
    'prettier --write'
  ],
  
  // TypeScript files - run type checking
  '*.{ts,tsx}': [
    () => 'npm run type-check'
  ],
  
  // JSON, CSS, Markdown files
  '*.{json,css,md}': [
    'prettier --write'
  ],
  
  // Package.json - validate structure
  'package.json': [
    'npm run type-check',
    () => 'node -e "JSON.parse(require(\'fs\').readFileSync(\'package.json\', \'utf8\'))"'
  ],
  
  // Test files - run related tests
  '*.{test,spec}.{ts,tsx,js,jsx}': [
    () => 'npm run test:unit -- --passWithNoTests --findRelatedTests --bail'
  ],
  
  // Configuration files - validate syntax
  '*.{js,json}': [
    () => 'node -c'
  ],
  
  // CSS files - run through PostCSS
  '*.css': [
    'prettier --write',
    () => 'npm run build:css'
  ]
};
import { app } from 'electron';
import * as path from 'path';
import * as fs from 'fs-extra';
import { logger } from '../utils/logger';
import {
  AIProvider,
  AIProviderType,
  AIModel,
  AIModelType,
  AICapability,
  UserPreferences,
  ResponseStyle,
} from '../../shared/types/AI';

export interface AIServiceConfiguration {
  providers: AIProvider[];
  models: AIModel[];
  defaultProvider: string;
  defaultModel: string;
  userPreferences: UserPreferences;
  features: AIFeatureConfig;
  security: AISecurityConfig;
  performance: AIPerformanceConfig;
  monitoring: AIMonitoringConfig;
}

export interface AIFeatureConfig {
  enableEmbeddings: boolean;
  enableReasoning: boolean;
  enableAgentExecution: boolean;
  enableKnowledgeBase: boolean;
  enableFormFilling: boolean;
  enableDocumentAnalysis: boolean;
  enableBatchProcessing: boolean;
  enableCaching: boolean;
}

export interface AISecurityConfig {
  encryptApiKeys: boolean;
  enableRateLimiting: boolean;
  maxRequestsPerMinute: number;
  enableAuditLogging: boolean;
  allowedDomains: string[];
  blockSensitiveData: boolean;
  dataRetentionDays: number;
}

export interface AIPerformanceConfig {
  maxConcurrentRequests: number;
  requestTimeoutMs: number;
  cacheSize: number;
  cacheTTLSeconds: number;
  enableCompression: boolean;
  batchSize: number;
  workerThreads: number;
}

export interface AIMonitoringConfig {
  enableMetrics: boolean;
  enableHealthChecks: boolean;
  healthCheckIntervalMs: number;
  enableErrorTracking: boolean;
  enableUsageTracking: boolean;
  metricsRetentionDays: number;
}

/**
 * AI Service Configuration Manager
 * Handles configuration loading, validation, and management for AI services
 */
export class AIServiceConfigManager {
  private config: AIServiceConfiguration;
  private readonly configPath: string;
  private readonly defaultConfig: AIServiceConfiguration;

  constructor() {
    this.configPath = path.join(app.getPath('userData'), 'ai-service-config.json');
    this.defaultConfig = this.createDefaultConfig();
    this.config = { ...this.defaultConfig };
  }

  /**
   * Initialize configuration manager
   */
  async initialize(): Promise<void> {
    try {
      await this.loadConfiguration();
      this.validateConfiguration();
      logger.info('AI Service Configuration initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize AI Service Configuration', error);
      // Use default configuration on error
      this.config = { ...this.defaultConfig };
      await this.saveConfiguration();
    }
  }

  /**
   * Load configuration from file
   */
  private async loadConfiguration(): Promise<void> {
    try {
      if (await fs.pathExists(this.configPath)) {
        const configData = await fs.readJson(this.configPath);
        this.config = this.mergeWithDefaults(configData);
        logger.info('Configuration loaded from file', { path: this.configPath });
      } else {
        this.config = { ...this.defaultConfig };
        await this.saveConfiguration();
        logger.info('Created default configuration file', { path: this.configPath });
      }
    } catch (error) {
      logger.error('Failed to load configuration', error);
      throw error;
    }
  }

  /**
   * Save configuration to file
   */
  async saveConfiguration(): Promise<void> {
    try {
      await fs.ensureDir(path.dirname(this.configPath));
      await fs.writeJson(this.configPath, this.config, { spaces: 2 });
      logger.info('Configuration saved to file', { path: this.configPath });
    } catch (error) {
      logger.error('Failed to save configuration', error);
      throw error;
    }
  }

  /**
   * Validate configuration
   */
  private validateConfiguration(): void {
    const errors: string[] = [];

    // Validate providers
    if (!this.config.providers || this.config.providers.length === 0) {
      errors.push('At least one AI provider must be configured');
    }

    // Validate default provider exists
    const defaultProvider = this.config.providers.find(p => p.id === this.config.defaultProvider);
    if (!defaultProvider) {
      errors.push(`Default provider '${this.config.defaultProvider}' not found in providers list`);
    }

    // Validate models
    if (!this.config.models || this.config.models.length === 0) {
      errors.push('At least one AI model must be configured');
    }

    // Validate default model exists
    const defaultModel = this.config.models.find(m => m.id === this.config.defaultModel);
    if (!defaultModel) {
      errors.push(`Default model '${this.config.defaultModel}' not found in models list`);
    }

    // Validate performance settings
    if (this.config.performance.maxConcurrentRequests < 1) {
      errors.push('maxConcurrentRequests must be at least 1');
    }

    if (this.config.performance.requestTimeoutMs < 1000) {
      errors.push('requestTimeoutMs must be at least 1000ms');
    }

    // Validate security settings
    if (this.config.security.maxRequestsPerMinute < 1) {
      errors.push('maxRequestsPerMinute must be at least 1');
    }

    if (errors.length > 0) {
      throw new Error(`Configuration validation failed: ${errors.join(', ')}`);
    }
  }

  /**
   * Create default configuration
   */
  private createDefaultConfig(): AIServiceConfiguration {
    return {
      providers: [
        {
          id: 'openai',
          name: 'OpenAI',
          type: AIProviderType.OPENAI,
          endpoint: 'https://api.openai.com/v1',
          apiKey: process.env.OPENAI_API_KEY || '',
          models: [
            {
              id: 'gpt-4',
              name: 'GPT-4',
              type: AIModelType.CHAT,
              maxTokens: 8192,
              costPerToken: 0.00003,
              capabilities: [AICapability.TEXT_GENERATION, AICapability.TEXT_ANALYSIS],
            },
            {
              id: 'gpt-3.5-turbo',
              name: 'GPT-3.5 Turbo',
              type: AIModelType.CHAT,
              maxTokens: 4096,
              costPerToken: 0.000002,
              capabilities: [AICapability.TEXT_GENERATION],
            },
            {
              id: 'text-embedding-ada-002',
              name: 'Text Embedding Ada 002',
              type: AIModelType.EMBEDDING,
              maxTokens: 8191,
              costPerToken: 0.0000001,
              capabilities: [AICapability.TEXT_ANALYSIS],
            },
          ],
          isActive: true,
        },
        {
          id: 'azure',
          name: 'Azure OpenAI',
          type: AIProviderType.AZURE,
          endpoint: process.env.AZURE_AI_ENDPOINT || '',
          apiKey: process.env.AZURE_AI_KEY || '',
          models: [
            {
              id: 'gpt-4',
              name: 'GPT-4',
              type: AIModelType.CHAT,
              maxTokens: 8192,
              costPerToken: 0.00003,
              capabilities: [AICapability.TEXT_GENERATION, AICapability.TEXT_ANALYSIS],
            },
            {
              id: 'text-embedding-ada-002',
              name: 'Text Embedding Ada 002',
              type: AIModelType.EMBEDDING,
              maxTokens: 8191,
              costPerToken: 0.0000001,
              capabilities: [AICapability.TEXT_ANALYSIS],
            },
          ],
          isActive: false,
        },
      ],
      models: [
        {
          id: 'gpt-4',
          name: 'GPT-4',
          type: AIModelType.CHAT,
          maxTokens: 8192,
          costPerToken: 0.00003,
          capabilities: [
            AICapability.TEXT_GENERATION,
            AICapability.TEXT_ANALYSIS,
            AICapability.DOCUMENT_UNDERSTANDING,
            AICapability.QUESTION_ANSWERING,
            AICapability.SUMMARIZATION,
          ],
        },
        {
          id: 'gpt-3.5-turbo',
          name: 'GPT-3.5 Turbo',
          type: AIModelType.CHAT,
          maxTokens: 4096,
          costPerToken: 0.000002,
          capabilities: [
            AICapability.TEXT_GENERATION,
            AICapability.TEXT_ANALYSIS,
            AICapability.QUESTION_ANSWERING,
          ],
        },
        {
          id: 'text-embedding-ada-002',
          name: 'Text Embedding Ada 002',
          type: AIModelType.EMBEDDING,
          maxTokens: 8191,
          costPerToken: 0.0000001,
          capabilities: [AICapability.TEXT_ANALYSIS],
        },
      ],
      defaultProvider: 'openai',
      defaultModel: 'gpt-4',
      userPreferences: {
        preferredModel: 'gpt-4',
        temperature: 0.7,
        maxTokens: 1000,
        language: 'en',
        responseStyle: ResponseStyle.DETAILED,
      },
      features: {
        enableEmbeddings: true,
        enableReasoning: true,
        enableAgentExecution: true,
        enableKnowledgeBase: true,
        enableFormFilling: true,
        enableDocumentAnalysis: true,
        enableBatchProcessing: true,
        enableCaching: true,
      },
      security: {
        encryptApiKeys: true,
        enableRateLimiting: true,
        maxRequestsPerMinute: 60,
        enableAuditLogging: true,
        allowedDomains: [],
        blockSensitiveData: true,
        dataRetentionDays: 30,
      },
      performance: {
        maxConcurrentRequests: 10,
        requestTimeoutMs: 30000,
        cacheSize: 1000,
        cacheTTLSeconds: 3600,
        enableCompression: true,
        batchSize: 10,
        workerThreads: 2,
      },
      monitoring: {
        enableMetrics: false,
        enableHealthChecks: false,
        healthCheckIntervalMs: 60000,
        enableErrorTracking: false,
        enableUsageTracking: false,
        metricsRetentionDays: 7,
      },
    };
  }

  /**
   * Merge loaded configuration with defaults
   */
  private mergeWithDefaults(loadedConfig: Partial<AIServiceConfiguration>): AIServiceConfiguration {
    return {
      providers: loadedConfig.providers || this.defaultConfig.providers,
      models: loadedConfig.models || this.defaultConfig.models,
      defaultProvider: loadedConfig.defaultProvider || this.defaultConfig.defaultProvider,
      defaultModel: loadedConfig.defaultModel || this.defaultConfig.defaultModel,
      userPreferences: {
        ...this.defaultConfig.userPreferences,
        ...loadedConfig.userPreferences,
      },
      features: {
        ...this.defaultConfig.features,
        ...loadedConfig.features,
      },
      security: {
        ...this.defaultConfig.security,
        ...loadedConfig.security,
      },
      performance: {
        ...this.defaultConfig.performance,
        ...loadedConfig.performance,
      },
      monitoring: {
        ...this.defaultConfig.monitoring,
        ...loadedConfig.monitoring,
      },
    };
  }

  /**
   * Get current configuration
   */
  getConfiguration(): AIServiceConfiguration {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  async updateConfiguration(updates: Partial<AIServiceConfiguration>): Promise<void> {
    const newConfig = this.mergeWithDefaults({ ...this.config, ...updates });

    // Validate new configuration
    const tempConfig = this.config;
    this.config = newConfig;

    try {
      this.validateConfiguration();
      await this.saveConfiguration();
      logger.info('Configuration updated successfully');
    } catch (error) {
      // Restore previous configuration on validation error
      this.config = tempConfig;
      logger.error('Failed to update configuration', error);
      throw error;
    }
  }

  /**
   * Get provider by ID
   */
  getProvider(providerId: string): AIProvider | undefined {
    return this.config.providers.find(p => p.id === providerId);
  }

  /**
   * Get model by ID
   */
  getModel(modelId: string): AIModel | undefined {
    return this.config.models.find(m => m.id === modelId);
  }

  /**
   * Get active providers
   */
  getActiveProviders(): AIProvider[] {
    return this.config.providers.filter(p => p.isActive);
  }

  /**
   * Get models by capability
   */
  getModelsByCapability(capability: AICapability): AIModel[] {
    return this.config.models.filter(m => m.capabilities.includes(capability));
  }

  /**
   * Update provider
   */
  async updateProvider(providerId: string, updates: Partial<AIProvider>): Promise<void> {
    const providerIndex = this.config.providers.findIndex(p => p.id === providerId);
    if (providerIndex === -1) {
      throw new Error(`Provider '${providerId}' not found`);
    }

    const currentProvider = this.config.providers[providerIndex];
    if (!currentProvider) {
      throw new Error(`Provider at index ${providerIndex} not found`);
    }

    this.config.providers[providerIndex] = {
      id: currentProvider.id,
      name: currentProvider.name,
      type: currentProvider.type,
      endpoint: currentProvider.endpoint,
      apiKey: currentProvider.apiKey,
      models: currentProvider.models,
      isActive: currentProvider.isActive,
      ...updates,
    };

    await this.saveConfiguration();
    logger.info(`Provider '${providerId}' updated`);
  }

  /**
   * Update user preferences
   */
  async updateUserPreferences(preferences: Partial<UserPreferences>): Promise<void> {
    this.config.userPreferences = {
      ...this.config.userPreferences,
      ...preferences,
    };

    await this.saveConfiguration();
    logger.info('User preferences updated');
  }

  /**
   * Reset to default configuration
   */
  async resetToDefaults(): Promise<void> {
    this.config = { ...this.defaultConfig };
    await this.saveConfiguration();
    logger.info('Configuration reset to defaults');
  }

  /**
   * Export configuration
   */
  async exportConfiguration(filePath: string): Promise<void> {
    try {
      await fs.writeJson(filePath, this.config, { spaces: 2 });
      logger.info('Configuration exported', { path: filePath });
    } catch (error) {
      logger.error('Failed to export configuration', error);
      throw error;
    }
  }

  /**
   * Import configuration
   */
  async importConfiguration(filePath: string): Promise<void> {
    try {
      const importedConfig = await fs.readJson(filePath);
      await this.updateConfiguration(importedConfig);
      logger.info('Configuration imported', { path: filePath });
    } catch (error) {
      logger.error('Failed to import configuration', error);
      throw error;
    }
  }
}

// Export singleton instance
export const aiServiceConfig = new AIServiceConfigManager();

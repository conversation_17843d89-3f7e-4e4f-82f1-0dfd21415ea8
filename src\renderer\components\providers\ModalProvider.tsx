import React, { createContext, useContext, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface ModalOptions {
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closable?: boolean;
  backdrop?: boolean;
  className?: string;
  onClose?: () => void;
}

interface Modal {
  id: string;
  content: React.ReactNode;
  options: ModalOptions;
}

interface ModalContextType {
  modals: Modal[];
  openModal: (content: React.ReactNode, options?: ModalOptions) => string;
  closeModal: (id: string) => void;
  closeAllModals: () => void;
  confirm: (
    message: string,
    options?: {
      title?: string;
      confirmText?: string;
      cancelText?: string;
      type?: 'info' | 'warning' | 'error' | 'success';
    }
  ) => Promise<boolean>;
  alert: (
    message: string,
    options?: {
      title?: string;
      type?: 'info' | 'warning' | 'error' | 'success';
    }
  ) => Promise<void>;
}

const ModalContext = createContext<ModalContextType | undefined>(undefined);

/**
 * Modal provider component with advanced modal management
 */
export const ModalProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [modals, setModals] = useState<Modal[]>([]);

  const openModal = useCallback((content: React.ReactNode, options: ModalOptions = {}) => {
    const id = `modal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const modal: Modal = {
      id,
      content,
      options: {
        size: 'md',
        closable: true,
        backdrop: true,
        ...options,
      },
    };

    setModals(prev => [...prev, modal]);
    return id;
  }, []);

  const closeModal = useCallback((id: string) => {
    setModals(prev => {
      const modal = prev.find(m => m.id === id);
      if (modal?.options.onClose) {
        modal.options.onClose();
      }
      return prev.filter(m => m.id !== id);
    });
  }, []);

  const closeAllModals = useCallback(() => {
    setModals(prev => {
      prev.forEach(modal => {
        if (modal.options.onClose) {
          modal.options.onClose();
        }
      });
      return [];
    });
  }, []);

  const confirm = useCallback((
    message: string,
    options: {
      title?: string;
      confirmText?: string;
      cancelText?: string;
      type?: 'info' | 'warning' | 'error' | 'success';
    } = {}
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      const {
        title = 'Confirm',
        confirmText = 'Confirm',
        cancelText = 'Cancel',
        type = 'info',
      } = options;

      const handleConfirm = () => {
        closeModal(modalId);
        resolve(true);
      };

      const handleCancel = () => {
        closeModal(modalId);
        resolve(false);
      };

      const getTypeStyles = () => {
        switch (type) {
          case 'warning':
            return {
              icon: '⚠️',
              iconColor: 'text-warning',
              confirmClass: 'btn-warning',
            };
          case 'error':
            return {
              icon: '❌',
              iconColor: 'text-error',
              confirmClass: 'btn-error',
            };
          case 'success':
            return {
              icon: '✅',
              iconColor: 'text-success',
              confirmClass: 'btn-success',
            };
          default:
            return {
              icon: 'ℹ️',
              iconColor: 'text-info',
              confirmClass: 'btn-primary',
            };
        }
      };

      const typeStyles = getTypeStyles();

      const content = (
        <div className="p-6">
          <div className="flex items-center mb-4">
            <span className={`text-2xl mr-3 ${typeStyles.iconColor}`}>
              {typeStyles.icon}
            </span>
            <h3 className="text-lg font-semibold">{title}</h3>
          </div>
          <p className="text-base-content/80 mb-6">{message}</p>
          <div className="flex justify-end space-x-3">
            <button className="btn btn-ghost" onClick={handleCancel}>
              {cancelText}
            </button>
            <button className={`btn ${typeStyles.confirmClass}`} onClick={handleConfirm}>
              {confirmText}
            </button>
          </div>
        </div>
      );

      const modalId = openModal(content, {
        size: 'sm',
        closable: false,
        backdrop: true,
      });
    });
  }, [openModal, closeModal]);

  const alert = useCallback((
    message: string,
    options: {
      title?: string;
      type?: 'info' | 'warning' | 'error' | 'success';
    } = {}
  ): Promise<void> => {
    return new Promise((resolve) => {
      const { title = 'Alert', type = 'info' } = options;

      const handleOk = () => {
        closeModal(modalId);
        resolve();
      };

      const getTypeStyles = () => {
        switch (type) {
          case 'warning':
            return { icon: '⚠️', iconColor: 'text-warning' };
          case 'error':
            return { icon: '❌', iconColor: 'text-error' };
          case 'success':
            return { icon: '✅', iconColor: 'text-success' };
          default:
            return { icon: 'ℹ️', iconColor: 'text-info' };
        }
      };

      const typeStyles = getTypeStyles();

      const content = (
        <div className="p-6">
          <div className="flex items-center mb-4">
            <span className={`text-2xl mr-3 ${typeStyles.iconColor}`}>
              {typeStyles.icon}
            </span>
            <h3 className="text-lg font-semibold">{title}</h3>
          </div>
          <p className="text-base-content/80 mb-6">{message}</p>
          <div className="flex justify-end">
            <button className="btn btn-primary" onClick={handleOk}>
              OK
            </button>
          </div>
        </div>
      );

      const modalId = openModal(content, {
        size: 'sm',
        closable: false,
        backdrop: true,
      });
    });
  }, [openModal, closeModal]);

  const value: ModalContextType = {
    modals,
    openModal,
    closeModal,
    closeAllModals,
    confirm,
    alert,
  };

  return (
    <ModalContext.Provider value={value}>
      {children}
      <ModalRenderer modals={modals} onClose={closeModal} />
    </ModalContext.Provider>
  );
};

/**
 * Modal renderer component
 */
const ModalRenderer: React.FC<{
  modals: Modal[];
  onClose: (id: string) => void;
}> = ({ modals, onClose }) => {
  const getSizeClasses = (size: ModalOptions['size']) => {
    switch (size) {
      case 'sm':
        return 'max-w-sm';
      case 'md':
        return 'max-w-md';
      case 'lg':
        return 'max-w-2xl';
      case 'xl':
        return 'max-w-4xl';
      case 'full':
        return 'max-w-full mx-4';
      default:
        return 'max-w-md';
    }
  };

  return (
    <AnimatePresence>
      {modals.map((modal, index) => (
        <motion.div
          key={modal.id}
          className="fixed inset-0 flex items-center justify-center"
          style={{ zIndex: 1000 + index }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          {/* Backdrop */}
          {modal.options.backdrop && (
            <motion.div
              className="absolute inset-0 bg-black/50 backdrop-blur-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => modal.options.closable && onClose(modal.id)}
            />
          )}

          {/* Modal content */}
          <motion.div
            className={`
              relative bg-base-100 rounded-lg shadow-xl max-h-[90vh] overflow-auto
              ${getSizeClasses(modal.options.size)}
              ${modal.options.className || ''}
            `}
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ duration: 0.2 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close button */}
            {modal.options.closable && (
              <button
                className="absolute top-4 right-4 btn btn-ghost btn-sm btn-circle z-10"
                onClick={() => onClose(modal.id)}
                aria-label="Close modal"
              >
                ✕
              </button>
            )}

            {/* Title */}
            {modal.options.title && (
              <div className="px-6 py-4 border-b border-base-300">
                <h2 className="text-xl font-semibold">{modal.options.title}</h2>
              </div>
            )}

            {/* Content */}
            <div className={modal.options.title ? '' : 'pt-4'}>
              {modal.content}
            </div>
          </motion.div>
        </motion.div>
      ))}
    </AnimatePresence>
  );
};

/**
 * Hook to use modal context
 */
export const useModal = () => {
  const context = useContext(ModalContext);
  if (context === undefined) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};

export default ModalProvider;

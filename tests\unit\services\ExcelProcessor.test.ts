import * as ExcelJS from 'exceljs';
import { ExcelProcessor } from '../../../src/main/services/ExcelProcessor';
import { DocumentType, ProcessingOptions } from '../../../src/shared/types/Document';

describe('ExcelProcessor', () => {
  let processor: ExcelProcessor;

  beforeEach(() => {
    processor = new ExcelProcessor();
  });

  describe('Constructor and Capabilities', () => {
    it('should initialize with correct capabilities', () => {
      const capabilities = processor.getCapabilities();

      expect(capabilities.supportedTypes).toContain(DocumentType.EXCEL);
      expect(capabilities.canExtractText).toBe(true);
      expect(capabilities.canExtractTables).toBe(true);
      expect(capabilities.canPreserveFormatting).toBe(true);
      expect(capabilities.maxFileSize).toBe(50 * 1024 * 1024);
    });

    it('should support Excel document type', () => {
      expect(processor.canProcess(DocumentType.EXCEL)).toBe(true);
      expect(processor.canProcess(DocumentType.PDF)).toBe(false);
    });
  });

  describe('Excel Format Detection', () => {
    it('should detect XLSX format from buffer signature', () => {
      // XLSX files start with ZIP signature (PK)
      const xlsxBuffer = Buffer.from([0x50, 0x4b, 0x03, 0x04, 0x00, 0x00, 0x00, 0x00]);
      const format = processor.detectExcelFormat(xlsxBuffer, 'test.xlsx');
      expect(format).toBe('.xlsx');
    });

    it('should detect XLSM format from filename', () => {
      const xlsxBuffer = Buffer.from([0x50, 0x4b, 0x03, 0x04, 0x00, 0x00, 0x00, 0x00]);
      const format = processor.detectExcelFormat(xlsxBuffer, 'test.xlsm');
      expect(format).toBe('.xlsm');
    });

    it('should detect XLS format from buffer signature', () => {
      // XLS files have OLE signature
      const xlsBuffer = Buffer.from([0xd0, 0xcf, 0x11, 0xe0, 0x00, 0x00, 0x00, 0x00]);
      const format = processor.detectExcelFormat(xlsBuffer, 'test.xls');
      expect(format).toBe('.xls');
    });

    it('should throw error for unknown format', () => {
      const unknownBuffer = Buffer.from([0x00, 0x00, 0x00, 0x00]);
      expect(() => processor.detectExcelFormat(unknownBuffer)).toThrow(
        'Unable to detect Excel file format'
      );
    });
  });

  describe('Cell Data Extraction', () => {
    let workbook: ExcelJS.Workbook;
    let worksheet: ExcelJS.Worksheet;

    beforeEach(() => {
      workbook = new ExcelJS.Workbook();
      worksheet = workbook.addWorksheet('Test Sheet');
    });

    it('should extract text cell data correctly', () => {
      worksheet.getCell('A1').value = 'Hello World';
      const cellData = processor.extractCellData(worksheet, 'A1');

      expect(cellData.value).toBe('Hello World');
      expect(cellData.type).toBe('text');
      expect(cellData.address).toBe('A1');
    });

    it('should extract number cell data correctly', () => {
      worksheet.getCell('B1').value = 42;
      const cellData = processor.extractCellData(worksheet, 'B1');

      expect(cellData.value).toBe(42);
      expect(cellData.type).toBe('number');
    });

    it('should extract boolean cell data correctly', () => {
      worksheet.getCell('C1').value = true;
      const cellData = processor.extractCellData(worksheet, 'C1');

      expect(cellData.value).toBe(true);
      expect(cellData.type).toBe('boolean');
    });

    it('should extract date cell data correctly', () => {
      const testDate = new Date('2023-01-01');
      worksheet.getCell('D1').value = testDate;
      const cellData = processor.extractCellData(worksheet, 'D1');

      expect(cellData.value).toEqual(testDate);
      expect(cellData.type).toBe('date');
    });

    it('should extract formula cell data correctly', () => {
      worksheet.getCell('E1').value = { formula: 'SUM(A1:A10)', result: 100 };
      const cellData = processor.extractCellData(worksheet, 'E1');

      expect(cellData.type).toBe('formula');
      expect(cellData.formula).toBe('SUM(A1:A10)');
      expect(cellData.calculatedValue).toBe(100);
    });

    it('should handle null/undefined values', () => {
      const cellData = processor.extractCellData(worksheet, 'F1');

      expect(cellData.value).toBeNull();
      expect(cellData.type).toBe('text');
    });
  });

  describe('Header Detection', () => {
    let workbook: ExcelJS.Workbook;
    let worksheet: ExcelJS.Worksheet;

    beforeEach(() => {
      workbook = new ExcelJS.Workbook();
      worksheet = workbook.addWorksheet('Test Sheet');
    });

    it('should detect header row correctly', () => {
      // Set up header row with text values
      worksheet.getCell('A1').value = 'Name';
      worksheet.getCell('B1').value = 'Age';
      worksheet.getCell('C1').value = 'Email';

      // Set up data rows with mixed types
      worksheet.getCell('A2').value = 'John Doe';
      worksheet.getCell('B2').value = 30;
      worksheet.getCell('C2').value = '<EMAIL>';

      const headerRow = processor.detectHeaderRow(worksheet, 1, 3, 1, 3);
      expect(headerRow).toBe(1);
    });

    it('should return null when no header row is found', () => {
      // Set up all numeric data
      worksheet.getCell('A1').value = 1;
      worksheet.getCell('B1').value = 2;
      worksheet.getCell('C1').value = 3;

      const headerRow = processor.detectHeaderRow(worksheet, 1, 1, 1, 3);
      expect(headerRow).toBeNull();
    });
  });

  describe('Column Mapping', () => {
    let workbook: ExcelJS.Workbook;
    let worksheet: ExcelJS.Worksheet;

    beforeEach(() => {
      workbook = new ExcelJS.Workbook();
      worksheet = workbook.addWorksheet('Test Sheet');
    });

    it('should extract column mapping from headers', () => {
      worksheet.getCell('A1').value = 'First Name';
      worksheet.getCell('B1').value = 'Last Name';
      worksheet.getCell('C1').value = 'Email Address';

      const mapping = processor.extractColumnMapping(worksheet, 1, 1, 3);

      expect(mapping.get(1)).toBe('First Name');
      expect(mapping.get(2)).toBe('Last Name');
      expect(mapping.get(3)).toBe('Email Address');
    });

    it('should handle empty header cells', () => {
      worksheet.getCell('A1').value = 'Name';
      worksheet.getCell('B1').value = '';
      worksheet.getCell('C1').value = 'Email';

      const mapping = processor.extractColumnMapping(worksheet, 1, 1, 3);

      expect(mapping.get(1)).toBe('Name');
      expect(mapping.has(2)).toBe(false);
      expect(mapping.get(3)).toBe('Email');
    });
  });

  describe('Data Type Inference', () => {
    let workbook: ExcelJS.Workbook;
    let worksheet: ExcelJS.Worksheet;

    beforeEach(() => {
      workbook = new ExcelJS.Workbook();
      worksheet = workbook.addWorksheet('Test Sheet');
    });

    it('should infer column data types correctly', () => {
      // Set up mixed data types
      worksheet.getCell('A1').value = 'John';
      worksheet.getCell('A2').value = 'Jane';
      worksheet.getCell('A3').value = 'Bob';

      worksheet.getCell('B1').value = 25;
      worksheet.getCell('B2').value = 30;
      worksheet.getCell('B3').value = 35;

      const columnTypes = processor.inferColumnDataTypes(worksheet, 1, 3, 1, 2);

      const textColumn = columnTypes.get(1);
      expect(textColumn?.primaryType).toBe('text');
      expect(textColumn?.confidence).toBeGreaterThan(0.9);

      const numberColumn = columnTypes.get(2);
      expect(numberColumn?.primaryType).toBe('number');
      expect(numberColumn?.statistics).toBeDefined();
      expect(numberColumn?.statistics?.mean).toBe(30);
    });
  });

  describe('Missing Value Detection', () => {
    let workbook: ExcelJS.Workbook;
    let worksheet: ExcelJS.Worksheet;

    beforeEach(() => {
      workbook = new ExcelJS.Workbook();
      worksheet = workbook.addWorksheet('Test Sheet');
    });

    it('should detect missing values correctly', () => {
      worksheet.getCell('A1').value = 'Data';
      worksheet.getCell('A2').value = '';
      worksheet.getCell('A3').value = 'More Data';

      const analysis = processor.detectMissingValues(worksheet, 'A1:A3');

      expect(analysis.missingCount).toBe(1);
      expect(analysis.missingRate).toBeCloseTo(0.33, 2);
      expect(analysis.missingCells).toContain('A2');
      expect(analysis.severity).toBe('medium');
    });

    it('should handle range with no missing values', () => {
      worksheet.getCell('A1').value = 'Data1';
      worksheet.getCell('A2').value = 'Data2';
      worksheet.getCell('A3').value = 'Data3';

      const analysis = processor.detectMissingValues(worksheet, 'A1:A3');

      expect(analysis.missingCount).toBe(0);
      expect(analysis.missingRate).toBe(0);
      expect(analysis.severity).toBe('low');
    });
  });

  describe('Data Cleaning', () => {
    let workbook: ExcelJS.Workbook;
    let worksheet: ExcelJS.Worksheet;

    beforeEach(() => {
      workbook = new ExcelJS.Workbook();
      worksheet = workbook.addWorksheet('Test Sheet');
    });

    it('should clean data according to options', () => {
      worksheet.getCell('A1').value = '  John Doe  ';
      worksheet.getCell('A2').value = 'jane smith';
      worksheet.getCell('A3').value = 'BOB JOHNSON';

      const result = processor.cleanColumnData(worksheet, 1, 1, 3, {
        trimWhitespace: true,
        normalizeCase: 'title',
      });

      expect(result.cleanedValues[0]).toBe('John Doe');
      expect(result.cleanedValues[1]).toBe('Jane Smith');
      expect(result.cleanedValues[2]).toBe('Bob Johnson');
      expect(result.changedCount).toBe(3);
    });

    it('should handle missing value filling', () => {
      worksheet.getCell('A1').value = 'Data';
      worksheet.getCell('A2').value = '';
      worksheet.getCell('A3').value = 'More Data';

      const result = processor.cleanColumnData(worksheet, 1, 1, 3, {
        fillMissingWith: 'N/A',
      });

      expect(result.cleanedValues[1]).toBe('N/A');
      expect(result.changedCount).toBe(1);
    });
  });

  describe('Text Extraction', () => {
    it('should extract text from Excel document', async () => {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Test Sheet');

      worksheet.getCell('A1').value = 'Header 1';
      worksheet.getCell('B1').value = 'Header 2';
      worksheet.getCell('A2').value = 'Data 1';
      worksheet.getCell('B2').value = 'Data 2';

      const buffer = await workbook.xlsx.writeBuffer();

      const options: ProcessingOptions = {
        extractText: true,
        extractImages: false,
        extractTables: false,
        detectFormFields: false,
        performOCR: false,
        enhanceImages: false,
        preserveFormatting: false,
      };

      const text = await processor.extractText(Buffer.from(buffer), options);

      expect(text).toContain('Test Sheet');
      expect(text).toContain('Header 1');
      expect(text).toContain('Header 2');
      expect(text).toContain('Data 1');
      expect(text).toContain('Data 2');
    });
  });

  describe('Structured Data Extraction', () => {
    it('should extract structured data from Excel document', async () => {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Test Sheet');

      worksheet.getCell('A1').value = 'Name';
      worksheet.getCell('B1').value = 'Age';
      worksheet.getCell('A2').value = 'John';
      worksheet.getCell('B2').value = 30;

      const buffer = await workbook.xlsx.writeBuffer();

      const options: ProcessingOptions = {
        extractText: true,
        extractImages: false,
        extractTables: true,
        detectFormFields: false,
        performOCR: false,
        enhanceImages: false,
        preserveFormatting: false,
      };

      const extractedData = await processor.extractStructuredData(Buffer.from(buffer), options);

      expect(extractedData.length).toBeGreaterThan(0);

      // Should have table data
      const tableData = extractedData.find(data => data.type === 'table');
      expect(tableData).toBeDefined();

      // Should have cell data
      const cellData = extractedData.filter(data => data.type === 'text');
      expect(cellData.length).toBeGreaterThan(0);
    });
  });

  describe('Validation', () => {
    it('should validate extracted data', async () => {
      const mockExtractedData = [
        {
          id: 'test-table-1',
          documentId: 'test-doc',
          type: 'table' as any,
          content: {
            id: 'table1',
            pageNumber: 1,
            bounds: { x: 0, y: 0, width: 100, height: 50, pageNumber: 1 },
            rows: [
              {
                cells: [
                  {
                    value: 'Header 1',
                    type: 'text' as const,
                    bounds: { x: 0, y: 0, width: 50, height: 25, pageNumber: 1 },
                  },
                  {
                    value: 'Header 2',
                    type: 'text' as const,
                    bounds: { x: 50, y: 0, width: 50, height: 25, pageNumber: 1 },
                  },
                ],
              },
              {
                cells: [
                  {
                    value: 'Data 1',
                    type: 'text' as const,
                    bounds: { x: 0, y: 25, width: 50, height: 25, pageNumber: 1 },
                  },
                  {
                    value: 'Data 2',
                    type: 'text' as const,
                    bounds: { x: 50, y: 25, width: 50, height: 25, pageNumber: 1 },
                  },
                ],
              },
            ],
            headers: ['Header 1', 'Header 2'],
          },
          confidence: 0.9,
          extractionMethod: 'excel_parser' as any,
          createdAt: new Date(),
        },
      ];

      const validationResults = await processor.validateExtraction(mockExtractedData);

      expect(validationResults).toHaveLength(1);
      expect(validationResults[0]?.isValid).toBe(true);
      expect(validationResults[0]?.fieldId).toBe('test-table-1');
    });

    it('should detect empty table validation error', async () => {
      const mockExtractedData = [
        {
          id: 'test-empty-table',
          documentId: 'test-doc',
          type: 'table' as any,
          content: {
            id: 'empty-table',
            pageNumber: 1,
            bounds: { x: 0, y: 0, width: 100, height: 50, pageNumber: 1 },
            rows: [],
            headers: [],
          },
          confidence: 0.9,
          extractionMethod: 'excel_parser' as any,
          createdAt: new Date(),
        },
      ];

      const validationResults = await processor.validateExtraction(mockExtractedData);

      expect(validationResults[0]?.isValid).toBe(false);
      expect(validationResults[0]?.errors[0]?.code).toBe('EMPTY_TABLE');
    });
  });
});

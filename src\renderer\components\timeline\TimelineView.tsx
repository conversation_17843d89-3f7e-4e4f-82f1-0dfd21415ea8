import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  TimelineEntry,
  TimelineQuery,
  TimelineFilter,
  TimelineSortField,
  SortDirection,
  ActionType,
  TimelineEntryType,
  TimelinePriority,
  Branch,
  UndoRedoState,
} from '../../../shared/types/Timeline';

export interface TimelineViewProps {
  entries: TimelineEntry[];
  branches: Branch[];
  undoRedoState: UndoRedoState;
  isLoading?: boolean;
  onEntryClick?: (entry: TimelineEntry) => void;
  onBranchSwitch?: (branchId: string) => void;
  onUndo?: () => void;
  onRedo?: () => void;
  onCreateCheckpoint?: () => void;
  onFilter?: (filter: TimelineFilter) => void;
  onSort?: (field: TimelineSortField, direction: SortDirection) => void;
  className?: string;
}

export interface TimelineViewState {
  selectedEntry: TimelineEntry | null;
  filter: TimelineFilter;
  sortField: TimelineSortField;
  sortDirection: SortDirection;
  showBranches: boolean;
  searchTerm: string;
}

const TimelineView: React.FC<TimelineViewProps> = ({
  entries,
  branches,
  undoRedoState,
  isLoading = false,
  onEntryClick,
  onBranchSwitch,
  onUndo,
  onRedo,
  onCreateCheckpoint,
  onFilter,
  onSort,
  className = '',
}) => {
  const [state, setState] = useState<TimelineViewState>({
    selectedEntry: null,
    filter: {},
    sortField: TimelineSortField.CREATED_AT,
    sortDirection: SortDirection.DESC,
    showBranches: true,
    searchTerm: '',
  });

  const filteredEntries = useMemo(() => {
    let filtered = [...entries];

    // Apply search filter
    if (state.searchTerm) {
      filtered = filtered.filter(
        entry =>
          entry.description.toLowerCase().includes(state.searchTerm.toLowerCase()) ||
          entry.action.toLowerCase().includes(state.searchTerm.toLowerCase())
      );
    }

    // Apply type filter
    if (state.filter.types?.length) {
      filtered = filtered.filter(entry => state.filter.types!.includes(entry.type));
    }

    // Apply action filter
    if (state.filter.actions?.length) {
      filtered = filtered.filter(entry => state.filter.actions!.includes(entry.action));
    }

    // Apply date range filter
    if (state.filter.dateRange) {
      filtered = filtered.filter(entry => {
        const entryDate = entry.createdAt;
        return (
          entryDate >= state.filter.dateRange!.start && entryDate <= state.filter.dateRange!.end
        );
      });
    }

    // Apply user filter
    if (state.filter.users?.length) {
      filtered = filtered.filter(entry => state.filter.users!.includes(entry.userId || ''));
    }

    // Apply priority filter
    if (state.filter.priority?.length) {
      filtered = filtered.filter(entry => state.filter.priority!.includes(entry.metadata.priority));
    }

    return filtered;
  }, [entries, state.filter, state.searchTerm]);

  const sortedEntries = useMemo(() => {
    const sorted = [...filteredEntries];

    sorted.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (state.sortField) {
        case TimelineSortField.CREATED_AT:
          aValue = a.createdAt.getTime();
          bValue = b.createdAt.getTime();
          break;
        case TimelineSortField.TYPE:
          aValue = a.type;
          bValue = b.type;
          break;
        case TimelineSortField.ACTION:
          aValue = a.action;
          bValue = b.action;
          break;
        case TimelineSortField.USER:
          aValue = a.userId || '';
          bValue = b.userId || '';
          break;
        case TimelineSortField.PRIORITY:
          aValue = a.metadata.priority;
          bValue = b.metadata.priority;
          break;
        case TimelineSortField.PROCESSING_TIME:
          aValue = a.processingTime;
          bValue = b.processingTime;
          break;
        default:
          aValue = a.createdAt.getTime();
          bValue = b.createdAt.getTime();
      }

      if (state.sortDirection === SortDirection.ASC) {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return sorted;
  }, [filteredEntries, state.sortField, state.sortDirection]);

  const handleEntryClick = (entry: TimelineEntry) => {
    setState(prev => ({ ...prev, selectedEntry: entry }));
    onEntryClick?.(entry);
  };

  const handleFilterChange = (newFilter: Partial<TimelineFilter>) => {
    const updatedFilter = { ...state.filter, ...newFilter };
    setState(prev => ({ ...prev, filter: updatedFilter }));
    onFilter?.(updatedFilter);
  };

  const handleSortChange = (field: TimelineSortField) => {
    const direction =
      state.sortField === field && state.sortDirection === SortDirection.ASC
        ? SortDirection.DESC
        : SortDirection.ASC;

    setState(prev => ({ ...prev, sortField: field, sortDirection: direction }));
    onSort?.(field, direction);
  };

  const getEntryIcon = (entry: TimelineEntry): string => {
    switch (entry.type) {
      case TimelineEntryType.DOCUMENT_OPERATION:
        return '📄';
      case TimelineEntryType.AI_OPERATION:
        return '🤖';
      case TimelineEntryType.USER_ACTION:
        return '👤';
      case TimelineEntryType.SYSTEM_EVENT:
        return '⚙️';
      case TimelineEntryType.CHECKPOINT:
        return '📍';
      case TimelineEntryType.BRANCH_OPERATION:
        return '🌿';
      case TimelineEntryType.MERGE_OPERATION:
        return '🔀';
      default:
        return '📝';
    }
  };

  const getEntryColor = (entry: TimelineEntry): string => {
    switch (entry.metadata.priority) {
      case TimelinePriority.CRITICAL:
        return 'border-red-500 bg-red-50';
      case TimelinePriority.HIGH:
        return 'border-orange-500 bg-orange-50';
      case TimelinePriority.NORMAL:
        return 'border-blue-500 bg-blue-50';
      case TimelinePriority.LOW:
        return 'border-gray-500 bg-gray-50';
      default:
        return 'border-gray-300 bg-white';
    }
  };

  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <div className={`timeline-view flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className='timeline-header bg-white border-b border-gray-200 p-4'>
        <div className='flex items-center justify-between mb-4'>
          <h2 className='text-xl font-semibold text-gray-900'>Timeline</h2>

          {/* Action buttons */}
          <div className='flex items-center space-x-2'>
            <button
              onClick={onUndo}
              disabled={!undoRedoState.canUndo}
              className='btn btn-sm btn-outline disabled:opacity-50'
              title='Undo'
            >
              ↶
            </button>
            <button
              onClick={onRedo}
              disabled={!undoRedoState.canRedo}
              className='btn btn-sm btn-outline disabled:opacity-50'
              title='Redo'
            >
              ↷
            </button>
            <button
              onClick={onCreateCheckpoint}
              className='btn btn-sm btn-primary'
              title='Create Checkpoint'
            >
              📍 Checkpoint
            </button>
          </div>
        </div>

        {/* Search and filters */}
        <div className='flex items-center space-x-4'>
          <div className='flex-1'>
            <input
              type='text'
              placeholder='Search timeline...'
              value={state.searchTerm}
              onChange={e => setState(prev => ({ ...prev, searchTerm: e.target.value }))}
              className='input input-bordered w-full'
            />
          </div>

          <div className='dropdown dropdown-end'>
            <label tabIndex={0} className='btn btn-outline btn-sm'>
              Filter
            </label>
            <div
              tabIndex={0}
              className='dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52'
            >
              <div className='form-control'>
                <label className='label'>
                  <span className='label-text'>Entry Types</span>
                </label>
                {Object.values(TimelineEntryType).map(type => (
                  <label key={type} className='label cursor-pointer'>
                    <span className='label-text'>{type}</span>
                    <input
                      type='checkbox'
                      className='checkbox checkbox-sm'
                      checked={state.filter.types?.includes(type) || false}
                      onChange={e => {
                        const types = state.filter.types || [];
                        const newTypes = e.target.checked
                          ? [...types, type]
                          : types.filter(t => t !== type);
                        handleFilterChange({ types: newTypes });
                      }}
                    />
                  </label>
                ))}
              </div>
            </div>
          </div>

          <div className='dropdown dropdown-end'>
            <label tabIndex={0} className='btn btn-outline btn-sm'>
              Sort
            </label>
            <div
              tabIndex={0}
              className='dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52'
            >
              {Object.values(TimelineSortField).map(field => (
                <button
                  key={field}
                  className={`btn btn-ghost btn-sm justify-start ${
                    state.sortField === field ? 'btn-active' : ''
                  }`}
                  onClick={() => handleSortChange(field)}
                >
                  {field}{' '}
                  {state.sortField === field &&
                    (state.sortDirection === SortDirection.ASC ? '↑' : '↓')}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Branch selector */}
      {state.showBranches && branches.length > 0 && (
        <div className='branch-selector bg-gray-50 border-b border-gray-200 p-3'>
          <div className='flex items-center space-x-2'>
            <span className='text-sm font-medium text-gray-700'>Branch:</span>
            <select
              className='select select-bordered select-sm'
              onChange={e => onBranchSwitch?.(e.target.value)}
              value={branches.find(b => b.isActive)?.id || ''}
            >
              {branches.map(branch => (
                <option key={branch.id} value={branch.id}>
                  {branch.name} {branch.isActive ? '(current)' : ''}
                </option>
              ))}
            </select>
          </div>
        </div>
      )}

      {/* Timeline content */}
      <div className='timeline-content flex-1 overflow-auto p-4'>
        {isLoading ? (
          <div className='flex items-center justify-center h-32'>
            <div className='loading loading-spinner loading-lg'></div>
          </div>
        ) : sortedEntries.length === 0 ? (
          <div className='text-center text-gray-500 py-8'>
            <p>No timeline entries found</p>
            {state.searchTerm && (
              <p className='text-sm mt-2'>Try adjusting your search or filters</p>
            )}
          </div>
        ) : (
          <div className='timeline-entries space-y-4'>
            <AnimatePresence>
              {sortedEntries.map((entry, index) => (
                <motion.div
                  key={entry.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2, delay: index * 0.05 }}
                  className={`timeline-entry border-l-4 pl-4 py-3 cursor-pointer hover:bg-gray-50 rounded-r-lg transition-colors ${getEntryColor(
                    entry
                  )} ${state.selectedEntry?.id === entry.id ? 'ring-2 ring-blue-500' : ''}`}
                  onClick={() => handleEntryClick(entry)}
                >
                  <div className='flex items-start space-x-3'>
                    <div className='flex-shrink-0 text-2xl'>{getEntryIcon(entry)}</div>

                    <div className='flex-1 min-w-0'>
                      <div className='flex items-center justify-between'>
                        <h3 className='text-sm font-medium text-gray-900 truncate'>
                          {entry.description}
                        </h3>
                        <span className='text-xs text-gray-500'>{formatDate(entry.createdAt)}</span>
                      </div>

                      <div className='mt-1 flex items-center space-x-4 text-xs text-gray-500'>
                        <span className='badge badge-outline badge-xs'>{entry.type}</span>
                        <span className='badge badge-outline badge-xs'>{entry.action}</span>
                        {entry.userId && <span>by {entry.userId}</span>}
                        {entry.processingTime > 0 && <span>{entry.processingTime}ms</span>}
                      </div>

                      {entry.affectedDocuments.length > 0 && (
                        <div className='mt-2'>
                          <span className='text-xs text-gray-500'>
                            Affected: {entry.affectedDocuments.length} document(s)
                          </span>
                        </div>
                      )}

                      {entry.metadata.tags.length > 0 && (
                        <div className='mt-2 flex flex-wrap gap-1'>
                          {entry.metadata.tags.map(tag => (
                            <span key={tag} className='badge badge-ghost badge-xs'>
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}
      </div>

      {/* Entry details modal */}
      {state.selectedEntry && (
        <div className='modal modal-open'>
          <div className='modal-box max-w-2xl'>
            <h3 className='font-bold text-lg mb-4'>Timeline Entry Details</h3>

            <div className='space-y-4'>
              <div>
                <label className='label'>
                  <span className='label-text font-medium'>Description</span>
                </label>
                <p className='text-sm'>{state.selectedEntry.description}</p>
              </div>

              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <label className='label'>
                    <span className='label-text font-medium'>Type</span>
                  </label>
                  <p className='text-sm'>{state.selectedEntry.type}</p>
                </div>
                <div>
                  <label className='label'>
                    <span className='label-text font-medium'>Action</span>
                  </label>
                  <p className='text-sm'>{state.selectedEntry.action}</p>
                </div>
              </div>

              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <label className='label'>
                    <span className='label-text font-medium'>User</span>
                  </label>
                  <p className='text-sm'>{state.selectedEntry.userId || 'System'}</p>
                </div>
                <div>
                  <label className='label'>
                    <span className='label-text font-medium'>Created</span>
                  </label>
                  <p className='text-sm'>{formatDate(state.selectedEntry.createdAt)}</p>
                </div>
              </div>

              {state.selectedEntry.affectedDocuments.length > 0 && (
                <div>
                  <label className='label'>
                    <span className='label-text font-medium'>Affected Documents</span>
                  </label>
                  <ul className='text-sm space-y-1'>
                    {state.selectedEntry.affectedDocuments.map(docId => (
                      <li key={docId} className='text-blue-600'>
                        {docId}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <div>
                <label className='label'>
                  <span className='label-text font-medium'>Metadata</span>
                </label>
                <pre className='text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32'>
                  {JSON.stringify(state.selectedEntry.metadata, null, 2)}
                </pre>
              </div>
            </div>

            <div className='modal-action'>
              <button
                className='btn'
                onClick={() => setState(prev => ({ ...prev, selectedEntry: null }))}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TimelineView;

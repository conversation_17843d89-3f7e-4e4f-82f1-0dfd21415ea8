# AI Document Processor

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D18.0.0-brightgreen)](https://nodejs.org/)
[![Electron](https://img.shields.io/badge/Electron-37.2.5-blue)](https://electronjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.3.3-blue)](https://www.typescriptlang.org/)

> Enterprise-grade AI-powered document processing and intelligent paperwork
> management system

## Overview

AI Document Processor is a sophisticated cross-platform desktop application
built on Electron that combines advanced AI capabilities with intuitive document
management. The system employs a microservices-inspired architecture featuring
AI agents with comprehensive ETL-like pipelines, multi-process architecture, and
enterprise-level features to analyze, process, and automatically fill complex
document types including tax forms, legal documents, financial statements, and
regulatory filings.

## 🚀 Key Features

### Intelligent AI-Powered Processing

- **Advanced ETL Pipeline**: Multi-stage document processing with AI-powered
  extraction, transformation, and loading
- **Multi-Model AI Integration**: Azure AI, OpenAI, LangChain agents with
  embedding, reasoning, and chat generation
- **Semantic Knowledge Base**: ChromaDB-powered vector storage with semantic
  search and relationship mapping
- **Natural Language Processing**: Entity extraction, relationship mapping, and
  knowledge graph construction
- **Mathematical Computation**: Complex calculations, tax computations, and
  financial analysis

### Enterprise Document Management

- **Multi-Format Processing**: PDF (forms/text), Excel (formulas/charts), Word
  (structure/media), CSV (large datasets), Images (OCR)
- **Template Engine**: Coordinate mapping, variable assignment, bulk processing,
  and reusable configurations
- **Version Control System**: Git-like timeline management with branching,
  merging, and visual diff capabilities
- **Multi-File Concurrency**: Browser-style tab management with state isolation
  and session persistence
- **Project Management**: VSCode-inspired file explorer with project-based
  organization

### Advanced User Experience

- **Modern React UI**: TypeScript, TailwindCSS, DaisyUI with responsive design
  and theming
- **Advanced Components**: Monaco Editor, timeline viewer, diff viewer, form
  editor, and knowledge base interface
- **Annotation System**: Digital signatures, comments, highlights, drawings, and
  markup tools
- **Accessibility Compliance**: WCAG 2.1 AA compliance with screen reader and
  keyboard navigation support
- **Performance Optimization**: Multi-level caching, lazy loading,
  virtualization, and resource management

### Development Experience

- **Comprehensive Logging**: Multi-process structured JSON logging with IDE
  integration
- **Real-time Debugging**: Project-based log files accessible directly in your
  IDE
- **Console Interception**: Automatic capture of all console output and errors
- **Development Tools**: Hot reload, TypeScript support, and comprehensive
  testing

## 🛠️ Technology Stack

### Core Framework

- **Electron 37.2.5**: Cross-platform desktop application framework
- **Node.js ≥18.0.0**: Runtime environment with multi-process architecture
- **TypeScript 5.3.3**: Primary development language with strict mode

### Frontend Technologies

- **React 18.2.0**: UI component library with concurrent features
- **TailwindCSS 4.1.11**: Utility-first CSS framework
- **DaisyUI 5.0.50**: Component library with theme customization
- **Zustand 4.4.7**: Lightweight state management
- **React Query 5.17.0**: Server state management and caching
- **Monaco Editor**: VSCode editor component with syntax highlighting
- **Framer Motion 10.16.16**: Animation library for smooth transitions

### Document Processing

- **PDF.js 5.4.54**: PDF rendering, parsing, and form field extraction
- **PDFKit 0.17.1**: PDF generation and form filling
- **Tesseract.js 6.0.1**: OCR engine with multi-language support
- **Sharp 0.33.2**: High-performance image processing
- **Mammoth 1.6.0**: Word document processing
- **ExcelJS 4.4.0**: Excel file processing with formula evaluation

### AI and Machine Learning

- **Azure AI Inference 1.0.0-beta.6**: Azure AI model integration
- **OpenAI 4.28.0**: GPT model integration with streaming
- **LangChain 0.1.25**: AI application framework with agents and tools
- **ChromaDB 3.0.10**: Vector database for semantic search
- **TensorFlow.js 4.15.0**: Client-side machine learning
- **Natural 6.12.0**: Natural language processing utilities

### Database and Storage

- **Better-SQLite3 12.2.0**: High-performance SQLite database
- **Knex.js 3.1.0**: SQL query builder with migrations
- **Node-Cache 5.1.2**: In-memory caching with TTL

## 📋 Prerequisites

- **Node.js**: Version 18.0.0 or higher
- **npm**: Version 9.0.0 or higher
- **Git**: For version control
- **Python**: Required for some native dependencies (automatically handled by
  electron-builder)

## 🚀 Quick Start

### Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/hepzceo/ai-document-processor.git
   cd ai-document-processor
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Set up environment variables**

   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

4. **Initialize the database**
   ```bash
   npm run db:migrate
   npm run db:seed
   ```

### Development

1. **Start the development server**

   ```bash
   npm run dev
   ```

   This will start both the CSS watcher and Electron in development mode with
   hot reload.

2. **Alternative development commands**
   ```bash
   npm start              # Start Electron without CSS watching
   npm run build:css      # Build CSS once
   npm run build:css:watch # Watch CSS changes
   ```

### Development Logging

The application includes a comprehensive logging system that writes structured
JSON logs to the `logs/` directory in your project root:

- **Multi-process logging**: Captures logs from main, renderer, preload, and
  Chromium processes
- **IDE integration**: Open log files directly in your IDE for real-time
  debugging
- **Structured format**: Each log entry is a JSON object with timestamp, level,
  source, and data
- **Automatic rotation**: Log files are rotated when they exceed 10MB
- **Git ignored**: Log files are automatically excluded from version control

**View logs:**

1. Navigate to the `logs/` directory in your IDE
2. Open any `.log` file to view structured JSON logs
3. Use your IDE's search to find specific entries (e.g., search for
   `"level":"error"`)

See [docs/logging-system.md](docs/logging-system.md) for detailed documentation.

### Building and Packaging

1. **Build for production**

   ```bash
   npm run build
   ```

2. **Package the application**
   ```bash
   npm run package        # Package for current platform
   npm run make          # Create distributable packages
   npm run dist          # Full distribution build
   ```

## ⚙️ Development Automation

The project includes comprehensive development automation to ensure code quality
and streamline the development workflow:

### Automated Setup

For new developers, use the automated setup script:

```bash
npm run setup
```

This script will:

- Check Node.js and npm versions
- Install all dependencies
- Set up environment variables from template
- Initialize and migrate the database
- Install Git hooks
- Build the project
- Run initial tests
- Provide next steps guidance

### Git Hooks (Husky)

Pre-commit hooks automatically run on every commit:

- **Lint-staged**: Runs ESLint and Prettier on staged files
- **Type checking**: Validates TypeScript types
- **Unit tests**: Runs tests for changed files

Pre-push hooks run before pushing:

- **Full test suite**: Runs all tests
- **Security tests**: Checks for vulnerabilities
- **Performance tests**: Validates performance benchmarks
- **Linting check**: Ensures no linting issues
- **Format check**: Validates code formatting

### Code Quality Automation

The project uses several tools for automated code quality:

```bash
# Lint-staged (runs automatically on commit)
npx lint-staged

# Manual quality checks
npm run validate          # Run all quality checks
npm run lint             # ESLint with auto-fix
npm run format           # Prettier formatting
npm run type-check       # TypeScript validation
```

### VS Code Integration

The project includes comprehensive VS Code configuration:

- **Settings**: Optimized editor settings for the project
- **Extensions**: Recommended extensions for development
- **Tasks**: Pre-configured build and test tasks
- **Launch configurations**: Debug configurations for Electron
- **Workspace**: Complete workspace setup

Open the workspace file for the best experience:

```bash
code .vscode/ai-document-processor.code-workspace
```

### Development Validation

Validate your development environment setup:

```bash
npm run validate-setup
```

This checks:

- All configuration files exist
- Git hooks are properly installed
- VS Code configuration is complete
- Build tools are working
- Dependencies are installed

### Continuous Integration Scripts

Additional scripts for CI/CD workflows:

```bash
npm run ci               # Full CI pipeline (install, validate, build)
npm run clean            # Clean build artifacts
npm run clean:deps      # Clean and reinstall dependencies
npm run clean:all        # Complete cleanup
```

## 🧪 Testing

The project includes comprehensive testing infrastructure:

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:unit          # Unit tests
npm run test:integration   # Integration tests
npm run test:e2e          # End-to-end tests with Playwright
npm run test:performance  # Performance benchmarking
npm run test:security     # Security vulnerability tests

# Development testing
npm run test:watch        # Watch mode for development
npm run test:coverage     # Generate coverage reports
```

## 🔧 Development Scripts

### Code Quality

```bash
npm run lint              # ESLint with auto-fix
npm run lint:check        # ESLint check only
npm run format            # Prettier formatting
npm run format:check      # Prettier check only
npm run type-check        # TypeScript type checking
```

### Database Management

```bash
npm run db:migrate        # Run database migrations
npm run db:rollback       # Rollback last migration
npm run db:seed          # Seed database with test data
npm run db:reset         # Reset database completely
```

### Analysis and Optimization

```bash
npm run analyze          # Bundle analysis with webpack-bundle-analyzer
```

## 📁 Project Structure

```
ai-document-processor/
├── src/
│   ├── main/                    # Electron main process (Node.js/TypeScript)
│   │   ├── services/           # Core business logic services
│   │   ├── workers/            # Worker processes for CPU-intensive tasks
│   │   ├── database/           # Database management and migrations
│   │   └── ipc/               # Inter-process communication handlers
│   ├── renderer/               # React frontend (TypeScript/JSX)
│   │   ├── components/         # React UI components
│   │   ├── hooks/             # Custom React hooks
│   │   ├── stores/            # Zustand state management
│   │   └── services/          # Frontend service layer
│   ├── shared/                 # Shared code between processes
│   │   ├── types/             # TypeScript type definitions
│   │   ├── utils/             # Shared utility functions
│   │   └── constants/         # Shared constants and enums
│   └── preload/               # Preload scripts for security
├── tests/                      # Comprehensive testing infrastructure
├── config/                     # Configuration files
├── assets/                     # Static assets and resources
└── public/                     # Build output and static files
```

## 🔐 Security

The application implements enterprise-grade security measures:

- **Content Security Policy**: Strict CSP headers preventing XSS
- **Context Isolation**: Renderer process isolation with secure IPC
- **Input Validation**: Comprehensive validation using Joi schemas
- **Encryption**: AES-256 encryption for sensitive data
- **Audit Logging**: Comprehensive operation logging

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow TypeScript strict mode requirements
- Maintain 95%+ test coverage for critical paths
- Ensure WCAG 2.1 AA accessibility compliance
- Use conventional commit messages
- Update documentation for new features

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file
for details.

## 👥 Authors

- **hepzceo** - _Initial work_ - [GitHub](https://github.com/hepzceo)
- Email: <EMAIL>

## 🙏 Acknowledgments

- Built with [Electron](https://electronjs.org/)
- UI powered by [React](https://reactjs.org/) and
  [TailwindCSS](https://tailwindcss.com/)
- AI integration through
  [Azure AI](https://azure.microsoft.com/en-us/products/ai-services) and
  [OpenAI](https://openai.com/)
- Document processing with [PDF.js](https://mozilla.github.io/pdf.js/),
  [Tesseract.js](https://tesseract.projectnaptha.com/), and more

## 📞 Support

For support, email <EMAIL> or create an issue on
GitHub.

---

**Note**: This is an enterprise-grade application designed for professional
document processing workflows. Please ensure you have the necessary API keys and
permissions for AI services before deployment.

---
type: 'always_apply'
---

{ "enabled": true, "name": "Auto Cleanup on Delete", "description":
"Automatically removes instances and references of deleted files from other
parts of the codebase, but only after ensuring necessary logic is migrated when
deemed appropriate", "version": "1", "when": { "type": "fileDeleted",
"patterns": [ "**/*" ] }, "then": { "type": "askAgent", "prompt": "A file has
been deleted from the codebase. Please analyze the deleted file and the
remaining codebase to:\n\n1. Identify all references to the deleted file
(imports, requires, file paths, etc.)\n2. Determine if any critical logic from
the deleted file needs to be migrated elsewhere\n3. If migration is needed,
suggest where and how to migrate the necessary logic\n4. Remove all references
to the deleted file from the codebase only after confirming logic migration is
complete or unnecessary\n5. Update any configuration files, build scripts, or
documentation that referenced the deleted file\n\nBe thorough in your analysis
and conservative about removing references - only proceed with cleanup after
ensuring no critical functionality will be lost." } } { "enabled": true, "name":
"ChromaDB Vector Guard", "description": "Monitors code changes to ensure vector
embeddings are always accomplished using ChromaDB dependency, prevents pollution
of the vector database, and ensures complete coverage of embeddings and vector
storage in the knowledge pipeline", "version": "1", "when": { "type":
"fileEdited", "patterns": [ "src/*.js", "src/*.ts", "*.py", "*.js", "*.ts" ] },
"then": { "type": "askAgent", "prompt": "Review the code changes and ensure
that: 1) All vector embedding operations use ChromaDB as the vector database
dependency, 2) ChromaDB is not being polluted with unnecessary data or improper
usage, 3) The embeddings and vector storage parts of the knowledge pipeline are
completely covered by ChromaDB implementation. Flag any violations or suggest
corrections to maintain proper ChromaDB usage patterns." } } { "enabled": true,
"name": "Code Quality Analyzer", "description": "Monitors source code files for
changes and analyzes modified code for potential improvements, including code
smells, design patterns, and best practices. Generates suggestions for improving
code quality while maintaining existing functionality.", "version": "1", "when":
{ "type": "fileEdited", "patterns": [ "src/*.js", "src/*.html", "src/*.css",
"*.js", "*.json" ] }, "then": { "type": "askAgent", "prompt": "Analyze the
recently modified code files for potential improvements. Focus on:\n\n1. Code
smells and anti-patterns\n2. Opportunities to apply better design patterns\n3.
Best practices for the specific language/framework\n4. Readability improvements
(naming, structure, comments)\n5. Maintainability enhancements (modularity,
separation of concerns)\n6. Performance optimizations (without changing
functionality)\n\nFor each suggestion, provide:\n- Clear explanation of the
issue or opportunity\n- Specific code examples showing the improvement\n-
Reasoning for why the change would be beneficial\n- Any potential trade-offs to
consider\n\nMaintain the existing functionality while suggesting improvements
that make the code more professional, readable, and maintainable." } } {
"enabled": true, "name": "CS Principles Enforcer", "description": "Automatically
reviews code changes to enforce core computer science principles including DRY,
KISS, SRP, Separation of Concerns, Fail Fast, and proper modularity", "version":
"1", "when": { "type": "fileEdited", "patterns": [ "src/*.js", "src/*.html",
"src/*.css", "*.js", "*.json" ] }, "then": { "type": "askAgent", "prompt":
"Review the changed files for violations of core CS principles: DRY (Don't
Repeat Yourself), KISS (Keep It Simple), SRP (Single Responsibility Principle),
Separation of Concerns, Fail Fast/Fail Loud, Use Established Interfaces,
Command-Query Separation, and Modularity/Reusability. Identify any code that
violates these principles and suggest specific refactoring improvements. Focus
on: 1) Repetitive logic that should be extracted into reusable functions, 2)
Overly complex or clever code that should be simplified, 3) Functions doing
multiple things that should be split, 4) Mixed concerns that should be
separated, 5) Silent failures that should fail fast and loud, 6) Reinvented
functionality that should use existing interfaces, 7) Functions that both
command and query, 8) Non-modular code that should be made reusable." } } {
"enabled": true, "name": "DaisyUI Component Enforcer", "description": "Monitors
UI component files and ensures daisyUI library with tailwindcss is used for
component creation with current documentation", "version": "1", "when": {
"type": "fileEdited", "patterns": [ "src/*.js", "src/*.jsx", "src/*.ts",
"src/*.tsx", "src/*.html", "src/*.css" ] }, "then": { "type": "askAgent",
"prompt": "When UI components are to be created always use the daisyUI library
and tailwindcss, #fetch https://daisyui.com/llms.txt for uptodate docs. Review
the changed files and ensure any UI components being created or modified follow
daisyUI patterns and conventions." } } { "enabled": true, "name": "Documentation
Sync", "description": "Listens to JavaScript source files and configuration
changes to automatically update project documentation", "version": "1", "when":
{ "type": "fileEdited", "patterns": [ "src/*.js", "src/*.html", "src/*.css",
"package.json", "forge.config.js" ] }, "then": { "type": "askAgent", "prompt":
"The source files in this JavaScript/Electron project have been modified. Please
review the changes and update the project documentation accordingly. If there's
a README.md file, update it to reflect any new features, API changes, or usage
instructions. If there's a /docs folder, update the relevant documentation files
there as well. Focus on keeping the documentation accurate and helpful for users
and developers." } }{ "enabled": true, "name": "CDN Usage Prevention",
"description": "Monitors HTML and JavaScript files for CDN links and suggests
npm package alternatives to ensure the application works offline", "version":
"1", "when": { "type": "fileCreated", "patterns": [ "src/*.html", "src/*.js",
"*.html", "*.js" ] }, "then": { "type": "askAgent", "prompt": "Review the
modified files for any CDN links (like cdnjs.cloudflare.com, unpkg.com,
jsdelivr.net, etc.). If you find any CDN references, suggest equivalent npm
packages that can be installed and bundled instead. Provide specific
installation commands and import statements to replace the CDN usage. Ensure the
application remains capable of working offline." } }{ "enabled": true, "name":
"Structure Enforcement", "description": "Monitors code changes to enforce proper
project structure, file organization, naming conventions, and architectural
patterns as defined in the steering docs", "version": "1", "when": { "type":
"fileEdited", "patterns": [ "src/**/*.ts", "src/**/*.tsx", "src/**/*.js",
"src/**/*.jsx", "tests/**/*.ts", "tests/**/*.tsx", "config/**/*.js",
"config/**/*.ts" ] }, "then": { "type": "askAgent", "prompt": "Review the
changed files to ensure they follow the proper structure defined in the steering
docs:\n\n1. Check file placement follows the multi-process architecture (main/,
renderer/, shared/, preload/)\n2. Verify naming conventions (PascalCase for
classes/components, camelCase for utilities)\n3. Ensure proper import patterns
(absolute imports from @/, relative for same directory)\n4. Validate service
organization and dependency injection patterns\n5. Check TypeScript type
definitions are in shared/types/\n6. Verify IPC handlers are properly organized
in main/ipc/\n7. Ensure React components follow the established hierarchy\n8.
Check database migrations follow numbering and naming conventions\n9. Validate
test files mirror source structure\n10. Ensure proper separation of concerns
between layers\n\nProvide specific feedback on any violations and suggest
corrections to align with the established architecture." } }{ "enabled": true,
"name": "Auto Test Coverage", "description": "Monitors source file changes to
identify new/modified functions, check test coverage, generate missing tests,
run tests, and update coverage reports", "version": "1", "when": { "type":
"fileEdited", "patterns": [ "src/*.js", "src/*.ts", "src/*.jsx", "src/*.tsx" ]
}, "then": { "type": "askAgent", "prompt": "A source file has been modified.
Please:\n1. Analyze the changed file to identify new or modified functions and
methods\n2. Check if corresponding tests exist for these changes in the test
directory\n3. Identify any missing test coverage for the new/modified code\n4.
Generate comprehensive test cases for any uncovered functionality\n5. Run the
test suite to verify all tests pass\n6. Update and generate coverage reports\n7.
Provide a summary of the testing actions taken and current coverage
status\n\nFocus on ensuring thorough test coverage for all new and modified
functionality." } }{ "enabled": true, "name": "Unused Variables Implementation
Guard", "description": "Monitors file changes to detect unused variables and
ensures they are properly implemented in the intended feature rather than being
removed as shortcuts to production readiness", "version": "1", "when": { "type":
"fileCreated", "patterns": [ "src/**/*.ts", "src/**/*.tsx", "src/**/*.js",
"src/**/*.jsx" ] }, "then": { "type": "askAgent", "prompt": "A file has been
created or modified that may contain unused variables. Please analyze the code
for:\n\n1. **Unused Variable Detection**: Identify any variables, imports, or
function parameters that are declared but not used\n2. **Intent Analysis**:
Determine if these unused variables represent incomplete feature implementation
rather than actual unused code\n3. **Implementation Guidance**: For each unused
variable, provide specific guidance on how it should be properly implemented in
the intended feature\n4. **Code Quality Check**: Ensure the feature is fully
implemented using all declared variables before considering the code
production-ready\n\n**Important**: Do NOT suggest removing unused variables as a
quick fix. Instead, focus on completing the intended functionality that would
utilize these variables. If a variable truly serves no purpose, explain why and
provide context for safe removal.\n\nPlease provide:\n- List of unused variables
found\n- Analysis of their intended purpose based on naming and context\n-
Specific implementation suggestions for each unused variable\n- Code examples
showing proper usage\n- Verification that the feature is complete and
production-ready" } }

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Document Processor</title>
    <!-- Content Security Policy for Electron -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';">
    <link rel="stylesheet" type="text/css" href="output.css">
    <script>
      // Global polyfill - must be first to prevent runtime errors
      (function() {
        'use strict';

        // Ensure global is defined before any other scripts load
        if (typeof global === 'undefined') {
          if (typeof globalThis !== 'undefined') {
            globalThis.global = globalThis;
          } else if (typeof window !== 'undefined') {
            window.global = window;
          } else if (typeof self !== 'undefined') {
            self.global = self;
          }
        }

        // Also ensure it's available on window for webpack compatibility
        if (typeof window !== 'undefined') {
          if (typeof window.global === 'undefined') {
            window.global = globalThis || window;
          }

          // Ensure process is available for webpack polyfills
          if (typeof window.process === 'undefined') {
            window.process = { env: {} };
          }
        }
      })();
    </script>
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>

import { EventEmitter } from 'events';
import { Worker } from 'worker_threads';
import { createLogger } from '../utils/logger';
import { Document, ExtractedData, FormTemplate } from '../../shared/types/Document';
import { FormFillerService, FormFillingOptions, FormFillingResult } from './FormFillerService';

const logger = createLogger('BulkProcessor');

export interface BulkProcessingJob {
  id: string;
  name: string;
  documents: Document[];
  template?: FormTemplate;
  extractedData?: ExtractedData[];
  options: FormFillingOptions;
  priority: JobPriority;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  status: JobStatus;
}

export enum JobStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  PAUSED = 'paused',
}

export enum JobPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  URGENT = 4,
}

export interface BulkProcessingProgress {
  jobId: string;
  totalDocuments: number;
  processedDocuments: number;
  successfulDocuments: number;
  failedDocuments: number;
  currentDocument?: string;
  estimatedTimeRemaining?: number;
  averageProcessingTime: number;
  throughput: number; // documents per minute
}

export interface BulkProcessingResult {
  jobId: string;
  success: boolean;
  totalDocuments: number;
  successfulResults: FormFillingResult[];
  failedResults: Array<{ document: Document; error: Error }>;
  processingTime: number;
  statistics: BulkProcessingStatistics;
}

export interface BulkProcessingStatistics {
  totalJobs: number;
  completedJobs: number;
  failedJobs: number;
  averageJobTime: number;
  averageDocumentTime: number;
  throughput: number;
  errorRate: number;
  commonErrors: Array<{ error: string; count: number }>;
  resourceUsage: ResourceUsage;
}

export interface ResourceUsage {
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  networkUsage: number;
  activeWorkers: number;
  queueSize: number;
}

export interface BulkProcessingOptions {
  maxConcurrentJobs: number;
  maxConcurrentDocuments: number;
  enableRetry: boolean;
  maxRetries: number;
  retryDelay: number;
  enableProgressReporting: boolean;
  progressReportInterval: number;
  enableResourceMonitoring: boolean;
  memoryThreshold: number;
  cpuThreshold: number;
}

/**
 * Bulk processing workflow for batch form processing
 */
export class BulkProcessor extends EventEmitter {
  private readonly formFillerService: FormFillerService;
  private readonly options: BulkProcessingOptions;
  private readonly jobQueue: BulkProcessingJob[] = [];
  private readonly activeJobs: Map<string, BulkProcessingJob> = new Map();
  private readonly workers: Worker[] = [];
  private readonly statistics: BulkProcessingStatistics;
  private isProcessing = false;
  private progressInterval?: NodeJS.Timeout;

  constructor(formFillerService: FormFillerService, options: Partial<BulkProcessingOptions> = {}) {
    super();
    this.formFillerService = formFillerService;
    this.options = this.mergeDefaultOptions(options);
    this.statistics = this.initializeStatistics();

    this.setupProgressReporting();
    this.setupResourceMonitoring();
  }

  /**
   * Add a bulk processing job to the queue
   */
  public addJob(
    name: string,
    documents: Document[],
    options: FormFillingOptions,
    template?: FormTemplate,
    extractedData?: ExtractedData[],
    priority: JobPriority = JobPriority.NORMAL
  ): string {
    const jobId = this.generateJobId();

    const job: BulkProcessingJob = {
      id: jobId,
      name,
      documents,
      ...(template && { template }),
      ...(extractedData && { extractedData }),
      options,
      priority,
      createdAt: new Date(),
      status: JobStatus.PENDING,
    };

    // Insert job in priority order
    this.insertJobByPriority(job);

    logger.info('Bulk processing job added', {
      jobId,
      name,
      documentCount: documents.length,
      priority,
    });

    this.emit('jobAdded', job);

    // Start processing if not already running
    if (!this.isProcessing) {
      this.startProcessing();
    }

    return jobId;
  }

  /**
   * Start processing jobs from the queue
   */
  public async startProcessing(): Promise<void> {
    if (this.isProcessing) {
      logger.warn('Bulk processing already running');
      return;
    }

    this.isProcessing = true;
    logger.info('Starting bulk processing');

    this.emit('processingStarted');

    try {
      while (this.jobQueue.length > 0 && this.isProcessing) {
        // Check resource constraints
        if (this.activeJobs.size >= this.options.maxConcurrentJobs) {
          await this.waitForJobCompletion();
          continue;
        }

        // Get next job
        const job = this.jobQueue.shift();
        if (!job) continue;

        // Start processing job
        this.processJob(job);
      }

      // Wait for all active jobs to complete
      while (this.activeJobs.size > 0) {
        await this.waitForJobCompletion();
      }

      this.isProcessing = false;
      logger.info('Bulk processing completed');
      this.emit('processingCompleted');
    } catch (error) {
      this.isProcessing = false;
      logger.error('Bulk processing failed', { error });
      this.emit('processingFailed', error);
    }
  }

  /**
   * Stop processing (graceful shutdown)
   */
  public async stopProcessing(): Promise<void> {
    logger.info('Stopping bulk processing');
    this.isProcessing = false;

    // Wait for active jobs to complete
    const activeJobPromises = Array.from(this.activeJobs.values()).map(job =>
      this.waitForJobCompletion(job.id)
    );

    await Promise.all(activeJobPromises);

    // Cleanup workers
    this.cleanupWorkers();

    logger.info('Bulk processing stopped');
    this.emit('processingStopped');
  }

  /**
   * Cancel a specific job
   */
  public cancelJob(jobId: string): boolean {
    // Remove from queue if pending
    const queueIndex = this.jobQueue.findIndex(job => job.id === jobId);
    if (queueIndex >= 0) {
      const job = this.jobQueue.splice(queueIndex, 1)[0];
      if (job) {
        job.status = JobStatus.CANCELLED;
        logger.info('Job cancelled from queue', { jobId });
        this.emit('jobCancelled', job);
      }
      return true;
    }

    // Cancel active job
    const activeJob = this.activeJobs.get(jobId);
    if (activeJob) {
      activeJob.status = JobStatus.CANCELLED;
      logger.info('Active job cancelled', { jobId });
      this.emit('jobCancelled', activeJob);
      return true;
    }

    return false;
  }

  /**
   * Pause a specific job
   */
  public pauseJob(jobId: string): boolean {
    const activeJob = this.activeJobs.get(jobId);
    if (activeJob && activeJob.status === JobStatus.RUNNING) {
      activeJob.status = JobStatus.PAUSED;
      logger.info('Job paused', { jobId });
      this.emit('jobPaused', activeJob);
      return true;
    }
    return false;
  }

  /**
   * Resume a paused job
   */
  public resumeJob(jobId: string): boolean {
    const activeJob = this.activeJobs.get(jobId);
    if (activeJob && activeJob.status === JobStatus.PAUSED) {
      activeJob.status = JobStatus.RUNNING;
      logger.info('Job resumed', { jobId });
      this.emit('jobResumed', activeJob);
      return true;
    }
    return false;
  }

  /**
   * Get job status
   */
  public getJobStatus(jobId: string): JobStatus | null {
    const queuedJob = this.jobQueue.find(job => job.id === jobId);
    if (queuedJob) return queuedJob.status;

    const activeJob = this.activeJobs.get(jobId);
    if (activeJob) return activeJob.status;

    return null;
  }

  /**
   * Get processing progress for a job
   */
  public getJobProgress(jobId: string): BulkProcessingProgress | null {
    const job = this.activeJobs.get(jobId);
    if (!job) return null;

    // This would be updated during processing
    return {
      jobId,
      totalDocuments: job.documents.length,
      processedDocuments: 0, // Would be tracked during processing
      successfulDocuments: 0,
      failedDocuments: 0,
      averageProcessingTime: 0,
      throughput: 0,
    };
  }

  /**
   * Get overall processing statistics
   */
  public getStatistics(): BulkProcessingStatistics {
    return { ...this.statistics };
  }

  /**
   * Process a single job
   */
  private async processJob(job: BulkProcessingJob): Promise<void> {
    job.status = JobStatus.RUNNING;
    job.startedAt = new Date();
    this.activeJobs.set(job.id, job);

    logger.info('Processing job started', {
      jobId: job.id,
      documentCount: job.documents.length,
    });

    this.emit('jobStarted', job);

    try {
      const results: FormFillingResult[] = [];
      const errors: Array<{ document: Document; error: Error }> = [];
      let processedCount = 0;

      // Process documents with concurrency control
      const documentBatches = this.createDocumentBatches(
        job.documents,
        this.options.maxConcurrentDocuments
      );

      for (const batch of documentBatches) {
        // Check if job was cancelled externally
        const currentJob = this.activeJobs.get(job.id);
        if (!currentJob || currentJob.status === JobStatus.CANCELLED) {
          break;
        }

        // Wait if job is paused
        while (currentJob && currentJob.status === JobStatus.PAUSED) {
          await this.sleep(1000);
        }

        // Process batch in parallel
        const batchPromises = batch.map(async document => {
          try {
            const result = await this.formFillerService.fillForm(
              document,
              job.extractedData || [],
              job.options
            );
            results.push(result);
            processedCount++;

            this.emit('documentProcessed', {
              jobId: job.id,
              document,
              result,
              progress: processedCount / job.documents.length,
            });
          } catch (error) {
            errors.push({ document, error: error as Error });
            processedCount++;

            this.emit('documentFailed', {
              jobId: job.id,
              document,
              error,
              progress: processedCount / job.documents.length,
            });

            // Retry logic
            if (this.options.enableRetry) {
              await this.retryDocument(document, job, errors);
            }
          }
        });

        await Promise.all(batchPromises);
      }

      // Complete job
      job.status = errors.length === 0 ? JobStatus.COMPLETED : JobStatus.FAILED;
      job.completedAt = new Date();

      const result: BulkProcessingResult = {
        jobId: job.id,
        success: errors.length === 0,
        totalDocuments: job.documents.length,
        successfulResults: results,
        failedResults: errors,
        processingTime: job.completedAt.getTime() - job.startedAt!.getTime(),
        statistics: this.statistics,
      };

      this.updateStatistics(result);
      this.activeJobs.delete(job.id);

      logger.info('Job completed', {
        jobId: job.id,
        success: result.success,
        successfulDocuments: results.length,
        failedDocuments: errors.length,
      });

      this.emit('jobCompleted', job, result);
    } catch (error) {
      job.status = JobStatus.FAILED;
      job.completedAt = new Date();
      this.activeJobs.delete(job.id);

      logger.error('Job failed', { error, jobId: job.id });
      this.emit('jobFailed', job, error);
    }
  }

  /**
   * Retry processing a failed document
   */
  private async retryDocument(
    document: Document,
    job: BulkProcessingJob,
    errors: Array<{ document: Document; error: Error }>
  ): Promise<void> {
    const maxRetries = this.options.maxRetries;
    let retryCount = 0;

    while (retryCount < maxRetries) {
      await this.sleep(this.options.retryDelay);
      retryCount++;

      try {
        const result = await this.formFillerService.fillForm(
          document,
          job.extractedData || [],
          job.options
        );

        // Remove from errors if successful
        const errorIndex = errors.findIndex(e => e.document.id === document.id);
        if (errorIndex >= 0) {
          errors.splice(errorIndex, 1);
        }

        logger.info('Document retry successful', {
          documentId: document.id,
          retryCount,
        });

        this.emit('documentRetrySuccessful', {
          jobId: job.id,
          document,
          result,
          retryCount,
        });

        break;
      } catch (error) {
        logger.warn('Document retry failed', {
          documentId: document.id,
          retryCount,
          error,
        });

        if (retryCount === maxRetries) {
          this.emit('documentRetryExhausted', {
            jobId: job.id,
            document,
            error,
            retryCount,
          });
        }
      }
    }
  }

  /**
   * Create document batches for parallel processing
   */
  private createDocumentBatches(documents: Document[], batchSize: number): Document[][] {
    const batches: Document[][] = [];

    for (let i = 0; i < documents.length; i += batchSize) {
      batches.push(documents.slice(i, i + batchSize));
    }

    return batches;
  }

  /**
   * Insert job in queue by priority
   */
  private insertJobByPriority(job: BulkProcessingJob): void {
    let insertIndex = this.jobQueue.length;

    for (let i = 0; i < this.jobQueue.length; i++) {
      const queueJob = this.jobQueue[i];
      if (queueJob && job.priority > queueJob.priority) {
        insertIndex = i;
        break;
      }
    }

    this.jobQueue.splice(insertIndex, 0, job);
  }

  /**
   * Wait for any job to complete
   */
  private async waitForJobCompletion(specificJobId?: string): Promise<void> {
    return new Promise(resolve => {
      const checkCompletion = () => {
        if (specificJobId) {
          if (!this.activeJobs.has(specificJobId)) {
            resolve();
            return;
          }
        } else {
          if (this.activeJobs.size < this.options.maxConcurrentJobs) {
            resolve();
            return;
          }
        }

        setTimeout(checkCompletion, 100);
      };

      checkCompletion();
    });
  }

  /**
   * Setup progress reporting
   */
  private setupProgressReporting(): void {
    if (this.options.enableProgressReporting) {
      this.progressInterval = setInterval(() => {
        this.emitProgressUpdate();
      }, this.options.progressReportInterval);
    }
  }

  /**
   * Setup resource monitoring
   */
  private setupResourceMonitoring(): void {
    if (this.options.enableResourceMonitoring) {
      setInterval(() => {
        this.monitorResources();
      }, 5000); // Monitor every 5 seconds
    }
  }

  /**
   * Emit progress update
   */
  private emitProgressUpdate(): void {
    const progress = {
      queueSize: this.jobQueue.length,
      activeJobs: this.activeJobs.size,
      isProcessing: this.isProcessing,
      statistics: this.statistics,
    };

    this.emit('progressUpdate', progress);
  }

  /**
   * Monitor system resources
   */
  private monitorResources(): void {
    const usage = process.memoryUsage();
    const memoryUsage = usage.heapUsed / usage.heapTotal;

    if (memoryUsage > this.options.memoryThreshold) {
      logger.warn('High memory usage detected', { memoryUsage });
      this.emit('highMemoryUsage', memoryUsage);
    }

    // Update statistics
    this.statistics.resourceUsage = {
      cpuUsage: 0, // Would be calculated from system metrics
      memoryUsage,
      diskUsage: 0, // Would be calculated from system metrics
      networkUsage: 0, // Would be calculated from system metrics
      activeWorkers: this.workers.length,
      queueSize: this.jobQueue.length,
    };
  }

  /**
   * Update processing statistics
   */
  private updateStatistics(result: BulkProcessingResult): void {
    this.statistics.totalJobs++;

    if (result.success) {
      this.statistics.completedJobs++;
    } else {
      this.statistics.failedJobs++;
    }

    // Update averages
    const totalTime =
      this.statistics.averageJobTime * (this.statistics.totalJobs - 1) + result.processingTime;
    this.statistics.averageJobTime = totalTime / this.statistics.totalJobs;

    // Update error rate
    this.statistics.errorRate = this.statistics.failedJobs / this.statistics.totalJobs;

    // Update common errors
    for (const failedResult of result.failedResults) {
      const errorMessage = failedResult.error.message;
      const existingError = this.statistics.commonErrors.find(e => e.error === errorMessage);

      if (existingError) {
        existingError.count++;
      } else {
        this.statistics.commonErrors.push({ error: errorMessage, count: 1 });
      }
    }

    // Sort common errors by count
    this.statistics.commonErrors.sort((a, b) => b.count - a.count);

    // Keep only top 10 errors
    this.statistics.commonErrors = this.statistics.commonErrors.slice(0, 10);
  }

  /**
   * Cleanup workers
   */
  private cleanupWorkers(): void {
    for (const worker of this.workers) {
      worker.terminate();
    }
    this.workers.length = 0;
  }

  /**
   * Utility methods
   */
  private generateJobId(): string {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private initializeStatistics(): BulkProcessingStatistics {
    return {
      totalJobs: 0,
      completedJobs: 0,
      failedJobs: 0,
      averageJobTime: 0,
      averageDocumentTime: 0,
      throughput: 0,
      errorRate: 0,
      commonErrors: [],
      resourceUsage: {
        cpuUsage: 0,
        memoryUsage: 0,
        diskUsage: 0,
        networkUsage: 0,
        activeWorkers: 0,
        queueSize: 0,
      },
    };
  }

  private mergeDefaultOptions(options: Partial<BulkProcessingOptions>): BulkProcessingOptions {
    return {
      maxConcurrentJobs: options.maxConcurrentJobs ?? 3,
      maxConcurrentDocuments: options.maxConcurrentDocuments ?? 5,
      enableRetry: options.enableRetry ?? true,
      maxRetries: options.maxRetries ?? 3,
      retryDelay: options.retryDelay ?? 1000,
      enableProgressReporting: options.enableProgressReporting ?? true,
      progressReportInterval: options.progressReportInterval ?? 5000,
      enableResourceMonitoring: options.enableResourceMonitoring ?? true,
      memoryThreshold: options.memoryThreshold ?? 0.8,
      cpuThreshold: options.cpuThreshold ?? 0.8,
    };
  }

  /**
   * Cleanup on destruction
   */
  public destroy(): void {
    this.stopProcessing();

    if (this.progressInterval) {
      clearInterval(this.progressInterval);
    }

    this.removeAllListeners();
    logger.info('Bulk processor destroyed');
  }
}

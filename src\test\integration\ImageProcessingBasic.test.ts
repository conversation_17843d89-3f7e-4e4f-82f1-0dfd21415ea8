import { describe, test, expect, beforeAll } from '@jest/globals';
import sharp from 'sharp';
import { ImageProcessor } from '../../main/services/ImageProcessor';

describe('Basic Image Processing Integration', () => {
  let imageProcessor: ImageProcessor;
  let testImageBuffer: Buffer;

  beforeAll(async () => {
    imageProcessor = new ImageProcessor();
    
    // Create a simple test image
    testImageBuffer = await sharp({
      create: {
        width: 400,
        height: 300,
        channels: 3,
        background: { r: 255, g: 255, b: 255 },
      },
    })
      .png()
      .toBuffer();
  });

  test('should create ImageProcessor instance', () => {
    expect(imageProcessor).toBeInstanceOf(ImageProcessor);
  });

  test('should process image successfully', async () => {
    const result = await imageProcessor.processImage(testImageBuffer);

    expect(result).toBeDefined();
    expect(result.success).toBe(true);
    expect(result.originalImage).toBeDefined();
    expect(result.processedImage).toBeDefined();
    expect(result.processingTime).toBeGreaterThan(0);
  });

  test('should enhance image for OCR', async () => {
    const enhancedBuffer = await imageProcessor.enhanceForOCR(testImageBuffer);

    expect(enhancedBuffer).toBeInstanceOf(Buffer);
    expect(enhancedBuffer.length).toBeGreaterThan(0);

    // Verify the enhanced image properties
    const metadata = await sharp(enhancedBuffer).metadata();
    expect(metadata.width).toBeGreaterThanOrEqual(2000);
    expect(metadata.format).toBe('png');
  });

  test('should get image data', async () => {
    const imageData = await imageProcessor.getImageData(testImageBuffer);

    expect(imageData).toBeDefined();
    expect(imageData.width).toBe(400);
    expect(imageData.height).toBe(300);
    expect(imageData.format).toBe('png');
    expect(imageData.data).toBeInstanceOf(ArrayBuffer);
  });

  test('should upscale image', async () => {
    const upscaledBuffer = await imageProcessor.upscaleImage(testImageBuffer, 2);
    const metadata = await sharp(upscaledBuffer).metadata();

    expect(metadata.width).toBe(800);
    expect(metadata.height).toBe(600);
  });

  test('should convert image format', async () => {
    const jpegBuffer = await imageProcessor.convertFormat(testImageBuffer, 'jpeg', 90);
    const metadata = await sharp(jpegBuffer).metadata();

    expect(metadata.format).toBe('jpeg');
  });

  test('should get OCR optimized options', () => {
    const options = imageProcessor.getOCROptimizedOptions();

    expect(options).toBeDefined();
    expect(options.enhanceForOCR).toBe(true);
    expect(options.upscaleRatio).toBe(2);
    expect(options.removeNoise).toBe(true);
    expect(options.adjustContrast).toBe(true);
    expect(options.sharpen).toBe(true);
    expect(options.deskew).toBe(true);
    expect(options.cropToContent).toBe(true);
    expect(options.outputFormat).toBe('png');
  });
});

// Import polyfills first
import './polyfills';

import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';
import './styles/globals.css';
import { ErrorBoundary } from './components/common/ErrorBoundary';

// Global error handlers for unhandled errors
window.addEventListener('error', event => {
  console.error('Global error:', event.error);
  if (window.electronAPI?.logToMain) {
    window.electronAPI.logToMain('error', 'Global JavaScript Error', {
      error: {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
      },
      timestamp: new Date().toISOString(),
    });
  }
});

window.addEventListener('unhandledrejection', event => {
  console.error('Unhandled promise rejection:', event.reason);
  if (window.electronAPI?.logToMain) {
    window.electronAPI.logToMain('error', 'Unhandled Promise Rejection', {
      reason: event.reason?.toString(),
      stack: event.reason?.stack,
      timestamp: new Date().toISOString(),
    });
  }
});

// Performance monitoring
if (typeof window.performance !== 'undefined') {
  window.addEventListener('load', () => {
    setTimeout(() => {
      const perfData = window.performance.timing;
      const loadTime = perfData.loadEventEnd - perfData.navigationStart;
      console.log(`Application loaded in ${loadTime}ms`);

      if (window.electronAPI?.logToMain) {
        window.electronAPI.logToMain('info', 'Application Performance', {
          loadTime,
          domContentLoaded: perfData.domContentLoadedEventEnd - perfData.navigationStart,
          timestamp: new Date().toISOString(),
        });
      }
    }, 0);
  });
}

// Initialize React application
console.log('Initializing React application...');

const container = document.getElementById('root');
if (!container) {
  const error = new Error('Root element not found');
  console.error('Root element not found!');

  if (window.electronAPI?.logToMain) {
    window.electronAPI.logToMain('error', 'React Initialization Error', {
      error: error.message,
      timestamp: new Date().toISOString(),
    });
  }

  throw error;
}

console.log('Root element found, creating React root...');

// Create React 18 root with concurrent features
const root = createRoot(container, {
  // Enable concurrent features
  identifierPrefix: 'ai-doc-processor',
});

// Enhanced error handler for the main error boundary
const handleGlobalError = (error: Error, errorInfo: React.ErrorInfo) => {
  console.error('Application Error Boundary:', error, errorInfo);

  if (window.electronAPI?.logToMain) {
    window.electronAPI.logToMain('error', 'React Error Boundary', {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      errorInfo: {
        componentStack: errorInfo.componentStack,
      },
      timestamp: new Date().toISOString(),
    });
  }
};

console.log('Rendering React app with enhanced features...');

// Render with React 18 features and enhanced error handling
root.render(
  <React.StrictMode>
    <ErrorBoundary onError={handleGlobalError}>
      <App />
    </ErrorBoundary>
  </React.StrictMode>
);

console.log('React app rendered successfully with concurrent features!');

// Enhanced hot module replacement for development
declare const module: any;
if (module.hot && process.env.NODE_ENV === 'development') {
  module.hot.accept('./App', () => {
    console.log('Hot reloading App component...');
    try {
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      const NextApp = require('./App').default;
      root.render(
        <React.StrictMode>
          <ErrorBoundary onError={handleGlobalError}>
            <NextApp />
          </ErrorBoundary>
        </React.StrictMode>
      );
      console.log('Hot reload successful!');
    } catch (error) {
      console.error('Hot reload failed:', error);
    }
  });
}

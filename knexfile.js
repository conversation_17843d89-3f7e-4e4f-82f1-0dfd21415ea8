const path = require('path');
const { app } = require('electron');

// Get user data directory, fallback for non-Electron environments
const getUserDataPath = () => {
  try {
    return app ? app.getPath('userData') : path.join(__dirname, 'data');
  } catch (error) {
    console.warn('Failed to get user data path:', error);
    return path.join(__dirname, 'data');
  }
};

module.exports = {
  development: {
    client: 'better-sqlite3',
    connection: {
      filename: path.join(getUserDataPath(), 'ai-document-processor-dev.db'),
    },
    migrations: {
      directory: './src/main/database/migrations',
      extension: 'ts',
    },
    seeds: {
      directory: './src/main/database/seeds',
    },
    useNullAsDefault: true,
    pool: {
      afterCreate: (conn, cb) => {
        // Enable foreign key constraints
        conn.run('PRAGMA foreign_keys = ON', cb);
      },
    },
  },

  test: {
    client: 'better-sqlite3',
    connection: {
      filename: ':memory:',
    },
    migrations: {
      directory: './src/main/database/migrations',
      extension: 'ts',
    },
    seeds: {
      directory: './src/main/database/seeds',
    },
    useNullAsDefault: true,
    pool: {
      afterCreate: (conn, cb) => {
        conn.run('PRAGMA foreign_keys = ON', cb);
      },
    },
  },

  production: {
    client: 'better-sqlite3',
    connection: {
      filename: path.join(getUserDataPath(), 'ai-document-processor.db'),
    },
    migrations: {
      directory: './src/main/database/migrations',
      extension: 'ts',
    },
    useNullAsDefault: true,
    pool: {
      afterCreate: (conn, cb) => {
        conn.run('PRAGMA foreign_keys = ON', cb);
        // Enable WAL mode for better performance
        conn.run('PRAGMA journal_mode = WAL', cb);
        // Optimize SQLite settings
        conn.run('PRAGMA synchronous = NORMAL', cb);
        conn.run('PRAGMA cache_size = 1000', cb);
        conn.run('PRAGMA temp_store = memory', cb);
      },
    },
  },
};

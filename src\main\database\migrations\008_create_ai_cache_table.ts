import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create ai_cache table for AI response caching
  await knex.schema.createTable('ai_cache', (table) => {
    // Primary key
    table.string('id', 36).primary().notNullable().comment('UUID primary key');

    // Cache key and identification
    table.string('cache_key', 128).unique().notNullable().comment('Unique cache key (hash of request)');
    table.string('request_hash', 64).notNullable().comment('SHA-256 hash of the request parameters');
    table.string('model_name', 100).notNullable().comment('AI model used for this request');
    table.string('model_version', 50).comment('Version of the AI model');
    table.string('provider', 50).notNullable().comment('AI provider (openai, azure, etc.)');

    // Request information
    table.text('request_type').notNullable().comment('Type of AI request (completion, embedding, analysis, etc.)');
    table.json('request_parameters').notNullable().comment('Original request parameters');
    table.binary('request_data').comment('Compressed request data if large');
    table.integer('request_size').unsigned().comment('Size of request data in bytes');

    // Response information
    table.binary('response_data').notNullable().comment('Compressed AI response data');
    table.integer('response_size').unsigned().notNullable().comment('Size of response data in bytes');
    table.string('compression_method', 50).defaultTo('lz4').comment('Compression method used');
    table.json('response_metadata').comment('Response metadata (tokens, confidence, etc.)');

    // Performance metrics
    table.integer('processing_time_ms').unsigned().comment('Time taken to process the request');
    table.integer('tokens_used').unsigned().comment('Number of tokens used');
    table.decimal('cost', 10, 6).comment('Cost of the API call');
    table.string('currency', 3).defaultTo('USD').comment('Currency for cost');

    // Cache management
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable().comment('Cache entry creation timestamp');
    table.timestamp('last_accessed_at').defaultTo(knex.fn.now()).notNullable().comment('Last time cache was accessed');
    table.timestamp('expires_at').comment('Cache expiration timestamp');
    table.integer('access_count').defaultTo(0).notNullable().comment('Number of times cache has been accessed');
    table.boolean('is_valid').defaultTo(true).notNullable().comment('Whether cache entry is valid');

    // Quality and confidence
    table.decimal('confidence_score', 5, 4).comment('Confidence score of the cached response');
    table.json('quality_metrics').comment('Quality metrics for the response');
    table.boolean('is_verified').defaultTo(false).notNullable().comment('Whether response has been verified');
    table.timestamp('verified_at').comment('When response was verified');

    // Context and relationships
    table.string('document_id', 36).comment('Related document ID if applicable');
    table.foreign('document_id').references('id').inTable('documents').onDelete('SET NULL');
    table.string('session_id', 36).comment('Session ID when request was made');
    table.string('user_id', 100).comment('User who made the request');
    table.json('context_tags').comment('Context tags for categorization');

    // Cache optimization
    table.integer('priority').defaultTo(5).notNullable().comment('Cache priority (1-10, higher = more important)');
    table.boolean('pin_cache').defaultTo(false).notNullable().comment('Whether to pin this cache entry');
    table.string('cache_group', 100).comment('Cache group for batch operations');
    table.json('dependencies').comment('Cache dependencies (other cache keys)');

    // Error handling and debugging
    table.text('error_message').comment('Error message if request failed');
    table.json('debug_info').comment('Debug information for troubleshooting');
    table.boolean('is_error_cache').defaultTo(false).notNullable().comment('Whether this caches an error response');

    // Constraints
    table.check('request_size >= 0', [], 'request_size_positive');
    table.check('response_size >= 0', [], 'response_size_positive');
    table.check('processing_time_ms >= 0', [], 'processing_time_positive');
    table.check('tokens_used >= 0', [], 'tokens_used_positive');
    table.check('cost >= 0', [], 'cost_positive');
    table.check('access_count >= 0', [], 'access_count_positive');
    table.check('priority >= 1 AND priority <= 10', [], 'priority_range');
    table.check('confidence_score IS NULL OR (confidence_score >= 0 AND confidence_score <= 1)', [], 'confidence_range');
    table.check("request_type IN ('completion', 'embedding', 'analysis', 'classification', 'extraction', 'generation', 'translation', 'summarization', 'other')", [], 'valid_request_type');
    table.check("provider IN ('openai', 'azure', 'anthropic', 'google', 'local', 'other')", [], 'valid_provider');
    table.check("compression_method IN ('lz4', 'gzip', 'brotli', 'none')", [], 'valid_compression_method');
    table.check("currency IN ('USD', 'EUR', 'GBP', 'JPY', 'other')", [], 'valid_currency');

    // JSON validation
    table.check("json_valid(request_parameters)", [], 'valid_request_parameters');
    table.check("json_valid(response_metadata) OR response_metadata IS NULL", [], 'valid_response_metadata');
    table.check("json_valid(quality_metrics) OR quality_metrics IS NULL", [], 'valid_quality_metrics');
    table.check("json_valid(context_tags) OR context_tags IS NULL", [], 'valid_context_tags');
    table.check("json_valid(dependencies) OR dependencies IS NULL", [], 'valid_dependencies');
    table.check("json_valid(debug_info) OR debug_info IS NULL", [], 'valid_debug_info');

    // Indexes for performance
    table.index(['cache_key'], 'idx_ai_cache_key');
    table.index(['request_hash'], 'idx_ai_cache_request_hash');
    table.index(['model_name'], 'idx_ai_cache_model');
    table.index(['provider'], 'idx_ai_cache_provider');
    table.index(['request_type'], 'idx_ai_cache_request_type');
    table.index(['created_at'], 'idx_ai_cache_created_at');
    table.index(['last_accessed_at'], 'idx_ai_cache_last_accessed');
    table.index(['expires_at'], 'idx_ai_cache_expires_at');
    table.index(['access_count'], 'idx_ai_cache_access_count');
    table.index(['is_valid'], 'idx_ai_cache_is_valid');
    table.index(['document_id'], 'idx_ai_cache_document');
    table.index(['session_id'], 'idx_ai_cache_session');
    table.index(['user_id'], 'idx_ai_cache_user');
    table.index(['priority'], 'idx_ai_cache_priority');
    table.index(['pin_cache'], 'idx_ai_cache_pin');
    table.index(['cache_group'], 'idx_ai_cache_group');
    table.index(['is_error_cache'], 'idx_ai_cache_error');

    // Composite indexes for common queries
    table.index(['model_name', 'provider'], 'idx_ai_cache_model_provider');
    table.index(['request_type', 'model_name'], 'idx_ai_cache_type_model');
    table.index(['is_valid', 'expires_at'], 'idx_ai_cache_valid_expires');
    table.index(['last_accessed_at', 'access_count'], 'idx_ai_cache_accessed_count');
    table.index(['document_id', 'request_type'], 'idx_ai_cache_doc_type');
    table.index(['user_id', 'created_at'], 'idx_ai_cache_user_created');
    table.index(['priority', 'pin_cache'], 'idx_ai_cache_priority_pin');
  });

  // Create trigger to update last_accessed_at when access_count increases
  await knex.raw(`
    CREATE TRIGGER update_cache_access_time
    AFTER UPDATE OF access_count ON ai_cache
    FOR EACH ROW
    WHEN NEW.access_count > OLD.access_count
    BEGIN
      UPDATE ai_cache
      SET last_accessed_at = CURRENT_TIMESTAMP
      WHERE id = NEW.id;
    END
  `);

  // Create trigger to invalidate expired cache entries
  await knex.raw(`
    CREATE TRIGGER invalidate_expired_cache
    AFTER UPDATE OF last_accessed_at ON ai_cache
    FOR EACH ROW
    WHEN NEW.expires_at IS NOT NULL AND NEW.expires_at < CURRENT_TIMESTAMP AND NEW.is_valid = 1
    BEGIN
      UPDATE ai_cache
      SET is_valid = 0
      WHERE id = NEW.id;
    END
  `);

  // Create view for valid cache entries
  await knex.raw(`
    CREATE VIEW valid_cache_entries AS
    SELECT
      id,
      cache_key,
      model_name,
      provider,
      request_type,
      created_at,
      last_accessed_at,
      expires_at,
      access_count,
      confidence_score,
      processing_time_ms,
      tokens_used,
      cost,
      response_size,
      (julianday(CURRENT_TIMESTAMP) - julianday(last_accessed_at)) * 1440 as minutes_since_access
    FROM ai_cache
    WHERE is_valid = 1
      AND (expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP)
      AND is_error_cache = 0
  `);

  // Create view for cache statistics by model
  await knex.raw(`
    CREATE VIEW cache_model_stats AS
    SELECT
      model_name,
      provider,
      request_type,
      COUNT(*) as total_entries,
      COUNT(CASE WHEN is_valid = 1 THEN 1 END) as valid_entries,
      COUNT(CASE WHEN is_error_cache = 1 THEN 1 END) as error_entries,
      SUM(access_count) as total_accesses,
      AVG(access_count) as avg_accesses_per_entry,
      SUM(tokens_used) as total_tokens,
      SUM(cost) as total_cost,
      AVG(processing_time_ms) as avg_processing_time,
      AVG(confidence_score) as avg_confidence,
      SUM(response_size) as total_response_size,
      MAX(last_accessed_at) as last_model_access
    FROM ai_cache
    GROUP BY model_name, provider, request_type
    ORDER BY total_accesses DESC
  `);

  // Create view for cache cleanup candidates
  await knex.raw(`
    CREATE VIEW cache_cleanup_candidates AS
    SELECT
      id,
      cache_key,
      model_name,
      request_type,
      created_at,
      last_accessed_at,
      access_count,
      response_size,
      priority,
      pin_cache,
      CASE
        WHEN expires_at IS NOT NULL AND expires_at < CURRENT_TIMESTAMP THEN 'Expired'
        WHEN is_valid = 0 THEN 'Invalid'
        WHEN is_error_cache = 1 AND access_count = 0 THEN 'Unused Error Cache'
        WHEN access_count = 0 AND created_at < datetime('now', '-30 days') THEN 'Unused Old Entry'
        WHEN access_count < 3 AND last_accessed_at < datetime('now', '-90 days') THEN 'Rarely Used'
        WHEN response_size > 1048576 AND access_count < 5 THEN 'Large Rarely Used'
        ELSE 'Other'
      END as cleanup_reason,
      (julianday(CURRENT_TIMESTAMP) - julianday(last_accessed_at)) * 1440 as minutes_since_access
    FROM ai_cache
    WHERE pin_cache = 0
      AND (
        (expires_at IS NOT NULL AND expires_at < CURRENT_TIMESTAMP)
        OR is_valid = 0
        OR (is_error_cache = 1 AND access_count = 0)
        OR (access_count = 0 AND created_at < datetime('now', '-30 days'))
        OR (access_count < 3 AND last_accessed_at < datetime('now', '-90 days'))
        OR (response_size > 1048576 AND access_count < 5)
      )
    ORDER BY
      CASE cleanup_reason
        WHEN 'Expired' THEN 1
        WHEN 'Invalid' THEN 2
        WHEN 'Unused Error Cache' THEN 3
        WHEN 'Unused Old Entry' THEN 4
        WHEN 'Rarely Used' THEN 5
        WHEN 'Large Rarely Used' THEN 6
        ELSE 7
      END,
      last_accessed_at ASC
  `);

  // Create view for cache hit rate analysis
  await knex.raw(`
    CREATE VIEW cache_hit_analysis AS
    SELECT
      DATE(created_at) as cache_date,
      model_name,
      provider,
      request_type,
      COUNT(*) as entries_created,
      SUM(access_count) as total_hits,
      AVG(access_count) as avg_hits_per_entry,
      COUNT(CASE WHEN access_count > 1 THEN 1 END) as reused_entries,
      ROUND(
        CAST(COUNT(CASE WHEN access_count > 1 THEN 1 END) AS FLOAT) /
        CAST(COUNT(*) AS FLOAT) * 100, 2
      ) as reuse_percentage
    FROM ai_cache
    WHERE created_at >= datetime('now', '-30 days')
    GROUP BY DATE(created_at), model_name, provider, request_type
    ORDER BY cache_date DESC, total_hits DESC
  `);
}

export async function down(knex: Knex): Promise<void> {
  // Drop triggers first
  await knex.raw('DROP TRIGGER IF EXISTS invalidate_expired_cache');
  await knex.raw('DROP TRIGGER IF EXISTS update_cache_access_time');

  // Drop views
  await knex.raw('DROP VIEW IF EXISTS cache_hit_analysis');
  await knex.raw('DROP VIEW IF EXISTS cache_cleanup_candidates');
  await knex.raw('DROP VIEW IF EXISTS cache_model_stats');
  await knex.raw('DROP VIEW IF EXISTS valid_cache_entries');

  // Drop main table
  await knex.schema.dropTableIfExists('ai_cache');
}

import * as natural from 'natural';
import nlp from 'compromise';
import {
  NLPResult,
  Entity,
  EntityType,
  EntityCategory,
  EntityMention,
  SentimentResult,
  SentimentLabel,
  Topic,
  Keyword,
  Relationship,
  RelationshipType,
  KnowledgeGraph,
  GraphNode,
  GraphEdge,
  NodeType,
  NodeProperties,
  GraphStatistics,
  GraphMetadata,
  CentralityMeasures,
  GraphPath,
  PathType,
  TraversalResult,
  TraversalOrder,
  DocumentSimilarity,
  SimilarityMethod,
  SimilarityFeatures,
  ClusterResult,
  ClusteringOptions,
  ClusteringMethod,
  ReadabilityScore,
  ComplexityLevel,
  TextQualityAssessment,
  GrammarAssessment,
  GrammarError,
  ErrorSeverity,
  VocabularyAssessment,
  StructureAssessment,
  QualityImprovement,
  ImprovementType,
  ImprovementPriority,
} from '../../shared/types/AI';
import { aiModelClient } from './AIModelClient';
import { chromaKnowledgeBase } from './ChromaKnowledgeBase';

/**
 * Natural Language Processing service using Natural.js library
 * Provides text preprocessing, sentiment analysis, entity extraction, and classification
 */
export class NLPProcessor {
  private tokenizer!: natural.WordTokenizer;
  private stemmer!: typeof natural.PorterStemmer;
  private sentimentAnalyzer!: natural.SentimentAnalyzer;
  private languageDetector: any;

  constructor() {
    this.initializeComponents();
  }

  /**
   * Initialize Natural.js components
   */
  private initializeComponents(): void {
    // Initialize tokenizer
    this.tokenizer = new natural.WordTokenizer();

    // Initialize stemmer
    this.stemmer = natural.PorterStemmer;

    // Initialize sentiment analyzer
    this.sentimentAnalyzer = new natural.SentimentAnalyzer(
      'English',
      natural.PorterStemmer,
      'afinn'
    );

    // Initialize language detector (simplified for testing)
    this.languageDetector = {
      detect: () => [['en', 0.8]],
    };

    // Set up phonetic matching (with error handling for testing)
    try {
      // Note: These methods may not be available in all versions of natural
      if (typeof (natural.Metaphone as any).attach === 'function') {
        (natural.Metaphone as any).attach();
      }
      if (typeof (natural.SoundEx as any).attach === 'function') {
        (natural.SoundEx as any).attach();
      }
      if (typeof (natural.DoubleMetaphone as any).attach === 'function') {
        (natural.DoubleMetaphone as any).attach();
      }
    } catch (error) {
      console.warn('Phonetic matching setup failed:', error);
    }
  }

  /**
   * Process text through complete NLP pipeline
   */
  processText(text: string): NLPResult {
    const startTime = Date.now();

    try {
      // Preprocess text
      const preprocessedText = this.preprocessText(text);

      // Detect language
      const language = this.detectLanguage(text);

      // Extract entities
      const entities = this.extractEntities(preprocessedText);

      // Analyze sentiment
      const sentiment = this.analyzeSentiment(preprocessedText);

      // Extract topics and keywords
      const topics = this.extractTopics(preprocessedText);
      const keywords = this.extractKeywords(preprocessedText);

      // Extract relationships (basic implementation)
      const relationships = this.extractRelationships(entities, preprocessedText);

      // Generate summary (basic implementation)
      const summary = this.generateSummary(preprocessedText);

      const processingTime = Date.now() - startTime;

      return {
        text: preprocessedText,
        language: language.language,
        confidence: language.confidence,
        entities,
        relationships,
        sentiment,
        topics,
        keywords,
        summary,
        processingTime,
      };
    } catch (error) {
      throw new Error(
        `NLP processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Preprocess and normalize text
   */
  preprocessText(text: string): string {
    if (!text || typeof text !== 'string') {
      return '';
    }

    // Remove extra whitespace and normalize
    let processed = text.trim().replace(/\s+/g, ' ');

    // Remove special characters but keep punctuation for sentence structure
    processed = processed.replace(/[^\w\s.,!?;:()\-'"]/g, '');

    // Normalize quotes
    processed = processed.replace(/[""]/g, '"').replace(/['']/g, "'");

    return processed;
  }

  /**
   * Detect language of text
   */
  detectLanguage(text: string): { language: string; confidence: number } {
    try {
      const detections = (this.languageDetector as any).detect(text, 1);

      if (detections && detections.length > 0) {
        const [language, confidence] = detections[0];
        return {
          language: language || 'en',
          confidence: Math.max(0, Math.min(1, confidence || 0.5)),
        };
      }

      return { language: 'en', confidence: 0.5 };
    } catch (error) {
      console.warn('Language detection failed:', error);
      return { language: 'en', confidence: 0.5 };
    }
  }

  /**
   * Analyze sentiment of text
   */
  analyzeSentiment(text: string): SentimentResult {
    try {
      // Tokenize text
      const tokens = this.tokenizer.tokenize(text.toLowerCase()) || [];

      // Stem tokens
      const stemmedTokens = tokens.map(token => this.stemmer.stem(token));

      // Analyze sentiment
      const score = this.sentimentAnalyzer.getSentiment(stemmedTokens);

      // Normalize score to -1 to 1 range
      const polarity = Math.max(-1, Math.min(1, score));

      // Calculate confidence based on absolute score
      const confidence = Math.min(1, Math.abs(polarity) * 2);

      // Determine label
      let label: SentimentLabel;
      if (polarity > 0.1) {
        label = SentimentLabel.POSITIVE;
      } else if (polarity < -0.1) {
        label = SentimentLabel.NEGATIVE;
      } else {
        label = SentimentLabel.NEUTRAL;
      }

      return {
        polarity,
        subjectivity: confidence, // Using confidence as proxy for subjectivity
        confidence,
        label,
      };
    } catch (error) {
      console.warn('Sentiment analysis failed:', error);
      return {
        polarity: 0,
        subjectivity: 0,
        confidence: 0,
        label: SentimentLabel.NEUTRAL,
      };
    }
  }

  /**
   * Extract entities from text (basic implementation)
   */
  extractEntities(text: string): Entity[] {
    const entities: Entity[] = [];

    try {
      // Extract email addresses
      const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
      const emails = text.match(emailRegex) || [];

      emails.forEach((email, index) => {
        const startOffset = text.indexOf(email);
        entities.push(
          this.createEntity(
            `email_${index}`,
            email,
            { category: EntityCategory.EMAIL, subtype: 'email', description: 'Email address' },
            0.9,
            [
              {
                text: email,
                startOffset,
                endOffset: startOffset + email.length,
                confidence: 0.9,
                context: this.getContext(text, startOffset, email.length),
                documentId: '',
              },
            ]
          )
        );
      });

      // Extract phone numbers
      const phoneRegex = /\b(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})\b/g;
      const phones = text.match(phoneRegex) || [];

      phones.forEach((phone, index) => {
        const startOffset = text.indexOf(phone);
        entities.push(
          this.createEntity(
            `phone_${index}`,
            phone,
            { category: EntityCategory.PHONE, subtype: 'phone', description: 'Phone number' },
            0.8,
            [
              {
                text: phone,
                startOffset,
                endOffset: startOffset + phone.length,
                confidence: 0.8,
                context: this.getContext(text, startOffset, phone.length),
                documentId: '',
              },
            ]
          )
        );
      });

      // Extract URLs
      const urlRegex =
        /https?:\/\/(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?/g;
      const urls = text.match(urlRegex) || [];

      urls.forEach((url, index) => {
        const startOffset = text.indexOf(url);
        entities.push(
          this.createEntity(
            `url_${index}`,
            url,
            { category: EntityCategory.URL, subtype: 'url', description: 'Web URL' },
            0.95,
            [
              {
                text: url,
                startOffset,
                endOffset: startOffset + url.length,
                confidence: 0.95,
                context: this.getContext(text, startOffset, url.length),
                documentId: '',
              },
            ]
          )
        );
      });

      // Extract monetary amounts
      const moneyRegex = /\$[\d,]+\.?\d*/g;
      const amounts = text.match(moneyRegex) || [];

      amounts.forEach((amount, index) => {
        const startOffset = text.indexOf(amount);
        entities.push(
          this.createEntity(
            `money_${index}`,
            amount,
            { category: EntityCategory.MONEY, subtype: 'currency', description: 'Monetary amount' },
            0.85,
            [
              {
                text: amount,
                startOffset,
                endOffset: startOffset + amount.length,
                confidence: 0.85,
                context: this.getContext(text, startOffset, amount.length),
                documentId: '',
              },
            ]
          )
        );
      });

      // Extract dates (basic patterns)
      const dateRegex =
        /\b(?:\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}|\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2})\b/g;
      const dates = text.match(dateRegex) || [];

      dates.forEach((date, index) => {
        const startOffset = text.indexOf(date);
        entities.push(
          this.createEntity(
            `date_${index}`,
            date,
            { category: EntityCategory.DATE, subtype: 'date', description: 'Date' },
            0.7,
            [
              {
                text: date,
                startOffset,
                endOffset: startOffset + date.length,
                confidence: 0.7,
                context: this.getContext(text, startOffset, date.length),
                documentId: '',
              },
            ]
          )
        );
      });
    } catch (error) {
      console.warn('Entity extraction failed:', error);
    }

    return entities;
  }

  /**
   * Extract topics from text using advanced topic modeling algorithms
   */
  extractTopics(
    text: string,
    options?: {
      method?: 'tfidf' | 'lda' | 'hybrid';
      numTopics?: number;
      minConfidence?: number;
    }
  ): Topic[] {
    try {
      const method = options?.method || 'hybrid';
      const numTopics = options?.numTopics || 5;
      const minConfidence = options?.minConfidence || 0.1;

      switch (method) {
        case 'tfidf':
          return this.extractTopicsWithTfIdf(text, numTopics, minConfidence);
        case 'lda':
          return this.extractTopicsWithLDA(text, numTopics, minConfidence);
        case 'hybrid':
          return this.extractTopicsHybrid(text, numTopics, minConfidence);
        default:
          return this.extractTopicsWithTfIdf(text, numTopics, minConfidence);
      }
    } catch (error) {
      console.warn('Topic extraction failed:', error);
      return [];
    }
  }

  /**
   * Extract topics using TF-IDF (enhanced version)
   */
  private extractTopicsWithTfIdf(text: string, numTopics: number, minConfidence: number): Topic[] {
    try {
      // Tokenize and clean text
      const tokens = this.tokenizer.tokenize(text.toLowerCase()) || [];
      const cleanTokens = tokens.filter(
        token => token.length > 2 && !natural.stopwords.includes(token) && /^[a-zA-Z]+$/.test(token)
      );

      if (cleanTokens.length === 0) return [];

      // Calculate term frequency
      const termFreq: Record<string, number> = {};
      cleanTokens.forEach(token => {
        const stemmed = this.stemmer.stem(token);
        termFreq[stemmed] = (termFreq[stemmed] || 0) + 1;
      });

      // Calculate TF-IDF scores (simplified IDF)
      const tfidfScores: Record<string, number> = {};
      const totalTerms = cleanTokens.length;

      Object.entries(termFreq).forEach(([term, freq]) => {
        const tf = freq / totalTerms;
        // Simplified IDF calculation (in practice, you'd use a corpus)
        const idf = Math.log(totalTerms / freq);
        tfidfScores[term] = tf * idf;
      });

      // Get top terms as topics
      const sortedTerms = Object.entries(tfidfScores)
        .sort(([, a], [, b]) => b - a)
        .slice(0, numTopics)
        .filter(([, score]) => score >= minConfidence);

      // Group related terms into topics
      const topics: Topic[] = [];

      for (let i = 0; i < sortedTerms.length; i++) {
        const termData = sortedTerms[i];
        if (!termData) continue;
        const [mainTerm, score] = termData;

        // Find related terms (simple co-occurrence)
        const relatedTerms = this.findRelatedTerms(mainTerm, text, cleanTokens);
        const keywords = [mainTerm, ...relatedTerms.slice(0, 4)];

        // Generate topic name and description
        const topicName = this.generateTopicName(keywords);
        const description = this.generateTopicDescription(keywords, text);

        topics.push({
          id: `tfidf_topic_${i}`,
          name: topicName,
          confidence: Math.min(1, score * 2), // Normalize confidence
          keywords,
          description,
        });
      }

      return topics;
    } catch (error) {
      console.warn('TF-IDF topic extraction failed:', error);
      return [];
    }
  }

  /**
   * Extract topics using simplified LDA-like approach
   */
  private extractTopicsWithLDA(text: string, numTopics: number, minConfidence: number): Topic[] {
    try {
      // Split text into sentences as "documents"
      const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);

      if (sentences.length < 2) {
        // Fall back to TF-IDF for short texts
        return this.extractTopicsWithTfIdf(text, numTopics, minConfidence);
      }

      // Create vocabulary
      const vocabulary = new Set<string>();
      const documentTerms: string[][] = [];

      sentences.forEach(sentence => {
        const tokens = this.tokenizer.tokenize(sentence.toLowerCase()) || [];
        const cleanTokens = tokens.filter(
          token =>
            token.length > 2 && !natural.stopwords.includes(token) && /^[a-zA-Z]+$/.test(token)
        );

        const stemmedTokens = cleanTokens.map(token => this.stemmer.stem(token));
        stemmedTokens.forEach(token => vocabulary.add(token));
        documentTerms.push(stemmedTokens);
      });

      // Vocabulary array not needed for this simplified approach

      // Simplified topic modeling using co-occurrence patterns
      const topicTerms: Record<string, string[]> = {};
      const topicScores: Record<string, number> = {};

      // Find term co-occurrences
      const cooccurrences: Record<string, Record<string, number>> = {};

      documentTerms.forEach(terms => {
        for (let i = 0; i < terms.length; i++) {
          for (let j = i + 1; j < terms.length; j++) {
            const term1 = terms[i];
            const term2 = terms[j];

            if (term1 && term2) {
              if (!cooccurrences[term1]) cooccurrences[term1] = {};
              if (!cooccurrences[term2]) cooccurrences[term2] = {};

              cooccurrences[term1][term2] = (cooccurrences[term1][term2] || 0) + 1;
              cooccurrences[term2][term1] = (cooccurrences[term2][term1] || 0) + 1;
            }
          }
        }
      });

      // Create topics based on co-occurrence clusters
      const usedTerms = new Set<string>();
      let topicIndex = 0;

      for (const [term, cooccurringTerms] of Object.entries(cooccurrences)) {
        if (usedTerms.has(term) || topicIndex >= numTopics) continue;

        const sortedCooccurring = Object.entries(cooccurringTerms)
          .sort(([, a], [, b]) => b - a)
          .slice(0, 4)
          .map(([t]) => t);

        const topicTermList = [term, ...sortedCooccurring];
        const topicScore =
          Object.values(cooccurringTerms).reduce((sum, count) => sum + count, 0) /
          Object.keys(cooccurringTerms).length;

        if (topicScore >= minConfidence) {
          topicTerms[`lda_topic_${topicIndex}`] = topicTermList;
          topicScores[`lda_topic_${topicIndex}`] = topicScore;

          topicTermList.forEach(t => usedTerms.add(t));
          topicIndex++;
        }
      }

      // Convert to Topic objects
      const topics: Topic[] = [];

      Object.entries(topicTerms).forEach(([topicId, terms]) => {
        const topicName = this.generateTopicName(terms);
        const description = this.generateTopicDescription(terms, text);
        const confidence = Math.min(1, (topicScores[topicId] || 0) / 10);

        topics.push({
          id: topicId,
          name: topicName,
          confidence,
          keywords: terms,
          description,
        });
      });

      return topics.sort((a, b) => b.confidence - a.confidence);
    } catch (error) {
      console.warn('LDA topic extraction failed:', error);
      return this.extractTopicsWithTfIdf(text, numTopics, minConfidence);
    }
  }

  /**
   * Extract topics using hybrid approach (TF-IDF + LDA + semantic analysis)
   */
  private extractTopicsHybrid(text: string, numTopics: number, minConfidence: number): Topic[] {
    try {
      // Get topics from both methods
      const tfidfTopics = this.extractTopicsWithTfIdf(
        text,
        Math.ceil(numTopics * 0.6),
        minConfidence
      );
      const ldaTopics = this.extractTopicsWithLDA(text, Math.ceil(numTopics * 0.6), minConfidence);

      // Merge and deduplicate topics
      const allTopics = [...tfidfTopics, ...ldaTopics];
      const mergedTopics: Topic[] = [];
      const usedKeywords = new Set<string>();

      // Sort by confidence and merge similar topics
      allTopics.sort((a, b) => b.confidence - a.confidence);

      for (const topic of allTopics) {
        if (mergedTopics.length >= numTopics) break;

        // Check if this topic is too similar to existing ones
        const similarity = this.calculateTopicSimilarity(topic, mergedTopics);

        if (similarity < 0.7) {
          // Not too similar
          // Enhance topic with semantic analysis
          const enhancedTopic = this.enhanceTopicWithSemantics(topic, text);
          mergedTopics.push(enhancedTopic);

          topic.keywords.forEach(keyword => usedKeywords.add(keyword));
        }
      }

      // Add theme-based topics if we have fewer than requested
      if (mergedTopics.length < numTopics) {
        const themeTopics = this.extractThemeBasedTopics(
          text,
          numTopics - mergedTopics.length,
          usedKeywords
        );
        mergedTopics.push(...themeTopics);
      }

      return mergedTopics.slice(0, numTopics);
    } catch (error) {
      console.warn('Hybrid topic extraction failed:', error);
      return this.extractTopicsWithTfIdf(text, numTopics, minConfidence);
    }
  }

  /**
   * Extract keywords from text
   */
  extractKeywords(text: string): Keyword[] {
    try {
      // Tokenize text
      const tokens = this.tokenizer.tokenize(text.toLowerCase()) || [];

      // Filter meaningful tokens
      const meaningfulTokens = tokens.filter(
        token => token.length > 2 && !natural.stopwords.includes(token) && /^[a-zA-Z]+$/.test(token)
      );

      // Calculate frequency
      const frequency: Record<string, number> = {};
      meaningfulTokens.forEach(token => {
        frequency[token] = (frequency[token] || 0) + 1;
      });

      // Calculate importance (simple TF-based)
      const totalTokens = meaningfulTokens.length;

      return Object.entries(frequency)
        .map(([text, freq]) => ({
          text,
          frequency: freq,
          importance: freq / totalTokens,
          partOfSpeech: '', // Would need POS tagger for this
        }))
        .sort((a, b) => b.importance - a.importance)
        .slice(0, 10);
    } catch (error) {
      console.warn('Keyword extraction failed:', error);
      return [];
    }
  }

  /**
   * Extract basic relationships between entities
   */
  extractRelationships(entities: Entity[], _text: string): Relationship[] {
    const relationships: Relationship[] = [];

    try {
      // Simple co-occurrence based relationships
      for (let i = 0; i < entities.length; i++) {
        for (let j = i + 1; j < entities.length; j++) {
          const entity1 = entities[i];
          const entity2 = entities[j];

          if (!entity1 || !entity2) continue;

          // Check if entities appear close to each other
          const mention1 = entity1.mentions[0];
          const mention2 = entity2.mentions[0];

          if (mention1 && mention2) {
            const distance = Math.abs(mention1.startOffset - mention2.startOffset);

            // If entities are within 100 characters, consider them related
            if (distance < 100) {
              relationships.push({
                id: `rel_${i}_${j}`,
                type: RelationshipType.RELATED_TO,
                sourceId: entity1.id,
                targetId: entity2.id,
                strength: Math.max(0.1, 1 - distance / 100),
                confidence: 0.6,
                properties: {
                  description: `Co-occurrence relationship`,
                  weight: 1,
                  bidirectional: true,
                },
                createdAt: new Date(),
              });
            }
          }
        }
      }
    } catch (error) {
      console.warn('Relationship extraction failed:', error);
    }

    return relationships;
  }

  /**
   * Generate basic text summary
   */
  generateSummary(text: string): string {
    try {
      // Split into sentences
      const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

      if (sentences.length <= 2) {
        return text;
      }

      // Score sentences based on keyword frequency
      const keywords = this.extractKeywords(text);
      const keywordSet = new Set(keywords.slice(0, 5).map(k => k.text));

      const sentenceScores = sentences.map(sentence => {
        const tokens = this.tokenizer.tokenize(sentence.toLowerCase()) || [];
        const score = tokens.reduce((acc, token) => {
          return acc + (keywordSet.has(token) ? 1 : 0);
        }, 0);
        return { sentence: sentence.trim(), score };
      });

      // Get top 2 sentences
      const topSentences = sentenceScores
        .sort((a, b) => b.score - a.score)
        .slice(0, 2)
        .map(s => s.sentence);

      return topSentences.join('. ') + '.';
    } catch (error) {
      console.warn('Summary generation failed:', error);
      return text.substring(0, 200) + '...';
    }
  }

  /**
   * Classify text into categories
   */
  classifyText(text: string, categories: string[]): { category: string; confidence: number } {
    try {
      // This is a basic implementation - in production, you'd train a proper classifier
      const keywords = this.extractKeywords(text);
      const topKeywords = keywords.slice(0, 5).map(k => k.text);

      // Simple keyword-based classification
      const scores: Record<string, number> = {};

      categories.forEach(category => {
        scores[category] =
          topKeywords.reduce((acc, keyword) => {
            // Simple similarity check (could be improved with word embeddings)
            const similarity = natural.JaroWinklerDistance(keyword, category.toLowerCase());
            return acc + similarity;
          }, 0) / topKeywords.length;
      });

      const bestCategory = Object.entries(scores).sort(([, a], [, b]) => b - a)[0];

      return {
        category: bestCategory ? bestCategory[0] : categories[0] || 'unknown',
        confidence: bestCategory ? Math.min(1, bestCategory[1]) : 0.1,
      };
    } catch (error) {
      console.warn('Text classification failed:', error);
      return { category: categories[0] || 'unknown', confidence: 0.1 };
    }
  }

  /**
   * Detect emotion in text (basic implementation)
   */
  detectEmotion(text: string): { emotion: string; confidence: number } {
    try {
      const sentiment = this.analyzeSentiment(text);

      // Map sentiment to basic emotions
      if (sentiment.polarity > 0.5) {
        return { emotion: 'joy', confidence: sentiment.confidence };
      } else if (sentiment.polarity < -0.5) {
        return { emotion: 'sadness', confidence: sentiment.confidence };
      } else if (Math.abs(sentiment.polarity) < 0.1) {
        return { emotion: 'neutral', confidence: sentiment.confidence };
      } else if (sentiment.polarity > 0) {
        return { emotion: 'contentment', confidence: sentiment.confidence };
      } else {
        return { emotion: 'concern', confidence: sentiment.confidence };
      }
    } catch (error) {
      console.warn('Emotion detection failed:', error);
      return { emotion: 'neutral', confidence: 0.1 };
    }
  }

  /**
   * Helper method to create entity objects
   */
  private createEntity(
    id: string,
    name: string,
    type: EntityType,
    confidence: number,
    mentions: EntityMention[]
  ): Entity {
    return {
      id,
      name,
      type,
      confidence,
      mentions,
      attributes: {
        aliases: [],
        properties: {},
        metadata: {},
      },
      relationships: [],
      canonicalForm: name.toLowerCase(),
    };
  }

  /**
   * Get context around a text position
   */
  private getContext(text: string, startOffset: number, length: number): string {
    const contextSize = 50;
    const start = Math.max(0, startOffset - contextSize);
    const end = Math.min(text.length, startOffset + length + contextSize);
    return text.substring(start, end);
  }

  /**
   * Calculate text similarity using Jaro-Winkler distance
   */
  calculateSimilarity(text1: string, text2: string): number {
    try {
      return natural.JaroWinklerDistance(text1.toLowerCase(), text2.toLowerCase());
    } catch (error) {
      console.warn('Similarity calculation failed:', error);
      return 0;
    }
  }

  /**
   * Get phonetic representation of text
   */
  getPhoneticRepresentation(text: string): {
    metaphone: string;
    soundex: string;
    doubleMetaphone: string[];
  } {
    try {
      return {
        metaphone:
          typeof (natural.Metaphone as any).process === 'function'
            ? (natural.Metaphone as any).process(text)
            : '',
        soundex:
          typeof (natural.SoundEx as any).process === 'function'
            ? (natural.SoundEx as any).process(text)
            : '',
        doubleMetaphone:
          typeof (natural.DoubleMetaphone as any).process === 'function'
            ? (natural.DoubleMetaphone as any).process(text)
            : [],
      };
    } catch (error) {
      console.warn('Phonetic processing failed:', error);
      return {
        metaphone: '',
        soundex: '',
        doubleMetaphone: [],
      };
    }
  }

  // ===== Knowledge Graph Construction Methods =====

  /**
   * Enhance an existing knowledge graph with additional semantic relationships
   */
  enhanceKnowledgeGraph(graph: KnowledgeGraph, additionalText?: string): KnowledgeGraph {
    try {
      const enhancedGraph = { ...graph };

      // If additional text is provided, extract more entities and relationships
      if (additionalText) {
        const newEntities = this.extractNamedEntities(additionalText);
        const newBasicEntities = this.extractEntities(additionalText);
        const allNewEntities = [...newEntities, ...newBasicEntities];

        // Convert new entities to nodes
        const newNodes = this.entitiesToGraphNodes(allNewEntities);

        // Merge with existing nodes (deduplicate)
        const allNodes = [...enhancedGraph.nodes, ...newNodes];
        enhancedGraph.nodes = this.deduplicateGraphNodes(allNodes);

        // Extract relationships from new text
        const newRelationships = this.extractAdvancedRelationships(additionalText, allNewEntities);
        const newEdges = this.relationshipsToGraphEdges(newRelationships, enhancedGraph.nodes);

        // Merge with existing edges (deduplicate)
        const allEdges = [...enhancedGraph.edges, ...newEdges];
        enhancedGraph.edges = this.deduplicateGraphEdges(allEdges);
      }

      // Add semantic relationships based on node similarity
      const semanticEdges = this.inferSemanticRelationships(enhancedGraph.nodes);
      enhancedGraph.edges = this.deduplicateGraphEdges([...enhancedGraph.edges, ...semanticEdges]);

      // Recalculate statistics
      enhancedGraph.statistics = this.calculateGraphStatistics(
        enhancedGraph.nodes,
        enhancedGraph.edges
      );

      // Update metadata
      enhancedGraph.metadata.updatedAt = new Date();
      enhancedGraph.metadata.version = enhancedGraph.metadata.version + 1;

      return enhancedGraph;
    } catch (error) {
      console.warn('Knowledge graph enhancement failed:', error);
      return graph;
    }
  }

  /**
   * Find shortest path between two nodes using Dijkstra's algorithm
   */
  findShortestPath(graph: KnowledgeGraph, sourceId: string, targetId: string): GraphNode[] | null {
    try {
      // Build adjacency list
      const adjacencyList = new Map<string, Array<{ nodeId: string; weight: number }>>();

      // Initialize adjacency list
      graph.nodes.forEach(node => {
        adjacencyList.set(node.id, []);
      });

      // Populate adjacency list from edges
      graph.edges.forEach(edge => {
        const sourceNeighbors = adjacencyList.get(edge.source) || [];
        const targetNeighbors = adjacencyList.get(edge.target) || [];

        sourceNeighbors.push({ nodeId: edge.target, weight: edge.weight });

        // If bidirectional relationship, add reverse edge
        if (edge.relationship.properties.bidirectional) {
          targetNeighbors.push({ nodeId: edge.source, weight: edge.weight });
        }
      });

      // Dijkstra's algorithm
      const distances = new Map<string, number>();
      const previous = new Map<string, string | null>();
      const unvisited = new Set<string>();

      // Initialize distances
      graph.nodes.forEach(node => {
        distances.set(node.id, node.id === sourceId ? 0 : Infinity);
        previous.set(node.id, null);
        unvisited.add(node.id);
      });

      while (unvisited.size > 0) {
        // Find unvisited node with minimum distance
        let currentNode: string | null = null;
        let minDistance = Infinity;

        for (const nodeId of unvisited) {
          const distance = distances.get(nodeId) || Infinity;
          if (distance < minDistance) {
            minDistance = distance;
            currentNode = nodeId;
          }
        }

        if (!currentNode || minDistance === Infinity) {
          break; // No path exists
        }

        unvisited.delete(currentNode);

        // If we reached the target, break
        if (currentNode === targetId) {
          break;
        }

        // Update distances to neighbors
        const neighbors = adjacencyList.get(currentNode) || [];
        neighbors.forEach(neighbor => {
          if (unvisited.has(neighbor.nodeId)) {
            const altDistance = (distances.get(currentNode!) || 0) + 1 / neighbor.weight; // Inverse weight for shortest path
            if (altDistance < (distances.get(neighbor.nodeId) || Infinity)) {
              distances.set(neighbor.nodeId, altDistance);
              previous.set(neighbor.nodeId, currentNode);
            }
          }
        });
      }

      // Reconstruct path
      if (!previous.get(targetId)) {
        return null; // No path exists
      }

      const path: GraphNode[] = [];
      let currentId: string | null = targetId;

      while (currentId) {
        const node = graph.nodes.find(n => n.id === currentId);
        if (node) {
          path.unshift(node);
        }
        currentId = previous.get(currentId) || null;
      }

      return path.length > 0 ? path : null;
    } catch (error) {
      console.warn('Shortest path calculation failed:', error);
      return null;
    }
  }

  /**
   * Get neighbors of a specific node
   */
  getNodeNeighbors(graph: KnowledgeGraph, nodeId: string): GraphNode[] {
    try {
      const neighborIds = new Set<string>();

      // Find all connected nodes
      graph.edges.forEach(edge => {
        if (edge.source === nodeId) {
          neighborIds.add(edge.target);
        } else if (edge.target === nodeId && edge.relationship.properties.bidirectional) {
          neighborIds.add(edge.source);
        }
      });

      // Return neighbor nodes
      return graph.nodes.filter(node => neighborIds.has(node.id));
    } catch (error) {
      console.warn('Getting node neighbors failed:', error);
      return [];
    }
  }

  /**
   * Calculate centrality measures for all nodes
   */
  calculateNodeCentrality(graph: KnowledgeGraph): Record<
    string,
    {
      degree: number;
      betweenness: number;
      closeness: number;
    }
  > {
    try {
      const centrality: Record<string, { degree: number; betweenness: number; closeness: number }> =
        {};

      // Initialize centrality measures
      graph.nodes.forEach(node => {
        centrality[node.id] = {
          degree: 0,
          betweenness: 0,
          closeness: 0,
        };
      });

      // Calculate degree centrality
      graph.edges.forEach(edge => {
        const sourceNode = centrality[edge.source];
        const targetNode = centrality[edge.target];
        if (sourceNode) {
          sourceNode.degree += 1;
        }
        if (edge.relationship.properties.bidirectional && targetNode) {
          targetNode.degree += 1;
        }
      });

      // Calculate betweenness and closeness centrality
      graph.nodes.forEach(sourceNode => {
        let totalDistance = 0;
        let reachableNodes = 0;

        graph.nodes.forEach(targetNode => {
          if (sourceNode.id !== targetNode.id) {
            const path = this.findShortestPath(graph, sourceNode.id, targetNode.id);
            if (path && path.length > 1) {
              totalDistance += path.length - 1;
              reachableNodes += 1;

              // Update betweenness centrality for intermediate nodes
              for (let i = 1; i < path.length - 1; i++) {
                const nodeId = path[i]?.id;
                if (nodeId && centrality[nodeId]) {
                  centrality[nodeId].betweenness += 1;
                }
              }
            }
          }
        });

        // Calculate closeness centrality
        if (reachableNodes > 0) {
          const sourceNodeCentrality = centrality[sourceNode.id];
          if (sourceNodeCentrality) {
            sourceNodeCentrality.closeness = reachableNodes / totalDistance;
          }
        }
      });

      // Normalize betweenness centrality
      const nodeCount = graph.nodes.length;
      if (nodeCount > 2) {
        const normalizationFactor = ((nodeCount - 1) * (nodeCount - 2)) / 2;
        Object.keys(centrality).forEach(nodeId => {
          if (centrality[nodeId]) {
            centrality[nodeId].betweenness /= normalizationFactor;
          }
        });
      }

      return centrality;
    } catch (error) {
      console.warn('Centrality calculation failed:', error);

      // Return default centrality values
      const defaultCentrality: Record<
        string,
        { degree: number; betweenness: number; closeness: number }
      > = {};
      graph.nodes.forEach(node => {
        defaultCentrality[node.id] = { degree: 0, betweenness: 0, closeness: 0 };
      });
      return defaultCentrality;
    }
  }

  /**
   * Export knowledge graph in various formats
   */
  exportKnowledgeGraph(
    graph: KnowledgeGraph,
    format: 'json' | 'graphml' | 'cytoscape' | 'gephi' | 'json-ld'
  ): string {
    try {
      // Check for invalid graph structure
      if (!graph || !graph.nodes || !Array.isArray(graph.nodes)) {
        throw new Error('Invalid graph structure: nodes must be an array');
      }

      switch (format) {
        case 'json':
          return JSON.stringify(graph, null, 2);

        case 'graphml':
          return this.exportAsGraphML(graph);

        case 'cytoscape':
          return this.exportAsCytoscape(graph);

        case 'gephi':
          return this.exportAsGephi(graph);

        case 'json-ld':
          return this.exportAsJsonLD(graph);

        default:
          throw new Error(`Unsupported export format: ${format}`);
      }
    } catch (error) {
      console.warn('Knowledge graph export failed:', error);
      return JSON.stringify({
        error: `Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      });
    }
  }

  // ===== Compromise.js Advanced NLP Methods =====

  /**
   * Extract named entities using Compromise.js
   */
  extractNamedEntities(text: string): Entity[] {
    const entities: Entity[] = [];

    try {
      const doc = nlp(text);

      // Extract people
      const people = doc.people().out('array') as string[];
      people.forEach((person: string, index: number) => {
        const matches = doc.match(person);
        matches.forEach((match: any) => {
          const startOffset = text.indexOf(match.text());
          if (startOffset !== -1) {
            entities.push(
              this.createEntity(
                `person_${index}_0`,
                person,
                { category: EntityCategory.PERSON, subtype: 'person', description: 'Person name' },
                0.85,
                [
                  {
                    text: match.text(),
                    startOffset,
                    endOffset: startOffset + match.text().length,
                    confidence: 0.85,
                    context: this.getContext(text, startOffset, match.text().length),
                    documentId: '',
                  },
                ]
              )
            );
          }
        });
      });

      // Extract places
      const places = doc.places().out('array') as string[];
      places.forEach((place: string, index: number) => {
        const matches = doc.match(place);
        matches.forEach((match: any) => {
          const startOffset = text.indexOf(match.text());
          if (startOffset !== -1) {
            entities.push(
              this.createEntity(
                `place_${index}_0`,
                place,
                {
                  category: EntityCategory.LOCATION,
                  subtype: 'place',
                  description: 'Location or place',
                },
                0.8,
                [
                  {
                    text: match.text(),
                    startOffset,
                    endOffset: startOffset + match.text().length,
                    confidence: 0.8,
                    context: this.getContext(text, startOffset, match.text().length),
                    documentId: '',
                  },
                ]
              )
            );
          }
        });
      });

      // Extract organizations
      const organizations = doc.organizations().out('array') as string[];
      organizations.forEach((org: string, index: number) => {
        const matches = doc.match(org);
        matches.forEach((match: any) => {
          const startOffset = text.indexOf(match.text());
          if (startOffset !== -1) {
            entities.push(
              this.createEntity(
                `org_${index}_0`,
                org,
                {
                  category: EntityCategory.ORGANIZATION,
                  subtype: 'organization',
                  description: 'Organization name',
                },
                0.8,
                [
                  {
                    text: match.text(),
                    startOffset,
                    endOffset: startOffset + match.text().length,
                    confidence: 0.8,
                    context: this.getContext(text, startOffset, match.text().length),
                    documentId: '',
                  },
                ]
              )
            );
          }
        });
      });

      // Extract dates (if available)
      const dates = (doc as any).dates ? ((doc as any).dates().out('array') as string[]) : [];
      dates.forEach((date: string, index: number) => {
        const matches = doc.match(date);
        matches.forEach((match: any) => {
          const startOffset = text.indexOf(match.text());
          if (startOffset !== -1) {
            entities.push(
              this.createEntity(
                `date_compromise_${index}_0`,
                date,
                { category: EntityCategory.DATE, subtype: 'date', description: 'Date reference' },
                0.9,
                [
                  {
                    text: match.text(),
                    startOffset,
                    endOffset: startOffset + match.text().length,
                    confidence: 0.9,
                    context: this.getContext(text, startOffset, match.text().length),
                    documentId: '',
                  },
                ]
              )
            );
          }
        });
      });

      // Extract money/values
      const money = doc.money().out('array') as string[];
      money.forEach((amount: string, index: number) => {
        const matches = doc.match(amount);
        matches.forEach((match: any) => {
          const startOffset = text.indexOf(match.text());
          if (startOffset !== -1) {
            entities.push(
              this.createEntity(
                `money_compromise_${index}_0`,
                amount,
                {
                  category: EntityCategory.MONEY,
                  subtype: 'currency',
                  description: 'Monetary amount',
                },
                0.9,
                [
                  {
                    text: match.text(),
                    startOffset,
                    endOffset: startOffset + match.text().length,
                    confidence: 0.9,
                    context: this.getContext(text, startOffset, match.text().length),
                    documentId: '',
                  },
                ]
              )
            );
          }
        });
      });
    } catch (error) {
      console.warn('Named entity extraction with Compromise failed:', error);
    }

    return entities;
  }

  /**
   * Perform part-of-speech tagging
   */
  performPOSTagging(text: string): Array<{ text: string; pos: string; confidence: number }> {
    try {
      const doc = nlp(text);
      const terms = doc.terms().out('array');

      return terms.map((term: string) => {
        const termDoc = nlp(term);
        let pos = 'unknown';
        let confidence = 0.5;

        // Determine part of speech
        if (termDoc.nouns().length > 0) {
          pos = 'noun';
          confidence = 0.8;
        } else if (termDoc.verbs().length > 0) {
          pos = 'verb';
          confidence = 0.8;
        } else if (termDoc.adjectives().length > 0) {
          pos = 'adjective';
          confidence = 0.8;
        } else if (termDoc.adverbs().length > 0) {
          pos = 'adverb';
          confidence = 0.8;
        } else if (termDoc.pronouns().length > 0) {
          pos = 'pronoun';
          confidence = 0.9;
        }

        return {
          text: term,
          pos,
          confidence,
        };
      });
    } catch (error) {
      console.warn('POS tagging failed:', error);
      return [];
    }
  }

  /**
   * Extract relationships between entities using grammatical analysis
   */
  extractAdvancedRelationships(text: string, entities: Entity[]): Relationship[] {
    const relationships: Relationship[] = [];

    try {
      const doc = nlp(text);

      // Find sentences with multiple entities
      const sentences = doc.sentences().out('array');

      sentences.forEach((sentence: string, sentenceIndex: number) => {
        const sentenceDoc = nlp(sentence);
        const sentenceEntities = entities.filter(entity => sentence.includes(entity.name));

        if (sentenceEntities.length >= 2) {
          // Look for verb relationships
          const verbs = sentenceDoc.verbs().out('array');

          for (let i = 0; i < sentenceEntities.length; i++) {
            for (let j = i + 1; j < sentenceEntities.length; j++) {
              const entity1 = sentenceEntities[i];
              const entity2 = sentenceEntities[j];

              // Determine relationship type based on context
              let relationshipType = RelationshipType.RELATED_TO;
              let confidence = 0.6;

              // Check for specific relationship patterns
              if (
                verbs.some((verb: string) =>
                  ['is', 'are', 'was', 'were'].includes(verb.toLowerCase())
                )
              ) {
                relationshipType = RelationshipType.IS_A;
                confidence = 0.7;
              } else if (
                verbs.some((verb: string) =>
                  ['has', 'have', 'contains', 'includes'].includes(verb.toLowerCase())
                )
              ) {
                relationshipType = RelationshipType.CONTAINS;
                confidence = 0.8;
              } else if (
                verbs.some((verb: string) =>
                  ['works', 'employed', 'manages'].includes(verb.toLowerCase())
                )
              ) {
                relationshipType = RelationshipType.PART_OF;
                confidence = 0.75;
              }

              relationships.push({
                id: `adv_rel_${sentenceIndex}_${i}_${j}`,
                type: relationshipType,
                sourceId: entity1?.id || '',
                targetId: entity2?.id || '',
                strength: confidence,
                confidence,
                properties: {
                  description: `Relationship extracted from sentence: "${sentence.substring(0, 100)}..."`,
                  weight: 1,
                  bidirectional: relationshipType === RelationshipType.RELATED_TO,
                  context: sentence,
                },
                createdAt: new Date(),
              });
            }
          }
        }
      });
    } catch (error) {
      console.warn('Advanced relationship extraction failed:', error);
    }

    return relationships;
  }

  /**
   * Generate extractive summary using key phrases
   */
  generateAdvancedSummary(text: string, maxSentences: number = 3): string {
    try {
      const doc = nlp(text);
      const sentences = doc.sentences();

      if (sentences.length <= maxSentences) {
        return text;
      }

      // Score sentences based on various factors
      const sentenceScores = sentences.out('array').map((sentence: string, index: number) => {
        const sentenceDoc = nlp(sentence);
        let score = 0;

        // Factor 1: Sentence position (earlier sentences get higher scores)
        score += ((sentences.length - index) / sentences.length) * 0.3;

        // Factor 2: Presence of named entities
        const entityCount =
          sentenceDoc.people().length +
          sentenceDoc.places().length +
          sentenceDoc.organizations().length;
        score += entityCount * 0.2;

        // Factor 3: Presence of numbers/dates (often important)
        const numberCount =
          ((sentenceDoc as any).values ? (sentenceDoc as any).values().length : 0) +
          ((sentenceDoc as any).dates ? (sentenceDoc as any).dates().length : 0);
        score += numberCount * 0.15;

        // Factor 4: Sentence length (moderate length preferred)
        const wordCount = sentenceDoc.terms().length;
        if (wordCount >= 10 && wordCount <= 30) {
          score += 0.2;
        }

        // Factor 5: Presence of important verbs
        const importantVerbs = sentenceDoc
          .verbs()
          .out('array')
          .filter((verb: string) =>
            ['is', 'are', 'has', 'have', 'will', 'can', 'must', 'should'].includes(
              verb.toLowerCase()
            )
          );
        score += importantVerbs.length * 0.15;

        return { sentence, score, index };
      });

      // Select top sentences and maintain original order
      const topSentences = sentenceScores
        .sort((a: any, b: any) => b.score - a.score)
        .slice(0, maxSentences)
        .sort((a: any, b: any) => a.index - b.index)
        .map((s: any) => s.sentence);

      return topSentences.join(' ');
    } catch (error) {
      console.warn('Advanced summary generation failed:', error);
      return this.generateSummary(text);
    }
  }

  /**
   * Extract key phrases using Compromise.js
   */
  extractKeyPhrases(text: string): Array<{ phrase: string; importance: number; type: string }> {
    try {
      const doc = nlp(text);
      const phrases: Array<{ phrase: string; importance: number; type: string }> = [];

      // Extract noun phrases
      const nounPhrases = doc.nouns().out('array');
      nounPhrases.forEach((phrase: any) => {
        phrases.push({
          phrase,
          importance: 0.8,
          type: 'noun_phrase',
        });
      });

      // Extract verb phrases
      const verbPhrases = doc.verbs().out('array');
      verbPhrases.forEach((phrase: any) => {
        phrases.push({
          phrase,
          importance: 0.6,
          type: 'verb_phrase',
        });
      });

      // Extract adjective phrases
      const adjPhrases = doc.adjectives().out('array');
      adjPhrases.forEach((phrase: any) => {
        phrases.push({
          phrase,
          importance: 0.5,
          type: 'adjective_phrase',
        });
      });

      // Remove duplicates and sort by importance
      const uniquePhrases = phrases.filter(
        (phrase, index, self) => index === self.findIndex(p => p.phrase === phrase.phrase)
      );

      return uniquePhrases.sort((a, b) => b.importance - a.importance).slice(0, 20);
    } catch (error) {
      console.warn('Key phrase extraction failed:', error);
      return [];
    }
  }

  /**
   * Enhanced entity extraction combining Natural.js and Compromise.js
   */
  extractEntitiesEnhanced(text: string): Entity[] {
    try {
      // Get entities from both Natural.js and Compromise.js
      const naturalEntities = this.extractEntities(text);
      const compromiseEntities = this.extractNamedEntities(text);

      // Merge and deduplicate entities
      const allEntities = [...naturalEntities, ...compromiseEntities];
      const uniqueEntities = this.deduplicateEntities(allEntities);

      return uniqueEntities;
    } catch (error) {
      console.warn('Enhanced entity extraction failed:', error);
      return [];
    }
  }

  /**
   * Deduplicate entities based on text similarity
   */
  private deduplicateEntities(entities: Entity[]): Entity[] {
    const unique: Entity[] = [];

    entities.forEach(entity => {
      const isDuplicate = unique.some(existing => {
        const similarity = this.calculateSimilarity(entity.name, existing.name);
        return similarity > 0.8 && entity.type.category === existing.type.category;
      });

      if (!isDuplicate) {
        unique.push(entity);
      }
    });

    return unique;
  }

  // ===== Entity Extraction and Relationship Mapping =====

  /**
   * Main entity extraction method for document analysis with ChromaDB integration
   * IMPORTANT: All extracted entities are stored in ChromaDB for semantic search
   */
  async extractEntitiesFromDocument(text: string, documentId: string = ''): Promise<Entity[]> {
    try {
      // Get enhanced entities from both Natural.js and Compromise.js
      const entities = this.extractEntitiesEnhanced(text);

      // Update document ID in mentions
      entities.forEach(entity => {
        entity.mentions.forEach(mention => {
          mention.documentId = documentId;
        });
      });

      // Perform entity linking and disambiguation
      const linkedEntities = this.performEntityLinking(entities);

      // Calculate confidence scores
      const scoredEntities = this.calculateEntityConfidence(linkedEntities, text);

      // Normalize and standardize entities
      const normalizedEntities = this.normalizeEntities(scoredEntities);

      // CRITICAL: Store entities in ChromaDB for semantic search and cross-document linking
      await this.storeEntitiesInChroma(normalizedEntities, documentId);

      return normalizedEntities;
    } catch (error) {
      console.warn('Document entity extraction failed:', error);
      return [];
    }
  }

  /**
   * Store extracted entities in ChromaDB for semantic search
   * CRITICAL: This ensures all entities are properly indexed for cross-document analysis
   */
  private async storeEntitiesInChroma(entities: Entity[], documentId: string): Promise<void> {
    try {
      for (const entity of entities) {
        // Generate embedding for entity
        const entityText = `${entity.name} ${entity.type.description} ${entity.attributes.aliases?.join(' ') || ''}`;

        try {
          // Use the proper ChromaDB integration method for embedding generation and storage
          await aiModelClient.generateAndStoreEmbeddings(
            entityText,
            `entity_${entity.id}_${documentId}`,
            'extracted_entities',
            {
              type: 'extracted_entity',
              entityId: entity.id,
              entityName: entity.name,
              entityType: entity.type.category,
              confidence: entity.confidence,
              documentId: documentId,
              mentions: entity.mentions.length,
              aliases: entity.attributes.aliases || [],
              canonicalForm: entity.canonicalForm,
              extractedAt: new Date().toISOString(),
              tags: ['extracted_entity', entity.type.category, documentId],
            }
          );
        } catch (embeddingError) {
          console.warn(
            `Failed to generate and store embedding for entity ${entity.name}:`,
            embeddingError
          );

          // Fallback: Store without embedding using ChromaDB directly
          // const entityKnowledgeItem = {
          //   id: `entity_${entity.id}_${documentId}`,
          //   content: entityText,
          //   metadata: {
          //     type: 'extracted_entity',
          //     entityId: entity.id,
          //     entityName: entity.name,
          //     entityType: entity.type.category,
          //     confidence: entity.confidence,
          //     documentId: documentId,
          //     mentions: entity.mentions.length,
          //     aliases: entity.attributes.aliases || [],
          //     canonicalForm: entity.canonicalForm,
          //     extractedAt: new Date().toISOString(),
          //   },
          //   tags: ['extracted_entity', entity.type.category, documentId],
          //   createdAt: new Date().toISOString(),
          //   updatedAt: new Date().toISOString(),
          // };

          // await chromaKnowledgeBase.storeInformation(entityKnowledgeItem, 'extracted_entities');
        }
      }

      console.log(
        `Successfully stored ${entities.length} entities in ChromaDB for document ${documentId}`
      );
    } catch (error) {
      console.error('Failed to store entities in ChromaDB:', error);
      // Don't throw error to avoid breaking the extraction process
    }
  }

  /**
   * Perform entity linking and disambiguation
   */
  performEntityLinking(entities: Entity[]): Entity[] {
    const linkedEntities: Entity[] = [];
    const entityGroups: Map<string, Entity[]> = new Map();

    // Group similar entities
    entities.forEach(entity => {
      const key = `${entity.type.category}_${entity.canonicalForm}`;
      if (!entityGroups.has(key)) {
        entityGroups.set(key, []);
      }
      entityGroups.get(key)!.push(entity);
    });

    // Merge similar entities
    entityGroups.forEach((group, _key) => {
      if (group.length === 1) {
        const firstEntity = group[0];
        if (firstEntity) {
          linkedEntities.push(firstEntity);
        }
      } else {
        // Merge entities with same canonical form
        const mergedEntity = this.mergeEntities(group);
        linkedEntities.push(mergedEntity);
      }
    });

    return linkedEntities;
  }

  /**
   * Merge multiple entities into one
   */
  private mergeEntities(entities: Entity[]): Entity {
    const primary = entities[0];
    const allMentions: EntityMention[] = [];
    const allAliases: string[] = [];

    entities.forEach(entity => {
      allMentions.push(...entity.mentions);
      allAliases.push(...entity.attributes.aliases);
      allAliases.push(entity.name);
    });

    // Remove duplicates
    const uniqueAliases = [...new Set(allAliases)];

    if (!primary) {
      throw new Error('No primary entity found for merging');
    }

    return {
      ...primary,
      id: primary.id,
      mentions: allMentions,
      confidence: Math.max(...entities.map(e => e.confidence)),
      attributes: {
        ...primary.attributes,
        aliases: uniqueAliases,
        properties: {
          ...primary.attributes.properties,
          mergedFrom: entities.map(e => e.id),
        },
      },
    };
  }

  /**
   * Calculate entity confidence scores
   */
  calculateEntityConfidence(entities: Entity[], text: string): Entity[] {
    return entities.map(entity => {
      let confidence = entity.confidence;

      // Boost confidence based on frequency
      const frequency = entity.mentions.length;
      confidence += Math.min(0.2, frequency * 0.05);

      // Boost confidence based on context
      const contextScore = this.analyzeEntityContext(entity, text);
      confidence += contextScore * 0.1;

      // Boost confidence for well-formed entities
      if (this.isWellFormedEntity(entity)) {
        confidence += 0.1;
      }

      return {
        ...entity,
        confidence: Math.min(1, confidence),
      };
    });
  }

  /**
   * Analyze entity context for confidence scoring
   */
  private analyzeEntityContext(entity: Entity, _text: string): number {
    let score = 0;

    entity.mentions.forEach(mention => {
      const context = mention.context.toLowerCase();

      // Check for contextual indicators based on entity type
      switch (entity.type.category) {
        case EntityCategory.PERSON:
          if (
            context.includes('mr.') ||
            context.includes('ms.') ||
            context.includes('dr.') ||
            context.includes('prof.')
          ) {
            score += 0.3;
          }
          break;
        case EntityCategory.ORGANIZATION:
          if (
            context.includes('inc.') ||
            context.includes('corp.') ||
            context.includes('ltd.') ||
            context.includes('company')
          ) {
            score += 0.3;
          }
          break;
        case EntityCategory.LOCATION:
          if (
            context.includes('city') ||
            context.includes('state') ||
            context.includes('country') ||
            context.includes('street')
          ) {
            score += 0.3;
          }
          break;
        case EntityCategory.MONEY:
          if (
            context.includes('paid') ||
            context.includes('cost') ||
            context.includes('price') ||
            context.includes('amount')
          ) {
            score += 0.3;
          }
          break;
      }
    });

    return Math.min(1, score / entity.mentions.length);
  }

  /**
   * Check if entity is well-formed
   */
  private isWellFormedEntity(entity: Entity): boolean {
    const name = entity.name.trim();

    // Check length
    if (name.length < 2 || name.length > 100) {
      return false;
    }

    // Check for valid characters based on entity type
    switch (entity.type.category) {
      case EntityCategory.PERSON:
        return /^[A-Za-z\s\-'.]+$/.test(name);
      case EntityCategory.EMAIL:
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(name);
      case EntityCategory.PHONE:
        return /^[\d\s\-\(\)\+\.]+$/.test(name);
      case EntityCategory.URL:
        return /^https?:\/\//.test(name);
      case EntityCategory.MONEY:
        return /^[\$\d\,\.]+$/.test(name);
      default:
        return true;
    }
  }

  /**
   * Normalize and standardize entities
   */
  normalizeEntities(entities: Entity[]): Entity[] {
    return entities.map(entity => {
      let normalizedName = entity.name;

      // Normalize based on entity type
      switch (entity.type.category) {
        case EntityCategory.PERSON:
          normalizedName = this.normalizePerson(entity.name);
          break;
        case EntityCategory.ORGANIZATION:
          normalizedName = this.normalizeOrganization(entity.name);
          break;
        case EntityCategory.LOCATION:
          normalizedName = this.normalizeLocation(entity.name);
          break;
        case EntityCategory.EMAIL:
          normalizedName = entity.name.toLowerCase();
          break;
        case EntityCategory.PHONE:
          normalizedName = this.normalizePhone(entity.name);
          break;
      }

      return {
        ...entity,
        name: normalizedName,
        canonicalForm: normalizedName.toLowerCase().trim(),
      };
    });
  }

  /**
   * Normalize person names
   */
  private normalizePerson(name: string): string {
    return name
      .split(' ')
      .map(part => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
      .join(' ')
      .trim();
  }

  /**
   * Normalize organization names
   */
  private normalizeOrganization(name: string): string {
    // Capitalize first letter of each word, preserve acronyms
    return name
      .split(' ')
      .map(word => {
        if (word.length <= 3 && word.toUpperCase() === word) {
          return word.toUpperCase(); // Keep acronyms
        }
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      })
      .join(' ')
      .trim();
  }

  /**
   * Normalize location names
   */
  private normalizeLocation(name: string): string {
    return name
      .split(' ')
      .map(part => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
      .join(' ')
      .trim();
  }

  /**
   * Normalize phone numbers
   */
  private normalizePhone(phone: string): string {
    // Remove all non-digit characters
    const digits = phone.replace(/\D/g, '');

    // Format as (XXX) XXX-XXXX for US numbers
    if (digits.length === 10) {
      return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
    } else if (digits.length === 11 && digits.startsWith('1')) {
      return `+1 (${digits.slice(1, 4)}) ${digits.slice(4, 7)}-${digits.slice(7)}`;
    }

    return phone; // Return original if can't normalize
  }

  /**
   * Extract comprehensive relationships between entities
   */
  extractEntityRelationships(entities: Entity[], text: string): Relationship[] {
    const relationships: Relationship[] = [];

    try {
      // Get basic co-occurrence relationships
      const cooccurrenceRels = this.extractRelationships(entities, text);
      relationships.push(...cooccurrenceRels);

      // Get advanced grammatical relationships
      const grammaticalRels = this.extractAdvancedRelationships(text, entities);
      relationships.push(...grammaticalRels);

      // Extract semantic relationships
      const semanticRels = this.extractSemanticRelationships(entities, text);
      relationships.push(...semanticRels);

      // Extract hierarchical relationships
      const hierarchicalRels = this.extractHierarchicalRelationships(entities);
      relationships.push(...hierarchicalRels);

      // Deduplicate and score relationships
      const uniqueRelationships = this.deduplicateRelationships(relationships);
      const scoredRelationships = this.scoreRelationships(uniqueRelationships, text);

      return scoredRelationships;
    } catch (error) {
      console.warn('Entity relationship extraction failed:', error);
      return [];
    }
  }

  /**
   * Extract semantic relationships based on entity types and context
   */
  private extractSemanticRelationships(entities: Entity[], text: string): Relationship[] {
    const relationships: Relationship[] = [];

    for (let i = 0; i < entities.length; i++) {
      for (let j = i + 1; j < entities.length; j++) {
        const entity1 = entities[i];
        const entity2 = entities[j];

        if (!entity1 || !entity2) {
          continue;
        }

        // Determine semantic relationship based on entity types
        const relationship = this.determineSemanticRelationship(entity1, entity2, text);

        if (relationship) {
          relationships.push({
            id: `semantic_${i}_${j}`,
            type: relationship.type,
            sourceId: entity1.id,
            targetId: entity2.id,
            strength: relationship.strength,
            confidence: relationship.confidence,
            properties: {
              description: relationship.description,
              weight: 1,
              bidirectional: relationship.bidirectional,
            },
            createdAt: new Date(),
          });
        }
      }
    }

    return relationships;
  }

  /**
   * Determine semantic relationship between two entities
   */
  private determineSemanticRelationship(
    entity1: Entity,
    entity2: Entity,
    _text: string
  ): {
    type: RelationshipType;
    strength: number;
    confidence: number;
    description: string;
    bidirectional: boolean;
  } | null {
    const type1 = entity1.type.category;
    const type2 = entity2.type.category;

    // Person-Organization relationships
    if (type1 === EntityCategory.PERSON && type2 === EntityCategory.ORGANIZATION) {
      return {
        type: RelationshipType.PART_OF,
        strength: 0.7,
        confidence: 0.6,
        description: 'Person associated with organization',
        bidirectional: false,
      };
    }

    // Person-Location relationships
    if (type1 === EntityCategory.PERSON && type2 === EntityCategory.LOCATION) {
      return {
        type: RelationshipType.RELATED_TO,
        strength: 0.6,
        confidence: 0.5,
        description: 'Person associated with location',
        bidirectional: false,
      };
    }

    // Organization-Location relationships
    if (type1 === EntityCategory.ORGANIZATION && type2 === EntityCategory.LOCATION) {
      return {
        type: RelationshipType.RELATED_TO,
        strength: 0.7,
        confidence: 0.6,
        description: 'Organization located in or associated with location',
        bidirectional: false,
      };
    }

    // Money-Person/Organization relationships
    if (
      type1 === EntityCategory.MONEY &&
      (type2 === EntityCategory.PERSON || type2 === EntityCategory.ORGANIZATION)
    ) {
      return {
        type: RelationshipType.RELATED_TO,
        strength: 0.8,
        confidence: 0.7,
        description: 'Financial relationship',
        bidirectional: false,
      };
    }

    return null;
  }

  /**
   * Extract hierarchical relationships (is-a, part-of)
   */
  private extractHierarchicalRelationships(entities: Entity[]): Relationship[] {
    const relationships: Relationship[] = [];

    // Group entities by type
    const entityGroups: Map<EntityCategory, Entity[]> = new Map();
    entities.forEach(entity => {
      if (!entityGroups.has(entity.type.category)) {
        entityGroups.set(entity.type.category, []);
      }
      entityGroups.get(entity.type.category)!.push(entity);
    });

    // Look for hierarchical patterns within groups
    entityGroups.forEach((group, category) => {
      if (group.length > 1) {
        // For organizations, look for parent-subsidiary relationships
        if (category === EntityCategory.ORGANIZATION) {
          this.findOrganizationHierarchy(group, relationships);
        }

        // For locations, look for geographic containment
        if (category === EntityCategory.LOCATION) {
          this.findLocationHierarchy(group, relationships);
        }
      }
    });

    return relationships;
  }

  /**
   * Find organization hierarchy relationships
   */
  private findOrganizationHierarchy(organizations: Entity[], relationships: Relationship[]): void {
    organizations.forEach((org1, i) => {
      organizations.slice(i + 1).forEach((org2, j) => {
        // Simple heuristic: shorter names might be parent companies
        if (org1.name.length < org2.name.length && org2.name.includes(org1.name)) {
          relationships.push({
            id: `org_hierarchy_${i}_${j}`,
            type: RelationshipType.PART_OF,
            sourceId: org2.id,
            targetId: org1.id,
            strength: 0.6,
            confidence: 0.5,
            properties: {
              description: 'Potential parent-subsidiary relationship',
              weight: 1,
              bidirectional: false,
            },
            createdAt: new Date(),
          });
        }
      });
    });
  }

  /**
   * Find location hierarchy relationships
   */
  private findLocationHierarchy(locations: Entity[], relationships: Relationship[]): void {
    // This is a simplified implementation
    // In practice, you'd use a geographic database
    const hierarchyKeywords = ['city', 'state', 'country', 'county', 'province'];

    locations.forEach((loc1, i) => {
      locations.slice(i + 1).forEach((loc2, j) => {
        // Check if one location might contain another
        const containsKeyword = hierarchyKeywords.some(
          keyword =>
            loc1.name.toLowerCase().includes(keyword) || loc2.name.toLowerCase().includes(keyword)
        );

        if (containsKeyword) {
          relationships.push({
            id: `loc_hierarchy_${i}_${j}`,
            type: RelationshipType.CONTAINS,
            sourceId: loc1.id,
            targetId: loc2.id,
            strength: 0.5,
            confidence: 0.4,
            properties: {
              description: 'Potential geographic containment',
              weight: 1,
              bidirectional: false,
            },
            createdAt: new Date(),
          });
        }
      });
    });
  }

  /**
   * Deduplicate relationships
   */
  private deduplicateRelationships(relationships: Relationship[]): Relationship[] {
    const unique: Relationship[] = [];
    const seen = new Set<string>();

    relationships.forEach(rel => {
      const key = `${rel.sourceId}_${rel.targetId}_${rel.type}`;
      const reverseKey = `${rel.targetId}_${rel.sourceId}_${rel.type}`;

      if (!seen.has(key) && !seen.has(reverseKey)) {
        unique.push(rel);
        seen.add(key);
      }
    });

    return unique;
  }

  /**
   * Score relationships based on various factors
   */
  private scoreRelationships(relationships: Relationship[], text: string): Relationship[] {
    return relationships.map(rel => {
      let score = rel.confidence;

      // Boost score based on relationship type reliability
      switch (rel.type) {
        case RelationshipType.IS_A:
          score += 0.1;
          break;
        case RelationshipType.PART_OF:
          score += 0.05;
          break;
        case RelationshipType.CONTAINS:
          score += 0.05;
          break;
      }

      // Boost score if relationship appears multiple times
      const relationshipMentions = this.countRelationshipMentions(rel, text);
      score += Math.min(0.2, relationshipMentions * 0.05);

      return {
        ...rel,
        confidence: Math.min(1, score),
        strength: Math.min(1, score),
      };
    });
  }

  /**
   * Count how many times a relationship is mentioned in text
   */
  private countRelationshipMentions(_relationship: Relationship, _text: string): number {
    // This is a simplified implementation
    // In practice, you'd use more sophisticated pattern matching
    return 1;
  }

  // ===== Knowledge Graph Construction =====

  /**
   * Build knowledge graph from entities and relationships
   */
  async buildKnowledgeGraph(
    entities: Entity[],
    relationships: Relationship[],
    graphName: string = 'Document Knowledge Graph'
  ): Promise<KnowledgeGraph> {
    try {
      // Convert entities to graph nodes
      const nodes = this.createGraphNodes(entities, 'default');

      // Convert relationships to graph edges
      const edges = this.createGraphEdges(relationships);

      // Calculate graph statistics
      const statistics = this.calculateGraphStatistics(nodes, edges);

      // Create graph metadata
      const metadata = this.createGraphMetadata(graphName);

      return {
        id: `kg_${Date.now()}`,
        name: graphName,
        description: `Knowledge graph constructed from ${entities.length} entities and ${relationships.length} relationships`,
        nodes,
        edges,
        metadata,
        statistics,
      };
    } catch (error) {
      console.warn('Knowledge graph construction failed:', error);
      throw new Error(
        `Failed to build knowledge graph: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Calculate graph statistics
   */
  private calculateGraphStatistics(nodes: GraphNode[], edges: GraphEdge[]): GraphStatistics {
    const nodeCount = nodes.length;
    const edgeCount = edges.length;

    // Calculate degree for each node
    const degrees = new Map<string, number>();
    nodes.forEach(node => degrees.set(node.id, 0));

    edges.forEach(edge => {
      degrees.set(edge.source, (degrees.get(edge.source) || 0) + 1);
      degrees.set(edge.target, (degrees.get(edge.target) || 0) + 1);
    });

    const degreeValues = Array.from(degrees.values());
    const averageDegree =
      degreeValues.length > 0
        ? degreeValues.reduce((sum, degree) => sum + degree, 0) / degreeValues.length
        : 0;

    // Calculate density
    const maxPossibleEdges = (nodeCount * (nodeCount - 1)) / 2;
    const density = maxPossibleEdges > 0 ? edgeCount / maxPossibleEdges : 0;

    // Calculate connected components (simplified)
    const components = this.calculateConnectedComponents(nodes, edges);

    // Calculate diameter (simplified - just max degree for now)
    const diameter = degreeValues.length > 0 ? Math.max(...degreeValues) : 0;

    return {
      nodeCount,
      edgeCount,
      averageDegree,
      density,
      components,
      diameter,
      clustering: 0, // Would need complex calculation
      centrality: {
        betweenness: {},
        closeness: {},
        degree: {},
        eigenvector: {},
      },
    };
  }

  /**
   * Create graph metadata
   */
  private createGraphMetadata(_graphName: string): GraphMetadata {
    return {
      createdAt: new Date(),
      updatedAt: new Date(),
      version: 1.0,
      author: 'NLPProcessor',
      tags: ['entity-relationship-extraction', 'natural-compromise-hybrid', 'pattern-based'],
    };
  }

  /**
   * Retrieve knowledge graph from ChromaDB by ID
   */
  async getKnowledgeGraphFromChroma(graphId: string): Promise<KnowledgeGraph | null> {
    // return null;
    try {
      const searchResults = await chromaKnowledgeBase.semanticSearch(
        graphId,
        'knowledge_graphs',
        1,
        0.9
      );

      if (searchResults.length === 0) {
        return null;
      }

      const firstResult = searchResults[0];
      if (!firstResult) {
        throw new Error('No search results found');
      }
      const graphData = JSON.parse(firstResult.item.content);

      // Retrieve entities
      // const entityResults = await chromaKnowledgeBase.semanticSearch(
      //   `knowledgeGraphId:${graphId}`,
      //   'entities',
      //   100,
      //   0.5
      // );

      // Retrieve relationships
      // const relationshipResults = await chromaKnowledgeBase.semanticSearch(
      //   `knowledgeGraphId:${graphId}`,
      //   'relationships',
      //   100,
      //   0.5
      // );

      // Reconstruct knowledge graph (simplified implementation)
      return {
        id: graphId,
        name: graphData.name,
        description: graphData.description,
        nodes: [], // Would need to reconstruct from entity results
        edges: [], // Would need to reconstruct from relationship results
        metadata: firstResult.item.metadata as unknown as GraphMetadata,
        statistics: {
          nodeCount: graphData.nodeCount,
          edgeCount: graphData.edgeCount,
          density: graphData.density,
          averageDegree: 0,
          components: 1,
          diameter: 0,
          clustering: 0,
          centrality: {
            betweenness: {},
            closeness: {},
            degree: {},
            eigenvector: {},
          },
        },
      };
    } catch (error) {
      console.error('Failed to retrieve knowledge graph from ChromaDB:', error);
      return null;
    }
  }

  /**
   * Search for entities in ChromaDB using semantic similarity
   */
  async searchEntitiesInChroma(
    query: string,
    limit: number = 10,
    threshold: number = 0.7
  ): Promise<Entity[]> {
    try {
      const searchResults = await chromaKnowledgeBase.semanticSearch(
        query,
        'entities',
        limit,
        threshold
      );

      return searchResults.map(result => ({
        id: result.item.metadata.entityId as string,
        name: result.item.content.split(' - ')[0] || '',
        type: {
          category: result.item.metadata.entityCategory as EntityCategory,
          description: result.item.content.split(' - ')[1]?.split(' (')[0] || '',
          confidence: (result.item.metadata.confidence as number) || 0,
        },
        confidence: (result.item.metadata.confidence as number) || 0,
        mentions: [],
        attributes: {
          aliases: (result.item.metadata.aliases as string[]) || [],
          properties: {},
          metadata: {},
        },
        canonicalForm: result.item.content.split(' - ')[0] || '',
        relationships: [], // Required property for Entity interface
      }));
    } catch (error) {
      console.error('Failed to search entities in ChromaDB:', error);
      return [];
    }
  }

  // ===== Knowledge Graph Construction Methods =====

  /**
   * Construct a knowledge graph from extracted entities and relationships
   */
  constructKnowledgeGraph(text: string, documentId: string, graphName?: string): KnowledgeGraph {
    try {
      // Extract entities and relationships
      const entities = this.extractEntities(text);
      const namedEntities = this.extractNamedEntities(text);
      const allEntities = [...entities, ...namedEntities];

      // Deduplicate entities
      const uniqueEntities = this.deduplicateEntities(allEntities);

      // Extract relationships
      const basicRelationships = this.extractRelationships(uniqueEntities, text);
      const advancedRelationships = this.extractAdvancedRelationships(text, uniqueEntities);
      const allRelationships = [...basicRelationships, ...advancedRelationships];

      // Create graph nodes from entities
      const nodes = this.createGraphNodes(uniqueEntities, documentId);

      // Create graph edges from relationships
      const edges = this.createGraphEdges(allRelationships);

      // Calculate graph statistics
      const statistics = this.calculateGraphStatistics(nodes, edges);

      const graph: KnowledgeGraph = {
        id: `kg_${documentId}_${Date.now()}`,
        name: graphName || `Knowledge Graph for Document ${documentId}`,
        description: `Automatically generated knowledge graph from document analysis`,
        nodes,
        edges,
        metadata: {
          createdAt: new Date(),
          updatedAt: new Date(),
          version: 1,
          author: 'NLPProcessor',
          tags: ['auto-generated', 'nlp', 'document-analysis'],
        },
        statistics,
      };

      return graph;
    } catch (error) {
      throw new Error(
        `Knowledge graph construction failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Create graph nodes from entities
   */
  private createGraphNodes(entities: Entity[], documentId: string): GraphNode[] {
    return entities.map((entity, _index) => {
      // Determine node type based on entity category
      let nodeType: NodeType;
      switch (entity.type.category) {
        case EntityCategory.PERSON:
          nodeType = NodeType.PERSON;
          break;
        case EntityCategory.ORGANIZATION:
          nodeType = NodeType.ORGANIZATION;
          break;
        case EntityCategory.LOCATION:
          nodeType = NodeType.LOCATION;
          break;
        default:
          nodeType = NodeType.ENTITY;
      }

      return {
        id: entity.id,
        label: entity.name,
        type: nodeType,
        properties: {
          name: entity.name,
          description: entity.type.description,
          category: entity.type.category,
          confidence: entity.confidence,
          attributes: {
            canonicalForm: entity.canonicalForm,
            mentions: entity.mentions.length,
            documentId,
            aliases: entity.attributes.aliases,
            ...entity.attributes.properties,
          },
        },
        position: {
          x: Math.random() * 1000, // Random initial positioning
          y: Math.random() * 1000,
        },
      };
    });
  }

  /**
   * Create graph edges from relationships
   */
  private createGraphEdges(relationships: Relationship[]): GraphEdge[] {
    return relationships.map(relationship => ({
      id: relationship.id,
      source: relationship.sourceId,
      target: relationship.targetId,
      relationship,
      weight: relationship.strength,
      style: {
        color: this.getRelationshipColor(relationship.type),
        width: Math.max(1, relationship.strength * 5),
        style: relationship.properties.bidirectional ? 'solid' : 'solid',
        arrow: !relationship.properties.bidirectional,
      },
    }));
  }

  /**
   * Get color for relationship type
   */
  private getRelationshipColor(type: RelationshipType): string {
    const colorMap: Record<RelationshipType, string> = {
      [RelationshipType.IS_A]: '#3B82F6', // Blue
      [RelationshipType.PART_OF]: '#10B981', // Green
      [RelationshipType.RELATED_TO]: '#6B7280', // Gray
      [RelationshipType.DEPENDS_ON]: '#F59E0B', // Yellow
      [RelationshipType.CONTAINS]: '#8B5CF6', // Purple
      [RelationshipType.REFERENCES]: '#EF4444', // Red
      [RelationshipType.SIMILAR_TO]: '#06B6D4', // Cyan
      [RelationshipType.OPPOSITE_OF]: '#EC4899', // Pink
    };
    return colorMap[type] || '#6B7280';
  }

  /**
   * Convert entities to graph nodes
   */
  private entitiesToGraphNodes(entities: Entity[]): GraphNode[] {
    return entities.map(entity => {
      let nodeType: NodeType;

      // Map entity categories to node types
      switch (entity.type.category) {
        case EntityCategory.PERSON:
          nodeType = NodeType.PERSON;
          break;
        case EntityCategory.ORGANIZATION:
          nodeType = NodeType.ORGANIZATION;
          break;
        case EntityCategory.LOCATION:
          nodeType = NodeType.LOCATION;
          break;
        default:
          nodeType = NodeType.ENTITY;
      }

      const properties: NodeProperties = {
        name: entity.name,
        description: entity.type.description,
        category: entity.type.category,
        confidence: entity.confidence,
        attributes: {
          canonicalForm: entity.canonicalForm,
          mentions: entity.mentions.length,
          ...entity.attributes.properties,
        },
      };

      return {
        id: entity.id,
        label: entity.name,
        type: nodeType,
        properties,
      };
    });
  }

  /**
   * Convert relationships to graph edges
   */
  private relationshipsToGraphEdges(
    relationships: Relationship[],
    nodes: GraphNode[]
  ): GraphEdge[] {
    const nodeIds = new Set(nodes.map(node => node.id));

    return relationships
      .filter(rel => nodeIds.has(rel.sourceId) && nodeIds.has(rel.targetId))
      .map(relationship => ({
        id: relationship.id,
        source: relationship.sourceId,
        target: relationship.targetId,
        relationship,
        weight: relationship.strength,
      }));
  }

  /**
   * Calculate number of connected components
   */
  private calculateConnectedComponents(nodes: GraphNode[], edges: GraphEdge[]): number {
    const visited = new Set<string>();
    let components = 0;

    const dfs = (nodeId: string) => {
      if (visited.has(nodeId)) return;
      visited.add(nodeId);

      // Find all connected nodes
      edges.forEach(edge => {
        if (edge.source === nodeId && !visited.has(edge.target)) {
          dfs(edge.target);
        } else if (
          edge.target === nodeId &&
          edge.relationship.properties.bidirectional &&
          !visited.has(edge.source)
        ) {
          dfs(edge.source);
        }
      });
    };

    nodes.forEach(node => {
      if (!visited.has(node.id)) {
        dfs(node.id);
        components++;
      }
    });

    return components;
  }

  /**
   * Deduplicate graph nodes
   */
  private deduplicateGraphNodes(nodes: GraphNode[]): GraphNode[] {
    const seen = new Map<string, GraphNode>();

    nodes.forEach(node => {
      const existing = seen.get(node.id);
      if (existing) {
        // Merge properties if confidence is higher
        if (node.properties.confidence > existing.properties.confidence) {
          seen.set(node.id, node);
        }
      } else {
        seen.set(node.id, node);
      }
    });

    return Array.from(seen.values());
  }

  /**
   * Deduplicate graph edges
   */
  private deduplicateGraphEdges(edges: GraphEdge[]): GraphEdge[] {
    const seen = new Map<string, GraphEdge>();

    edges.forEach(edge => {
      const key = `${edge.source}_${edge.target}_${edge.relationship.type}`;
      const existing = seen.get(key);

      if (existing) {
        // Merge edges by taking the one with higher weight
        if (edge.weight > existing.weight) {
          seen.set(key, edge);
        }
      } else {
        seen.set(key, edge);
      }
    });

    return Array.from(seen.values());
  }

  /**
   * Infer semantic relationships between nodes
   */
  private inferSemanticRelationships(nodes: GraphNode[]): GraphEdge[] {
    const semanticEdges: GraphEdge[] = [];

    for (let i = 0; i < nodes.length; i++) {
      for (let j = i + 1; j < nodes.length; j++) {
        const node1 = nodes[i];
        const node2 = nodes[j];

        if (!node1 || !node2) {
          continue;
        }

        // Calculate semantic similarity
        const similarity = this.calculateSimilarity(
          node1.properties.name.toLowerCase(),
          node2.properties.name.toLowerCase()
        );

        // Create semantic relationship if similarity is above threshold
        if (similarity > 0.6 && similarity < 0.8) {
          // Not too similar (would be duplicates)
          const relationship: Relationship = {
            id: `semantic_${node1.id}_${node2.id}`,
            type: RelationshipType.RELATED_TO,
            sourceId: node1.id,
            targetId: node2.id,
            strength: similarity,
            confidence: similarity * 0.8, // Lower confidence for inferred relationships
            properties: {
              description: 'Semantically inferred relationship',
              weight: similarity,
              bidirectional: true,
            },
            createdAt: new Date(),
          };

          semanticEdges.push({
            id: relationship.id,
            source: node1.id,
            target: node2.id,
            relationship,
            weight: similarity,
          });
        }
      }
    }

    return semanticEdges;
  }

  /**
   * Export graph as GraphML format
   */
  private exportAsGraphML(graph: KnowledgeGraph): string {
    let graphml = '<?xml version="1.0" encoding="UTF-8"?>\n';
    graphml += '<graphml xmlns="http://graphml.graphdrawing.org/xmlns" ';
    graphml += 'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" ';
    graphml += 'xsi:schemaLocation="http://graphml.graphdrawing.org/xmlns ';
    graphml += 'http://graphml.graphdrawing.org/xmlns/1.0/graphml.xsd">\n';

    // Define keys for attributes
    graphml += '  <key id="name" for="node" attr.name="name" attr.type="string"/>\n';
    graphml += '  <key id="type" for="node" attr.name="type" attr.type="string"/>\n';
    graphml += '  <key id="confidence" for="node" attr.name="confidence" attr.type="double"/>\n';
    graphml += '  <key id="weight" for="edge" attr.name="weight" attr.type="double"/>\n';
    graphml +=
      '  <key id="relationship_type" for="edge" attr.name="relationship_type" attr.type="string"/>\n';

    graphml += '  <graph id="G" edgedefault="undirected">\n';

    // Add nodes
    graph.nodes.forEach(node => {
      graphml += `    <node id="${node.id}">\n`;
      graphml += `      <data key="name">${this.escapeXml(node.properties.name)}</data>\n`;
      graphml += `      <data key="type">${node.type}</data>\n`;
      graphml += `      <data key="confidence">${node.properties.confidence}</data>\n`;
      graphml += '    </node>\n';
    });

    // Add edges
    graph.edges.forEach(edge => {
      graphml += `    <edge source="${edge.source}" target="${edge.target}">\n`;
      graphml += `      <data key="weight">${edge.weight}</data>\n`;
      graphml += `      <data key="relationship_type">${edge.relationship.type}</data>\n`;
      graphml += '    </edge>\n';
    });

    graphml += '  </graph>\n';
    graphml += '</graphml>';

    return graphml;
  }

  /**
   * Export graph as Cytoscape format
   */
  private exportAsCytoscape(graph: KnowledgeGraph): string {
    const cytoscapeData = {
      elements: {
        nodes: graph.nodes.map(node => ({
          data: {
            id: node.id,
            label: node.label,
            type: node.type,
            name: node.properties.name,
            confidence: node.properties.confidence,
          },
        })),
        edges: graph.edges.map(edge => ({
          data: {
            id: edge.id,
            source: edge.source,
            target: edge.target,
            weight: edge.weight,
            relationship_type: edge.relationship.type,
          },
        })),
      },
    };

    return JSON.stringify(cytoscapeData, null, 2);
  }

  /**
   * Export graph as Gephi format
   */
  private exportAsGephi(graph: KnowledgeGraph): string {
    const gephiData = {
      nodes: graph.nodes.map(node => ({
        id: node.id,
        label: node.label,
        attributes: {
          type: node.type,
          name: node.properties.name,
          confidence: node.properties.confidence,
          category: node.properties.category,
        },
      })),
      edges: graph.edges.map(edge => ({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        weight: edge.weight,
        attributes: {
          relationship_type: edge.relationship.type,
          strength: edge.relationship.strength,
          confidence: edge.relationship.confidence,
        },
      })),
    };

    return JSON.stringify(gephiData, null, 2);
  }

  /**
   * Export graph as JSON-LD format
   */
  private exportAsJsonLD(graph: KnowledgeGraph): string {
    const jsonLD = {
      '@context': {
        '@vocab': 'http://schema.org/',
        kg: 'http://example.com/knowledge-graph/',
        nodes: 'kg:nodes',
        edges: 'kg:edges',
        relationship: 'kg:relationship',
      },
      '@type': 'Graph',
      '@id': `kg:${graph.id}`,
      name: graph.name,
      description: graph.description,
      nodes: graph.nodes.map(node => ({
        '@type': 'Node',
        '@id': `kg:${node.id}`,
        name: node.properties.name,
        nodeType: node.type,
        confidence: node.properties.confidence,
        category: node.properties.category,
      })),
      edges: graph.edges.map(edge => ({
        '@type': 'Edge',
        '@id': `kg:${edge.id}`,
        source: `kg:${edge.source}`,
        target: `kg:${edge.target}`,
        relationship: edge.relationship.type,
        weight: edge.weight,
        confidence: edge.relationship.confidence,
      })),
      statistics: graph.statistics,
      metadata: graph.metadata,
    };

    return JSON.stringify(jsonLD, null, 2);
  }

  /**
   * Escape XML special characters
   */
  private escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;');
  }

  // ===== Enhanced Knowledge Graph Methods =====

  /**
   * Find shortest path between two nodes in the knowledge graph (enhanced version)
   */
  findShortestPathEnhanced(
    graph: KnowledgeGraph,
    startNodeId: string,
    endNodeId: string
  ): GraphPath | null {
    try {
      // Build adjacency list
      const adjacencyList = new Map<
        string,
        Array<{ nodeId: string; edgeId: string; weight: number }>
      >();

      graph.nodes.forEach(node => {
        adjacencyList.set(node.id, []);
      });

      graph.edges.forEach(edge => {
        const neighbors = adjacencyList.get(edge.source) || [];
        neighbors.push({ nodeId: edge.target, edgeId: edge.id, weight: edge.weight });
        adjacencyList.set(edge.source, neighbors);

        // For undirected graph, add reverse edge
        const reverseNeighbors = adjacencyList.get(edge.target) || [];
        reverseNeighbors.push({ nodeId: edge.source, edgeId: edge.id, weight: edge.weight });
        adjacencyList.set(edge.target, reverseNeighbors);
      });

      // Dijkstra's algorithm
      const distances = new Map<string, number>();
      const previous = new Map<string, { nodeId: string; edgeId: string }>();
      const unvisited = new Set<string>();

      graph.nodes.forEach(node => {
        distances.set(node.id, node.id === startNodeId ? 0 : Infinity);
        unvisited.add(node.id);
      });

      while (unvisited.size > 0) {
        // Find unvisited node with minimum distance
        let currentNode: string | null = null;
        let minDistance = Infinity;

        for (const nodeId of unvisited) {
          const distance = distances.get(nodeId) || Infinity;
          if (distance < minDistance) {
            minDistance = distance;
            currentNode = nodeId;
          }
        }

        if (!currentNode || minDistance === Infinity) break;

        unvisited.delete(currentNode);

        if (currentNode === endNodeId) break;

        const neighbors = adjacencyList.get(currentNode) || [];
        for (const neighbor of neighbors) {
          if (!unvisited.has(neighbor.nodeId)) continue;

          const currentDistance = distances.get(currentNode) || Infinity;
          const newDistance = currentDistance + neighbor.weight;
          const existingDistance = distances.get(neighbor.nodeId) || Infinity;

          if (newDistance < existingDistance) {
            distances.set(neighbor.nodeId, newDistance);
            previous.set(neighbor.nodeId, { nodeId: currentNode, edgeId: neighbor.edgeId });
          }
        }
      }

      // Reconstruct path
      if (!previous.has(endNodeId)) return null;

      const path: string[] = [];
      const edges: string[] = [];
      let current = endNodeId;

      while (current !== startNodeId) {
        path.unshift(current);
        const prev = previous.get(current);
        if (!prev) break;
        edges.unshift(prev.edgeId);
        current = prev.nodeId;
      }
      path.unshift(startNodeId);

      return {
        nodes: path,
        edges,
        length: path.length - 1,
        weight: distances.get(endNodeId) || 0,
        type: PathType.SHORTEST,
      };
    } catch (error) {
      console.warn('Shortest path calculation failed:', error);
      return null;
    }
  }

  /**
   * Perform depth-first traversal of the knowledge graph
   */
  depthFirstTraversal(graph: KnowledgeGraph, startNodeId: string): TraversalResult {
    try {
      const visited = new Set<string>();
      const visitedNodes: string[] = [];
      const visitedEdges: string[] = [];
      const components: string[][] = [];

      // Build adjacency list
      const adjacencyList = new Map<string, Array<{ nodeId: string; edgeId: string }>>();

      graph.nodes.forEach(node => {
        adjacencyList.set(node.id, []);
      });

      graph.edges.forEach(edge => {
        const neighbors = adjacencyList.get(edge.source) || [];
        neighbors.push({ nodeId: edge.target, edgeId: edge.id });
        adjacencyList.set(edge.source, neighbors);

        const reverseNeighbors = adjacencyList.get(edge.target) || [];
        reverseNeighbors.push({ nodeId: edge.source, edgeId: edge.id });
        adjacencyList.set(edge.target, reverseNeighbors);
      });

      const dfs = (nodeId: string, component: string[]) => {
        if (visited.has(nodeId)) return;

        visited.add(nodeId);
        visitedNodes.push(nodeId);
        component.push(nodeId);

        const neighbors = adjacencyList.get(nodeId) || [];
        for (const neighbor of neighbors) {
          if (!visited.has(neighbor.nodeId)) {
            visitedEdges.push(neighbor.edgeId);
            dfs(neighbor.nodeId, component);
          }
        }
      };

      // Start DFS from the specified node
      if (graph.nodes.some(node => node.id === startNodeId)) {
        const component: string[] = [];
        dfs(startNodeId, component);
        if (component.length > 0) {
          components.push(component);
        }
      }

      // Find remaining components
      for (const node of graph.nodes) {
        if (!visited.has(node.id)) {
          const component: string[] = [];
          dfs(node.id, component);
          if (component.length > 0) {
            components.push(component);
          }
        }
      }

      return {
        visitedNodes,
        visitedEdges,
        order: TraversalOrder.DEPTH_FIRST,
        depth: Math.max(...components.map(c => c.length)),
        components,
      };
    } catch (error) {
      console.warn('Depth-first traversal failed:', error);
      return {
        visitedNodes: [],
        visitedEdges: [],
        order: TraversalOrder.DEPTH_FIRST,
        depth: 0,
        components: [],
      };
    }
  }

  /**
   * Calculate centrality measures for all nodes in the graph
   */
  calculateCentralityMeasures(graph: KnowledgeGraph): CentralityMeasures {
    try {
      const nodeCount = graph.nodes.length;
      const betweenness: Record<string, number> = {};
      const closeness: Record<string, number> = {};
      const degree: Record<string, number> = {};
      const eigenvector: Record<string, number> = {};

      // Initialize measures
      graph.nodes.forEach(node => {
        betweenness[node.id] = 0;
        closeness[node.id] = 0;
        degree[node.id] = 0;
        eigenvector[node.id] = 1 / Math.sqrt(nodeCount);
      });

      // Calculate degree centrality
      graph.edges.forEach(edge => {
        degree[edge.source] = (degree[edge.source] || 0) + 1;
        degree[edge.target] = (degree[edge.target] || 0) + 1;
      });

      // Normalize degree centrality
      const maxDegree = Math.max(...Object.values(degree));
      if (maxDegree > 0) {
        Object.keys(degree).forEach(nodeId => {
          degree[nodeId] = (degree[nodeId] || 0) / maxDegree;
        });
      }

      // Calculate closeness centrality (simplified)
      graph.nodes.forEach(node => {
        let totalDistance = 0;
        let reachableNodes = 0;

        graph.nodes.forEach(otherNode => {
          if (node.id !== otherNode.id) {
            const path = this.findShortestPathEnhanced(graph, node.id, otherNode.id);
            if (path) {
              totalDistance += path.length;
              reachableNodes++;
            }
          }
        });

        if (reachableNodes > 0) {
          closeness[node.id] = reachableNodes / totalDistance;
        }
      });

      // Calculate betweenness centrality (simplified)
      graph.nodes.forEach(sourceNode => {
        graph.nodes.forEach(targetNode => {
          if (sourceNode.id !== targetNode.id) {
            const path = this.findShortestPathEnhanced(graph, sourceNode.id, targetNode.id);
            if (path && path.nodes.length > 2) {
              // Count intermediate nodes
              for (let i = 1; i < path.nodes.length - 1; i++) {
                const intermediateNode = path.nodes[i];
                if (intermediateNode) {
                  betweenness[intermediateNode] = (betweenness[intermediateNode] || 0) + 1;
                }
              }
            }
          }
        });
      });

      // Normalize betweenness centrality
      const maxBetweenness = Math.max(...Object.values(betweenness));
      if (maxBetweenness > 0) {
        Object.keys(betweenness).forEach(nodeId => {
          betweenness[nodeId] = (betweenness[nodeId] || 0) / maxBetweenness;
        });
      }

      // Eigenvector centrality (simplified power iteration)
      for (let iteration = 0; iteration < 10; iteration++) {
        const newEigenvector: Record<string, number> = {};

        graph.nodes.forEach(node => {
          newEigenvector[node.id] = 0;
        });

        graph.edges.forEach(edge => {
          const targetValue = newEigenvector[edge.target];
          const sourceValue = eigenvector[edge.source];
          if (targetValue !== undefined && sourceValue !== undefined) {
            newEigenvector[edge.target] = targetValue + sourceValue;
          }

          const sourceNewValue = newEigenvector[edge.source];
          const targetOldValue = eigenvector[edge.target];
          if (sourceNewValue !== undefined && targetOldValue !== undefined) {
            newEigenvector[edge.source] = sourceNewValue + targetOldValue;
          }
        });

        // Normalize
        const norm = Math.sqrt(
          Object.values(newEigenvector).reduce((sum, val) => sum + val * val, 0)
        );
        if (norm > 0) {
          Object.keys(newEigenvector).forEach(nodeId => {
            const newValue = newEigenvector[nodeId];
            if (newValue !== undefined) {
              eigenvector[nodeId] = newValue / norm;
            }
          });
        }
      }

      return {
        betweenness,
        closeness,
        degree,
        eigenvector,
      };
    } catch (error) {
      console.warn('Centrality calculation failed:', error);
      return {
        betweenness: {},
        closeness: {},
        degree: {},
        eigenvector: {},
      };
    }
  }

  // ===== Document Similarity and Analysis Methods =====

  /**
   * Calculate similarity between two documents using multiple methods
   */
  async calculateDocumentSimilarity(
    text1: string,
    text2: string,
    documentId1: string,
    documentId2: string,
    method: SimilarityMethod = SimilarityMethod.HYBRID
  ): Promise<DocumentSimilarity> {
    try {
      let similarity = 0;
      const features: SimilarityFeatures = {
        textual: 0,
        semantic: 0,
        structural: 0,
        topical: 0,
      };

      switch (method) {
        case SimilarityMethod.COSINE:
          similarity = this.calculateCosineSimilarity(text1, text2);
          features.textual = similarity;
          break;

        case SimilarityMethod.JACCARD:
          similarity = this.calculateJaccardSimilarity(text1, text2);
          features.textual = similarity;
          break;

        case SimilarityMethod.SEMANTIC:
          similarity = await this.calculateSemanticSimilarity(text1, text2);
          features.semantic = similarity;
          break;

        case SimilarityMethod.HYBRID:
          features.textual = this.calculateCosineSimilarity(text1, text2);
          features.semantic = await this.calculateSemanticSimilarity(text1, text2);
          features.structural = this.calculateStructuralSimilarity(text1, text2);
          features.topical = this.calculateTopicalSimilarity(text1, text2);

          // Weighted combination
          similarity =
            features.textual * 0.3 +
            features.semantic * 0.4 +
            features.structural * 0.2 +
            features.topical * 0.1;
          break;

        default:
          similarity = this.calculateCosineSimilarity(text1, text2);
          features.textual = similarity;
      }

      return {
        documentId1,
        documentId2,
        similarity: Math.max(0, Math.min(1, similarity)), // Clamp to [0, 1]
        method,
        features,
      };
    } catch (error) {
      console.warn('Document similarity calculation failed:', error);
      return {
        documentId1,
        documentId2,
        similarity: 0,
        method,
        features: { textual: 0, semantic: 0, structural: 0, topical: 0 },
      };
    }
  }

  /**
   * Calculate cosine similarity between two texts using TF-IDF vectors
   */
  private calculateCosineSimilarity(text1: string, text2: string): number {
    try {
      // Tokenize and clean texts
      const tokens1 = this.tokenizer.tokenize(text1.toLowerCase()) || [];
      const tokens2 = this.tokenizer.tokenize(text2.toLowerCase()) || [];

      const cleanTokens1 = tokens1.filter(
        token => token.length > 2 && !natural.stopwords.includes(token) && /^[a-zA-Z]+$/.test(token)
      );
      const cleanTokens2 = tokens2.filter(
        token => token.length > 2 && !natural.stopwords.includes(token) && /^[a-zA-Z]+$/.test(token)
      );

      // Create vocabulary
      const vocabulary = new Set([...cleanTokens1, ...cleanTokens2]);
      const vocabArray = Array.from(vocabulary);

      // Calculate TF-IDF vectors
      const vector1 = this.calculateTfIdfVector(cleanTokens1, vocabArray);
      const vector2 = this.calculateTfIdfVector(cleanTokens2, vocabArray);

      // Calculate cosine similarity
      const dotProduct = vector1.reduce((sum, val, i) => sum + val * (vector2[i] || 0), 0);
      const magnitude1 = Math.sqrt(vector1.reduce((sum, val) => sum + val * val, 0));
      const magnitude2 = Math.sqrt(vector2.reduce((sum, val) => sum + val * val, 0));

      if (magnitude1 === 0 || magnitude2 === 0) return 0;

      return dotProduct / (magnitude1 * magnitude2);
    } catch (error) {
      console.warn('Cosine similarity calculation failed:', error);
      return 0;
    }
  }

  /**
   * Calculate TF-IDF vector for a document
   */
  private calculateTfIdfVector(tokens: string[], vocabulary: string[]): number[] {
    const termFreq: Record<string, number> = {};

    // Calculate term frequency
    tokens.forEach(token => {
      const stemmed = this.stemmer.stem(token);
      termFreq[stemmed] = (termFreq[stemmed] || 0) + 1;
    });

    // Convert to TF-IDF vector
    return vocabulary.map(term => {
      const stemmed = this.stemmer.stem(term);
      const tf = (termFreq[stemmed] || 0) / tokens.length;
      // Simplified IDF (in practice, you'd calculate this from a corpus)
      const idf = Math.log(1 + 1 / (1 + (termFreq[stemmed] || 0)));
      return tf * idf;
    });
  }

  /**
   * Calculate Jaccard similarity between two texts
   */
  private calculateJaccardSimilarity(text1: string, text2: string): number {
    try {
      const tokens1 = new Set(this.tokenizer.tokenize(text1.toLowerCase()) || []);
      const tokens2 = new Set(this.tokenizer.tokenize(text2.toLowerCase()) || []);

      const intersection = new Set([...tokens1].filter(token => tokens2.has(token)));
      const union = new Set([...tokens1, ...tokens2]);

      return union.size === 0 ? 0 : intersection.size / union.size;
    } catch (error) {
      console.warn('Jaccard similarity calculation failed:', error);
      return 0;
    }
  }

  /**
   * Calculate semantic similarity using embeddings
   */
  private async calculateSemanticSimilarity(text1: string, text2: string): Promise<number> {
    try {
      // Generate embeddings for both texts
      const embeddingResponse1 = await aiModelClient.generateEmbeddings({
        text: text1,
        model: 'text-embedding-ada-002',
      });
      const embeddingResponse2 = await aiModelClient.generateEmbeddings({
        text: text2,
        model: 'text-embedding-ada-002',
      });

      const embedding1 = embeddingResponse1.embedding;
      const embedding2 = embeddingResponse2.embedding;

      if (!embedding1 || !embedding2 || embedding1.length !== embedding2.length) {
        return 0;
      }

      // Calculate cosine similarity between embeddings
      const dotProduct = embedding1.reduce((sum, val, i) => sum + val * (embedding2[i] || 0), 0);
      const magnitude1 = Math.sqrt(embedding1.reduce((sum, val) => sum + val * val, 0));
      const magnitude2 = Math.sqrt(embedding2.reduce((sum, val) => sum + val * val, 0));

      if (magnitude1 === 0 || magnitude2 === 0) return 0;

      return dotProduct / (magnitude1 * magnitude2);
    } catch (error) {
      console.warn('Semantic similarity calculation failed:', error);
      return 0;
    }
  }

  /**
   * Calculate structural similarity based on document structure
   */
  private calculateStructuralSimilarity(text1: string, text2: string): number {
    try {
      // Analyze document structure
      const structure1 = this.analyzeDocumentStructure(text1);
      const structure2 = this.analyzeDocumentStructure(text2);

      // Compare structural features
      const sentenceLengthSim =
        1 -
        Math.abs(structure1.avgSentenceLength - structure2.avgSentenceLength) /
          Math.max(structure1.avgSentenceLength, structure2.avgSentenceLength, 1);

      const paragraphSim =
        1 -
        Math.abs(structure1.paragraphCount - structure2.paragraphCount) /
          Math.max(structure1.paragraphCount, structure2.paragraphCount, 1);

      const punctuationSim =
        1 - Math.abs(structure1.punctuationDensity - structure2.punctuationDensity);

      return (sentenceLengthSim + paragraphSim + punctuationSim) / 3;
    } catch (error) {
      console.warn('Structural similarity calculation failed:', error);
      return 0;
    }
  }

  /**
   * Analyze document structure
   */
  private analyzeDocumentStructure(text: string): {
    avgSentenceLength: number;
    paragraphCount: number;
    punctuationDensity: number;
  } {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    const punctuationCount = (text.match(/[.!?,:;]/g) || []).length;

    return {
      avgSentenceLength:
        sentences.length > 0
          ? sentences.reduce((sum, s) => sum + s.trim().split(/\s+/).length, 0) / sentences.length
          : 0,
      paragraphCount: paragraphs.length,
      punctuationDensity: text.length > 0 ? punctuationCount / text.length : 0,
    };
  }

  /**
   * Calculate topical similarity based on extracted topics
   */
  private calculateTopicalSimilarity(text1: string, text2: string): number {
    try {
      const topics1 = this.extractTopics(text1);
      const topics2 = this.extractTopics(text2);

      if (topics1.length === 0 && topics2.length === 0) return 1;
      if (topics1.length === 0 || topics2.length === 0) return 0;

      // Create topic keyword sets
      const keywords1 = new Set(topics1.flatMap(topic => topic.keywords));
      const keywords2 = new Set(topics2.flatMap(topic => topic.keywords));

      // Calculate Jaccard similarity of topic keywords
      const intersection = new Set([...keywords1].filter(keyword => keywords2.has(keyword)));
      const union = new Set([...keywords1, ...keywords2]);

      return union.size === 0 ? 0 : intersection.size / union.size;
    } catch (error) {
      console.warn('Topical similarity calculation failed:', error);
      return 0;
    }
  }

  /**
   * Cluster documents based on similarity
   */
  async clusterDocuments(
    documents: Array<{ id: string; text: string }>,
    options: ClusteringOptions = { method: ClusteringMethod.KMEANS, numberOfClusters: 3 }
  ): Promise<ClusterResult[]> {
    try {
      if (documents.length === 0) return [];

      switch (options.method) {
        case ClusteringMethod.KMEANS:
          return await this.performKMeansClustering(documents, options);

        case ClusteringMethod.HIERARCHICAL:
          return await this.performHierarchicalClustering(documents, options);

        case ClusteringMethod.TOPIC_BASED:
          return this.performTopicBasedClustering(documents, options);

        default:
          return await this.performKMeansClustering(documents, options);
      }
    } catch (error) {
      console.warn('Document clustering failed:', error);
      return [];
    }
  }

  /**
   * Perform K-means clustering on documents
   */
  private async performKMeansClustering(
    documents: Array<{ id: string; text: string }>,
    options: ClusteringOptions
  ): Promise<ClusterResult[]> {
    try {
      const k = options.numberOfClusters || 3;
      const maxIterations = options.maxIterations || 100;

      // Generate embeddings for all documents
      const embeddings: Array<{ id: string; embedding: number[] }> = [];

      for (const doc of documents) {
        const embeddingResponse = await aiModelClient.generateEmbeddings({
          text: doc.text,
          model: 'text-embedding-ada-002',
        });
        embeddings.push({ id: doc.id, embedding: embeddingResponse.embedding });
      }

      if (embeddings.length === 0) return [];

      const dimensions = embeddings[0]?.embedding.length || 0;
      if (dimensions === 0) return [];

      // Initialize centroids randomly
      let centroids: number[][] = [];
      for (let i = 0; i < k; i++) {
        const centroid: number[] = [];
        for (let j = 0; j < dimensions; j++) {
          centroid.push(Math.random() * 2 - 1); // Random values between -1 and 1
        }
        centroids.push(centroid);
      }

      let assignments: number[] = new Array(embeddings.length).fill(0);
      let converged = false;
      let iteration = 0;

      while (!converged && iteration < maxIterations) {
        const newAssignments: number[] = [];

        // Assign each document to nearest centroid
        for (let i = 0; i < embeddings.length; i++) {
          let minDistance = Infinity;
          let bestCluster = 0;

          for (let j = 0; j < k; j++) {
            const embedding = embeddings[i]?.embedding;
            const centroid = centroids[j];
            if (embedding && centroid) {
              const distance = this.calculateEuclideanDistance(embedding, centroid);
              if (distance < minDistance) {
                minDistance = distance;
                bestCluster = j;
              }
            }
          }

          newAssignments.push(bestCluster);
        }

        // Check for convergence
        converged = newAssignments.every((assignment, i) => assignment === assignments[i]);
        assignments = newAssignments;

        if (!converged) {
          // Update centroids
          const newCentroids: number[][] = [];

          for (let j = 0; j < k; j++) {
            const clusterPoints = embeddings.filter((_, i) => assignments[i] === j);

            if (clusterPoints.length === 0) {
              // Keep the old centroid if no points assigned
              const oldCentroid = centroids[j];
              if (oldCentroid) {
                newCentroids.push([...oldCentroid]);
              }
            } else {
              const newCentroid: number[] = [];
              for (let d = 0; d < dimensions; d++) {
                const sum = clusterPoints.reduce(
                  (acc, point) => acc + (point.embedding[d] || 0),
                  0
                );
                newCentroid.push(sum / clusterPoints.length);
              }
              newCentroids.push(newCentroid);
            }
          }

          centroids = newCentroids;
        }

        iteration++;
      }

      // Create cluster results
      const clusters: ClusterResult[] = [];

      for (let j = 0; j < k; j++) {
        const clusterDocuments = documents.filter((_, i) => assignments[i] === j);

        if (clusterDocuments.length > 0) {
          // Calculate cluster coherence
          const clusterEmbeddings = embeddings.filter((_, i) => assignments[i] === j);
          const coherence = this.calculateClusterCoherence(clusterEmbeddings.map(e => e.embedding));

          // Extract topics for this cluster
          const clusterTexts = clusterDocuments.map(doc => doc.text).join(' ');
          const topics = this.extractTopics(clusterTexts);

          // Find representative document (closest to centroid)
          let representative = clusterDocuments[0]?.id || '';
          let minDistanceToCenter = Infinity;

          clusterEmbeddings.forEach((embedding, i) => {
            const centroid = centroids[j];
            const embeddingVector = embedding?.embedding;
            const clusterDoc = clusterDocuments[i];

            if (centroid && embeddingVector && clusterDoc) {
              const distance = this.calculateEuclideanDistance(embeddingVector, centroid);
              if (distance < minDistanceToCenter) {
                minDistanceToCenter = distance;
                representative = clusterDoc.id;
              }
            }
          });

          const centroid = centroids[j];
          if (centroid) {
            clusters.push({
              clusterId: `cluster_${j}`,
              documents: clusterDocuments.map(doc => doc.id),
              centroid,
              coherence,
              topics: topics.slice(0, 3), // Top 3 topics
              representative,
            });
          }
        }
      }

      return clusters;
    } catch (error) {
      console.warn('K-means clustering failed:', error);
      return [];
    }
  }

  /**
   * Calculate Euclidean distance between two vectors
   */
  private calculateEuclideanDistance(vector1: number[], vector2: number[]): number {
    if (vector1.length !== vector2.length) return Infinity;

    const sumSquaredDiffs = vector1.reduce((sum, val, i) => {
      const diff = val - (vector2[i] || 0);
      return sum + diff * diff;
    }, 0);

    return Math.sqrt(sumSquaredDiffs);
  }

  /**
   * Calculate cluster coherence (average pairwise similarity within cluster)
   */
  private calculateClusterCoherence(embeddings: number[][]): number {
    if (embeddings.length <= 1) return 1;

    let totalSimilarity = 0;
    let pairCount = 0;

    for (let i = 0; i < embeddings.length; i++) {
      for (let j = i + 1; j < embeddings.length; j++) {
        // Calculate cosine similarity
        const embedding1 = embeddings[i];
        const embedding2 = embeddings[j];

        if (!embedding1 || !embedding2) continue;

        const dotProduct = embedding1.reduce((sum, val, k) => sum + val * (embedding2[k] || 0), 0);
        const magnitude1 = Math.sqrt(embedding1.reduce((sum, val) => sum + val * val, 0));
        const magnitude2 = Math.sqrt(embedding2.reduce((sum, val) => sum + val * val, 0));

        if (magnitude1 > 0 && magnitude2 > 0) {
          const similarity = dotProduct / (magnitude1 * magnitude2);
          totalSimilarity += similarity;
          pairCount++;
        }
      }
    }

    return pairCount > 0 ? totalSimilarity / pairCount : 0;
  }

  /**
   * Perform hierarchical clustering (simplified agglomerative clustering)
   */
  private async performHierarchicalClustering(
    documents: Array<{ id: string; text: string }>,
    options: ClusteringOptions
  ): Promise<ClusterResult[]> {
    try {
      const threshold = options.threshold || 0.5;

      // Calculate similarity matrix
      const similarities: number[][] = [];

      for (let i = 0; i < documents.length; i++) {
        similarities[i] = [];
        const currentRow = similarities[i] as number[];
        for (let j = 0; j < documents.length; j++) {
          if (i === j) {
            currentRow[j] = 1;
          } else {
            const doc1 = documents[i];
            const doc2 = documents[j];
            if (doc1 && doc2) {
              const similarity = await this.calculateDocumentSimilarity(
                doc1.text,
                doc2.text,
                doc1.id,
                doc2.id,
                SimilarityMethod.HYBRID
              );
              currentRow[j] = similarity.similarity;
            } else {
              currentRow[j] = 0;
            }
          }
        }
      }

      // Start with each document as its own cluster
      const clusters: number[][] = documents.map((_, i) => [i]);

      // Merge clusters until threshold is reached
      while (clusters.length > 1) {
        let maxSimilarity = -1;
        let mergeIndices: [number, number] = [0, 1];

        // Find most similar clusters
        for (let i = 0; i < clusters.length; i++) {
          for (let j = i + 1; j < clusters.length; j++) {
            const cluster1 = clusters[i];
            const cluster2 = clusters[j];
            if (cluster1 && cluster2) {
              const similarity = this.calculateClusterSimilarity(cluster1, cluster2, similarities);
              if (similarity > maxSimilarity) {
                maxSimilarity = similarity;
                mergeIndices = [i, j];
              }
            }
          }
        }

        // Stop if similarity is below threshold
        if (maxSimilarity < threshold) break;

        // Merge clusters
        const [i, j] = mergeIndices;
        const cluster1 = clusters[i];
        const cluster2 = clusters[j];
        if (cluster1 && cluster2) {
          clusters[i] = [...cluster1, ...cluster2];
          clusters.splice(j, 1);
        }
      }

      // Convert to ClusterResult format
      const results: ClusterResult[] = [];
      for (let i = 0; i < clusters.length; i++) {
        const cluster = clusters[i];
        if (cluster) {
          const clusterDocs = cluster.map(idx => documents[idx]).filter(doc => doc !== undefined);
          if (clusterDocs.length > 0) {
            const clusterTexts = clusterDocs.map(doc => doc.text).join(' ');
            const topics = this.extractTopics(clusterTexts);

            results.push({
              clusterId: `hierarchical_cluster_${i}`,
              documents: clusterDocs.map(doc => doc.id),
              centroid: [], // Not applicable for hierarchical clustering
              coherence: this.calculateTextCoherence(clusterDocs.map(doc => doc.text)),
              topics: topics.slice(0, 3),
              representative: clusterDocs[0]?.id || '',
            });
          }
        }
      }

      return results;
    } catch (error) {
      console.warn('Hierarchical clustering failed:', error);
      return [];
    }
  }

  /**
   * Calculate similarity between two clusters
   */
  private calculateClusterSimilarity(
    cluster1: number[],
    cluster2: number[],
    similarities: number[][]
  ): number {
    let totalSimilarity = 0;
    let pairCount = 0;

    for (const i of cluster1) {
      for (const j of cluster2) {
        const row = similarities[i];
        if (row && row[j] !== undefined) {
          totalSimilarity += row[j];
          pairCount++;
        }
      }
    }

    return pairCount > 0 ? totalSimilarity / pairCount : 0;
  }

  /**
   * Perform topic-based clustering
   */
  private performTopicBasedClustering(
    documents: Array<{ id: string; text: string }>,
    options: ClusteringOptions
  ): ClusterResult[] {
    try {
      // Extract topics for each document
      const documentTopics = documents.map(doc => ({
        id: doc.id,
        text: doc.text,
        topics: this.extractTopics(doc.text),
      }));

      // Group documents by dominant topic
      const topicGroups = new Map<string, Array<{ id: string; text: string }>>();

      documentTopics.forEach(docTopic => {
        if (docTopic.topics.length > 0) {
          const dominantTopic = docTopic.topics[0]?.name;
          if (dominantTopic) {
            if (!topicGroups.has(dominantTopic)) {
              topicGroups.set(dominantTopic, []);
            }
            const group = topicGroups.get(dominantTopic);
            if (group) {
              group.push({ id: docTopic.id, text: docTopic.text });
            }
          }
        }
      });

      // Convert to ClusterResult format
      const results: ClusterResult[] = [];
      let clusterIndex = 0;

      for (const [, docs] of topicGroups) {
        if (docs.length >= (options.minClusterSize || 1)) {
          const clusterTexts = docs.map(doc => doc.text).join(' ');
          const topics = this.extractTopics(clusterTexts);

          results.push({
            clusterId: `topic_cluster_${clusterIndex}`,
            documents: docs.map(doc => doc.id),
            centroid: [], // Not applicable for topic-based clustering
            coherence: this.calculateTextCoherence(docs.map(doc => doc.text)),
            topics: topics.slice(0, 3),
            representative: docs[0]?.id || '',
          });

          clusterIndex++;
        }
      }

      return results;
    } catch (error) {
      console.warn('Topic-based clustering failed:', error);
      return [];
    }
  }

  /**
   * Calculate text coherence for a group of texts
   */
  private calculateTextCoherence(texts: string[]): number {
    if (texts.length <= 1) return 1;

    try {
      // Extract keywords from all texts
      const allKeywords = texts.flatMap(text => this.extractKeywords(text));
      const keywordCounts = new Map<string, number>();

      allKeywords.forEach(keyword => {
        keywordCounts.set(keyword.text, (keywordCounts.get(keyword.text) || 0) + 1);
      });

      // Calculate coherence based on shared keywords
      const sharedKeywords = Array.from(keywordCounts.entries()).filter(([, count]) => count > 1);
      const totalKeywords = keywordCounts.size;

      return totalKeywords > 0 ? sharedKeywords.length / totalKeywords : 0;
    } catch (error) {
      console.warn('Text coherence calculation failed:', error);
      return 0;
    }
  }

  // ===== Readability Analysis Methods =====

  /**
   * Calculate comprehensive readability scores for text
   */
  calculateReadability(text: string): ReadabilityScore {
    try {
      const stats = this.calculateTextStatistics(text);

      // Calculate various readability metrics
      const fleschKincaid = this.calculateFleschKincaidGradeLevel(stats);
      const fleschReadingEase = this.calculateFleschReadingEase(stats);
      const gunningFog = this.calculateGunningFogIndex(stats);
      const smog = this.calculateSMOGIndex(stats);
      const ari = this.calculateAutomatedReadabilityIndex(stats);
      const colemanLiau = this.calculateColemanLiauIndex(stats);

      // Calculate overall score (average of normalized scores)
      const overall =
        (this.normalizeReadabilityScore(fleschReadingEase, 'flesch_ease') +
          this.normalizeReadabilityScore(fleschKincaid, 'grade_level') +
          this.normalizeReadabilityScore(gunningFog, 'grade_level') +
          this.normalizeReadabilityScore(smog, 'grade_level') +
          this.normalizeReadabilityScore(ari, 'grade_level') +
          this.normalizeReadabilityScore(colemanLiau, 'grade_level')) /
        6;

      // Determine complexity level
      const complexity = this.determineComplexityLevel(overall);
      const gradeLevel = this.determineGradeLevel(fleschKincaid);

      return {
        overall: Math.round(overall * 100) / 100,
        fleschKincaid: Math.round(fleschKincaid * 100) / 100,
        fleschReadingEase: Math.round(fleschReadingEase * 100) / 100,
        gunningFog: Math.round(gunningFog * 100) / 100,
        smog: Math.round(smog * 100) / 100,
        automatedReadabilityIndex: Math.round(ari * 100) / 100,
        colemanLiau: Math.round(colemanLiau * 100) / 100,
        gradeLevel,
        complexity,
      };
    } catch (error) {
      console.warn('Readability calculation failed:', error);
      return {
        overall: 0,
        fleschKincaid: 0,
        fleschReadingEase: 0,
        gunningFog: 0,
        smog: 0,
        automatedReadabilityIndex: 0,
        colemanLiau: 0,
        gradeLevel: 'Unknown',
        complexity: ComplexityLevel.STANDARD,
      };
    }
  }

  /**
   * Calculate basic text statistics needed for readability formulas
   */
  private calculateTextStatistics(text: string): {
    sentences: number;
    words: number;
    syllables: number;
    complexWords: number;
    characters: number;
    charactersNoSpaces: number;
  } {
    // Count sentences
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0).length;

    // Count words
    const words = text.split(/\s+/).filter(w => w.trim().length > 0).length;

    // Count characters
    const characters = text.length;
    const charactersNoSpaces = text.replace(/\s/g, '').length;

    // Count syllables and complex words
    let syllables = 0;
    let complexWords = 0;

    const wordList = text
      .toLowerCase()
      .split(/\s+/)
      .filter(w => /^[a-zA-Z]+$/.test(w));

    wordList.forEach(word => {
      const syllableCount = this.countSyllables(word);
      syllables += syllableCount;

      // Complex words have 3+ syllables
      if (syllableCount >= 3) {
        complexWords++;
      }
    });

    return {
      sentences: Math.max(sentences, 1), // Avoid division by zero
      words: Math.max(words, 1),
      syllables: Math.max(syllables, 1),
      complexWords,
      characters,
      charactersNoSpaces,
    };
  }

  /**
   * Count syllables in a word (simplified algorithm)
   */
  private countSyllables(word: string): number {
    if (word.length <= 3) return 1;

    // Remove common endings that don't add syllables
    word = word.replace(/(?:es|ed|e)$/, '');

    // Count vowel groups
    const vowelGroups = word.match(/[aeiouy]+/g);
    const syllableCount = vowelGroups ? vowelGroups.length : 1;

    // Minimum of 1 syllable
    return Math.max(syllableCount, 1);
  }

  /**
   * Calculate Flesch-Kincaid Grade Level
   */
  private calculateFleschKincaidGradeLevel(stats: {
    sentences: number;
    words: number;
    syllables: number;
  }): number {
    return 0.39 * (stats.words / stats.sentences) + 11.8 * (stats.syllables / stats.words) - 15.59;
  }

  /**
   * Calculate Flesch Reading Ease
   */
  private calculateFleschReadingEase(stats: {
    sentences: number;
    words: number;
    syllables: number;
  }): number {
    return (
      206.835 - 1.015 * (stats.words / stats.sentences) - 84.6 * (stats.syllables / stats.words)
    );
  }

  /**
   * Calculate Gunning Fog Index
   */
  private calculateGunningFogIndex(stats: {
    sentences: number;
    words: number;
    complexWords: number;
  }): number {
    return 0.4 * (stats.words / stats.sentences + 100 * (stats.complexWords / stats.words));
  }

  /**
   * Calculate SMOG Index
   */
  private calculateSMOGIndex(stats: { sentences: number; complexWords: number }): number {
    return 1.043 * Math.sqrt(stats.complexWords * (30 / stats.sentences)) + 3.1291;
  }

  /**
   * Calculate Automated Readability Index
   */
  private calculateAutomatedReadabilityIndex(stats: {
    sentences: number;
    words: number;
    charactersNoSpaces: number;
  }): number {
    return (
      4.71 * (stats.charactersNoSpaces / stats.words) +
      0.5 * (stats.words / stats.sentences) -
      21.43
    );
  }

  /**
   * Calculate Coleman-Liau Index
   */
  private calculateColemanLiauIndex(stats: {
    sentences: number;
    words: number;
    charactersNoSpaces: number;
  }): number {
    const L = (stats.charactersNoSpaces / stats.words) * 100;
    const S = (stats.sentences / stats.words) * 100;
    return 0.0588 * L - 0.296 * S - 15.8;
  }

  /**
   * Normalize readability scores to 0-1 scale
   */
  private normalizeReadabilityScore(score: number, type: 'flesch_ease' | 'grade_level'): number {
    if (type === 'flesch_ease') {
      // Flesch Reading Ease: 0-100, higher is easier
      return Math.max(0, Math.min(1, score / 100));
    } else {
      // Grade level scores: typically 0-20, lower is easier
      return Math.max(0, Math.min(1, 1 - score / 20));
    }
  }

  /**
   * Determine complexity level from overall score
   */
  private determineComplexityLevel(score: number): ComplexityLevel {
    if (score >= 0.9) return ComplexityLevel.VERY_EASY;
    if (score >= 0.8) return ComplexityLevel.EASY;
    if (score >= 0.7) return ComplexityLevel.FAIRLY_EASY;
    if (score >= 0.6) return ComplexityLevel.STANDARD;
    if (score >= 0.5) return ComplexityLevel.FAIRLY_DIFFICULT;
    if (score >= 0.3) return ComplexityLevel.DIFFICULT;
    return ComplexityLevel.VERY_DIFFICULT;
  }

  /**
   * Determine grade level description from Flesch-Kincaid score
   */
  private determineGradeLevel(fleschKincaid: number): string {
    if (fleschKincaid < 6) return 'Elementary School';
    if (fleschKincaid < 9) return 'Middle School';
    if (fleschKincaid < 13) return 'High School';
    if (fleschKincaid < 16) return 'College Level';
    if (fleschKincaid < 18) return 'Graduate Level';
    return 'Post-Graduate Level';
  }

  // ===== Text Quality Assessment Methods =====

  /**
   * Assess overall text quality with improvement suggestions
   */
  assessTextQuality(text: string): TextQualityAssessment {
    try {
      // Calculate readability
      const readability = this.calculateReadability(text);

      // Assess different quality dimensions
      const clarity = this.assessClarity(text);
      const coherence = this.assessCoherence(text);
      const conciseness = this.assessConciseness(text);
      const grammar = this.assessGrammar(text);
      const vocabulary = this.assessVocabulary(text);
      const structure = this.assessStructure(text);

      // Calculate overall quality score
      const overall =
        readability.overall * 0.2 +
        clarity * 0.15 +
        coherence * 0.15 +
        conciseness * 0.1 +
        grammar.score * 0.2 +
        vocabulary.diversity * 0.1 +
        structure.organization * 0.1;

      // Generate improvement suggestions
      const suggestions = this.generateQualityImprovements(
        text,
        readability,
        clarity,
        coherence,
        conciseness,
        grammar,
        vocabulary,
        structure
      );

      return {
        overall: Math.round(overall * 100) / 100,
        readability,
        clarity: Math.round(clarity * 100) / 100,
        coherence: Math.round(coherence * 100) / 100,
        conciseness: Math.round(conciseness * 100) / 100,
        grammar,
        vocabulary,
        structure,
        suggestions,
      };
    } catch (error) {
      console.warn('Text quality assessment failed:', error);
      return {
        overall: 0,
        readability: this.calculateReadability(''),
        clarity: 0,
        coherence: 0,
        conciseness: 0,
        grammar: { score: 0, errors: [], suggestions: [] },
        vocabulary: {
          diversity: 0,
          complexity: 0,
          appropriateness: 0,
          repetition: 0,
          suggestions: [],
        },
        structure: {
          organization: 0,
          flow: 0,
          transitions: 0,
          paragraphStructure: 0,
          suggestions: [],
        },
        suggestions: [],
      };
    }
  }

  /**
   * Assess text clarity
   */
  private assessClarity(text: string): number {
    try {
      const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
      let clarityScore = 1.0;

      // Penalize very long sentences
      const avgSentenceLength =
        sentences.reduce((sum, s) => sum + s.split(/\s+/).length, 0) / sentences.length;
      if (avgSentenceLength > 25) {
        clarityScore -= 0.2;
      } else if (avgSentenceLength > 20) {
        clarityScore -= 0.1;
      }

      // Penalize excessive use of passive voice (simplified detection)
      const passiveCount = sentences.filter(s =>
        /\b(was|were|been|being)\s+\w+ed\b/.test(s.toLowerCase())
      ).length;
      const passiveRatio = passiveCount / sentences.length;
      if (passiveRatio > 0.3) {
        clarityScore -= 0.2;
      } else if (passiveRatio > 0.2) {
        clarityScore -= 0.1;
      }

      // Penalize excessive jargon (words longer than 12 characters)
      const words = text.split(/\s+/).filter(w => /^[a-zA-Z]+$/.test(w));
      const longWords = words.filter(w => w.length > 12).length;
      const jargonRatio = longWords / words.length;
      if (jargonRatio > 0.1) {
        clarityScore -= 0.15;
      } else if (jargonRatio > 0.05) {
        clarityScore -= 0.05;
      }

      return Math.max(0, Math.min(1, clarityScore));
    } catch (error) {
      console.warn('Clarity assessment failed:', error);
      return 0.5;
    }
  }

  /**
   * Assess text coherence
   */
  private assessCoherence(text: string): number {
    try {
      const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
      if (paragraphs.length <= 1) return 0.8; // Single paragraph gets decent score

      let coherenceScore = 1.0;

      // Check for transition words
      const transitionWords = [
        'however',
        'therefore',
        'furthermore',
        'moreover',
        'consequently',
        'additionally',
        'meanwhile',
        'nevertheless',
        'thus',
        'hence',
        'first',
        'second',
        'finally',
        'in conclusion',
        'for example',
      ];

      const transitionCount = paragraphs.filter(p =>
        transitionWords.some(word => p.toLowerCase().includes(word))
      ).length;

      const transitionRatio = transitionCount / paragraphs.length;
      if (transitionRatio < 0.3) {
        coherenceScore -= 0.2;
      } else if (transitionRatio < 0.5) {
        coherenceScore -= 0.1;
      }

      // Check topic consistency using keyword overlap
      const keywords = this.extractKeywords(text);
      const topKeywords = keywords.slice(0, 10).map(k => k.text);

      let topicConsistency = 0;
      paragraphs.forEach(paragraph => {
        const paragraphWords = paragraph.toLowerCase().split(/\s+/);
        const keywordMatches = topKeywords.filter(keyword =>
          paragraphWords.some(word => word.includes(keyword.toLowerCase()))
        ).length;
        topicConsistency += keywordMatches / topKeywords.length;
      });

      topicConsistency /= paragraphs.length;
      coherenceScore *= topicConsistency;

      return Math.max(0, Math.min(1, coherenceScore));
    } catch (error) {
      console.warn('Coherence assessment failed:', error);
      return 0.5;
    }
  }

  /**
   * Assess text conciseness
   */
  private assessConciseness(text: string): number {
    try {
      let concisenessScore = 1.0;

      // Check for redundant phrases
      const redundantPhrases = [
        'in order to',
        'due to the fact that',
        'at this point in time',
        'it is important to note that',
        'it should be noted that',
        'the fact that',
        'in the event that',
        'for the purpose of',
      ];

      redundantPhrases.forEach(phrase => {
        const regex = new RegExp(phrase, 'gi');
        const matches = text.match(regex);
        if (matches) {
          concisenessScore -= matches.length * 0.05;
        }
      });

      // Check for excessive adverbs
      const adverbs = text.match(/\b\w+ly\b/g) || [];
      const words = text.split(/\s+/).length;
      const adverbRatio = adverbs.length / words;
      if (adverbRatio > 0.05) {
        concisenessScore -= 0.1;
      }

      // Check for filler words
      const fillerWords = ['very', 'really', 'quite', 'rather', 'somewhat', 'fairly'];
      fillerWords.forEach(filler => {
        const regex = new RegExp(`\\b${filler}\\b`, 'gi');
        const matches = text.match(regex);
        if (matches) {
          concisenessScore -= matches.length * 0.02;
        }
      });

      return Math.max(0, Math.min(1, concisenessScore));
    } catch (error) {
      console.warn('Conciseness assessment failed:', error);
      return 0.5;
    }
  }

  /**
   * Assess grammar (simplified)
   */
  private assessGrammar(text: string): GrammarAssessment {
    try {
      const errors: GrammarError[] = [];
      const suggestions: string[] = [];
      let score = 1.0;

      // Check for common grammar issues
      const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

      sentences.forEach((sentence, _index) => {
        const trimmed = sentence.trim();

        // Check for sentence fragments (very basic)
        if (
          trimmed.length > 0 &&
          !trimmed.match(/\b(is|are|was|were|have|has|had|will|would|can|could|should|must)\b/i)
        ) {
          errors.push({
            type: 'fragment',
            message: 'Possible sentence fragment',
            startOffset: text.indexOf(trimmed),
            endOffset: text.indexOf(trimmed) + trimmed.length,
            severity: ErrorSeverity.MEDIUM,
            suggestion: 'Consider adding a verb to complete the sentence',
          });
          score -= 0.05;
        }

        // Check for run-on sentences
        if (trimmed.split(/\s+/).length > 30) {
          errors.push({
            type: 'run-on',
            message: 'Sentence may be too long',
            startOffset: text.indexOf(trimmed),
            endOffset: text.indexOf(trimmed) + trimmed.length,
            severity: ErrorSeverity.LOW,
            suggestion: 'Consider breaking this into shorter sentences',
          });
          score -= 0.03;
        }
      });

      // Check for subject-verb agreement (very basic)
      const disagreementPatterns = [
        /\b(he|she|it)\s+(are|were)\b/gi,
        /\b(they|we|you)\s+(is|was)\b/gi,
      ];

      disagreementPatterns.forEach(pattern => {
        const matches = text.match(pattern);
        if (matches) {
          matches.forEach(match => {
            const startOffset = text.indexOf(match);
            errors.push({
              type: 'subject-verb-agreement',
              message: 'Possible subject-verb disagreement',
              startOffset,
              endOffset: startOffset + match.length,
              severity: ErrorSeverity.HIGH,
              suggestion: 'Check subject-verb agreement',
            });
            score -= 0.1;
          });
        }
      });

      // Generate general suggestions
      if (errors.length > 0) {
        suggestions.push('Review grammar and sentence structure');
      }
      if (errors.filter(e => e.type === 'run-on').length > 0) {
        suggestions.push('Break long sentences into shorter ones');
      }

      return {
        score: Math.max(0, Math.min(1, score)),
        errors,
        suggestions,
      };
    } catch (error) {
      console.warn('Grammar assessment failed:', error);
      return {
        score: 0.5,
        errors: [],
        suggestions: [],
      };
    }
  }

  /**
   * Assess vocabulary quality
   */
  private assessVocabulary(text: string): VocabularyAssessment {
    try {
      const words = text
        .toLowerCase()
        .split(/\s+/)
        .filter(w => /^[a-zA-Z]+$/.test(w));
      const uniqueWords = new Set(words);

      // Calculate diversity (unique words / total words)
      const diversity = words.length > 0 ? uniqueWords.size / words.length : 0;

      // Calculate complexity (average word length)
      const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;
      const complexity = Math.min(1, avgWordLength / 8); // Normalize to 0-1

      // Calculate repetition (1 - diversity)
      const repetition = 1 - diversity;

      // Assess appropriateness (simplified - based on common words)
      const commonWords = new Set([
        'the',
        'be',
        'to',
        'of',
        'and',
        'a',
        'in',
        'that',
        'have',
        'i',
        'it',
        'for',
        'not',
        'on',
        'with',
        'he',
        'as',
        'you',
        'do',
        'at',
      ]);

      const commonWordCount = words.filter(word => commonWords.has(word)).length;
      const appropriateness = words.length > 0 ? 1 - commonWordCount / words.length : 0;

      const suggestions: string[] = [];
      if (diversity < 0.5) {
        suggestions.push('Use more varied vocabulary to avoid repetition');
      }
      if (complexity < 0.3) {
        suggestions.push('Consider using more sophisticated vocabulary');
      }
      if (appropriateness < 0.3) {
        suggestions.push('Use more specific and descriptive words');
      }

      return {
        diversity: Math.round(diversity * 100) / 100,
        complexity: Math.round(complexity * 100) / 100,
        appropriateness: Math.round(appropriateness * 100) / 100,
        repetition: Math.round(repetition * 100) / 100,
        suggestions,
      };
    } catch (error) {
      console.warn('Vocabulary assessment failed:', error);
      return {
        diversity: 0,
        complexity: 0,
        appropriateness: 0,
        repetition: 1,
        suggestions: [],
      };
    }
  }

  /**
   * Assess text structure
   */
  private assessStructure(text: string): StructureAssessment {
    try {
      const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
      const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

      // Assess organization (paragraph length consistency)
      let organization = 1.0;
      if (paragraphs.length > 1) {
        const paragraphLengths = paragraphs.map(p => p.split(/\s+/).length);
        const avgLength =
          paragraphLengths.reduce((sum, len) => sum + len, 0) / paragraphLengths.length;
        const variance =
          paragraphLengths.reduce((sum, len) => sum + Math.pow(len - avgLength, 2), 0) /
          paragraphLengths.length;
        const coefficient = Math.sqrt(variance) / avgLength;
        organization = Math.max(0, 1 - coefficient); // Lower variance = better organization
      }

      // Assess flow (sentence length variation)
      let flow = 1.0;
      if (sentences.length > 1) {
        const sentenceLengths = sentences.map(s => s.split(/\s+/).length);
        const avgSentenceLength =
          sentenceLengths.reduce((sum, len) => sum + len, 0) / sentenceLengths.length;
        const sentenceVariance =
          sentenceLengths.reduce((sum, len) => sum + Math.pow(len - avgSentenceLength, 2), 0) /
          sentenceLengths.length;
        const sentenceCoefficient = Math.sqrt(sentenceVariance) / avgSentenceLength;
        flow = Math.min(1, sentenceCoefficient); // Some variation is good for flow
      }

      // Assess transitions (already calculated in coherence, simplified here)
      const transitionWords = [
        'however',
        'therefore',
        'furthermore',
        'moreover',
        'consequently',
        'additionally',
        'meanwhile',
        'nevertheless',
        'thus',
        'hence',
      ];
      const transitionCount = paragraphs.filter(p =>
        transitionWords.some(word => p.toLowerCase().includes(word))
      ).length;
      const transitions =
        paragraphs.length > 0 ? Math.min(1, transitionCount / paragraphs.length) : 0;

      // Assess paragraph structure (introduction, body, conclusion pattern)
      let paragraphStructure = 0.8; // Default decent score
      if (paragraphs.length >= 3) {
        // Check if first paragraph is shorter (introduction)
        // Check if last paragraph is shorter (conclusion)
        const firstLength = paragraphs[0]?.split(/\s+/).length || 0;
        const lastLength = paragraphs[paragraphs.length - 1]?.split(/\s+/).length || 0;
        const avgMiddleLength =
          paragraphs.slice(1, -1).reduce((sum, p) => sum + p.split(/\s+/).length, 0) /
          (paragraphs.length - 2);

        if (firstLength < avgMiddleLength && lastLength < avgMiddleLength) {
          paragraphStructure = 1.0; // Good structure
        }
      }

      const suggestions: string[] = [];
      if (organization < 0.6) {
        suggestions.push('Make paragraph lengths more consistent');
      }
      if (flow < 0.4) {
        suggestions.push('Vary sentence lengths for better flow');
      }
      if (transitions < 0.3) {
        suggestions.push('Add more transition words between paragraphs');
      }

      return {
        organization: Math.round(organization * 100) / 100,
        flow: Math.round(flow * 100) / 100,
        transitions: Math.round(transitions * 100) / 100,
        paragraphStructure: Math.round(paragraphStructure * 100) / 100,
        suggestions,
      };
    } catch (error) {
      console.warn('Structure assessment failed:', error);
      return {
        organization: 0,
        flow: 0,
        transitions: 0,
        paragraphStructure: 0,
        suggestions: [],
      };
    }
  }

  /**
   * Generate quality improvement suggestions
   */
  private generateQualityImprovements(
    _text: string,
    readability: ReadabilityScore,
    clarity: number,
    _coherence: number,
    conciseness: number,
    grammar: GrammarAssessment,
    vocabulary: VocabularyAssessment,
    structure: StructureAssessment
  ): QualityImprovement[] {
    const suggestions: QualityImprovement[] = [];

    // Readability improvements
    if (readability.overall < 0.6) {
      suggestions.push({
        type: ImprovementType.READABILITY,
        description: 'Text is difficult to read',
        priority: ImprovementPriority.HIGH,
        suggestion: 'Simplify sentence structure and use shorter words',
        example:
          'Break long sentences into shorter ones and replace complex words with simpler alternatives',
      });
    }

    // Clarity improvements
    if (clarity < 0.6) {
      suggestions.push({
        type: ImprovementType.CLARITY,
        description: 'Text lacks clarity',
        priority: ImprovementPriority.HIGH,
        suggestion: 'Use active voice and reduce jargon',
        example: 'Change "The report was written by the team" to "The team wrote the report"',
      });
    }

    // Conciseness improvements
    if (conciseness < 0.6) {
      suggestions.push({
        type: ImprovementType.CONCISENESS,
        description: 'Text is wordy',
        priority: ImprovementPriority.MEDIUM,
        suggestion: 'Remove redundant phrases and filler words',
        example: 'Change "in order to" to "to" and remove words like "very" and "really"',
      });
    }

    // Grammar improvements
    if (grammar.score < 0.7) {
      suggestions.push({
        type: ImprovementType.GRAMMAR,
        description: 'Grammar issues detected',
        priority: ImprovementPriority.HIGH,
        suggestion: 'Review sentence structure and verb agreement',
        example: 'Check for complete sentences and proper subject-verb agreement',
      });
    }

    // Vocabulary improvements
    if (vocabulary.diversity < 0.5) {
      suggestions.push({
        type: ImprovementType.VOCABULARY,
        description: 'Limited vocabulary variety',
        priority: ImprovementPriority.MEDIUM,
        suggestion: 'Use more varied and descriptive words',
        example: 'Replace repeated words with synonyms and use more specific terms',
      });
    }

    // Structure improvements
    if (structure.organization < 0.6) {
      suggestions.push({
        type: ImprovementType.STRUCTURE,
        description: 'Poor text organization',
        priority: ImprovementPriority.MEDIUM,
        suggestion: 'Improve paragraph structure and logical flow',
        example: 'Organize ideas into clear paragraphs with topic sentences',
      });
    }

    return suggestions;
  }

  // ===== Topic Modeling Helper Methods =====

  /**
   * Find terms related to a main term through co-occurrence
   */
  private findRelatedTerms(mainTerm: string, text: string, _allTokens: string[]): string[] {
    try {
      const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
      const relatedTerms: Record<string, number> = {};

      sentences.forEach(sentence => {
        const sentenceTokens = this.tokenizer.tokenize(sentence.toLowerCase()) || [];
        const cleanSentenceTokens = sentenceTokens.filter(
          token =>
            token.length > 2 && !natural.stopwords.includes(token) && /^[a-zA-Z]+$/.test(token)
        );

        const stemmedTokens = cleanSentenceTokens.map(token => this.stemmer.stem(token));

        if (stemmedTokens.includes(mainTerm)) {
          stemmedTokens.forEach(token => {
            if (token !== mainTerm) {
              relatedTerms[token] = (relatedTerms[token] || 0) + 1;
            }
          });
        }
      });

      return Object.entries(relatedTerms)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([term]) => term);
    } catch (error) {
      console.warn('Related terms extraction failed:', error);
      return [];
    }
  }

  /**
   * Generate a topic name from keywords
   */
  private generateTopicName(keywords: string[]): string {
    try {
      if (keywords.length === 0) return 'Unknown Topic';

      // Use the most frequent/important keyword as the base
      const mainKeyword = keywords[0];
      if (!mainKeyword) return 'Unknown Topic';

      // Capitalize and clean up the main keyword
      const cleanedKeyword = mainKeyword.charAt(0).toUpperCase() + mainKeyword.slice(1);

      // If we have multiple keywords, create a compound name
      if (keywords.length > 1) {
        const secondKeyword = keywords[1];
        if (secondKeyword) {
          return `${cleanedKeyword} & ${secondKeyword.charAt(0).toUpperCase() + secondKeyword.slice(1)}`;
        }
      }

      return cleanedKeyword;
    } catch (error) {
      console.warn('Topic name generation failed:', error);
      return 'Topic';
    }
  }

  /**
   * Generate a topic description from keywords and text
   */
  private generateTopicDescription(keywords: string[], text: string): string {
    try {
      if (keywords.length === 0) return 'No description available';

      // Find sentences that contain the keywords
      const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
      const relevantSentences: string[] = [];

      sentences.forEach(sentence => {
        const lowerSentence = sentence.toLowerCase();
        const keywordMatches = keywords.filter(keyword =>
          lowerSentence.includes(keyword.toLowerCase())
        ).length;

        if (keywordMatches >= Math.min(2, keywords.length)) {
          relevantSentences.push(sentence.trim());
        }
      });

      if (relevantSentences.length > 0) {
        // Return the first relevant sentence as description
        const firstSentence = relevantSentences[0];
        if (firstSentence) {
          return firstSentence.substring(0, 150) + (firstSentence.length > 150 ? '...' : '');
        }
      }

      // Fallback: create description from keywords
      return `Topic related to ${keywords.slice(0, 3).join(', ')}`;
    } catch (error) {
      console.warn('Topic description generation failed:', error);
      return 'Topic description';
    }
  }

  /**
   * Calculate similarity between a topic and a list of existing topics
   */
  private calculateTopicSimilarity(topic: Topic, existingTopics: Topic[]): number {
    try {
      if (existingTopics.length === 0) return 0;

      let maxSimilarity = 0;

      existingTopics.forEach(existingTopic => {
        // Calculate keyword overlap
        const topicKeywords = new Set(topic.keywords.map(k => k.toLowerCase()));
        const existingKeywords = new Set(existingTopic.keywords.map(k => k.toLowerCase()));

        const intersection = new Set([...topicKeywords].filter(k => existingKeywords.has(k)));
        const union = new Set([...topicKeywords, ...existingKeywords]);

        const jaccardSimilarity = union.size > 0 ? intersection.size / union.size : 0;

        if (jaccardSimilarity > maxSimilarity) {
          maxSimilarity = jaccardSimilarity;
        }
      });

      return maxSimilarity;
    } catch (error) {
      console.warn('Topic similarity calculation failed:', error);
      return 0;
    }
  }

  /**
   * Enhance a topic with semantic analysis
   */
  private enhanceTopicWithSemantics(topic: Topic, text: string): Topic {
    try {
      // Find additional semantic keywords
      const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
      const semanticKeywords: string[] = [];

      sentences.forEach(sentence => {
        const lowerSentence = sentence.toLowerCase();
        const hasTopicKeywords = topic.keywords.some(keyword =>
          lowerSentence.includes(keyword.toLowerCase())
        );

        if (hasTopicKeywords) {
          // Extract potential semantic keywords from this sentence
          const tokens = this.tokenizer.tokenize(sentence.toLowerCase()) || [];
          const cleanTokens = tokens.filter(
            token =>
              token.length > 3 &&
              !natural.stopwords.includes(token) &&
              /^[a-zA-Z]+$/.test(token) &&
              !topic.keywords.includes(token)
          );

          semanticKeywords.push(...cleanTokens.slice(0, 2));
        }
      });

      // Add unique semantic keywords
      const uniqueSemanticKeywords = [...new Set(semanticKeywords)].slice(0, 3);
      const enhancedKeywords = [...topic.keywords, ...uniqueSemanticKeywords];

      // Recalculate confidence based on enhanced keywords
      const enhancedConfidence = Math.min(1, topic.confidence * 1.1);

      return {
        ...topic,
        keywords: enhancedKeywords,
        confidence: enhancedConfidence,
        description: this.generateTopicDescription(enhancedKeywords, text),
      };
    } catch (error) {
      console.warn('Topic semantic enhancement failed:', error);
      return topic;
    }
  }

  /**
   * Extract theme-based topics
   */
  private extractThemeBasedTopics(
    text: string,
    numTopics: number,
    usedKeywords: Set<string>
  ): Topic[] {
    try {
      const topics: Topic[] = [];

      // Define common themes and their associated keywords
      const themes = {
        Technology: ['technology', 'digital', 'software', 'computer', 'internet', 'data', 'system'],
        Business: ['business', 'company', 'market', 'customer', 'revenue', 'profit', 'strategy'],
        Health: ['health', 'medical', 'patient', 'treatment', 'doctor', 'hospital', 'care'],
        Education: [
          'education',
          'student',
          'school',
          'learning',
          'teacher',
          'university',
          'knowledge',
        ],
        Finance: ['finance', 'money', 'investment', 'bank', 'financial', 'economic', 'cost'],
        Environment: [
          'environment',
          'climate',
          'energy',
          'green',
          'sustainable',
          'nature',
          'pollution',
        ],
      };

      const lowerText = text.toLowerCase();

      Object.entries(themes).forEach(([themeName, themeKeywords]) => {
        if (topics.length >= numTopics) return;

        // Count theme keyword occurrences
        const themeScore = themeKeywords.reduce((score, keyword) => {
          if (!usedKeywords.has(keyword)) {
            const regex = new RegExp(`\\b${keyword}\\b`, 'g');
            const matches = lowerText.match(regex);
            return score + (matches ? matches.length : 0);
          }
          return score;
        }, 0);

        if (themeScore > 0) {
          const relevantKeywords = themeKeywords
            .filter(keyword => lowerText.includes(keyword) && !usedKeywords.has(keyword))
            .slice(0, 5);

          if (relevantKeywords.length > 0) {
            topics.push({
              id: `theme_${themeName.toLowerCase()}`,
              name: themeName,
              confidence: Math.min(1, themeScore / 10),
              keywords: relevantKeywords,
              description: `Theme-based topic related to ${themeName.toLowerCase()}`,
            });

            relevantKeywords.forEach(keyword => usedKeywords.add(keyword));
          }
        }
      });

      return topics.sort((a, b) => b.confidence - a.confidence);
    } catch (error) {
      console.warn('Theme-based topic extraction failed:', error);
      return [];
    }
  }
}

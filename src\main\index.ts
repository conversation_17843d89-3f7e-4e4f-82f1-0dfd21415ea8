import { app, BrowserWindow, Menu } from 'electron';
import * as path from 'path';
import { setupKnowledgeHandlers } from './ipc/knowledgeHandlers';
import { setupLogHandlers } from './ipc/logHandlers';
import { chromaKnowledgeBase } from './services/ChromaKnowledgeBase';
import { logger } from './utils/logger';

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) {
  app.quit();
}

// Keep a global reference of the window object
let mainWindow: BrowserWindow | null = null;

const createWindow = (): void => {
  // Create the browser window.
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 800,
    minHeight: 600,
    titleBarStyle: 'hidden',
    titleBarOverlay: {
      color: '#1f2937',
      symbolColor: '#ffffff',
      height: 32,
    },
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,

      preload: path.join(__dirname, '../preload/index.js'),
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false,
    },
    icon: path.join(__dirname, '../../assets/icons/icon.png'), // Add app icon
    show: false, // Don't show until ready
  });

  // Load the app
  if (process.env.NODE_ENV === 'development' && process.env.DEV_SERVER === 'true') {
    // In development with dev server, load from webpack dev server
    mainWindow.loadURL('http://localhost:4000').catch(error => {
      logger.error('Failed to load dev server URL', error);
    });
  } else {
    // Load from built files
    const htmlPath = path.join(__dirname, '../renderer/index.html');
    logger.info('Loading HTML file from:', htmlPath);
    mainWindow.loadFile(htmlPath).catch(error => {
      logger.error('Failed to load HTML file', { htmlPath, error: error.message });
    });
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    if (mainWindow) {
      mainWindow.show();

      // Removed console interception telemetry code

      // Handle page load events
      mainWindow.webContents.once('did-finish-load', () => {
        if (mainWindow) {
          logger.info('Page finished loading');
        }
      });

      // Handle load failures
      mainWindow.webContents.on(
        'did-fail-load',
        (_event, errorCode, errorDescription, validatedURL) => {
          logger.error('Failed to load page', {
            errorCode,
            errorDescription,
            url: validatedURL,
          });
        }
      );

      // Open DevTools in development
      if (process.env.NODE_ENV === 'development') {
        mainWindow.webContents.openDevTools();
      }

      logger.info('Main window shown and ready');
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    require('electron').shell.openExternal(url);
    return { action: 'deny' };
  });

  // Security: Prevent new window creation
  mainWindow.webContents.setWindowOpenHandler(() => {
    return { action: 'deny' };
  });

  // Security: Prevent navigation to external URLs
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const allowedOrigins = ['http://localhost:4000', 'file://'];

    if (!allowedOrigins.some(origin => navigationUrl.startsWith(origin))) {
      event.preventDefault();
    }
  });
};

// Create application menu
const createMenu = (): void => {
  const template: Electron.MenuItemConstructorOptions[] = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Project',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // TODO: Implement new project functionality
            console.log('New Project clicked');
          },
        },
        {
          label: 'Open Document',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            // TODO: Implement open document functionality
            console.log('Open Document clicked');
          },
        },
        { type: 'separator' },
        {
          label: 'Save',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            // TODO: Implement save functionality
            console.log('Save clicked');
          },
        },
        {
          label: 'Save As...',
          accelerator: 'CmdOrCtrl+Shift+S',
          click: () => {
            // TODO: Implement save as functionality
            console.log('Save As clicked');
          },
        },
        { type: 'separator' },
        {
          role: 'quit',
        },
      ],
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'selectAll' },
      ],
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' },
      ],
    },
    {
      label: 'AI',
      submenu: [
        {
          label: 'Process Document',
          accelerator: 'CmdOrCtrl+P',
          click: () => {
            // TODO: Implement AI document processing
            console.log('Process Document clicked');
          },
        },
        {
          label: 'Fill Form',
          accelerator: 'CmdOrCtrl+F',
          click: () => {
            // TODO: Implement AI form filling
            console.log('Fill Form clicked');
          },
        },
        { type: 'separator' },
        {
          label: 'Knowledge Base',
          accelerator: 'CmdOrCtrl+K',
          click: () => {
            // TODO: Open knowledge base
            console.log('Knowledge Base clicked');
          },
        },
      ],
    },
    {
      label: 'Window',
      submenu: [{ role: 'minimize' }, { role: 'close' }],
    },
    {
      role: 'help',
      submenu: [
        {
          label: 'About AI Document Processor',
          click: () => {
            // TODO: Show about dialog
            console.log('About clicked');
          },
        },
        {
          label: 'Documentation',
          click: () => {
            require('electron').shell.openExternal(
              'https://github.com/hepzceo/ai-document-processor'
            );
          },
        },
      ],
    },
  ];

  // macOS specific menu adjustments
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' },
      ],
    });

    // Window menu
    const windowMenu = template[5]?.submenu as Electron.MenuItemConstructorOptions[];
    if (windowMenu) {
      windowMenu.push(
        { type: 'separator' },
        { role: 'front' },
        { type: 'separator' },
        { role: 'window' }
      );
    }
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
};

// App event handlers
app.whenReady().then(async () => {
  // Initialize logging system
  logger.info('AI Document Processor starting up', {
    version: app.getVersion(),
    platform: process.platform,
    arch: process.arch,
    nodeVersion: process.version,
    electronVersion: process.versions.electron,
  });

  // Setup IPC handlers for logging
  setupLogHandlers();

  // Setup IPC handlers for knowledge base
  setupKnowledgeHandlers();

  // Initialize ChromaDB
  try {
    await chromaKnowledgeBase.initialize();
    logger.info('ChromaDB initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize ChromaDB', error);
  }

  createWindow();
  createMenu();

  // macOS: Re-create window when dock icon is clicked
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });

  logger.info('Application initialization completed');
});

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', async () => {
  logger.info('All windows closed');

  if (process.platform !== 'darwin') {
    // Flush logs before quitting
    await logger.flush();
    logger.info('Application shutting down');
    app.quit();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (_event, contents) => {
  contents.setWindowOpenHandler(() => {
    return { action: 'deny' };
  });
});

// Handle certificate errors
app.on('certificate-error', (event, _webContents, _url, _error, _certificate, callback) => {
  // In production, you should implement proper certificate validation
  if (process.env.NODE_ENV === 'development') {
    event.preventDefault();
    callback(true);
  } else {
    callback(false);
  }
});

// Prevent navigation to external URLs
app.on('web-contents-created', (_event, contents) => {
  contents.on('will-navigate', (event, navigationUrl) => {
    const allowedOrigins = ['http://localhost:4000', 'file://'];

    if (!allowedOrigins.some(origin => navigationUrl.startsWith(origin))) {
      event.preventDefault();
    }
  });
});

// Handle app quit
app.on('before-quit', async event => {
  logger.info('Application quit requested');

  // Prevent immediate quit to allow log flushing
  event.preventDefault();

  // Flush all pending logs
  await logger.flush();

  logger.info('Application shutdown complete');

  // Now actually quit
  app.exit(0);
});

// Handle app updates (placeholder for future implementation)
app.on('ready', () => {
  logger.info('AI Document Processor is ready');
});

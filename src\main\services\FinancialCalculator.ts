import { createLogger } from '../utils/logger';
import { CalculationEngine } from './CalculationEngine';

const logger = createLogger('FinancialCalculator');

export interface TaxBracket {
  min: number;
  max: number;
  rate: number;
  description: string;
}

export interface TaxCalculationOptions {
  income: number;
  filingStatus: 'single' | 'married_joint' | 'married_separate' | 'head_of_household';
  deductions: number;
  exemptions: number;
  state?: string;
  year?: number;
}

export interface TaxCalculationResult {
  grossIncome: number;
  adjustedGrossIncome: number;
  taxableIncome: number;
  federalTax: number;
  stateTax: number;
  totalTax: number;
  effectiveRate: number;
  marginalRate: number;
  afterTaxIncome: number;
  breakdown: TaxBreakdown[];
}

export interface TaxBreakdown {
  bracket: TaxBracket;
  taxableAmount: number;
  taxOwed: number;
}

export interface InterestCalculationOptions {
  principal: number;
  rate: number;
  time: number;
  compoundingFrequency?: number;
  paymentAmount?: number;
  paymentFrequency?: number;
}

export interface InterestCalculationResult {
  principal: number;
  interestEarned: number;
  totalAmount: number;
  effectiveRate: number;
  compoundingPeriods: number;
  breakdown: InterestPeriod[];
}

export interface InterestPeriod {
  period: number;
  startingBalance: number;
  interestEarned: number;
  endingBalance: number;
  payment?: number;
}

export interface CurrencyConversionOptions {
  amount: number;
  fromCurrency: string;
  toCurrency: string;
  exchangeRate?: number;
  date?: Date;
}

export interface CurrencyConversionResult {
  originalAmount: number;
  convertedAmount: number;
  exchangeRate: number;
  fromCurrency: string;
  toCurrency: string;
  conversionDate: Date;
  fees?: number;
}

export interface FinancialPlanningOptions {
  currentAge: number;
  retirementAge: number;
  currentSavings: number;
  monthlyContribution: number;
  expectedReturn: number;
  inflationRate: number;
  retirementGoal?: number;
}

export interface FinancialPlanningResult {
  projectedSavings: number;
  monthlyRetirementIncome: number;
  shortfall: number;
  recommendedContribution: number;
  yearsToRetirement: number;
  projectionBreakdown: FinancialProjection[];
}

export interface FinancialProjection {
  year: number;
  age: number;
  contribution: number;
  growth: number;
  balance: number;
  inflationAdjustedBalance: number;
}

/**
 * Financial Calculator for comprehensive financial calculations
 * Handles tax calculations, interest calculations, currency conversion, and financial planning
 */
export class FinancialCalculator {
  // private _calculationEngine: CalculationEngine;
  private taxBrackets: Map<string, TaxBracket[]> = new Map();
  private exchangeRates: Map<string, number> = new Map();

  constructor(_calculationEngine: CalculationEngine) {
    // this._calculationEngine = calculationEngine;
    this.initializeTaxBrackets();
    this.initializeExchangeRates();

    logger.info('FinancialCalculator initialized');
  }

  /**
   * Calculate federal and state taxes
   */
  public async calculateTax(options: TaxCalculationOptions): Promise<TaxCalculationResult> {
    logger.debug('Calculating taxes', { options });

    const { income, filingStatus, deductions, exemptions, state, year = 2024 } = options;

    // Calculate adjusted gross income
    const adjustedGrossIncome = income; // Simplified - could include adjustments

    // Calculate taxable income
    const standardDeduction = this.getStandardDeduction(filingStatus, year);
    const totalDeductions = Math.max(deductions, standardDeduction);
    const taxableIncome = Math.max(0, adjustedGrossIncome - totalDeductions - exemptions);

    // Calculate federal tax
    const federalBrackets = this.getTaxBrackets('federal', filingStatus, year);
    const federalTaxResult = this.calculateProgressiveTax(taxableIncome, federalBrackets);

    // Calculate state tax
    let stateTax = 0;
    if (state) {
      const stateBrackets = this.getTaxBrackets(state, filingStatus, year);
      if (stateBrackets.length > 0) {
        const stateTaxResult = this.calculateProgressiveTax(taxableIncome, stateBrackets);
        stateTax = stateTaxResult.totalTax;
      }
    }

    const totalTax = federalTaxResult.totalTax + stateTax;
    const effectiveRate = income > 0 ? (totalTax / income) * 100 : 0;
    const marginalRate = federalTaxResult.marginalRate;
    const afterTaxIncome = income - totalTax;

    const result: TaxCalculationResult = {
      grossIncome: income,
      adjustedGrossIncome,
      taxableIncome,
      federalTax: federalTaxResult.totalTax,
      stateTax,
      totalTax,
      effectiveRate,
      marginalRate,
      afterTaxIncome,
      breakdown: federalTaxResult.breakdown,
    };

    logger.info('Tax calculation completed', {
      taxableIncome,
      federalTax: federalTaxResult.totalTax,
      stateTax,
      totalTax,
      effectiveRate,
    });

    return result;
  }

  /**
   * Calculate compound interest
   */
  public async calculateCompoundInterest(
    options: InterestCalculationOptions
  ): Promise<InterestCalculationResult> {
    logger.debug('Calculating compound interest', { options });

    const { principal, rate, time, compoundingFrequency = 12 } = options;

    const periodicRate = rate / compoundingFrequency;
    const totalPeriods = compoundingFrequency * time;

    const breakdown: InterestPeriod[] = [];
    let currentBalance = principal;

    for (let period = 1; period <= totalPeriods; period++) {
      const startingBalance = currentBalance;
      const interestEarned = startingBalance * periodicRate;
      currentBalance = startingBalance + interestEarned;

      breakdown.push({
        period,
        startingBalance,
        interestEarned,
        endingBalance: currentBalance,
      });
    }

    const totalAmount = currentBalance;
    const interestEarned = totalAmount - principal;
    const effectiveRate = ((totalAmount / principal) ** (1 / time) - 1) * 100;

    const result: InterestCalculationResult = {
      principal,
      interestEarned,
      totalAmount,
      effectiveRate,
      compoundingPeriods: totalPeriods,
      breakdown,
    };

    logger.info('Compound interest calculation completed', {
      principal,
      interestEarned,
      totalAmount,
      effectiveRate,
    });

    return result;
  }

  /**
   * Calculate simple interest
   */
  public async calculateSimpleInterest(
    options: InterestCalculationOptions
  ): Promise<InterestCalculationResult> {
    logger.debug('Calculating simple interest', { options });

    const { principal, rate, time } = options;

    const interestEarned = principal * rate * time;
    const totalAmount = principal + interestEarned;
    const effectiveRate = (interestEarned / principal) * 100;

    const breakdown: InterestPeriod[] = [
      {
        period: 1,
        startingBalance: principal,
        interestEarned,
        endingBalance: totalAmount,
      },
    ];

    const result: InterestCalculationResult = {
      principal,
      interestEarned,
      totalAmount,
      effectiveRate,
      compoundingPeriods: 1,
      breakdown,
    };

    logger.info('Simple interest calculation completed', {
      principal,
      interestEarned,
      totalAmount,
      effectiveRate,
    });

    return result;
  }

  /**
   * Convert currency
   */
  public async convertCurrency(
    options: CurrencyConversionOptions
  ): Promise<CurrencyConversionResult> {
    logger.debug('Converting currency', { options });

    const { amount, fromCurrency, toCurrency, exchangeRate, date = new Date() } = options;

    let rate = exchangeRate;
    if (!rate) {
      rate = this.getExchangeRate(fromCurrency, toCurrency);
    }

    const convertedAmount = amount * rate;

    const result: CurrencyConversionResult = {
      originalAmount: amount,
      convertedAmount,
      exchangeRate: rate,
      fromCurrency,
      toCurrency,
      conversionDate: date,
    };

    logger.info('Currency conversion completed', {
      amount,
      fromCurrency,
      toCurrency,
      convertedAmount,
      exchangeRate: rate,
    });

    return result;
  }

  /**
   * Calculate financial planning projections
   */
  public async calculateFinancialPlanning(
    options: FinancialPlanningOptions
  ): Promise<FinancialPlanningResult> {
    logger.debug('Calculating financial planning', { options });

    const {
      currentAge,
      retirementAge,
      currentSavings,
      monthlyContribution,
      expectedReturn,
      inflationRate,
      retirementGoal,
    } = options;

    const yearsToRetirement = retirementAge - currentAge;
    const monthlyReturn = expectedReturn / 12;

    const projectionBreakdown: FinancialProjection[] = [];
    let balance = currentSavings;

    for (let year = 1; year <= yearsToRetirement; year++) {
      const age = currentAge + year;
      const yearlyContribution = monthlyContribution * 12;

      // Calculate growth for the year
      let yearlyGrowth = 0;
      let yearEndBalance = balance;

      // Monthly compounding
      for (let month = 1; month <= 12; month++) {
        const monthlyGrowth = yearEndBalance * monthlyReturn;
        yearlyGrowth += monthlyGrowth;
        yearEndBalance += monthlyGrowth + monthlyContribution;
      }

      balance = yearEndBalance;
      const inflationAdjustedBalance = balance / Math.pow(1 + inflationRate, year);

      projectionBreakdown.push({
        year,
        age,
        contribution: yearlyContribution,
        growth: yearlyGrowth,
        balance,
        inflationAdjustedBalance,
      });
    }

    const projectedSavings = balance;
    const inflationAdjustedSavings =
      projectedSavings / Math.pow(1 + inflationRate, yearsToRetirement);

    // Estimate monthly retirement income (4% rule)
    const monthlyRetirementIncome = (inflationAdjustedSavings * 0.04) / 12;

    const shortfall = retirementGoal ? Math.max(0, retirementGoal - inflationAdjustedSavings) : 0;

    // Calculate recommended contribution to meet goal
    let recommendedContribution = monthlyContribution;
    if (retirementGoal && shortfall > 0) {
      // Simplified calculation - could be more sophisticated
      const additionalNeeded = shortfall;
      const futureValueFactor = Math.pow(1 + expectedReturn, yearsToRetirement);
      recommendedContribution = monthlyContribution + additionalNeeded / (futureValueFactor * 12);
    }

    const result: FinancialPlanningResult = {
      projectedSavings: inflationAdjustedSavings,
      monthlyRetirementIncome,
      shortfall,
      recommendedContribution,
      yearsToRetirement,
      projectionBreakdown,
    };

    logger.info('Financial planning calculation completed', {
      projectedSavings: inflationAdjustedSavings,
      monthlyRetirementIncome,
      shortfall,
      yearsToRetirement,
    });

    return result;
  }

  /**
   * Calculate percentage
   */
  public calculatePercentage(value: number, total: number): number {
    if (total === 0) {
      throw new Error('Total cannot be zero for percentage calculation');
    }
    return (value / total) * 100;
  }

  /**
   * Calculate percentage change
   */
  public calculatePercentageChange(oldValue: number, newValue: number): number {
    if (oldValue === 0) {
      throw new Error('Old value cannot be zero for percentage change calculation');
    }
    return ((newValue - oldValue) / oldValue) * 100;
  }

  /**
   * Calculate loan payment (PMT)
   */
  public calculateLoanPayment(principal: number, rate: number, periods: number): number {
    if (rate === 0) {
      return principal / periods;
    }

    const monthlyRate = rate / 12;
    const numerator = principal * monthlyRate * Math.pow(1 + monthlyRate, periods);
    const denominator = Math.pow(1 + monthlyRate, periods) - 1;

    return numerator / denominator;
  }

  /**
   * Calculate present value
   */
  public calculatePresentValue(futureValue: number, rate: number, periods: number): number {
    return futureValue / Math.pow(1 + rate, periods);
  }

  /**
   * Calculate future value
   */
  public calculateFutureValue(presentValue: number, rate: number, periods: number): number {
    return presentValue * Math.pow(1 + rate, periods);
  }

  /**
   * Initialize tax brackets for different jurisdictions
   */
  private initializeTaxBrackets(): void {
    // 2024 Federal Tax Brackets (Single)
    const federal2024Single: TaxBracket[] = [
      { min: 0, max: 11600, rate: 0.1, description: '10% bracket' },
      { min: 11600, max: 47150, rate: 0.12, description: '12% bracket' },
      { min: 47150, max: 100525, rate: 0.22, description: '22% bracket' },
      { min: 100525, max: 191675, rate: 0.24, description: '24% bracket' },
      { min: 191675, max: 243725, rate: 0.32, description: '32% bracket' },
      { min: 243725, max: 609350, rate: 0.35, description: '35% bracket' },
      { min: 609350, max: Infinity, rate: 0.37, description: '37% bracket' },
    ];

    // 2024 Federal Tax Brackets (Married Filing Jointly)
    const federal2024MarriedJoint: TaxBracket[] = [
      { min: 0, max: 23200, rate: 0.1, description: '10% bracket' },
      { min: 23200, max: 94300, rate: 0.12, description: '12% bracket' },
      { min: 94300, max: 201050, rate: 0.22, description: '22% bracket' },
      { min: 201050, max: 383350, rate: 0.24, description: '24% bracket' },
      { min: 383350, max: 487450, rate: 0.32, description: '32% bracket' },
      { min: 487450, max: 731200, rate: 0.35, description: '35% bracket' },
      { min: 731200, max: Infinity, rate: 0.37, description: '37% bracket' },
    ];

    this.taxBrackets.set('federal_single_2024', federal2024Single);
    this.taxBrackets.set('federal_married_joint_2024', federal2024MarriedJoint);

    // Example state tax brackets (California)
    const california2024Single: TaxBracket[] = [
      { min: 0, max: 10099, rate: 0.01, description: '1% bracket' },
      { min: 10099, max: 23942, rate: 0.02, description: '2% bracket' },
      { min: 23942, max: 37788, rate: 0.04, description: '4% bracket' },
      { min: 37788, max: 52455, rate: 0.06, description: '6% bracket' },
      { min: 52455, max: 66295, rate: 0.08, description: '8% bracket' },
      { min: 66295, max: 338639, rate: 0.093, description: '9.3% bracket' },
      { min: 338639, max: 406364, rate: 0.103, description: '10.3% bracket' },
      { min: 406364, max: 677278, rate: 0.113, description: '11.3% bracket' },
      { min: 677278, max: Infinity, rate: 0.123, description: '12.3% bracket' },
    ];

    this.taxBrackets.set('california_single_2024', california2024Single);

    logger.debug('Tax brackets initialized', {
      jurisdictions: Array.from(this.taxBrackets.keys()),
    });
  }

  /**
   * Initialize exchange rates (mock data - in real implementation, would fetch from API)
   */
  private initializeExchangeRates(): void {
    // Mock exchange rates (USD base)
    this.exchangeRates.set('USD_EUR', 0.85);
    this.exchangeRates.set('USD_GBP', 0.73);
    this.exchangeRates.set('USD_JPY', 110.0);
    this.exchangeRates.set('USD_CAD', 1.25);
    this.exchangeRates.set('USD_AUD', 1.35);
    this.exchangeRates.set('USD_CHF', 0.92);
    this.exchangeRates.set('USD_CNY', 6.45);

    // Reverse rates
    this.exchangeRates.set('EUR_USD', 1 / 0.85);
    this.exchangeRates.set('GBP_USD', 1 / 0.73);
    this.exchangeRates.set('JPY_USD', 1 / 110.0);
    this.exchangeRates.set('CAD_USD', 1 / 1.25);
    this.exchangeRates.set('AUD_USD', 1 / 1.35);
    this.exchangeRates.set('CHF_USD', 1 / 0.92);
    this.exchangeRates.set('CNY_USD', 1 / 6.45);

    logger.debug('Exchange rates initialized', {
      currencies: Array.from(this.exchangeRates.keys()),
    });
  }

  /**
   * Get tax brackets for jurisdiction and filing status
   */
  private getTaxBrackets(jurisdiction: string, filingStatus: string, year: number): TaxBracket[] {
    const key = `${jurisdiction}_${filingStatus}_${year}`;
    return this.taxBrackets.get(key) || [];
  }

  /**
   * Get standard deduction for filing status and year
   */
  private getStandardDeduction(filingStatus: string, _year: number): number {
    const deductions: Record<string, number> = {
      single: 14600,
      married_joint: 29200,
      married_separate: 14600,
      head_of_household: 21900,
    };

    return deductions[filingStatus] || 0;
  }

  /**
   * Calculate progressive tax
   */
  private calculateProgressiveTax(
    income: number,
    brackets: TaxBracket[]
  ): {
    totalTax: number;
    marginalRate: number;
    breakdown: TaxBreakdown[];
  } {
    let totalTax = 0;
    let marginalRate = 0;
    const breakdown: TaxBreakdown[] = [];

    for (const bracket of brackets) {
      if (income <= bracket.min) break;

      const taxableAmount = Math.min(income, bracket.max) - bracket.min;
      if (taxableAmount > 0) {
        const taxOwed = taxableAmount * bracket.rate;
        totalTax += taxOwed;
        marginalRate = bracket.rate * 100;

        breakdown.push({
          bracket,
          taxableAmount,
          taxOwed,
        });
      }
    }

    return { totalTax, marginalRate, breakdown };
  }

  /**
   * Get exchange rate between currencies
   */
  private getExchangeRate(fromCurrency: string, toCurrency: string): number {
    if (fromCurrency === toCurrency) return 1;

    const key = `${fromCurrency}_${toCurrency}`;
    const rate = this.exchangeRates.get(key);

    if (rate) return rate;

    // Try reverse lookup
    const reverseKey = `${toCurrency}_${fromCurrency}`;
    const reverseRate = this.exchangeRates.get(reverseKey);
    if (reverseRate) return 1 / reverseRate;

    // Default to 1 if not found (should fetch from API in real implementation)
    logger.warn('Exchange rate not found', { fromCurrency, toCurrency });
    return 1;
  }
}

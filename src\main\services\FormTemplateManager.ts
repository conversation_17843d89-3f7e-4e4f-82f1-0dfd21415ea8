import { Database } from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import { createLogger } from '../utils/logger';
import {
  FormTemplate,
  FieldMapping,
  CoordinateMapping,
  TemplateVariable,
  VariableType,
  ValidationRule,
  Document,
  FormField,
  DocumentType,
  TemplateMetadata,
} from '../../shared/types/Document';
import { dbConnection } from '../database/connection';
import { DocumentProcessingError } from './DocumentProcessor';

const logger = createLogger('FormTemplateManager');

export interface TemplateCreationOptions {
  includeCoordinates: boolean;
  includeValidation: boolean;
  includeVariables: boolean;
  autoDetectVariables: boolean;
  generateDescription: boolean;
}

export interface TemplateSearchOptions {
  documentType?: DocumentType;
  category?: string;
  tags?: string[];
  minSuccessRate?: number;
  sortBy?: 'name' | 'usage_count' | 'success_rate' | 'last_used' | 'created_at';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export interface TemplateUsageStats {
  totalUsage: number;
  successRate: number;
  averageProcessingTime: number;
  lastUsed: Date;
  commonErrors: string[];
  userFeedback: number;
}

export interface TemplateVersion {
  id: string;
  templateId: string;
  version: string;
  changes: string[];
  createdAt: Date;
  createdBy: string;
  isActive: boolean;
}

/**
 * Form template lifecycle management service
 */
export class FormTemplateManager {
  private readonly db: Database;

  constructor() {
    this.db = dbConnection.getConnection();
  }

  /**
   * Create a new template from a filled form
   */
  public async createTemplateFromForm(
    document: Document,
    formFields: FormField[],
    mappings: FieldMapping[],
    options: Partial<TemplateCreationOptions> = {}
  ): Promise<FormTemplate> {
    const creationOptions = this.mergeDefaultOptions(options);

    logger.info('Creating template from form', {
      documentId: document.id,
      fieldCount: formFields.length,
      mappingCount: mappings.length,
    });

    try {
      const templateId = uuidv4();
      const now = new Date();

      // Generate coordinate mappings
      const coordinateMappings: CoordinateMapping[] = [];
      if (creationOptions.includeCoordinates) {
        for (const field of formFields) {
          coordinateMappings.push({
            id: uuidv4(),
            name: field.name,
            sourceCoordinates: {
              x: field.bounds.x,
              y: field.bounds.y,
              width: field.bounds.width,
              height: field.bounds.height,
              pageNumber: field.bounds.pageNumber,
            },
            targetField: field.id,
            fieldType: field.type,
            confidence: 1.0,
            isActive: true,
          });
        }
      }

      // Generate template variables
      const variables: TemplateVariable[] = [];
      if (creationOptions.includeVariables) {
        variables.push(
          ...(await this.generateTemplateVariables(formFields, mappings, creationOptions))
        );
      }

      // Generate validation rules
      const validationRules: ValidationRule[] = [];
      if (creationOptions.includeValidation) {
        validationRules.push(...(await this.generateValidationRules(formFields)));
      }

      // Generate description
      const description = creationOptions.generateDescription
        ? await this.generateTemplateDescription(document, formFields)
        : `Template for ${document.name}`;

      // Create template metadata
      const metadata: TemplateMetadata = {
        author: 'system',
        sourceDocument: document.id,
        createdBy: 'system',
        tags: this.generateTags(document, formFields),
        category: this.detectCategory(document, formFields),
        subcategory: this.detectSubcategory(document, formFields),
        usageCount: 0,
        successRate: 0,
        averageProcessingTime: 0,
        usageStats: {
          totalUsage: 0,
          successRate: 0,
          averageProcessingTime: 0,
          lastUsed: now,
          commonErrors: [],
          userFeedback: 0,
        },
      };

      const template: FormTemplate = {
        id: templateId,
        name: this.generateTemplateName(document, formFields),
        documentType: document.type,
        version: '1.0',
        description,
        fieldMappings: mappings,
        coordinateMappings,
        variables,
        validationRules,
        metadata,
        createdAt: now,
        updatedAt: now,
      };

      // Save to database
      await this.saveTemplate(template);

      logger.info('Template created successfully', {
        templateId,
        name: template.name,
        fieldCount: formFields.length,
      });

      return template;
    } catch (error) {
      logger.error('Failed to create template from form', { error, documentId: document.id });
      throw new DocumentProcessingError(
        'Failed to create template from form',
        'TEMPLATE_CREATION_FAILED',
        document.id,
        'template_creation'
      );
    }
  }

  /**
   * Save template to database
   */
  public async saveTemplate(template: FormTemplate): Promise<void> {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO templates (
        id, name, document_type, description, version,
        field_mappings, coordinate_mappings, variables, validation_rules,
        metadata, tags, category, subcategory,
        usage_count, last_used, success_rate, usage_statistics,
        created_by, source_document_id, creation_metadata,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    try {
      stmt.run(
        template.id,
        template.name,
        template.documentType,
        template.description,
        template.version,
        JSON.stringify(template.fieldMappings),
        JSON.stringify(template.coordinateMappings),
        JSON.stringify(template.variables),
        JSON.stringify(template.validationRules),
        JSON.stringify(template.metadata),
        JSON.stringify(template.metadata.tags),
        template.metadata.category,
        template.metadata.subcategory,
        template.metadata.usageStats?.totalUsage || 0,
        template.metadata.usageStats?.lastUsed.toISOString() || new Date().toISOString(),
        template.metadata.usageStats?.successRate || 0,
        JSON.stringify(template.metadata.usageStats),
        template.metadata.createdBy,
        template.metadata.sourceDocument,
        JSON.stringify({ version: template.version }),
        template.createdAt.toISOString(),
        template.updatedAt.toISOString()
      );

      logger.debug('Template saved to database', { templateId: template.id });
    } catch (error) {
      logger.error('Failed to save template to database', { error, templateId: template.id });
      throw error;
    }
  }

  /**
   * Load template from database
   */
  public async loadTemplate(templateId: string): Promise<FormTemplate | null> {
    const stmt = this.db.prepare(`
      SELECT * FROM templates WHERE id = ?
    `);

    try {
      const row = stmt.get(templateId) as any;
      if (!row) return null;

      return this.rowToTemplate(row);
    } catch (error) {
      logger.error('Failed to load template from database', { error, templateId });
      throw error;
    }
  }

  /**
   * Search templates with filters
   */
  public async searchTemplates(options: TemplateSearchOptions = {}): Promise<FormTemplate[]> {
    let query = 'SELECT * FROM templates WHERE 1=1';
    const params: any[] = [];

    // Add filters
    if (options.documentType) {
      query += ' AND document_type = ?';
      params.push(options.documentType);
    }

    if (options.category) {
      query += ' AND category = ?';
      params.push(options.category);
    }

    if (options.minSuccessRate !== undefined) {
      query += ' AND success_rate >= ?';
      params.push(options.minSuccessRate);
    }

    if (options.tags && options.tags.length > 0) {
      query += ' AND json_extract(tags, "$") LIKE ?';
      params.push(`%${options.tags.join('%')}%`);
    }

    // Add sorting
    if (options.sortBy) {
      const sortOrder = options.sortOrder || 'desc';
      query += ` ORDER BY ${options.sortBy} ${sortOrder}`;
    }

    // Add pagination
    if (options.limit) {
      query += ' LIMIT ?';
      params.push(options.limit);

      if (options.offset) {
        query += ' OFFSET ?';
        params.push(options.offset);
      }
    }

    try {
      const stmt = this.db.prepare(query);
      const rows = stmt.all(...params) as any[];

      return rows.map(row => this.rowToTemplate(row));
    } catch (error) {
      logger.error('Failed to search templates', { error, options });
      throw error;
    }
  }

  /**
   * Update template usage statistics
   */
  public async updateTemplateUsage(
    templateId: string,
    success: boolean,
    processingTime: number,
    errorMessage?: string
  ): Promise<void> {
    const template = await this.loadTemplate(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    // Initialize usageStats if not present
    if (!template.metadata.usageStats) {
      template.metadata.usageStats = {
        totalUsage: 0,
        successRate: 0,
        averageProcessingTime: 0,
        lastUsed: new Date(),
        commonErrors: [],
        userFeedback: 0,
      };
    }

    // Update usage statistics
    template.metadata.usageStats.totalUsage++;
    template.metadata.usageStats.lastUsed = new Date();

    if (success) {
      const currentSuccessCount = Math.floor(
        template.metadata.usageStats.successRate * (template.metadata.usageStats.totalUsage - 1)
      );
      template.metadata.usageStats.successRate =
        (currentSuccessCount + 1) / template.metadata.usageStats.totalUsage;
    } else {
      const currentSuccessCount = Math.floor(
        template.metadata.usageStats.successRate * (template.metadata.usageStats.totalUsage - 1)
      );
      template.metadata.usageStats.successRate =
        currentSuccessCount / template.metadata.usageStats.totalUsage;

      if (errorMessage) {
        template.metadata.usageStats.commonErrors.push(errorMessage);
        // Keep only last 10 errors
        if (template.metadata.usageStats.commonErrors.length > 10) {
          template.metadata.usageStats.commonErrors =
            template.metadata.usageStats.commonErrors.slice(-10);
        }
      }
    }

    // Update average processing time
    const currentAverage = template.metadata.usageStats.averageProcessingTime;
    const totalUsage = template.metadata.usageStats.totalUsage;
    template.metadata.usageStats.averageProcessingTime =
      (currentAverage * (totalUsage - 1) + processingTime) / totalUsage;

    template.updatedAt = new Date();

    await this.saveTemplate(template);

    logger.debug('Template usage updated', {
      templateId,
      success,
      totalUsage: template.metadata.usageStats.totalUsage,
      successRate: template.metadata.usageStats.successRate,
    });
  }

  /**
   * Create a new version of a template
   */
  public async createTemplateVersion(
    templateId: string,
    changes: string[],
    updatedTemplate: Partial<FormTemplate>
  ): Promise<FormTemplate> {
    const existingTemplate = await this.loadTemplate(templateId);
    if (!existingTemplate) {
      throw new Error(`Template not found: ${templateId}`);
    }

    // Create new version
    const newVersion = this.incrementVersion(existingTemplate.version);
    const versionId = uuidv4();

    // Save version history
    const versionStmt = this.db.prepare(`
      INSERT INTO template_versions (
        id, template_id, version, changes, created_at, created_by, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `);

    versionStmt.run(
      versionId,
      templateId,
      existingTemplate.version,
      JSON.stringify(changes),
      new Date().toISOString(),
      'system',
      false
    );

    // Update template
    const updatedTemplateData: FormTemplate = {
      ...existingTemplate,
      ...updatedTemplate,
      version: newVersion,
      updatedAt: new Date(),
    };

    await this.saveTemplate(updatedTemplateData);

    logger.info('Template version created', {
      templateId,
      oldVersion: existingTemplate.version,
      newVersion,
      changes: changes.length,
    });

    return updatedTemplateData;
  }

  /**
   * Export template to JSON
   */
  public async exportTemplate(templateId: string): Promise<string> {
    const template = await this.loadTemplate(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    return JSON.stringify(template, null, 2);
  }

  /**
   * Import template from JSON
   */
  public async importTemplate(templateJson: string): Promise<FormTemplate> {
    try {
      const template = JSON.parse(templateJson) as FormTemplate;

      // Validate template structure
      await this.validateTemplate(template);

      // Generate new ID to avoid conflicts
      template.id = uuidv4();
      template.createdAt = new Date();
      template.updatedAt = new Date();

      await this.saveTemplate(template);

      logger.info('Template imported successfully', { templateId: template.id });
      return template;
    } catch (error) {
      logger.error('Failed to import template', { error });
      throw new DocumentProcessingError(
        'Failed to import template',
        'TEMPLATE_IMPORT_FAILED',
        '',
        'template_import'
      );
    }
  }

  /**
   * Delete template
   */
  public async deleteTemplate(templateId: string): Promise<void> {
    const stmt = this.db.prepare('DELETE FROM templates WHERE id = ?');
    const result = stmt.run(templateId);

    if (result.changes === 0) {
      throw new Error(`Template not found: ${templateId}`);
    }

    // Also delete version history
    const versionStmt = this.db.prepare('DELETE FROM template_versions WHERE template_id = ?');
    versionStmt.run(templateId);

    logger.info('Template deleted', { templateId });
  }

  /**
   * Validate template integrity
   */
  public async validateTemplate(template: FormTemplate): Promise<boolean> {
    // Check required fields
    if (!template.id || !template.name || !template.documentType) {
      throw new Error('Template missing required fields');
    }

    // Validate field mappings
    for (const mapping of template.fieldMappings) {
      if (!mapping.sourceField || !mapping.targetField) {
        throw new Error('Invalid field mapping');
      }
    }

    // Validate coordinate mappings
    for (const coordMapping of template.coordinateMappings) {
      if (!coordMapping.sourceCoordinates || !coordMapping.targetField) {
        throw new Error('Invalid coordinate mapping');
      }
    }

    // Validate variables
    for (const variable of template.variables) {
      if (!variable.name || !variable.type) {
        throw new Error('Invalid template variable');
      }
    }

    return true;
  }

  /**
   * Helper methods
   */
  private rowToTemplate(row: any): FormTemplate {
    return {
      id: row.id,
      name: row.name,
      documentType: row.document_type,
      version: row.version,
      description: row.description,
      fieldMappings: JSON.parse(row.field_mappings || '[]'),
      coordinateMappings: JSON.parse(row.coordinate_mappings || '[]'),
      variables: JSON.parse(row.variables || '[]'),
      validationRules: JSON.parse(row.validation_rules || '[]'),
      metadata: JSON.parse(row.metadata || '{}'),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      lastUsed: row.last_used ? new Date(row.last_used) : new Date(),
    };
  }

  private async generateTemplateVariables(
    formFields: FormField[],
    _mappings: FieldMapping[],
    options: TemplateCreationOptions
  ): Promise<TemplateVariable[]> {
    const variables: TemplateVariable[] = [];

    if (options.autoDetectVariables) {
      // Auto-detect variables from field names and mappings
      for (const field of formFields) {
        if (this.isVariableField(field)) {
          variables.push({
            name: field.name,
            type: this.mapFieldTypeToVariableType(field.type),
            defaultValue: field.value,
            description: `Variable for ${field.name}`,
            required: field.required,
            constraints: {},
          });
        }
      }
    }

    return variables;
  }

  private async generateValidationRules(formFields: FormField[]): Promise<ValidationRule[]> {
    const rules: ValidationRule[] = [];

    for (const field of formFields) {
      if (field.required) {
        rules.push({
          id: uuidv4(),
          name: `${field.name} Required`,
          type: 'required' as any,
          condition: {
            operator: 'IS_NOT_EMPTY' as any,
            value: null,
          },
          message: `${field.name} is required`,
          severity: 'error',
          enabled: true,
        });
      }
    }

    return rules;
  }

  private async generateTemplateDescription(
    document: Document,
    formFields: FormField[]
  ): Promise<string> {
    return `Template for ${document.name} with ${formFields.length} fields`;
  }

  private generateTemplateName(document: Document, _formFields: FormField[]): string {
    const baseName = document.name.replace(/\.[^/.]+$/, ''); // Remove extension
    return `${baseName} Template`;
  }

  private generateTags(document: Document, formFields: FormField[]): string[] {
    const tags: string[] = [];

    // Add document type tag
    tags.push(document.type);

    // Add field type tags
    const fieldTypes = [...new Set(formFields.map(f => f.type))];
    tags.push(...fieldTypes);

    // Add common form tags based on field names
    const fieldNames = formFields.map(f => f.name.toLowerCase()).join(' ');
    if (fieldNames.includes('tax')) tags.push('tax');
    if (fieldNames.includes('invoice')) tags.push('invoice');
    if (fieldNames.includes('application')) tags.push('application');
    if (fieldNames.includes('form')) tags.push('form');

    return tags;
  }

  private detectCategory(_document: Document, formFields: FormField[]): string {
    const fieldNames = formFields.map(f => f.name.toLowerCase()).join(' ');
    const documentName = _document.name.toLowerCase();

    if (fieldNames.includes('tax') || documentName.includes('tax')) return 'tax';
    if (fieldNames.includes('invoice') || documentName.includes('invoice')) return 'financial';
    if (fieldNames.includes('application') || documentName.includes('application'))
      return 'application';
    if (fieldNames.includes('contract') || documentName.includes('contract')) return 'legal';

    return 'general';
  }

  private detectSubcategory(_document: Document, formFields: FormField[]): string {
    const category = this.detectCategory(_document, formFields);

    switch (category) {
      case 'tax':
        return _document.name.toLowerCase().includes('1040') ? '1040' : 'general';
      case 'financial':
        return 'invoice';
      case 'application':
        return 'form';
      default:
        return 'standard';
    }
  }

  private isVariableField(field: FormField): boolean {
    // Fields that are likely to be variables (user input fields)
    return !field.readonly && field.type !== 'button';
  }

  private mapFieldTypeToVariableType(fieldType: string): VariableType {
    const typeMap: Record<string, VariableType> = {
      text: VariableType.STRING,
      number: VariableType.NUMBER,
      date: VariableType.DATE,
      checkbox: VariableType.BOOLEAN,
      email: VariableType.STRING,
      phone: VariableType.STRING,
    };

    return typeMap[fieldType] || VariableType.STRING;
  }

  private incrementVersion(version: string): string {
    const parts = version.split('.');
    const patch = parseInt(parts[2] || '0') + 1;
    return `${parts[0]}.${parts[1]}.${patch}`;
  }

  private mergeDefaultOptions(options: Partial<TemplateCreationOptions>): TemplateCreationOptions {
    return {
      includeCoordinates: options.includeCoordinates ?? true,
      includeValidation: options.includeValidation ?? true,
      includeVariables: options.includeVariables ?? true,
      autoDetectVariables: options.autoDetectVariables ?? true,
      generateDescription: options.generateDescription ?? true,
    };
  }
}

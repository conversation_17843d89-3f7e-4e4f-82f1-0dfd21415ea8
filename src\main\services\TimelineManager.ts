import { Database } from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import { app } from 'electron';
import path from 'path';
import {
  TimelineEntry,
  CheckpointId,
  ApplicationState,
  DiffResult,
  VisualDiff,
  MergeResult,
  BranchId,
  DocumentVersion,
  ActionType,
  TimelineEntryType,
  TimelinePriority,
  UndoRedoState,
  TimelineQuery,
  CompressionAlgorithm,
  Branch,
  MergeConflict,
  ConflictResolution,
  ResolutionStrategy,
  ChangeType,
} from '../../shared/types/Timeline';
import { CompressionEngine } from './CompressionEngine';
import { DiffEngine } from './DiffEngine';

export interface TimelineManagerConfig {
  databasePath?: string;
  maxUndoStackSize?: number;
  compressionEnabled?: boolean;
  compressionAlgorithm?: CompressionAlgorithm;
  autoCheckpointInterval?: number;
  maxCheckpointAge?: number;
}

export class TimelineManager {
  private db!: Database;
  private compressionEngine: CompressionEngine;
  private diffEngine: DiffEngine;
  private currentBranch: string = 'main';
  private undoStack: CheckpointId[] = [];
  private redoStack: CheckpointId[] = [];
  private config: Required<TimelineManagerConfig>;
  private currentSessionId: string;

  constructor(config: TimelineManagerConfig = {}) {
    this.config = {
      databasePath: config.databasePath || path.join(app.getPath('userData'), 'timeline.db'),
      maxUndoStackSize: config.maxUndoStackSize || 100,
      compressionEnabled: config.compressionEnabled ?? true,
      compressionAlgorithm: config.compressionAlgorithm || CompressionAlgorithm.LZ4,
      autoCheckpointInterval: config.autoCheckpointInterval || 300000, // 5 minutes
      maxCheckpointAge: config.maxCheckpointAge || 2592000000, // 30 days
    };

    this.currentSessionId = uuidv4();
    this.compressionEngine = new CompressionEngine();
    this.diffEngine = new DiffEngine();
    this.initializeDatabase();
    this.setupAutoCheckpoint();
  }

  private initializeDatabase(): void {
    // Database connection is handled by the migration system
    // This method sets up any runtime-specific configurations
    this.db = require('../database/connection').getDatabase();
  }

  private setupAutoCheckpoint(): void {
    if (this.config.autoCheckpointInterval > 0) {
      setInterval(() => {
        this.createAutoCheckpoint();
      }, this.config.autoCheckpointInterval);
    }
  }

  // Core timeline operations
  async createCheckpoint(state: ApplicationState, description?: string): Promise<CheckpointId> {
    const checkpointId = uuidv4();
    const compressedState = this.config.compressionEnabled
      ? await this.compressionEngine.compress(JSON.stringify(state), {
          algorithm: this.config.compressionAlgorithm,
          level: 6,
        })
      : Buffer.from(JSON.stringify(state));

    const timelineEntry: Partial<TimelineEntry> = {
      id: uuidv4(),
      type: TimelineEntryType.CHECKPOINT,
      action: ActionType.CHECKPOINT_CREATED,
      description: description || `Checkpoint created at ${new Date().toISOString()}`,
      afterState: {
        id: uuidv4(),
        checkpointId,
        state,
        compression: {
          algorithm: this.config.compressionAlgorithm,
          level: 6,
        },
        metadata: {
          size: JSON.stringify(state).length,
          compressedSize: compressedState.length,
          compressionRatio: compressedState.length / JSON.stringify(state).length,
          checksum: this.calculateChecksum(JSON.stringify(state)),
          dependencies: [],
          tags: [],
          description: description || '',
        },
        createdAt: new Date(),
      },
      affectedDocuments: state.documents.map(doc => doc.documentId),
      userId: 'system',
      sessionId: this.currentSessionId,
      metadata: {
        warningCount: 0,
        tags: ['checkpoint'],
        category: 'system',
        priority: TimelinePriority.NORMAL,
        isReversible: true,
        dependencies: [],
      },
      createdAt: new Date(),
      processingTime: 0,
    };

    const startTime = Date.now();

    try {
      // Store checkpoint in database
      const stmt = this.db.prepare(`
        INSERT INTO timeline (
          id, action_type, description, category, after_state, compression_method,
          after_state_size, affected_documents, user_id, session_id, created_at,
          metadata, is_reversible, checkpoint_id, execution_time_ms
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const executionTime = Date.now() - startTime;

      stmt.run(
        timelineEntry.id,
        timelineEntry.action,
        timelineEntry.description,
        'system',
        compressedState,
        this.config.compressionAlgorithm,
        JSON.stringify(state).length,
        JSON.stringify(timelineEntry.affectedDocuments),
        timelineEntry.userId,
        timelineEntry.sessionId,
        timelineEntry.createdAt?.toISOString(),
        JSON.stringify(timelineEntry.metadata),
        1,
        checkpointId,
        executionTime
      );

      // Update undo/redo stacks
      this.undoStack.push(checkpointId);
      if (this.undoStack.length > this.config.maxUndoStackSize) {
        this.undoStack.shift();
      }
      this.redoStack = []; // Clear redo stack on new checkpoint

      return checkpointId;
    } catch (error) {
      throw new Error(
        `Failed to create checkpoint: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async restoreCheckpoint(checkpointId: CheckpointId): Promise<ApplicationState> {
    try {
      const stmt = this.db.prepare(`
        SELECT after_state, compression_method, after_state_size
        FROM timeline
        WHERE checkpoint_id = ? AND action_type = ?
      `);

      const result = stmt.get(checkpointId, ActionType.CHECKPOINT_CREATED) as
        | {
            after_state: Buffer;
            compression_method: string;
            after_state_size: number;
          }
        | undefined;

      if (!result) {
        throw new Error(`Checkpoint ${checkpointId} not found`);
      }

      // Decompress state
      const stateJson = this.config.compressionEnabled
        ? await this.compressionEngine.decompress(result.after_state, {
            algorithm: result.compression_method as CompressionAlgorithm,
          })
        : result.after_state.toString();

      const state: ApplicationState = JSON.parse(stateJson);

      // Create timeline entry for restoration
      await this.addTimelineEntry({
        type: TimelineEntryType.SYSTEM_EVENT,
        action: ActionType.CHECKPOINT_RESTORED,
        description: `Restored checkpoint ${checkpointId}`,
        affectedDocuments: state.documents.map(doc => doc.documentId),
        metadata: {
          warningCount: 0,
          tags: ['restore', 'checkpoint'],
          category: 'system',
          priority: TimelinePriority.HIGH,
          isReversible: false,
          dependencies: [checkpointId],
        },
      });

      return state;
    } catch (error) {
      throw new Error(
        `Failed to restore checkpoint: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async undo(): Promise<ApplicationState | null> {
    if (this.undoStack.length === 0) {
      return null;
    }

    const checkpointId = this.undoStack.pop()!;
    this.redoStack.push(checkpointId);

    return await this.restoreCheckpoint(checkpointId);
  }

  async redo(): Promise<ApplicationState | null> {
    if (this.redoStack.length === 0) {
      return null;
    }

    const checkpointId = this.redoStack.pop()!;
    this.undoStack.push(checkpointId);

    return await this.restoreCheckpoint(checkpointId);
  }

  async getTimeline(query?: TimelineQuery): Promise<TimelineEntry[]> {
    let sql = 'SELECT * FROM timeline';
    const params: any[] = [];
    const conditions: string[] = [];

    if (query?.filter) {
      if (query.filter.types?.length) {
        conditions.push(`action_type IN (${query.filter.types.map(() => '?').join(', ')})`);
        params.push(...query.filter.types);
      }

      if (query.filter.dateRange) {
        conditions.push('created_at BETWEEN ? AND ?');
        params.push(
          query.filter.dateRange.start.toISOString(),
          query.filter.dateRange.end.toISOString()
        );
      }

      if (query.filter.users?.length) {
        conditions.push(`user_id IN (${query.filter.users.map(() => '?').join(', ')})`);
        params.push(...query.filter.users);
      }

      if (query.filter.textSearch) {
        conditions.push('description LIKE ?');
        params.push(`%${query.filter.textSearch}%`);
      }
    }

    if (conditions.length > 0) {
      sql += ' WHERE ' + conditions.join(' AND ');
    }

    if (query?.sort) {
      sql += ` ORDER BY ${query.sort.field} ${query.sort.direction}`;
    } else {
      sql += ' ORDER BY sequence_number DESC';
    }

    if (query?.pagination) {
      sql += ` LIMIT ${query.pagination.limit} OFFSET ${query.pagination.offset}`;
    }

    const stmt = this.db.prepare(sql);
    const results = stmt.all(...params) as any[];

    return results.map(row => this.mapRowToTimelineEntry(row));
  }

  async createDiff(before: ApplicationState, after: ApplicationState): Promise<DiffResult> {
    return await this.diffEngine.createDiff(before, after);
  }

  async createVisualDiff(before: any, after: any): Promise<VisualDiff> {
    return await this.diffEngine.createVisualDiff(before, after);
  }

  private calculateChecksum(data: string): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  private async createAutoCheckpoint(): Promise<void> {
    // This would be called by the application to create automatic checkpoints
    // Implementation depends on how the application state is managed
  }

  private mapRowToTimelineEntry(row: any): TimelineEntry {
    return {
      id: row.id,
      type: row.action_type as TimelineEntryType,
      action: row.action_type as ActionType,
      description: row.description,
      affectedDocuments: JSON.parse(row.affected_documents || '[]'),
      userId: row.user_id,
      sessionId: row.session_id,
      metadata: JSON.parse(row.metadata || '{}'),
      createdAt: new Date(row.created_at),
      processingTime: row.execution_time_ms || 0,
    };
  }

  async addTimelineEntry(entry: Partial<TimelineEntry>): Promise<string> {
    const id = uuidv4();
    const now = new Date();

    const stmt = this.db.prepare(`
      INSERT INTO timeline (
        id, action_type, description, category, user_id, session_id,
        created_at, metadata, affected_documents, execution_time_ms
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      id,
      entry.action,
      entry.description,
      entry.metadata?.category || 'user',
      entry.userId || 'system',
      entry.sessionId || this.currentSessionId,
      now.toISOString(),
      JSON.stringify(entry.metadata || {}),
      JSON.stringify(entry.affectedDocuments || []),
      entry.processingTime || 0
    );

    return id;
  }

  getUndoRedoState(): UndoRedoState {
    return {
      undoStack: [...this.undoStack],
      redoStack: [...this.redoStack],
      currentPosition: this.undoStack.length,
      maxStackSize: this.config.maxUndoStackSize,
      canUndo: this.undoStack.length > 0,
      canRedo: this.redoStack.length > 0,
    };
  }

  // Branch management operations
  async createBranch(
    fromCheckpoint: CheckpointId,
    name: string,
    description?: string
  ): Promise<BranchId> {
    const branchId = uuidv4();

    try {
      // Check if branch name already exists
      const existingBranch = this.db.prepare('SELECT id FROM branches WHERE name = ?').get(name);
      if (existingBranch) {
        throw new Error(`Branch with name '${name}' already exists`);
      }

      // Verify the source checkpoint exists
      const checkpoint = this.db
        .prepare(
          `
        SELECT id FROM timeline WHERE checkpoint_id = ? AND action_type = ?
      `
        )
        .get(fromCheckpoint, ActionType.CHECKPOINT_CREATED);

      if (!checkpoint) {
        throw new Error(`Source checkpoint ${fromCheckpoint} not found`);
      }

      // Create the branch
      const stmt = this.db.prepare(`
        INSERT INTO branches (id, name, description, parent_branch, head_checkpoint, created_by, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);

      stmt.run(
        branchId,
        name,
        description || `Branch created from checkpoint ${fromCheckpoint}`,
        this.currentBranch,
        fromCheckpoint,
        'system',
        new Date().toISOString()
      );

      // Create timeline entry for branch creation
      await this.addTimelineEntry({
        type: TimelineEntryType.BRANCH_OPERATION,
        action: ActionType.BRANCH_CREATED,
        description: `Created branch '${name}' from checkpoint ${fromCheckpoint}`,
        affectedDocuments: [],
        metadata: {
          warningCount: 0,
          tags: ['branch', 'create'],
          category: 'system',
          priority: TimelinePriority.NORMAL,
          isReversible: true,
          dependencies: [fromCheckpoint],
        },
      });

      return branchId;
    } catch (error) {
      throw new Error(
        `Failed to create branch: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async switchBranch(branchId: BranchId): Promise<void> {
    try {
      // Verify the branch exists
      const branch = this.db
        .prepare(
          `
        SELECT id, name, head_checkpoint FROM branches WHERE id = ?
      `
        )
        .get(branchId) as { id: string; name: string; head_checkpoint: string } | undefined;

      if (!branch) {
        throw new Error(`Branch ${branchId} not found`);
      }

      // Switch to the branch
      this.currentBranch = branchId;

      // Restore the head checkpoint of the branch
      await this.restoreCheckpoint(branch.head_checkpoint);

      // Create timeline entry for branch switch
      await this.addTimelineEntry({
        type: TimelineEntryType.BRANCH_OPERATION,
        action: ActionType.BRANCH_SWITCHED,
        description: `Switched to branch '${branch.name}'`,
        affectedDocuments: [],
        metadata: {
          warningCount: 0,
          tags: ['branch', 'switch'],
          category: 'system',
          priority: TimelinePriority.NORMAL,
          isReversible: true,
          dependencies: [branch.head_checkpoint],
        },
      });
    } catch (error) {
      throw new Error(
        `Failed to switch branch: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async mergeBranches(sourceBranch: BranchId, targetBranch: BranchId): Promise<MergeResult> {
    try {
      // Get branch information
      const sourceInfo = this.db
        .prepare(
          `
        SELECT id, name, head_checkpoint FROM branches WHERE id = ?
      `
        )
        .get(sourceBranch) as { id: string; name: string; head_checkpoint: string } | undefined;

      const targetInfo = this.db
        .prepare(
          `
        SELECT id, name, head_checkpoint FROM branches WHERE id = ?
      `
        )
        .get(targetBranch) as { id: string; name: string; head_checkpoint: string } | undefined;

      if (!sourceInfo || !targetInfo) {
        throw new Error('Source or target branch not found');
      }

      // Get the states from both branches
      const sourceState = await this.restoreCheckpoint(sourceInfo.head_checkpoint);
      const targetState = await this.restoreCheckpoint(targetInfo.head_checkpoint);

      // Create diff to identify conflicts
      const diff = await this.createDiff(targetState, sourceState);
      const conflicts: MergeConflict[] = [];
      const resolutions: ConflictResolution[] = [];

      // Analyze changes for conflicts
      for (const change of diff.changes) {
        if (change.type === ChangeType.MODIFIED) {
          // This is a potential conflict
          conflicts.push({
            id: uuidv4(),
            type: 'content' as any,
            path: change.path,
            sourceValue: change.afterValue,
            targetValue: change.beforeValue,
            description: `Conflicting changes in ${change.path}`,
            severity: 'medium' as any,
          });
        }
      }

      // For now, auto-resolve conflicts by taking source values
      for (const conflict of conflicts) {
        resolutions.push({
          conflictId: conflict.id,
          strategy: ResolutionStrategy.TAKE_SOURCE,
          resolvedValue: conflict.sourceValue,
          reasoning: 'Auto-resolved by taking source branch changes',
          confidence: 0.8,
          isManual: false,
          resolvedAt: new Date(),
        });
      }

      // Create merged state (simplified - take source state for now)
      const mergedState = sourceState;
      const resultCheckpoint = await this.createCheckpoint(
        mergedState,
        `Merged ${sourceInfo.name} into ${targetInfo.name}`
      );

      // Update target branch head
      this.db
        .prepare(
          `
        UPDATE branches SET head_checkpoint = ? WHERE id = ?
      `
        )
        .run(resultCheckpoint, targetBranch);

      // Create timeline entry for merge
      await this.addTimelineEntry({
        type: TimelineEntryType.MERGE_OPERATION,
        action: ActionType.BRANCHES_MERGED,
        description: `Merged branch '${sourceInfo.name}' into '${targetInfo.name}'`,
        affectedDocuments: mergedState.documents.map(doc => doc.documentId),
        metadata: {
          warningCount: conflicts.length,
          tags: ['merge', 'branch'],
          category: 'system',
          priority: TimelinePriority.HIGH,
          isReversible: true,
          dependencies: [sourceInfo.head_checkpoint, targetInfo.head_checkpoint],
        },
      });

      const mergeResult: MergeResult = {
        id: uuidv4(),
        sourceBranch,
        targetBranch,
        resultCheckpoint,
        conflicts,
        resolutions,
        statistics: {
          totalConflicts: conflicts.length,
          resolvedConflicts: resolutions.length,
          unresolvedConflicts: conflicts.length - resolutions.length,
          automaticResolutions: resolutions.filter(r => !r.isManual).length,
          manualResolutions: resolutions.filter(r => r.isManual).length,
          processingTime: 0, // Would be calculated in real implementation
        },
        success: conflicts.length === resolutions.length,
        createdAt: new Date(),
      };

      return mergeResult;
    } catch (error) {
      throw new Error(
        `Failed to merge branches: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async getBranches(): Promise<Branch[]> {
    const stmt = this.db.prepare(`
      SELECT id, name, description, parent_branch, head_checkpoint, created_by, created_at
      FROM branches
      ORDER BY created_at DESC
    `);

    const results = stmt.all() as any[];

    return results.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description,
      parentBranch: row.parent_branch,
      headCheckpoint: row.head_checkpoint,
      createdAt: new Date(row.created_at),
      createdBy: row.created_by,
      isActive: row.id === this.currentBranch,
      isProtected: row.name === 'main',
      metadata: {
        tags: [],
        category: 'branch',
        purpose: row.description,
        collaborators: [],
        permissions: {
          canRead: ['*'],
          canWrite: ['*'],
          canMerge: ['*'],
          canDelete: row.name !== 'main' ? ['*'] : [],
        },
      },
    }));
  }

  async getDocumentHistory(documentId: string): Promise<DocumentVersion[]> {
    const stmt = this.db.prepare(`
      SELECT * FROM document_versions
      WHERE document_id = ?
      ORDER BY version_sequence DESC
    `);

    const results = stmt.all(documentId) as any[];

    return results.map(row => ({
      id: row.id,
      documentId: row.document_id,
      version: row.version_sequence,
      checkpointId: row.checkpoint_id || '',
      branchId: this.currentBranch,
      content: row.content_diff,
      metadata: {
        author: row.created_by,
        description: row.change_summary,
        tags: JSON.parse(row.tags || '[]'),
        size: row.content_size,
        checksum: row.content_hash,
        contentType: 'application/octet-stream',
      },
      parentVersion: row.parent_version_id,
      childVersions: [],
      createdAt: new Date(row.created_at),
    }));
  }

  getCurrentBranch(): string {
    return this.currentBranch;
  }

  async exportTimeline(format: 'json' | 'git'): Promise<Buffer> {
    const timeline = await this.getTimeline();

    if (format === 'json') {
      return Buffer.from(JSON.stringify(timeline, null, 2));
    } else if (format === 'git') {
      // Convert to git-like format
      const gitLog = timeline.map(entry => ({
        commit: entry.id,
        author: entry.userId,
        date: entry.createdAt.toISOString(),
        message: entry.description,
        files: entry.affectedDocuments,
      }));
      return Buffer.from(JSON.stringify(gitLog, null, 2));
    } else {
      throw new Error(`Unsupported export format: ${format}`);
    }
  }
}

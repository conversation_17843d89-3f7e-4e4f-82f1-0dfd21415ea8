import { v4 as uuidv4 } from 'uuid';
import * as crypto from 'crypto';
import { getDocument, GlobalWorkerOptions } from 'pdfjs-dist';
import { createCanvas } from 'canvas';
import sharp from 'sharp';
import { Document, FormField, DocumentType } from '../../shared/types/Document';
import { VisualDiffType, ImageData } from '../../shared/types/Timeline';

// Set up PDF.js worker
GlobalWorkerOptions.workerSrc = require.resolve('pdfjs-dist/build/pdf.worker.js');

export interface PDFDiffOptions {
  resolution?: number; // DPI for rendering
  enablePixelDiff?: boolean;
  enableAnnotationDiff?: boolean;
  enableFormFieldDiff?: boolean;
  highlightColor?: string;
  threshold?: number; // Pixel difference threshold (0-1)
  enableCaching?: boolean;
}

export interface PDFPageDiff {
  pageNumber: number;
  beforeImage: ImageData;
  afterImage: ImageData;
  diffImage: ImageData;
  pixelDifferences: number;
  similarity: number;
  annotations: AnnotationDiff[];
  formFields: FormFieldDiff[];
}

export interface AnnotationDiff {
  id: string;
  type: 'added' | 'removed' | 'modified';
  beforeAnnotation?: any;
  afterAnnotation?: any;
  coordinates: { x: number; y: number; width: number; height: number };
}

export interface FormFieldDiff {
  id: string;
  fieldName: string;
  type: 'added' | 'removed' | 'modified';
  beforeValue?: any;
  afterValue?: any;
  coordinates: { x: number; y: number; width: number; height: number };
}

export interface PDFDiffResult {
  id: string;
  type: VisualDiffType.PDF;
  pages: PDFPageDiff[];
  summary: PDFDiffSummary;
  metadata: {
    beforeDocument: string;
    afterDocument: string;
    resolution: number;
    threshold: number;
    processingTime: number;
    createdAt: Date;
  };
}

export interface PDFDiffSummary {
  totalPages: number;
  changedPages: number;
  totalPixelDifferences: number;
  averageSimilarity: number;
  annotationChanges: number;
  formFieldChanges: number;
}

export class PDFDiffEngine {
  private options: Required<PDFDiffOptions>;
  private cache: Map<string, any>;

  constructor(options: PDFDiffOptions = {}) {
    this.options = {
      resolution: options.resolution || 150,
      enablePixelDiff: options.enablePixelDiff ?? true,
      enableAnnotationDiff: options.enableAnnotationDiff ?? true,
      enableFormFieldDiff: options.enableFormFieldDiff ?? true,
      highlightColor: options.highlightColor || '#ff0000',
      threshold: options.threshold || 0.01,
      enableCaching: options.enableCaching ?? true,
    };

    this.cache = new Map();
  }

  /**
   * Create visual diff between two PDF documents
   */
  async createPDFDiff(beforeDoc: Document, afterDoc: Document): Promise<PDFDiffResult> {
    const startTime = Date.now();

    if (beforeDoc.type !== DocumentType.PDF || afterDoc.type !== DocumentType.PDF) {
      throw new Error('Both documents must be PDF type');
    }

    const beforeBuffer = Buffer.isBuffer(beforeDoc.content)
      ? beforeDoc.content
      : Buffer.from(beforeDoc.content as any);
    const afterBuffer = Buffer.isBuffer(afterDoc.content)
      ? afterDoc.content
      : Buffer.from(afterDoc.content as any);

    // Load PDF documents
    const beforePdf = await getDocument({ data: beforeBuffer }).promise;
    const afterPdf = await getDocument({ data: afterBuffer }).promise;

    const maxPages = Math.max(beforePdf.numPages, afterPdf.numPages);
    const pages: PDFPageDiff[] = [];

    // Process each page
    for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
      const pageDiff = await this.createPageDiff(
        beforePdf,
        afterPdf,
        pageNum,
        beforeDoc.id,
        afterDoc.id
      );
      if (pageDiff) {
        pages.push(pageDiff);
      }
    }

    // Calculate summary statistics
    const summary = this.calculateSummary(pages);

    const result: PDFDiffResult = {
      id: uuidv4(),
      type: VisualDiffType.PDF,
      pages,
      summary,
      metadata: {
        beforeDocument: beforeDoc.id,
        afterDocument: afterDoc.id,
        resolution: this.options.resolution,
        threshold: this.options.threshold,
        processingTime: Date.now() - startTime,
        createdAt: new Date(),
      },
    };

    return result;
  }

  /**
   * Create diff for a specific page
   */
  private async createPageDiff(
    beforePdf: any,
    afterPdf: any,
    pageNumber: number,
    beforeDocId: string,
    afterDocId: string
  ): Promise<PDFPageDiff | null> {
    try {
      // Check cache first
      const cacheKey = this.generatePageCacheKey(beforeDocId, afterDocId, pageNumber);
      if (this.options.enableCaching && this.cache.has(cacheKey)) {
        return this.cache.get(cacheKey);
      }

      // Render pages to images
      const beforeImage =
        pageNumber <= beforePdf.numPages
          ? await this.renderPageToImage(beforePdf, pageNumber)
          : null;
      const afterImage =
        pageNumber <= afterPdf.numPages ? await this.renderPageToImage(afterPdf, pageNumber) : null;

      if (!beforeImage && !afterImage) {
        return null;
      }

      // Create placeholder for missing pages
      const beforeImageData = beforeImage || (await this.createBlankImage());
      const afterImageData = afterImage || (await this.createBlankImage());

      // Create pixel-level diff
      const diffImage = await this.createPixelDiff(beforeImageData, afterImageData);
      const pixelDifferences = await this.countPixelDifferences(beforeImageData, afterImageData);
      const similarity = this.calculateImageSimilarity(
        beforeImageData,
        afterImageData,
        pixelDifferences
      );

      // Extract annotations and form fields if enabled
      const annotations = this.options.enableAnnotationDiff
        ? await this.compareAnnotations(beforePdf, afterPdf, pageNumber)
        : [];
      const formFields = this.options.enableFormFieldDiff
        ? await this.compareFormFields(beforePdf, afterPdf, pageNumber)
        : [];

      const pageDiff: PDFPageDiff = {
        pageNumber,
        beforeImage: beforeImageData,
        afterImage: afterImageData,
        diffImage,
        pixelDifferences,
        similarity,
        annotations,
        formFields,
      };

      // Cache the result
      if (this.options.enableCaching) {
        this.cache.set(cacheKey, pageDiff);
      }

      return pageDiff;
    } catch (error) {
      console.error(`Error creating diff for page ${pageNumber}:`, error);
      return null;
    }
  }

  /**
   * Render PDF page to image
   */
  private async renderPageToImage(pdf: any, pageNumber: number): Promise<ImageData> {
    const page = await pdf.getPage(pageNumber);
    const viewport = page.getViewport({ scale: this.options.resolution / 72 });

    const canvas = createCanvas(viewport.width, viewport.height);
    const context = canvas.getContext('2d');

    const renderContext = {
      canvasContext: context,
      viewport: viewport,
    };

    await page.render(renderContext).promise;

    const imageBuffer = canvas.toBuffer('image/png');

    return {
      width: viewport.width,
      height: viewport.height,
      format: 'png',
      data: imageBuffer.buffer.slice(
        imageBuffer.byteOffset,
        imageBuffer.byteOffset + imageBuffer.byteLength
      ),
    };
  }

  /**
   * Create blank image for missing pages
   */
  private async createBlankImage(): Promise<ImageData> {
    const width = 612; // Standard letter size at 72 DPI
    const height = 792;

    const canvas = createCanvas(width, height);
    const context = canvas.getContext('2d');

    // Fill with white background
    context.fillStyle = '#ffffff';
    context.fillRect(0, 0, width, height);

    const imageBuffer = canvas.toBuffer('image/png');

    return {
      width,
      height,
      format: 'png',
      data: imageBuffer.buffer.slice(
        imageBuffer.byteOffset,
        imageBuffer.byteOffset + imageBuffer.byteLength
      ),
    };
  }

  /**
   * Create pixel-level diff image with highlights
   */
  private async createPixelDiff(beforeImage: ImageData, afterImage: ImageData): Promise<ImageData> {
    const width = Math.max(beforeImage.width, afterImage.width);
    const height = Math.max(beforeImage.height, afterImage.height);

    // Use Sharp for efficient image processing
    const beforeSharp = sharp(Buffer.from(beforeImage.data)).resize(width, height);
    const afterSharp = sharp(Buffer.from(afterImage.data)).resize(width, height);

    const beforeBuffer = await beforeSharp.raw().toBuffer();
    const afterBuffer = await afterSharp.raw().toBuffer();

    const diffBuffer = Buffer.alloc(beforeBuffer.length);

    // Compare pixels and highlight differences
    for (let i = 0; i < beforeBuffer.length; i += 3) {
      const rDiff = Math.abs((beforeBuffer[i] || 0) - (afterBuffer[i] || 0));
      const gDiff = Math.abs((beforeBuffer[i + 1] || 0) - (afterBuffer[i + 1] || 0));
      const bDiff = Math.abs((beforeBuffer[i + 2] || 0) - (afterBuffer[i + 2] || 0));

      const totalDiff = (rDiff + gDiff + bDiff) / (3 * 255);

      if (totalDiff > this.options.threshold) {
        // Highlight difference in red
        diffBuffer[i] = 255; // R
        diffBuffer[i + 1] = 0; // G
        diffBuffer[i + 2] = 0; // B
      } else {
        // Keep original pixel
        diffBuffer[i] = afterBuffer[i] || 0;
        diffBuffer[i + 1] = afterBuffer[i + 1] || 0;
        diffBuffer[i + 2] = afterBuffer[i + 2] || 0;
      }
    }

    const diffImageBuffer = await sharp(diffBuffer, {
      raw: { width, height, channels: 3 },
    })
      .png()
      .toBuffer();

    return {
      width,
      height,
      format: 'png',
      data: diffImageBuffer.buffer.slice(
        diffImageBuffer.byteOffset,
        diffImageBuffer.byteOffset + diffImageBuffer.byteLength
      ),
    };
  }

  /**
   * Count pixel differences between two images
   */
  private async countPixelDifferences(
    beforeImage: ImageData,
    afterImage: ImageData
  ): Promise<number> {
    const width = Math.max(beforeImage.width, afterImage.width);
    const height = Math.max(beforeImage.height, afterImage.height);

    const beforeSharp = sharp(Buffer.from(beforeImage.data)).resize(width, height);
    const afterSharp = sharp(Buffer.from(afterImage.data)).resize(width, height);

    const beforeBuffer = await beforeSharp.raw().toBuffer();
    const afterBuffer = await afterSharp.raw().toBuffer();

    let differences = 0;

    for (let i = 0; i < beforeBuffer.length; i += 3) {
      const rDiff = Math.abs((beforeBuffer[i] || 0) - (afterBuffer[i] || 0));
      const gDiff = Math.abs((beforeBuffer[i + 1] || 0) - (afterBuffer[i + 1] || 0));
      const bDiff = Math.abs((beforeBuffer[i + 2] || 0) - (afterBuffer[i + 2] || 0));

      const totalDiff = (rDiff + gDiff + bDiff) / (3 * 255);

      if (totalDiff > this.options.threshold) {
        differences++;
      }
    }

    return differences;
  }

  /**
   * Calculate image similarity percentage
   */
  private calculateImageSimilarity(
    beforeImage: ImageData,
    afterImage: ImageData,
    pixelDifferences: number
  ): number {
    const totalPixels = Math.max(
      beforeImage.width * beforeImage.height,
      afterImage.width * afterImage.height
    );
    return Math.max(0, 1 - pixelDifferences / totalPixels);
  }

  /**
   * Compare annotations between PDF pages
   */
  private async compareAnnotations(
    beforePdf: any,
    afterPdf: any,
    pageNumber: number
  ): Promise<AnnotationDiff[]> {
    const annotations: AnnotationDiff[] = [];

    try {
      const beforeAnnotations =
        pageNumber <= beforePdf.numPages
          ? await this.extractPageAnnotations(beforePdf, pageNumber)
          : [];
      const afterAnnotations =
        pageNumber <= afterPdf.numPages
          ? await this.extractPageAnnotations(afterPdf, pageNumber)
          : [];

      // Find added annotations
      for (const afterAnnotation of afterAnnotations) {
        const beforeMatch = beforeAnnotations.find(b => this.annotationsMatch(b, afterAnnotation));
        if (!beforeMatch) {
          annotations.push({
            id: uuidv4(),
            type: 'added',
            afterAnnotation,
            coordinates: this.getAnnotationCoordinates(afterAnnotation),
          });
        }
      }

      // Find removed annotations
      for (const beforeAnnotation of beforeAnnotations) {
        const afterMatch = afterAnnotations.find(a => this.annotationsMatch(beforeAnnotation, a));
        if (!afterMatch) {
          annotations.push({
            id: uuidv4(),
            type: 'removed',
            beforeAnnotation,
            coordinates: this.getAnnotationCoordinates(beforeAnnotation),
          });
        }
      }

      // Find modified annotations
      for (const beforeAnnotation of beforeAnnotations) {
        const afterMatch = afterAnnotations.find(a => this.annotationsMatch(beforeAnnotation, a));
        if (afterMatch && !this.annotationsEqual(beforeAnnotation, afterMatch)) {
          annotations.push({
            id: uuidv4(),
            type: 'modified',
            beforeAnnotation,
            afterAnnotation: afterMatch,
            coordinates: this.getAnnotationCoordinates(afterMatch),
          });
        }
      }
    } catch (error) {
      console.error(`Error comparing annotations for page ${pageNumber}:`, error);
    }

    return annotations;
  }

  /**
   * Compare form fields between PDF pages
   */
  private async compareFormFields(
    beforePdf: any,
    afterPdf: any,
    pageNumber: number
  ): Promise<FormFieldDiff[]> {
    const formFields: FormFieldDiff[] = [];

    try {
      const beforeFields =
        pageNumber <= beforePdf.numPages
          ? await this.extractPageFormFields(beforePdf, pageNumber)
          : [];
      const afterFields =
        pageNumber <= afterPdf.numPages
          ? await this.extractPageFormFields(afterPdf, pageNumber)
          : [];

      // Find added form fields
      for (const afterField of afterFields) {
        const beforeMatch = beforeFields.find(b => b.name === afterField.name);
        if (!beforeMatch) {
          formFields.push({
            id: uuidv4(),
            fieldName: afterField.name,
            type: 'added',
            afterValue: afterField.value,
            coordinates: {
              x: afterField.bounds.x,
              y: afterField.bounds.y,
              width: afterField.bounds.width,
              height: afterField.bounds.height,
            },
          });
        }
      }

      // Find removed form fields
      for (const beforeField of beforeFields) {
        const afterMatch = afterFields.find(a => a.name === beforeField.name);
        if (!afterMatch) {
          formFields.push({
            id: uuidv4(),
            fieldName: beforeField.name,
            type: 'removed',
            beforeValue: beforeField.value,
            coordinates: {
              x: beforeField.bounds.x,
              y: beforeField.bounds.y,
              width: beforeField.bounds.width,
              height: beforeField.bounds.height,
            },
          });
        }
      }

      // Find modified form fields
      for (const beforeField of beforeFields) {
        const afterMatch = afterFields.find(a => a.name === beforeField.name);
        if (afterMatch && beforeField.value !== afterMatch.value) {
          formFields.push({
            id: uuidv4(),
            fieldName: beforeField.name,
            type: 'modified',
            beforeValue: beforeField.value,
            afterValue: afterMatch.value,
            coordinates: {
              x: afterMatch.bounds.x,
              y: afterMatch.bounds.y,
              width: afterMatch.bounds.width,
              height: afterMatch.bounds.height,
            },
          });
        }
      }
    } catch (error) {
      console.error(`Error comparing form fields for page ${pageNumber}:`, error);
    }

    return formFields;
  }

  /**
   * Extract annotations from a PDF page
   */
  private async extractPageAnnotations(pdf: any, pageNumber: number): Promise<any[]> {
    try {
      const page = await pdf.getPage(pageNumber);
      const annotations = await page.getAnnotations();
      return annotations || [];
    } catch (error) {
      console.error(`Error extracting annotations from page ${pageNumber}:`, error);
      return [];
    }
  }

  /**
   * Extract form fields from a PDF page
   */
  private async extractPageFormFields(pdf: any, pageNumber: number): Promise<FormField[]> {
    try {
      const page = await pdf.getPage(pageNumber);
      const annotations = await page.getAnnotations();

      return annotations
        .filter((annotation: any) => annotation.fieldType)
        .map((annotation: any) => ({
          id: annotation.id || uuidv4(),
          name: annotation.fieldName || annotation.title || '',
          type: this.mapPDFFieldType(annotation.fieldType),
          value: annotation.fieldValue || '',
          required: annotation.required || false,
          readonly: annotation.readOnly || false,
          bounds: {
            x: annotation.rect[0],
            y: annotation.rect[1],
            width: annotation.rect[2] - annotation.rect[0],
            height: annotation.rect[3] - annotation.rect[1],
            pageNumber,
          },
        }));
    } catch (error) {
      console.error(`Error extracting form fields from page ${pageNumber}:`, error);
      return [];
    }
  }

  /**
   * Check if two annotations match (same position and type)
   */
  private annotationsMatch(annotation1: any, annotation2: any): boolean {
    if (!annotation1 || !annotation2) return false;

    const rect1 = annotation1.rect || [];
    const rect2 = annotation2.rect || [];

    return (
      annotation1.subtype === annotation2.subtype &&
      Math.abs(rect1[0] - rect2[0]) < 1 &&
      Math.abs(rect1[1] - rect2[1]) < 1 &&
      Math.abs(rect1[2] - rect2[2]) < 1 &&
      Math.abs(rect1[3] - rect2[3]) < 1
    );
  }

  /**
   * Check if two annotations are equal (same content)
   */
  private annotationsEqual(annotation1: any, annotation2: any): boolean {
    return (
      this.annotationsMatch(annotation1, annotation2) &&
      annotation1.contents === annotation2.contents &&
      annotation1.title === annotation2.title
    );
  }

  /**
   * Get annotation coordinates
   */
  private getAnnotationCoordinates(annotation: any): {
    x: number;
    y: number;
    width: number;
    height: number;
  } {
    const rect = annotation.rect || [0, 0, 0, 0];
    return {
      x: rect[0],
      y: rect[1],
      width: rect[2] - rect[0],
      height: rect[3] - rect[1],
    };
  }

  /**
   * Map PDF field type to our FormFieldType
   */
  private mapPDFFieldType(pdfFieldType: string): any {
    const typeMap: Record<string, any> = {
      Tx: 'text',
      Ch: 'select',
      Btn: 'checkbox',
      Sig: 'signature',
    };

    return typeMap[pdfFieldType] || 'text';
  }

  /**
   * Calculate summary statistics for PDF diff
   */
  private calculateSummary(pages: PDFPageDiff[]): PDFDiffSummary {
    const totalPages = pages.length;
    const changedPages = pages.filter(p => p.similarity < 0.99).length;
    const totalPixelDifferences = pages.reduce((sum, p) => sum + p.pixelDifferences, 0);
    const averageSimilarity =
      pages.length > 0 ? pages.reduce((sum, p) => sum + p.similarity, 0) / pages.length : 1.0;
    const annotationChanges = pages.reduce((sum, p) => sum + p.annotations.length, 0);
    const formFieldChanges = pages.reduce((sum, p) => sum + p.formFields.length, 0);

    return {
      totalPages,
      changedPages,
      totalPixelDifferences,
      averageSimilarity,
      annotationChanges,
      formFieldChanges,
    };
  }

  /**
   * Generate cache key for page diff
   */
  private generatePageCacheKey(
    beforeDocId: string,
    afterDocId: string,
    pageNumber: number
  ): string {
    const content = `${beforeDocId}|${afterDocId}|${pageNumber}|${this.options.resolution}|${this.options.threshold}`;
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  /**
   * Clear the cache
   */
  public clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

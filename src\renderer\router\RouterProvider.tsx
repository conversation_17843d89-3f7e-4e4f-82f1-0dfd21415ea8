import React, { useEffect } from 'react';
import {
  create<PERSON><PERSON>er<PERSON><PERSON>er,
  Router<PERSON>rovider as ReactRouterProvider,
  useLocation,
  useNavigate,
} from 'react-router-dom';
import { routes, routeMetadata } from './routes';
import { ErrorBoundary } from '../components/common/ErrorBoundary';
import { LoadingSpinner } from '../components/common/LoadingSpinner';
import { AppProviders } from '../components/providers/AppProviders';

// Create router with enhanced error handling
const router = createBrowserRouter(routes, {
  future: {
    // Enable future React Router features
    v7_normalizeFormMethod: true,
  },
});

// Navigation guard component
const NavigationGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    // Update document title based on route
    const metadata = routeMetadata[location.pathname];
    if (metadata) {
      document.title = `${metadata.title} - AI Document Processor`;
    } else {
      document.title = 'AI Document Processor';
    }

    // Log navigation for analytics
    if (window.electronAPI?.logToMain) {
      window.electronAPI.logToMain('info', 'Navigation', {
        path: location.pathname,
        search: location.search,
        hash: location.hash,
        timestamp: new Date().toISOString(),
      });
    }

    // Check for route-specific requirements
    const currentMetadata = routeMetadata[location.pathname];
    if (currentMetadata?.requiresAuth) {
      // TODO: Implement authentication check
      console.log('Route requires authentication:', location.pathname);
    }
  }, [location, navigate]);

  return <>{children}</>;
};

// Router error boundary
const RouterErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const handleRouterError = (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('Router Error:', error, errorInfo);

    if (window.electronAPI?.logToMain) {
      window.electronAPI.logToMain('error', 'Router Error', {
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack,
        },
        errorInfo: {
          componentStack: errorInfo.componentStack,
        },
        timestamp: new Date().toISOString(),
      });
    }
  };

  return (
    <ErrorBoundary
      onError={handleRouterError}
      fallback={
        <div className='min-h-screen flex items-center justify-center bg-base-100'>
          <div className='text-center p-8'>
            <div className='w-16 h-16 bg-error/10 rounded-full flex items-center justify-center mx-auto mb-4'>
              <svg
                className='w-8 h-8 text-error'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'
                />
              </svg>
            </div>
            <h1 className='text-xl font-bold text-error mb-2'>Navigation Error</h1>
            <p className='text-base-content/70 text-sm mb-4'>
              There was an error loading this page.
            </p>
            <button className='btn btn-primary' onClick={() => (window.location.href = '/')}>
              Go to Dashboard
            </button>
          </div>
        </div>
      }
    >
      {children}
    </ErrorBoundary>
  );
};

// Main router provider component
export const AppRouterProvider: React.FC = () => {
  return (
    <AppProviders>
      <RouterErrorBoundary>
        <ReactRouterProvider
          router={router}
          fallbackElement={
            <div className='min-h-screen flex items-center justify-center bg-base-100'>
              <LoadingSpinner text='Loading application...' size='xl' />
            </div>
          }
        />
      </RouterErrorBoundary>
    </AppProviders>
  );
};

// Hook for programmatic navigation with analytics
export const useAppNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const navigateWithAnalytics = (to: string, options?: { replace?: boolean; state?: any }) => {
    // Log navigation intent
    if (window.electronAPI?.logToMain) {
      window.electronAPI.logToMain('info', 'Navigation Intent', {
        from: location.pathname,
        to,
        options,
        timestamp: new Date().toISOString(),
      });
    }

    navigate(to, options);
  };

  return {
    navigate: navigateWithAnalytics,
    location,
    goBack: () => window.history.back(),
    goForward: () => window.history.forward(),
  };
};

// Hook for route metadata
export const useRouteMetadata = () => {
  const location = useLocation();
  return routeMetadata[location.pathname] || null;
};

export default AppRouterProvider;

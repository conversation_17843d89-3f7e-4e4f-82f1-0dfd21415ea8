import { createLogger } from '../utils/logger';
import {
  Document,
  FormField,
  FormFieldType,
  DocumentCoordinates,
  ExtractionMethod,
} from '../../shared/types/Document';
import { AIModelClient } from './AIModelClient';
import { PDFProcessor } from './PDFProcessor';
import { OCREngine } from './OCREngine';
import { EnhancedImageProcessor } from './EnhancedImageProcessor';
import { DocumentProcessingError } from './DocumentProcessor';

const logger = createLogger('FormFieldDetector');

export interface FormFieldDetectionOptions {
  useAI: boolean;
  usePDFParser: boolean;
  useOCR: boolean;
  confidenceThreshold: number;
  enableTypeClassification: boolean;
  enableCoordinateMapping: boolean;
  enhanceImageQuality: boolean;
}

export interface FormFieldDetectionResult {
  fields: FormField[];
  confidence: number;
  detectionMethod: ExtractionMethod;
  processingTime: number;
  metadata: {
    totalFieldsDetected: number;
    fieldsByType: Record<FormFieldType, number>;
    averageConfidence: number;
    detectionMethods: ExtractionMethod[];
  };
}

export interface AIFormFieldAnalysis {
  fields: Array<{
    id: string;
    type: FormFieldType;
    label: string;
    coordinates: DocumentCoordinates;
    confidence: number;
    required: boolean;
    properties?: Record<string, any>;
  }>;
  confidence: number;
  reasoning: string;
}

/**
 * Intelligent form field detection service with AI-powered analysis
 */
export class FormFieldDetector {
  private readonly aiClient: AIModelClient;
  private readonly pdfProcessor: PDFProcessor;
  private readonly ocrEngine: OCREngine;
  private readonly imageProcessor: EnhancedImageProcessor;

  constructor(
    aiClient: AIModelClient,
    pdfProcessor: PDFProcessor,
    ocrEngine: OCREngine,
    imageProcessor: EnhancedImageProcessor
  ) {
    this.aiClient = aiClient;
    this.pdfProcessor = pdfProcessor;
    this.ocrEngine = ocrEngine;
    this.imageProcessor = imageProcessor;
  }

  /**
   * Detect form fields in a document using multiple methods
   */
  public async detectFormFields(
    document: Document,
    options: Partial<FormFieldDetectionOptions> = {}
  ): Promise<FormFieldDetectionResult> {
    const startTime = Date.now();
    const detectionOptions = this.mergeDefaultOptions(options);

    logger.info('Starting form field detection', {
      documentId: document.id,
      documentType: document.type,
      options: detectionOptions,
    });

    try {
      const allFields: FormField[] = [];
      const detectionMethods: ExtractionMethod[] = [];
      let totalConfidence = 0;
      let detectionCount = 0;

      // Method 1: PDF form field extraction (for PDF documents)
      if (document.type === 'pdf' && detectionOptions.usePDFParser) {
        try {
          const pdfFields = await this.extractPDFFormFields(document);
          if (pdfFields.length > 0) {
            allFields.push(...pdfFields);
            detectionMethods.push(ExtractionMethod.PDF_PARSER);
            totalConfidence += 0.95; // High confidence for native PDF fields
            detectionCount++;
            logger.info('PDF form fields extracted', { count: pdfFields.length });
          }
        } catch (error) {
          logger.warn('PDF form field extraction failed', { error });
        }
      }

      // Method 2: OCR-based form field detection
      if (detectionOptions.useOCR) {
        try {
          const ocrFields = await this.detectFormFieldsWithOCR(document, detectionOptions);
          if (ocrFields.length > 0) {
            allFields.push(...ocrFields);
            detectionMethods.push(ExtractionMethod.OCR);
            totalConfidence += 0.75; // Medium confidence for OCR detection
            detectionCount++;
            logger.info('OCR form fields detected', { count: ocrFields.length });
          }
        } catch (error) {
          logger.warn('OCR form field detection failed', { error });
        }
      }

      // Method 3: AI-powered form field detection
      if (detectionOptions.useAI) {
        try {
          const aiFields = await this.detectFormFieldsWithAI(document, detectionOptions);
          if (aiFields.length > 0) {
            allFields.push(...aiFields);
            detectionMethods.push(ExtractionMethod.AI_ANALYSIS);
            totalConfidence += 0.85; // High confidence for AI detection
            detectionCount++;
            logger.info('AI form fields detected', { count: aiFields.length });
          }
        } catch (error) {
          logger.warn('AI form field detection failed', { error });
        }
      }

      // Merge and deduplicate fields
      const mergedFields = await this.mergeAndDeduplicateFields(allFields, detectionOptions);

      // Classify field types if enabled
      if (detectionOptions.enableTypeClassification) {
        await this.classifyFieldTypes(mergedFields);
      }

      // Calculate metadata
      const fieldsByType = this.calculateFieldsByType(mergedFields);
      const averageConfidence = detectionCount > 0 ? totalConfidence / detectionCount : 0;

      const result: FormFieldDetectionResult = {
        fields: mergedFields,
        confidence: averageConfidence,
        detectionMethod: detectionMethods[0] || ExtractionMethod.MANUAL,
        processingTime: Date.now() - startTime,
        metadata: {
          totalFieldsDetected: mergedFields.length,
          fieldsByType,
          averageConfidence,
          detectionMethods,
        },
      };

      logger.info('Form field detection completed', {
        documentId: document.id,
        totalFields: mergedFields.length,
        confidence: averageConfidence,
        processingTime: result.processingTime,
      });

      return result;
    } catch (error) {
      logger.error('Form field detection failed', { error, documentId: document.id });
      throw new DocumentProcessingError(
        'Form field detection failed',
        'FORM_FIELD_DETECTION_FAILED',
        document.id,
        'form_field_detection'
      );
    }
  }

  /**
   * Extract form fields from PDF using PDF.js
   */
  private async extractPDFFormFields(document: Document): Promise<FormField[]> {
    try {
      if (!document.content) {
        throw new Error('Document content is missing');
      }
      const buffer = Buffer.isBuffer(document.content)
        ? document.content
        : Buffer.from(document.content as any);
      const formFieldCoordinates = await this.pdfProcessor.extractFormFieldCoordinates(buffer);

      return formFieldCoordinates.map((field, index) => ({
        id: field.name || `pdf_field_${index}`,
        name: field.name || `Field ${index + 1}`,
        type: this.mapPDFFieldType(field.type),
        value: '',
        required: field.properties?.required || false,
        readonly: field.properties?.readOnly || false,
        bounds: {
          x: field.coordinates.x,
          y: field.coordinates.y,
          width: field.coordinates.width,
          height: field.coordinates.height,
          pageNumber: field.coordinates.pageNumber,
        },
        options: field.properties?.options || [],
      }));
    } catch (error) {
      logger.error('Failed to extract PDF form fields', { error });
      throw error;
    }
  }

  /**
   * Detect form fields using OCR
   */
  private async detectFormFieldsWithOCR(
    document: Document,
    options: FormFieldDetectionOptions
  ): Promise<FormField[]> {
    try {
      let imageBuffer: Buffer;

      if (document.type === 'pdf') {
        // Convert PDF to image for OCR
        if (!document.content) {
          throw new Error('Document content is missing');
        }
        const buffer = Buffer.isBuffer(document.content)
          ? document.content
          : Buffer.from(document.content as any);
        const image = await this.pdfProcessor.convertPageToImage(buffer, 0);
        imageBuffer = image; // Use first page for now
      } else {
        if (!document.content) {
          throw new Error('Document content is missing');
        }
        imageBuffer = Buffer.isBuffer(document.content)
          ? document.content
          : Buffer.from(document.content as any);
      }

      // Enhance image quality if enabled
      if (options.enhanceImageQuality) {
        const result = await (this.imageProcessor as any).imageProcessor.processImage(
          imageBuffer,
          (this.imageProcessor as any).imageProcessor.getOCROptimizedOptions()
        );
        if (result.success) {
          imageBuffer = Buffer.from(result.processedImage.data);
        }
      }

      // Extract form fields using OCR
      const formFields = await this.ocrEngine.extractFormFields(imageBuffer);

      return formFields.filter(_field => {
        // Filter by confidence threshold
        return true; // OCR engine doesn't provide confidence yet
      });
    } catch (error) {
      logger.error('Failed to detect form fields with OCR', { error });
      throw error;
    }
  }

  /**
   * Detect form fields using AI vision
   */
  private async detectFormFieldsWithAI(
    document: Document,
    options: FormFieldDetectionOptions
  ): Promise<FormField[]> {
    try {
      let imageBuffer: Buffer;

      if (document.type === 'pdf') {
        // Convert PDF to image for AI analysis
        if (!document.content) {
          throw new Error('Document content is missing');
        }
        const buffer = Buffer.isBuffer(document.content)
          ? document.content
          : Buffer.from(document.content as any);
        const image = await this.pdfProcessor.convertPageToImage(buffer, 0);
        imageBuffer = image; // Use first page for now
      } else {
        if (!document.content) {
          throw new Error('Document content is missing');
        }
        imageBuffer = Buffer.isBuffer(document.content)
          ? document.content
          : Buffer.from(document.content as any);
      }

      // Enhance image quality if enabled
      if (options.enhanceImageQuality) {
        const result = await (this.imageProcessor as any).imageProcessor.processImage(
          imageBuffer,
          (this.imageProcessor as any).imageProcessor.getOCROptimizedOptions()
        );
        if (result.success) {
          imageBuffer = Buffer.from(result.processedImage.data);
        }
      }

      // Use AI to analyze form structure
      const analysis = await this.analyzeFormStructureWithAI(imageBuffer);

      return analysis.fields
        .filter(field => field.confidence >= options.confidenceThreshold)
        .map(field => ({
          id: field.id,
          name: field.label || field.id,
          type: field.type,
          value: '',
          required: field.required,
          readonly: false,
          bounds: {
            x: field.coordinates.x,
            y: field.coordinates.y,
            width: field.coordinates.width,
            height: field.coordinates.height,
            pageNumber: field.coordinates.pageNumber || 1,
          },
          options: field.properties?.options,
        }));
    } catch (error) {
      logger.error('Failed to detect form fields with AI', { error });
      throw error;
    }
  }

  /**
   * Analyze form structure using AI vision
   */
  private async analyzeFormStructureWithAI(imageBuffer: Buffer): Promise<AIFormFieldAnalysis> {
    const prompt = `
Analyze this form image and identify all form fields. For each field, provide:
1. Field type (text, checkbox, radio, dropdown, signature, date, number)
2. Approximate coordinates (x, y, width, height as percentages of image dimensions)
3. Field label or description
4. Whether the field appears to be required
5. Confidence score (0-1)

Return the analysis in JSON format with the following structure:
{
  "fields": [
    {
      "id": "unique_field_id",
      "type": "text|checkbox|radio|dropdown|signature|date|number",
      "label": "field label",
      "coordinates": {"x": 0.1, "y": 0.2, "width": 0.3, "height": 0.05},
      "confidence": 0.95,
      "required": true|false,
      "properties": {}
    }
  ],
  "confidence": 0.9,
  "reasoning": "explanation of analysis"
}
`;

    try {
      const response = await this.aiClient.analyzeImageWithPrompt(imageBuffer, prompt);
      return JSON.parse(response) as AIFormFieldAnalysis;
    } catch (error) {
      logger.error('AI form structure analysis failed', { error });
      throw error;
    }
  }

  /**
   * Map PDF field type to FormFieldType enum
   */
  private mapPDFFieldType(pdfType: string): FormFieldType {
    const typeMap: Record<string, FormFieldType> = {
      text: FormFieldType.TEXT,
      checkbox: FormFieldType.CHECKBOX,
      radio: FormFieldType.RADIO,
      dropdown: FormFieldType.DROPDOWN,
      signature: FormFieldType.SIGNATURE,
      date: FormFieldType.DATE,
      number: FormFieldType.NUMBER,
    };

    return typeMap[pdfType.toLowerCase()] || FormFieldType.TEXT;
  }

  /**
   * Merge and deduplicate form fields from different detection methods
   */
  private async mergeAndDeduplicateFields(
    fields: FormField[],
    _options: FormFieldDetectionOptions
  ): Promise<FormField[]> {
    if (fields.length === 0) return [];

    const mergedFields: FormField[] = [];
    const processedFields = new Set<string>();

    for (const field of fields) {
      const fieldKey = this.generateFieldKey(field);

      if (!processedFields.has(fieldKey)) {
        mergedFields.push(field);
        processedFields.add(fieldKey);
      } else {
        // Find existing field and merge properties
        const existingIndex = mergedFields.findIndex(f => this.generateFieldKey(f) === fieldKey);

        if (existingIndex >= 0) {
          const existingField = mergedFields[existingIndex];
          if (existingField) {
            mergedFields[existingIndex] = this.mergeFieldProperties(existingField, field);
          }
        }
      }
    }

    return mergedFields;
  }

  /**
   * Generate a unique key for field deduplication
   */
  private generateFieldKey(field: FormField): string {
    const x = Math.round(field.bounds.x / 10) * 10; // Round to nearest 10
    const y = Math.round(field.bounds.y / 10) * 10;
    return `${field.type}_${x}_${y}_${field.bounds.pageNumber || 1}`;
  }

  /**
   * Merge properties of duplicate fields
   */
  private mergeFieldProperties(existing: FormField, duplicate: FormField): FormField {
    return {
      ...existing,
      name: existing.name || duplicate.name,
      required: existing.required || duplicate.required,
      options: existing.options || duplicate.options || [],
    };
  }

  /**
   * Classify field types using heuristics and AI
   */
  private async classifyFieldTypes(fields: FormField[]): Promise<void> {
    for (const field of fields) {
      if (field.type === FormFieldType.TEXT) {
        // Try to classify text fields more specifically
        const classifiedType = this.classifyTextFieldType(field);
        if (classifiedType !== FormFieldType.TEXT) {
          field.type = classifiedType;
        }
      }
    }
  }

  /**
   * Classify text field type based on name and context
   */
  private classifyTextFieldType(field: FormField): FormFieldType {
    const name = field.name.toLowerCase();

    if (name.includes('date') || name.includes('birth') || name.includes('dob')) {
      return FormFieldType.DATE;
    }

    if (
      name.includes('amount') ||
      name.includes('price') ||
      name.includes('total') ||
      name.includes('income') ||
      name.includes('salary')
    ) {
      return FormFieldType.NUMBER;
    }

    if (name.includes('email') || name.includes('e-mail')) {
      return FormFieldType.EMAIL;
    }

    if (name.includes('phone') || name.includes('tel') || name.includes('mobile')) {
      return FormFieldType.PHONE;
    }

    return FormFieldType.TEXT;
  }

  /**
   * Calculate fields by type for metadata
   */
  private calculateFieldsByType(fields: FormField[]): Record<FormFieldType, number> {
    const counts: Record<FormFieldType, number> = {} as Record<FormFieldType, number>;

    // Initialize all types to 0
    Object.values(FormFieldType).forEach(type => {
      counts[type] = 0;
    });

    // Count fields by type
    fields.forEach(field => {
      counts[field.type]++;
    });

    return counts;
  }

  /**
   * Merge default options with provided options
   */
  private mergeDefaultOptions(
    options: Partial<FormFieldDetectionOptions>
  ): FormFieldDetectionOptions {
    return {
      useAI: options.useAI ?? true,
      usePDFParser: options.usePDFParser ?? true,
      useOCR: options.useOCR ?? true,
      confidenceThreshold: options.confidenceThreshold ?? 0.7,
      enableTypeClassification: options.enableTypeClassification ?? true,
      enableCoordinateMapping: options.enableCoordinateMapping ?? true,
      enhanceImageQuality: options.enhanceImageQuality ?? true,
    };
  }
}

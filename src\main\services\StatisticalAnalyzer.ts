import { createLogger } from '../utils/logger';
import { CalculationEngine } from './CalculationEngine';

const logger = createLogger('StatisticalAnalyzer');

export interface StatisticalSummary {
  count: number;
  mean: number;
  median: number;
  mode: number[];
  standardDeviation: number;
  variance: number;
  min: number;
  max: number;
  range: number;
  quartiles: Quartiles;
  skewness: number;
  kurtosis: number;
}

export interface Quartiles {
  q1: number;
  q2: number; // median
  q3: number;
  iqr: number; // interquartile range
}

export interface OutlierAnalysis {
  outliers: OutlierPoint[];
  method: OutlierDetectionMethod;
  threshold: number;
  cleanData: number[];
  outlierCount: number;
  outlierPercentage: number;
}

export interface OutlierPoint {
  value: number;
  index: number;
  zScore?: number;
  iqrScore?: number;
  severity: 'mild' | 'moderate' | 'extreme';
}

export enum OutlierDetectionMethod {
  Z_SCORE = 'z_score',
  IQR = 'iqr',
  MODIFIED_Z_SCORE = 'modified_z_score',
  ISOLATION_FOREST = 'isolation_forest',
}

export interface CorrelationAnalysis {
  correlationMatrix: number[][];
  variables: string[];
  significantCorrelations: CorrelationPair[];
  method: CorrelationMethod;
}

export interface CorrelationPair {
  variable1: string;
  variable2: string;
  correlation: number;
  pValue?: number;
  significance: 'weak' | 'moderate' | 'strong' | 'very_strong';
}

export enum CorrelationMethod {
  PEARSON = 'pearson',
  SPEARMAN = 'spearman',
  KENDALL = 'kendall',
}

export interface RegressionAnalysis {
  type: RegressionType;
  coefficients: number[];
  rSquared: number;
  adjustedRSquared: number;
  standardError: number;
  fStatistic: number;
  pValue: number;
  residuals: number[];
  predictions: number[];
  equation: string;
}

export enum RegressionType {
  LINEAR = 'linear',
  POLYNOMIAL = 'polynomial',
  EXPONENTIAL = 'exponential',
  LOGARITHMIC = 'logarithmic',
}

export interface TrendAnalysis {
  trend: TrendDirection;
  strength: number;
  seasonality: SeasonalityInfo;
  changePoints: ChangePoint[];
  forecast: ForecastPoint[];
  confidence: number;
}

export enum TrendDirection {
  INCREASING = 'increasing',
  DECREASING = 'decreasing',
  STABLE = 'stable',
  VOLATILE = 'volatile',
}

export interface SeasonalityInfo {
  hasSeasonality: boolean;
  period?: number;
  strength?: number;
  pattern?: number[];
}

export interface ChangePoint {
  index: number;
  value: number;
  significance: number;
  type: 'level' | 'trend' | 'variance';
}

export interface ForecastPoint {
  index: number;
  value: number;
  lowerBound: number;
  upperBound: number;
  confidence: number;
}

export interface DataValidationResult {
  isValid: boolean;
  errors: DataValidationError[];
  warnings: DataValidationWarning[];
  cleanedData: number[];
  summary: DataQualitySummary;
}

export interface DataValidationError {
  type: 'missing_values' | 'invalid_type' | 'out_of_range' | 'duplicate' | 'inconsistent';
  message: string;
  indices: number[];
  severity: 'low' | 'medium' | 'high';
}

export interface DataValidationWarning {
  type: 'potential_outlier' | 'low_variance' | 'high_correlation' | 'small_sample';
  message: string;
  details: any;
}

export interface DataQualitySummary {
  totalPoints: number;
  validPoints: number;
  missingPoints: number;
  duplicatePoints: number;
  outlierPoints: number;
  qualityScore: number; // 0-100
}

export interface VisualizationData {
  type: VisualizationType;
  data: any[];
  labels: string[];
  title: string;
  xAxis: AxisConfig;
  yAxis: AxisConfig;
  metadata: VisualizationMetadata;
}

export enum VisualizationType {
  HISTOGRAM = 'histogram',
  BOX_PLOT = 'box_plot',
  SCATTER_PLOT = 'scatter_plot',
  LINE_CHART = 'line_chart',
  BAR_CHART = 'bar_chart',
  CORRELATION_HEATMAP = 'correlation_heatmap',
  Q_Q_PLOT = 'q_q_plot',
}

export interface AxisConfig {
  label: string;
  min?: number;
  max?: number;
  scale: 'linear' | 'logarithmic';
}

export interface VisualizationMetadata {
  description: string;
  insights: string[];
  recommendations: string[];
}

/**
 * Statistical Analyzer for comprehensive data analysis
 * Provides statistical functions, outlier detection, correlation analysis, and data visualization preparation
 */
export class StatisticalAnalyzer {
  // private _calculationEngine: CalculationEngine;

  constructor(_calculationEngine: CalculationEngine) {
    // this._calculationEngine = calculationEngine;
    logger.info('StatisticalAnalyzer initialized');
  }

  /**
   * Calculate comprehensive statistical summary
   */
  public calculateSummary(data: number[]): StatisticalSummary {
    logger.debug('Calculating statistical summary', { dataLength: data.length });

    if (data.length === 0) {
      throw new Error('Cannot calculate statistics for empty dataset');
    }

    const sortedData = [...data].sort((a, b) => a - b);
    const count = data.length;
    const mean = this.calculateMean(data);
    const median = this.calculateMedian(sortedData);
    const mode = this.calculateMode(data);
    const variance = this.calculateVariance(data, mean);
    const standardDeviation = Math.sqrt(variance);
    const min = sortedData[0] ?? 0;
    const max = sortedData[sortedData.length - 1] ?? 0;
    const range = max - min;
    const quartiles = this.calculateQuartiles(sortedData);
    const skewness = this.calculateSkewness(data, mean, standardDeviation);
    const kurtosis = this.calculateKurtosis(data, mean, standardDeviation);

    const summary: StatisticalSummary = {
      count,
      mean,
      median,
      mode,
      standardDeviation,
      variance,
      min,
      max,
      range,
      quartiles,
      skewness,
      kurtosis,
    };

    logger.info('Statistical summary calculated', {
      count,
      mean: mean.toFixed(4),
      standardDeviation: standardDeviation.toFixed(4),
    });

    return summary;
  }

  /**
   * Detect outliers using various methods
   */
  public detectOutliers(
    data: number[],
    method: OutlierDetectionMethod = OutlierDetectionMethod.IQR,
    threshold: number = 1.5
  ): OutlierAnalysis {
    logger.debug('Detecting outliers', { method, threshold, dataLength: data.length });

    let outliers: OutlierPoint[] = [];
    let cleanData: number[] = [];

    switch (method) {
      case OutlierDetectionMethod.Z_SCORE:
        ({ outliers, cleanData } = this.detectOutliersZScore(data, threshold));
        break;
      case OutlierDetectionMethod.IQR:
        ({ outliers, cleanData } = this.detectOutliersIQR(data, threshold));
        break;
      case OutlierDetectionMethod.MODIFIED_Z_SCORE:
        ({ outliers, cleanData } = this.detectOutliersModifiedZScore(data, threshold));
        break;
      default:
        throw new Error(`Unsupported outlier detection method: ${method}`);
    }

    const outlierCount = outliers.length;
    const outlierPercentage = (outlierCount / data.length) * 100;

    const analysis: OutlierAnalysis = {
      outliers,
      method,
      threshold,
      cleanData,
      outlierCount,
      outlierPercentage,
    };

    logger.info('Outlier detection completed', {
      method,
      outlierCount,
      outlierPercentage: outlierPercentage.toFixed(2),
    });

    return analysis;
  }

  /**
   * Perform correlation analysis
   */
  public calculateCorrelation(
    datasets: Record<string, number[]>,
    method: CorrelationMethod = CorrelationMethod.PEARSON
  ): CorrelationAnalysis {
    logger.debug('Calculating correlation analysis', {
      method,
      variableCount: Object.keys(datasets).length,
    });

    const variables = Object.keys(datasets);
    const dataArrays = Object.values(datasets);

    // Validate data lengths
    const firstLength = dataArrays[0]?.length ?? 0;
    if (!dataArrays.every(arr => arr.length === firstLength)) {
      throw new Error('All datasets must have the same length for correlation analysis');
    }

    // Calculate correlation matrix
    const correlationMatrix: number[][] = [];
    const significantCorrelations: CorrelationPair[] = [];

    for (let i = 0; i < variables.length; i++) {
      correlationMatrix[i] = [];
      for (let j = 0; j < variables.length; j++) {
        let correlation: number;

        if (i === j) {
          correlation = 1.0;
        } else {
          switch (method) {
            case CorrelationMethod.PEARSON:
              correlation = this.calculatePearsonCorrelation(
                dataArrays[i] ?? [],
                dataArrays[j] ?? []
              );
              break;
            case CorrelationMethod.SPEARMAN:
              correlation = this.calculateSpearmanCorrelation(
                dataArrays[i] ?? [],
                dataArrays[j] ?? []
              );
              break;
            default:
              throw new Error(`Unsupported correlation method: ${method}`);
          }
        }

        if (!correlationMatrix[i]) {
          correlationMatrix[i] = [];
        }
        correlationMatrix[i]![j] = correlation;

        // Add to significant correlations if above threshold and not self-correlation
        if (i < j && Math.abs(correlation) > 0.3) {
          const var1 = variables[i];
          const var2 = variables[j];
          if (var1 && var2) {
            significantCorrelations.push({
              variable1: var1,
              variable2: var2,
              correlation,
              significance: this.getCorrelationSignificance(Math.abs(correlation)),
            });
          }
        }
      }
    }

    const analysis: CorrelationAnalysis = {
      correlationMatrix,
      variables,
      significantCorrelations,
      method,
    };

    logger.info('Correlation analysis completed', {
      method,
      variableCount: variables.length,
      significantCorrelations: significantCorrelations.length,
    });

    return analysis;
  }

  /**
   * Perform linear regression analysis
   */
  public performLinearRegression(xData: number[], yData: number[]): RegressionAnalysis {
    logger.debug('Performing linear regression', { dataLength: xData.length });

    if (xData.length !== yData.length) {
      throw new Error('X and Y data must have the same length');
    }

    if (xData.length < 2) {
      throw new Error('Need at least 2 data points for regression');
    }

    const n = xData.length;
    const sumX = xData.reduce((sum, x) => sum + x, 0);
    const sumY = yData.reduce((sum, y) => sum + y, 0);
    const sumXY = xData.reduce((sum, x, i) => sum + x * (yData[i] ?? 0), 0);
    const sumXX = xData.reduce((sum, x) => sum + x * x, 0);

    // Calculate coefficients (slope and intercept)
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;
    const coefficients = [intercept, slope];

    // Calculate predictions and residuals
    const predictions = xData.map(x => intercept + slope * x);
    const residuals = yData.map((y, i) => y - (predictions[i] ?? 0));

    // Calculate R-squared
    const yMean = sumY / n;
    const totalSumSquares = yData.reduce((sum, y) => sum + Math.pow(y - yMean, 2), 0);
    const residualSumSquares = residuals.reduce((sum, r) => sum + r * r, 0);
    const rSquared = 1 - residualSumSquares / totalSumSquares;

    // Calculate adjusted R-squared
    const adjustedRSquared = 1 - ((1 - rSquared) * (n - 1)) / (n - 2);

    // Calculate standard error
    const standardError = Math.sqrt(residualSumSquares / (n - 2));

    // Calculate F-statistic
    const meanSquareRegression = (totalSumSquares - residualSumSquares) / 1;
    const meanSquareResidual = residualSumSquares / (n - 2);
    const fStatistic = meanSquareRegression / meanSquareResidual;

    // Create equation string
    const equation = `y = ${intercept.toFixed(4)} + ${slope.toFixed(4)}x`;

    const analysis: RegressionAnalysis = {
      type: RegressionType.LINEAR,
      coefficients,
      rSquared,
      adjustedRSquared,
      standardError,
      fStatistic,
      pValue: 0, // Would need statistical tables for exact calculation
      residuals,
      predictions,
      equation,
    };

    logger.info('Linear regression completed', {
      rSquared: rSquared.toFixed(4),
      equation,
    });

    return analysis;
  }

  /**
   * Validate data quality
   */
  public validateData(
    data: any[],
    options: { allowMissing?: boolean; range?: [number, number] } = {}
  ): DataValidationResult {
    logger.debug('Validating data quality', { dataLength: data.length });

    const errors: DataValidationError[] = [];
    const warnings: DataValidationWarning[] = [];
    const cleanedData: number[] = [];

    let validPoints = 0;
    let missingPoints = 0;
    let duplicatePoints = 0;

    // Check for missing values and type validation
    for (let i = 0; i < data.length; i++) {
      const value = data[i];

      if (value === null || value === undefined || value === '') {
        missingPoints++;
        if (!options.allowMissing) {
          errors.push({
            type: 'missing_values',
            message: `Missing value at index ${i}`,
            indices: [i],
            severity: 'medium',
          });
        }
        continue;
      }

      const numValue = Number(value);
      if (isNaN(numValue)) {
        errors.push({
          type: 'invalid_type',
          message: `Invalid numeric value at index ${i}: ${value}`,
          indices: [i],
          severity: 'high',
        });
        continue;
      }

      // Range validation
      if (options.range) {
        const [min, max] = options.range;
        if (numValue < min || numValue > max) {
          errors.push({
            type: 'out_of_range',
            message: `Value ${numValue} at index ${i} is outside range [${min}, ${max}]`,
            indices: [i],
            severity: 'medium',
          });
          continue;
        }
      }

      cleanedData.push(numValue);
      validPoints++;
    }

    // Check for duplicates
    const valueCount = new Map<number, number[]>();
    cleanedData.forEach((value, index) => {
      if (!valueCount.has(value)) {
        valueCount.set(value, []);
      }
      valueCount.get(value)!.push(index);
    });

    for (const [value, indices] of valueCount) {
      if (indices.length > 1) {
        duplicatePoints += indices.length - 1;
        warnings.push({
          type: 'potential_outlier',
          message: `Duplicate value ${value} found at indices: ${indices.join(', ')}`,
          details: { value, indices },
        });
      }
    }

    // Additional warnings
    if (cleanedData.length < 30) {
      warnings.push({
        type: 'small_sample',
        message: 'Small sample size may affect statistical reliability',
        details: { sampleSize: cleanedData.length },
      });
    }

    if (cleanedData.length > 0) {
      const variance = this.calculateVariance(cleanedData);
      if (variance < 0.01) {
        warnings.push({
          type: 'low_variance',
          message: 'Low variance detected - data may be too uniform',
          details: { variance },
        });
      }
    }

    // Calculate quality score
    const qualityScore = Math.max(
      0,
      Math.min(100, (validPoints / data.length) * 100 - errors.length * 5 - warnings.length * 2)
    );

    const summary: DataQualitySummary = {
      totalPoints: data.length,
      validPoints,
      missingPoints,
      duplicatePoints,
      outlierPoints: 0, // Would be calculated separately
      qualityScore,
    };

    const result: DataValidationResult = {
      isValid: errors.length === 0,
      errors,
      warnings,
      cleanedData,
      summary,
    };

    logger.info('Data validation completed', {
      totalPoints: data.length,
      validPoints,
      errorCount: errors.length,
      warningCount: warnings.length,
      qualityScore: qualityScore.toFixed(1),
    });

    return result;
  }

  /**
   * Prepare data for visualization
   */
  public prepareVisualization(
    data: number[],
    type: VisualizationType,
    options: { title?: string; bins?: number } = {}
  ): VisualizationData {
    logger.debug('Preparing visualization data', { type, dataLength: data.length });

    let visualizationData: any[] = [];
    let labels: string[] = [];
    const insights: string[] = [];
    const recommendations: string[] = [];

    switch (type) {
      case VisualizationType.HISTOGRAM:
        const histogramResult = this.createHistogram(data, options.bins || 10);
        visualizationData = histogramResult.bins;
        labels = histogramResult.labels;
        insights.push(
          `Data distribution shows ${histogramResult.skewness > 0 ? 'right' : 'left'} skew`
        );
        break;

      case VisualizationType.BOX_PLOT:
        const summary = this.calculateSummary(data);
        visualizationData = [
          { label: 'Min', value: summary.min },
          { label: 'Q1', value: summary.quartiles.q1 },
          { label: 'Median', value: summary.median },
          { label: 'Q3', value: summary.quartiles.q3 },
          { label: 'Max', value: summary.max },
        ];
        insights.push(`IQR: ${summary.quartiles.iqr.toFixed(2)}`);
        break;

      default:
        visualizationData = data.map((value, index) => ({ x: index, y: value }));
    }

    const result: VisualizationData = {
      type,
      data: visualizationData,
      labels,
      title: options.title || `${type} Chart`,
      xAxis: { label: 'X', scale: 'linear' },
      yAxis: { label: 'Y', scale: 'linear' },
      metadata: {
        description: `${type} visualization of ${data.length} data points`,
        insights,
        recommendations,
      },
    };

    logger.info('Visualization data prepared', { type, dataPoints: visualizationData.length });
    return result;
  }

  // Private helper methods
  private calculateMean(data: number[]): number {
    return data.reduce((sum, value) => sum + value, 0) / data.length;
  }

  private calculateMedian(sortedData: number[]): number {
    const mid = Math.floor(sortedData.length / 2);
    return sortedData.length % 2 === 0
      ? ((sortedData[mid - 1] ?? 0) + (sortedData[mid] ?? 0)) / 2
      : (sortedData[mid] ?? 0);
  }

  private calculateMode(data: number[]): number[] {
    const frequency = new Map<number, number>();
    data.forEach(value => {
      frequency.set(value, (frequency.get(value) || 0) + 1);
    });

    const maxFreq = Math.max(...frequency.values());
    return Array.from(frequency.entries())
      .filter(([, freq]) => freq === maxFreq)
      .map(([value]) => value);
  }

  private calculateVariance(data: number[], mean?: number): number {
    const avg = mean ?? this.calculateMean(data);
    const squaredDiffs = data.map(value => Math.pow(value - avg, 2));
    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / data.length;
  }

  private calculateQuartiles(sortedData: number[]): Quartiles {
    const q1 = this.calculatePercentile(sortedData, 25);
    const q2 = this.calculatePercentile(sortedData, 50);
    const q3 = this.calculatePercentile(sortedData, 75);
    const iqr = q3 - q1;

    return { q1, q2, q3, iqr };
  }

  private calculatePercentile(sortedData: number[], percentile: number): number {
    const index = (percentile / 100) * (sortedData.length - 1);
    const lower = Math.floor(index);
    const upper = Math.ceil(index);
    const weight = index - lower;

    return (sortedData[lower] ?? 0) * (1 - weight) + (sortedData[upper] ?? 0) * weight;
  }

  private calculateSkewness(data: number[], mean: number, stdDev: number): number {
    const n = data.length;
    const skewSum = data.reduce((sum, value) => {
      return sum + Math.pow((value - mean) / stdDev, 3);
    }, 0);
    return (n / ((n - 1) * (n - 2))) * skewSum;
  }

  private calculateKurtosis(data: number[], mean: number, stdDev: number): number {
    const n = data.length;
    const kurtSum = data.reduce((sum, value) => {
      return sum + Math.pow((value - mean) / stdDev, 4);
    }, 0);
    return (
      ((n * (n + 1)) / ((n - 1) * (n - 2) * (n - 3))) * kurtSum -
      (3 * Math.pow(n - 1, 2)) / ((n - 2) * (n - 3))
    );
  }

  private detectOutliersIQR(
    data: number[],
    threshold: number
  ): { outliers: OutlierPoint[]; cleanData: number[] } {
    const sortedData = [...data].sort((a, b) => a - b);
    const quartiles = this.calculateQuartiles(sortedData);
    const lowerBound = quartiles.q1 - threshold * quartiles.iqr;
    const upperBound = quartiles.q3 + threshold * quartiles.iqr;

    const outliers: OutlierPoint[] = [];
    const cleanData: number[] = [];

    data.forEach((value, index) => {
      if (value < lowerBound || value > upperBound) {
        const iqrScore = Math.max(
          (lowerBound - value) / quartiles.iqr,
          (value - upperBound) / quartiles.iqr
        );
        outliers.push({
          value,
          index,
          iqrScore,
          severity: iqrScore > 3 ? 'extreme' : iqrScore > 1.5 ? 'moderate' : 'mild',
        });
      } else {
        cleanData.push(value);
      }
    });

    return { outliers, cleanData };
  }

  private detectOutliersZScore(
    data: number[],
    threshold: number
  ): { outliers: OutlierPoint[]; cleanData: number[] } {
    const mean = this.calculateMean(data);
    const stdDev = Math.sqrt(this.calculateVariance(data, mean));

    const outliers: OutlierPoint[] = [];
    const cleanData: number[] = [];

    data.forEach((value, index) => {
      const zScore = Math.abs((value - mean) / stdDev);
      if (zScore > threshold) {
        outliers.push({
          value,
          index,
          zScore,
          severity: zScore > 3 ? 'extreme' : zScore > 2 ? 'moderate' : 'mild',
        });
      } else {
        cleanData.push(value);
      }
    });

    return { outliers, cleanData };
  }

  private detectOutliersModifiedZScore(
    data: number[],
    threshold: number
  ): { outliers: OutlierPoint[]; cleanData: number[] } {
    const median = this.calculateMedian([...data].sort((a, b) => a - b));
    const deviations = data.map(value => Math.abs(value - median));
    const mad = this.calculateMedian([...deviations].sort((a, b) => a - b));

    const outliers: OutlierPoint[] = [];
    const cleanData: number[] = [];

    data.forEach((value, index) => {
      const modifiedZScore = (0.6745 * (value - median)) / mad;
      if (Math.abs(modifiedZScore) > threshold) {
        outliers.push({
          value,
          index,
          zScore: modifiedZScore,
          severity: Math.abs(modifiedZScore) > 3.5 ? 'extreme' : 'moderate',
        });
      } else {
        cleanData.push(value);
      }
    });

    return { outliers, cleanData };
  }

  private calculatePearsonCorrelation(x: number[], y: number[]): number {
    const n = x.length;
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * (y[i] ?? 0), 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);
    const sumYY = y.reduce((sum, val) => sum + val * val, 0);

    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));

    return denominator === 0 ? 0 : numerator / denominator;
  }

  private calculateSpearmanCorrelation(x: number[], y: number[]): number {
    const xRanks = this.getRanks(x);
    const yRanks = this.getRanks(y);
    return this.calculatePearsonCorrelation(xRanks, yRanks);
  }

  private getRanks(data: number[]): number[] {
    const sorted = data.map((value, index) => ({ value, index })).sort((a, b) => a.value - b.value);

    const ranks = new Array(data.length);
    sorted.forEach((item, rank) => {
      ranks[item.index] = rank + 1;
    });

    return ranks;
  }

  private getCorrelationSignificance(
    correlation: number
  ): 'weak' | 'moderate' | 'strong' | 'very_strong' {
    const abs = Math.abs(correlation);
    if (abs >= 0.8) return 'very_strong';
    if (abs >= 0.6) return 'strong';
    if (abs >= 0.3) return 'moderate';
    return 'weak';
  }

  private createHistogram(
    data: number[],
    bins: number
  ): { bins: any[]; labels: string[]; skewness: number } {
    const min = Math.min(...data);
    const max = Math.max(...data);
    const binWidth = (max - min) / bins;

    const binCounts = new Array(bins).fill(0);
    const binLabels: string[] = [];

    for (let i = 0; i < bins; i++) {
      const binStart = min + i * binWidth;
      const binEnd = min + (i + 1) * binWidth;
      binLabels.push(`${binStart.toFixed(2)}-${binEnd.toFixed(2)}`);
    }

    data.forEach(value => {
      const binIndex = Math.min(Math.floor((value - min) / binWidth), bins - 1);
      binCounts[binIndex]++;
    });

    const binData = binCounts.map((count, index) => ({
      bin: binLabels[index],
      count,
      frequency: count / data.length,
    }));

    const summary = this.calculateSummary(data);

    return {
      bins: binData,
      labels: binLabels,
      skewness: summary.skewness,
    };
  }
}

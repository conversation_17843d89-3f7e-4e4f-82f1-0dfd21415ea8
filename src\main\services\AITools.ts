import { evaluate } from 'mathjs';
import { logger } from '../utils/logger';
import { chromaKnowledgeBase } from './ChromaKnowledgeBase';
import {
  AITool,
  ToolInput,
  ToolOutput,
  ToolMetadata,
  ToolCategory,
  ToolParameter,
  AIContext,
} from '../../shared/types/AI';

/**
 * Enhanced AI Tool interface with comprehensive metadata and capabilities
 */
export interface EnhancedAITool extends AITool {
  id: string;
  name: string;
  description: string;
  parameters: ToolParameter[];
  metadata: ToolMetadata;
  execute(input: ToolInput): Promise<ToolOutput>;

  // Additional capabilities
  validate?(input: ToolInput): Promise<boolean>;
  getCapabilities?(): string[];
  getRequirements?(): string[];
}

/**
 * Tool Registry for managing and discovering available tools
 */
export class ToolRegistry {
  private tools = new Map<string, EnhancedAITool>();
  private categories = new Map<ToolCategory, EnhancedAITool[]>();

  /**
   * Register a new tool
   */
  register(tool: EnhancedAITool): void {
    this.tools.set(tool.id, tool);

    // Add to category index
    const category = tool.metadata.category;
    if (!this.categories.has(category)) {
      this.categories.set(category, []);
    }
    this.categories.get(category)?.push(tool);

    logger.info('Tool registered', {
      id: tool.id,
      name: tool.name,
      category: tool.metadata.category,
    });
  }

  /**
   * Get tool by ID
   */
  getTool(id: string): EnhancedAITool | undefined {
    return this.tools.get(id);
  }

  /**
   * Get all tools
   */
  getAllTools(): EnhancedAITool[] {
    return Array.from(this.tools.values());
  }

  /**
   * Get tools by category
   */
  getToolsByCategory(category: ToolCategory): EnhancedAITool[] {
    return this.categories.get(category) || [];
  }

  /**
   * Search tools by capability
   */
  searchByCapability(capability: string): EnhancedAITool[] {
    return this.getAllTools().filter(tool => tool.getCapabilities?.()?.includes(capability));
  }

  /**
   * Get tool metadata for discovery
   */
  getToolMetadata(): Array<{
    id: string;
    name: string;
    description: string;
    category: ToolCategory;
    parameters: ToolParameter[];
  }> {
    return this.getAllTools().map(tool => ({
      id: tool.id,
      name: tool.name,
      description: tool.description,
      category: tool.metadata.category,
      parameters: tool.parameters,
    }));
  }
}

/**
 * Tool Execution Pipeline with error handling and logging
 */
export class ToolExecutor {
  private registry: ToolRegistry;
  private executionHistory: Array<{
    toolId: string;
    input: ToolInput;
    output: ToolOutput;
    timestamp: Date;
    duration: number;
  }> = [];

  constructor(registry: ToolRegistry) {
    this.registry = registry;
  }

  /**
   * Execute a tool with comprehensive error handling
   */
  async execute(toolId: string, input: ToolInput, context?: AIContext): Promise<ToolOutput> {
    const startTime = Date.now();
    const tool = this.registry.getTool(toolId);

    if (!tool) {
      const error = `Tool not found: ${toolId}`;
      logger.error(error);
      return {
        result: null,
        success: false,
        error,
      };
    }

    try {
      // Validate input if tool supports validation
      if (tool.validate) {
        const isValid = await tool.validate(input);
        if (!isValid) {
          const error = `Invalid input for tool: ${toolId}`;
          logger.error(error, { input });
          return {
            result: null,
            success: false,
            error,
          };
        }
      }

      // Add context to input if available
      const enhancedInput: ToolInput = {
        ...input,
        ...(context && { context }),
      };

      // Execute the tool
      logger.info('Executing tool', { toolId, tool: tool.name });
      const output = await tool.execute(enhancedInput);

      // Record execution
      const duration = Date.now() - startTime;
      this.executionHistory.push({
        toolId,
        input: enhancedInput,
        output,
        timestamp: new Date(),
        duration,
      });

      logger.info('Tool execution completed', {
        toolId,
        success: output.success,
        duration,
      });

      return output;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      logger.error('Tool execution failed', {
        toolId,
        error: errorMessage,
        duration,
      });

      const failureOutput: ToolOutput = {
        result: null,
        success: false,
        error: `Tool execution failed: ${errorMessage}`,
        metadata: {
          duration,
          timestamp: new Date(),
        },
      };

      // Record failed execution
      this.executionHistory.push({
        toolId,
        input,
        output: failureOutput,
        timestamp: new Date(),
        duration,
      });

      return failureOutput;
    }
  }

  /**
   * Execute multiple tools in sequence
   */
  async executeSequence(
    executions: Array<{ toolId: string; input: ToolInput }>,
    context?: AIContext
  ): Promise<ToolOutput[]> {
    const results: ToolOutput[] = [];

    for (const execution of executions) {
      const result = await this.execute(execution.toolId, execution.input, context);
      results.push(result);

      // Stop on first failure if configured
      if (!result.success) {
        logger.warn('Tool sequence stopped due to failure', {
          toolId: execution.toolId,
          error: result.error,
        });
        break;
      }
    }

    return results;
  }

  /**
   * Get execution history
   */
  getExecutionHistory(): typeof this.executionHistory {
    return [...this.executionHistory];
  }

  /**
   * Clear execution history
   */
  clearHistory(): void {
    this.executionHistory = [];
  }
}

/**
 * AI Tools Manager - Central orchestration system
 */
export class AIToolsManager {
  private registry: ToolRegistry;
  private executor: ToolExecutor;

  constructor() {
    this.registry = new ToolRegistry();
    this.executor = new ToolExecutor(this.registry);
    this.initializeDefaultTools();
  }

  /**
   * Initialize default tools
   */
  private initializeDefaultTools(): void {
    // Register all default tools
    this.registry.register(new CalculatorTool());
    this.registry.register(new DocumentAnalysisTool());
    this.registry.register(new FormFillingTool());
    this.registry.register(new KnowledgeSearchTool());

    logger.info('Default AI tools initialized', {
      toolCount: this.registry.getAllTools().length,
    });
  }

  /**
   * Get the tool registry
   */
  getRegistry(): ToolRegistry {
    return this.registry;
  }

  /**
   * Get the tool executor
   */
  getExecutor(): ToolExecutor {
    return this.executor;
  }

  /**
   * Execute a tool
   */
  async executeTool(toolId: string, input: ToolInput, context?: AIContext): Promise<ToolOutput> {
    return this.executor.execute(toolId, input, context);
  }

  /**
   * Discover available tools
   */
  discoverTools(): Array<{
    id: string;
    name: string;
    description: string;
    category: ToolCategory;
    capabilities: string[];
  }> {
    return this.registry.getAllTools().map(tool => ({
      id: tool.id,
      name: tool.name,
      description: tool.description,
      category: tool.metadata.category,
      capabilities: tool.getCapabilities?.() || [],
    }));
  }

  /**
   * Get tools matching capabilities
   */
  getToolsForCapabilities(capabilities: string[]): EnhancedAITool[] {
    return this.registry.getAllTools().filter(tool => {
      const toolCapabilities = tool.getCapabilities?.() || [];
      return capabilities.some(cap => toolCapabilities.includes(cap));
    });
  }
}

/**
 * Calculator Tool for mathematical operations
 * Enhanced implementation with comprehensive mathematical capabilities
 */
export class CalculatorTool implements EnhancedAITool {
  id = 'calculator';
  name = 'calculator';
  description =
    'Performs mathematical calculations, evaluates expressions, and handles financial computations including tax calculations, interest, and percentages.';

  parameters: ToolParameter[] = [
    {
      name: 'expression',
      type: 'string',
      description:
        'Mathematical expression to evaluate (supports basic math, financial functions, and statistical operations)',
      required: true,
    },
    {
      name: 'type',
      type: 'string',
      description: 'Type of calculation: basic, financial, statistical, or unit_conversion',
      required: false,
      default: 'basic',
    },
    {
      name: 'precision',
      type: 'number',
      description: 'Number of decimal places for the result',
      required: false,
      default: 2,
    },
  ];

  metadata: ToolMetadata = {
    category: ToolCategory.CALCULATION,
    version: '1.0.0',
    author: 'AI Document Processor',
    tags: ['math', 'calculation', 'financial', 'statistics'],
    requirements: ['mathjs'],
  };

  async execute(input: ToolInput): Promise<ToolOutput> {
    try {
      const {
        expression,
        type = 'basic',
        precision = 2,
      } = input.parameters as {
        expression?: string;
        type?: string;
        precision?: number;
      };

      if (!expression || typeof expression !== 'string') {
        return {
          result: null,
          success: false,
          error: 'Missing or invalid expression parameter',
        };
      }

      let result: number;
      let explanation: string;

      switch (type) {
        case 'financial':
          result = this.evaluateFinancialExpression(expression);
          explanation = this.getFinancialExplanation(expression, result);
          break;
        case 'statistical':
          result = this.evaluateStatisticalExpression(expression);
          explanation = this.getStatisticalExplanation(expression, result);
          break;
        case 'unit_conversion':
          result = this.evaluateUnitConversion(expression);
          explanation = this.getUnitConversionExplanation(expression, result);
          break;
        default:
          result = evaluate(expression);
          explanation = `The result of ${expression} is ${result.toFixed(precision)}`;
      }

      const formattedResult = Number(result.toFixed(precision));

      logger.info('Calculator tool executed', {
        expression,
        type,
        result: formattedResult,
        precision,
      });

      return {
        result: {
          value: formattedResult,
          expression,
          explanation,
          type,
        },
        success: true,
        metadata: {
          expression,
          numericResult: formattedResult,
          type,
          precision,
          timestamp: new Date(),
        },
      };
    } catch (error) {
      logger.error('Calculator tool error', { input, error });
      return {
        result: null,
        success: false,
        error: `Failed to evaluate expression: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  async validate(input: ToolInput): Promise<boolean> {
    const { expression } = input.parameters as { expression?: string };

    if (!expression || typeof expression !== 'string') {
      return false;
    }

    // Basic validation - check for dangerous operations
    const dangerousPatterns = [
      /import\s/i,
      /require\s/i,
      /eval\s/i,
      /function\s/i,
      /=>/,
      /\bwhile\b/i,
      /\bfor\b/i,
    ];

    return !dangerousPatterns.some(pattern => pattern.test(expression));
  }

  getCapabilities(): string[] {
    return [
      'basic_math',
      'financial_calculations',
      'statistical_analysis',
      'unit_conversion',
      'tax_calculations',
      'interest_calculations',
      'percentage_calculations',
    ];
  }

  getRequirements(): string[] {
    return ['mathjs'];
  }

  private evaluateFinancialExpression(expression: string): number {
    // Handle common financial calculations
    if (expression.includes('compound_interest')) {
      return this.calculateCompoundInterest(expression);
    } else if (expression.includes('simple_interest')) {
      return this.calculateSimpleInterest(expression);
    } else if (expression.includes('tax_rate')) {
      return this.calculateTaxAmount(expression);
    } else if (expression.includes('percentage')) {
      return this.calculatePercentage(expression);
    }

    return evaluate(expression);
  }

  private evaluateStatisticalExpression(expression: string): number {
    // Handle statistical functions
    if (expression.includes('mean') || expression.includes('average')) {
      return this.calculateMean(expression);
    } else if (expression.includes('median')) {
      return this.calculateMedian(expression);
    } else if (expression.includes('std_dev')) {
      return this.calculateStandardDeviation(expression);
    }

    return evaluate(expression);
  }

  private evaluateUnitConversion(expression: string): number {
    // Handle unit conversions
    const conversionMap: Record<string, number | ((value: number) => number)> = {
      feet_to_meters: 0.3048,
      pounds_to_kg: 0.453592,
      fahrenheit_to_celsius: (f: number) => ((f - 32) * 5) / 9,
      celsius_to_fahrenheit: (c: number) => (c * 9) / 5 + 32,
    };

    for (const [conversion, factor] of Object.entries(conversionMap)) {
      if (expression.includes(conversion)) {
        const match = expression.match(/(\d+(?:\.\d+)?)/);
        if (match && match[1]) {
          const value = parseFloat(match[1]);
          return typeof factor === 'function' ? factor(value) : value * (factor as number);
        }
      }
    }

    return evaluate(expression);
  }

  private calculateCompoundInterest(expression: string): number {
    // Extract P, r, n, t from expression like "compound_interest(1000, 0.05, 12, 5)"
    const match = expression.match(/compound_interest\(([^)]+)\)/);
    if (match && match[1]) {
      const paramStrings = match[1].split(',');
      if (
        paramStrings.length >= 4 &&
        paramStrings[0] &&
        paramStrings[1] &&
        paramStrings[2] &&
        paramStrings[3]
      ) {
        const P = parseFloat(paramStrings[0].trim());
        const r = parseFloat(paramStrings[1].trim());
        const n = parseFloat(paramStrings[2].trim());
        const t = parseFloat(paramStrings[3].trim());
        if (!isNaN(P) && !isNaN(r) && !isNaN(n) && !isNaN(t)) {
          return P * Math.pow(1 + r / n, n * t);
        }
      }
    }
    return 0;
  }

  private calculateSimpleInterest(expression: string): number {
    // Extract P, r, t from expression like "simple_interest(1000, 0.05, 5)"
    const match = expression.match(/simple_interest\(([^)]+)\)/);
    if (match && match[1]) {
      const paramStrings = match[1].split(',');
      if (paramStrings.length >= 3 && paramStrings[0] && paramStrings[1] && paramStrings[2]) {
        const P = parseFloat(paramStrings[0].trim());
        const r = parseFloat(paramStrings[1].trim());
        const t = parseFloat(paramStrings[2].trim());
        if (!isNaN(P) && !isNaN(r) && !isNaN(t)) {
          return P * r * t;
        }
      }
    }
    return 0;
  }

  private calculateTaxAmount(expression: string): number {
    // Extract income and rate from expression like "tax_rate(50000, 0.22)"
    const match = expression.match(/tax_rate\(([^)]+)\)/);
    if (match && match[1]) {
      const params = match[1].split(',').map(x => parseFloat(x.trim()));
      if (params.length >= 2 && params[0] !== undefined && params[1] !== undefined) {
        return params[0] * params[1];
      }
    }
    return 0;
  }

  private calculatePercentage(expression: string): number {
    // Handle percentage calculations like "percentage(150, 20)" for 20% of 150
    const match = expression.match(/percentage\(([^)]+)\)/);
    if (match && match[1]) {
      const params = match[1].split(',').map(x => parseFloat(x.trim()));
      if (params.length >= 2 && params[0] !== undefined && params[1] !== undefined) {
        return (params[0] * params[1]) / 100;
      }
    }
    return 0;
  }

  private calculateMean(expression: string): number {
    const match = expression.match(/mean\(\[([^\]]+)\]\)/);
    if (match && match[1]) {
      const values = match[1].split(',').map(x => parseFloat(x.trim()));
      return values.reduce((sum, val) => sum + val, 0) / values.length;
    }
    return 0;
  }

  private calculateMedian(expression: string): number {
    const match = expression.match(/median\(\[([^\]]+)\]\)/);
    if (match && match[1]) {
      const values = match[1]
        .split(',')
        .map(x => parseFloat(x.trim()))
        .sort((a, b) => a - b);
      const mid = Math.floor(values.length / 2);
      if (values.length % 2 === 0) {
        const left = values[mid - 1];
        const right = values[mid];
        return left !== undefined && right !== undefined ? (left + right) / 2 : 0;
      } else {
        const middle = values[mid];
        return middle !== undefined ? middle : 0;
      }
    }
    return 0;
  }

  private calculateStandardDeviation(expression: string): number {
    const match = expression.match(/std_dev\(\[([^\]]+)\]\)/);
    if (match && match[1]) {
      const values = match[1].split(',').map(x => parseFloat(x.trim()));
      const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
      const variance =
        values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
      return Math.sqrt(variance);
    }
    return 0;
  }

  private getFinancialExplanation(expression: string, result: number): string {
    if (expression.includes('compound_interest')) {
      return `Compound interest calculation result: $${result.toFixed(2)}`;
    } else if (expression.includes('simple_interest')) {
      return `Simple interest amount: $${result.toFixed(2)}`;
    } else if (expression.includes('tax_rate')) {
      return `Tax amount: $${result.toFixed(2)}`;
    } else if (expression.includes('percentage')) {
      return `Percentage calculation result: ${result.toFixed(2)}`;
    }
    return `Financial calculation result: ${result.toFixed(2)}`;
  }

  private getStatisticalExplanation(expression: string, result: number): string {
    if (expression.includes('mean')) {
      return `Mean (average) value: ${result.toFixed(2)}`;
    } else if (expression.includes('median')) {
      return `Median value: ${result.toFixed(2)}`;
    } else if (expression.includes('std_dev')) {
      return `Standard deviation: ${result.toFixed(2)}`;
    }
    return `Statistical calculation result: ${result.toFixed(2)}`;
  }

  private getUnitConversionExplanation(expression: string, result: number): string {
    if (expression.includes('feet_to_meters')) {
      return `Conversion result: ${result.toFixed(2)} meters`;
    } else if (expression.includes('pounds_to_kg')) {
      return `Conversion result: ${result.toFixed(2)} kg`;
    } else if (expression.includes('fahrenheit_to_celsius')) {
      return `Conversion result: ${result.toFixed(2)}°C`;
    } else if (expression.includes('celsius_to_fahrenheit')) {
      return `Conversion result: ${result.toFixed(2)}°F`;
    }
    return `Unit conversion result: ${result.toFixed(2)}`;
  }
}

/**
 * Document Analysis Tool for comprehensive document content analysis
 * Enhanced implementation with multiple analysis types and NLP capabilities
 */
export class DocumentAnalysisTool implements EnhancedAITool {
  id = 'document_analyzer';
  name = 'document_analyzer';
  description =
    'Analyzes document content with multiple analysis types including summarization, entity extraction, sentiment analysis, classification, and quality assessment.';

  parameters: ToolParameter[] = [
    {
      name: 'documentId',
      type: 'string',
      description: 'ID of the document to analyze (alternative to content)',
      required: false,
    },
    {
      name: 'content',
      type: 'string',
      description: 'Document content to analyze directly (alternative to documentId)',
      required: false,
    },
    {
      name: 'analysisType',
      type: 'string',
      description:
        'Type of analysis: summary, entities, sentiment, classification, quality, or comprehensive',
      required: true,
    },
    {
      name: 'options',
      type: 'object',
      description: 'Additional analysis options (language, depth, etc.)',
      required: false,
    },
  ];

  metadata: ToolMetadata = {
    category: ToolCategory.ANALYSIS,
    version: '1.0.0',
    author: 'AI Document Processor',
    tags: ['document', 'analysis', 'nlp', 'entities', 'sentiment'],
    requirements: ['chromadb', 'natural'],
  };

  async execute(input: ToolInput): Promise<ToolOutput> {
    try {
      const {
        documentId,
        content,
        analysisType,
        options = {},
      } = input.parameters as {
        documentId?: string;
        content?: string;
        analysisType?: string;
        options?: Record<string, unknown>;
      };

      if (!documentId && !content) {
        return {
          result: null,
          success: false,
          error: 'Either documentId or content must be provided',
        };
      }

      if (!analysisType) {
        return {
          result: null,
          success: false,
          error:
            'analysisType must be provided (summary, entities, sentiment, classification, quality, comprehensive)',
        };
      }

      let documentContent = content;

      // If documentId provided, retrieve content from knowledge base
      if (documentId && !content) {
        const searchResults = await chromaKnowledgeBase.semanticSearch(
          documentId,
          'documents',
          1,
          0.9
        );

        if (searchResults.length === 0) {
          return {
            result: null,
            success: false,
            error: `Document with ID ${documentId} not found`,
          };
        }

        documentContent = searchResults[0]?.item.content;
      }

      if (!documentContent) {
        return {
          result: null,
          success: false,
          error: 'No document content available for analysis',
        };
      }

      let result: unknown;

      switch (analysisType) {
        case 'summary':
          result = this.generateSummary(documentContent, options);
          break;
        case 'entities':
          result = this.extractEntities(documentContent, options);
          break;
        case 'sentiment':
          result = this.analyzeSentiment(documentContent, options);
          break;
        case 'classification':
          result = this.classifyDocument(documentContent, options);
          break;
        case 'quality':
          result = this.assessDocumentQuality(documentContent, options);
          break;
        case 'comprehensive':
          result = await this.performComprehensiveAnalysis(documentContent, options);
          break;
        default:
          return {
            result: null,
            success: false,
            error: `Unsupported analysis type: ${analysisType}`,
          };
      }

      logger.info('Document analysis completed', {
        documentId,
        analysisType,
        contentLength: documentContent.length,
      });

      return {
        result,
        success: true,
        metadata: {
          documentId,
          analysisType,
          contentLength: documentContent.length,
          timestamp: new Date(),
          options,
        },
      };
    } catch (error) {
      logger.error('Document analysis tool error', { input, error });
      return {
        result: null,
        success: false,
        error: `Document analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  async validate(input: ToolInput): Promise<boolean> {
    const { documentId, content, analysisType } = input.parameters as {
      documentId?: string;
      content?: string;
      analysisType?: string;
    };

    // Must have either documentId or content
    if (!documentId && !content) {
      return false;
    }

    // Must have valid analysis type
    const validTypes = [
      'summary',
      'entities',
      'sentiment',
      'classification',
      'quality',
      'comprehensive',
    ];
    if (!analysisType || !validTypes.includes(analysisType)) {
      return false;
    }

    return true;
  }

  getCapabilities(): string[] {
    return [
      'document_summarization',
      'entity_extraction',
      'sentiment_analysis',
      'document_classification',
      'quality_assessment',
      'comprehensive_analysis',
      'nlp_processing',
    ];
  }

  getRequirements(): string[] {
    return ['chromadb', 'natural'];
  }

  private generateSummary(content: string, options: Record<string, unknown> = {}): object {
    const { maxSentences = 3, extractKeywords = true } = options;

    // Enhanced extractive summarization
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);

    // Score sentences based on word frequency and position
    const wordFreq: Record<string, number> = {};
    const words = content.toLowerCase().match(/\b\w+\b/g) || [];

    words.forEach(word => {
      if (word.length > 3) {
        wordFreq[word] = (wordFreq[word] || 0) + 1;
      }
    });

    const scoredSentences = sentences.map((sentence, index) => {
      const sentenceWords = sentence.toLowerCase().match(/\b\w+\b/g) || [];
      const score =
        sentenceWords.reduce((sum, word) => sum + (wordFreq[word] || 0), 0) / sentenceWords.length;
      const positionScore = 1 - index / sentences.length; // Earlier sentences get higher scores

      return {
        sentence: sentence.trim(),
        score: score + positionScore * 0.3,
        position: index,
      };
    });

    // Select top sentences
    const topSentences = scoredSentences
      .sort((a, b) => b.score - a.score)
      .slice(0, maxSentences as number)
      .sort((a, b) => a.position - b.position)
      .map(s => s.sentence);

    const summary = topSentences.join('. ') + (topSentences.length > 0 ? '.' : '');

    const result: any = {
      summary,
      sentenceCount: topSentences.length,
      originalLength: content.length,
      compressionRatio: summary.length / content.length,
    };

    if (extractKeywords) {
      const keywords = Object.entries(wordFreq)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([word]) => word);
      result.keywords = keywords;
    }

    return result;
  }

  private extractEntities(content: string, options: Record<string, unknown> = {}): object {
    const { includePositions = false } = options;

    // Enhanced entity extraction with more patterns
    const patterns = {
      emails: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
      phones: /\b(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})\b/g,
      dates: /\b(?:\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}|\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2})\b/g,
      money: /\$\d+(?:,\d{3})*(?:\.\d{2})?|\b\d+(?:,\d{3})*(?:\.\d{2})?\s*(?:dollars?|USD)\b/g,
      ssn: /\b\d{3}-\d{2}-\d{4}\b/g,
      urls: /https?:\/\/(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?/g,
      addresses:
        /\b\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Lane|Ln|Drive|Dr|Court|Ct|Place|Pl)\b/gi,
      names: /\b[A-Z][a-z]+\s+[A-Z][a-z]+\b/g, // Simple name pattern
    };

    const entities: Record<string, any[]> = {};

    for (const [type, pattern] of Object.entries(patterns)) {
      const matches = Array.from(content.matchAll(pattern));
      entities[type] = matches.map(match => {
        const entity: any = { text: match[0] };
        if (includePositions) {
          entity.start = match.index;
          entity.end = match.index! + match[0].length;
        }
        return entity;
      });
    }

    // Calculate confidence scores
    const confidence = Object.entries(entities).reduce(
      (acc, [type, items]) => {
        acc[type] = items.length > 0 ? Math.min(0.9, 0.5 + items.length * 0.1) : 0;
        return acc;
      },
      {} as Record<string, number>
    );

    return {
      entities,
      confidence,
      totalEntities: Object.values(entities).reduce((sum, items) => sum + items.length, 0),
      types: Object.keys(entities).filter(type => entities[type] && entities[type].length > 0),
    };
  }

  private analyzeSentiment(content: string, options: Record<string, unknown> = {}): object {
    const { includeEmotions = false } = options;

    // Enhanced sentiment analysis with more comprehensive word lists
    const sentimentWords = {
      positive: [
        'good',
        'great',
        'excellent',
        'amazing',
        'wonderful',
        'fantastic',
        'outstanding',
        'positive',
        'happy',
        'satisfied',
        'pleased',
        'delighted',
        'thrilled',
        'excited',
        'love',
        'like',
        'enjoy',
        'appreciate',
        'recommend',
        'perfect',
        'brilliant',
        'success',
        'successful',
        'achievement',
        'accomplish',
        'win',
        'victory',
      ],
      negative: [
        'bad',
        'terrible',
        'awful',
        'horrible',
        'disgusting',
        'disappointing',
        'poor',
        'negative',
        'unhappy',
        'dissatisfied',
        'angry',
        'frustrated',
        'annoyed',
        'upset',
        'hate',
        'dislike',
        'despise',
        'regret',
        'mistake',
        'error',
        'problem',
        'issue',
        'fail',
        'failure',
        'lose',
        'loss',
        'defeat',
        'disaster',
        'crisis',
      ],
      neutral: [
        'okay',
        'fine',
        'average',
        'normal',
        'standard',
        'typical',
        'usual',
        'regular',
        'acceptable',
        'adequate',
        'sufficient',
        'moderate',
        'fair',
        'reasonable',
      ],
    };

    const words = content.toLowerCase().match(/\b\w+\b/g) || [];
    const scores = {
      positive: 0,
      negative: 0,
      neutral: 0,
    };

    words.forEach(word => {
      if (sentimentWords.positive.includes(word)) scores.positive++;
      else if (sentimentWords.negative.includes(word)) scores.negative++;
      else if (sentimentWords.neutral.includes(word)) scores.neutral++;
    });

    const total = scores.positive + scores.negative + scores.neutral;
    const sentiment =
      scores.positive > scores.negative
        ? 'positive'
        : scores.negative > scores.positive
          ? 'negative'
          : 'neutral';

    // Calculate polarity (-1 to 1)
    const polarity = total > 0 ? (scores.positive - scores.negative) / total : 0;

    // Calculate confidence
    const confidence =
      total > 0 ? Math.max(scores.positive, scores.negative, scores.neutral) / total : 0;

    const result: any = {
      sentiment,
      polarity,
      confidence,
      scores,
      wordCount: words.length,
      sentimentWordCount: total,
    };

    if (includeEmotions) {
      // Basic emotion detection
      const emotions = {
        joy: ['happy', 'joy', 'excited', 'thrilled', 'delighted'],
        anger: ['angry', 'mad', 'furious', 'rage', 'annoyed'],
        sadness: ['sad', 'depressed', 'disappointed', 'upset', 'hurt'],
        fear: ['afraid', 'scared', 'worried', 'anxious', 'nervous'],
        surprise: ['surprised', 'shocked', 'amazed', 'astonished'],
      };

      const emotionScores: Record<string, number> = {};
      Object.entries(emotions).forEach(([emotion, emotionWords]) => {
        emotionScores[emotion] = words.filter(word => emotionWords.includes(word)).length;
      });

      result.emotions = emotionScores;
    }

    return result;
  }

  private classifyDocument(content: string, options: Record<string, unknown> = {}): object {
    const { includeConfidence = true, customCategories } = options;

    // Enhanced document classification with more categories and better scoring
    const defaultCategories = {
      tax: {
        keywords: [
          'tax',
          'irs',
          'deduction',
          'income',
          'w-2',
          '1099',
          'refund',
          'filing',
          'return',
          'withholding',
        ],
        weight: 1.0,
      },
      legal: {
        keywords: [
          'contract',
          'agreement',
          'legal',
          'court',
          'lawsuit',
          'attorney',
          'law',
          'clause',
          'terms',
          'liability',
        ],
        weight: 1.0,
      },
      financial: {
        keywords: [
          'bank',
          'account',
          'statement',
          'balance',
          'transaction',
          'payment',
          'credit',
          'debit',
          'loan',
          'mortgage',
        ],
        weight: 1.0,
      },
      medical: {
        keywords: [
          'medical',
          'doctor',
          'patient',
          'diagnosis',
          'treatment',
          'hospital',
          'clinic',
          'prescription',
          'health',
        ],
        weight: 1.0,
      },
      insurance: {
        keywords: [
          'insurance',
          'policy',
          'claim',
          'coverage',
          'premium',
          'deductible',
          'beneficiary',
          'underwriter',
        ],
        weight: 1.0,
      },
      business: {
        keywords: [
          'business',
          'company',
          'corporation',
          'llc',
          'partnership',
          'revenue',
          'profit',
          'employee',
          'management',
        ],
        weight: 1.0,
      },
      personal: {
        keywords: [
          'personal',
          'family',
          'home',
          'address',
          'phone',
          'email',
          'birthday',
          'spouse',
          'children',
        ],
        weight: 0.8,
      },
    };

    const categories = customCategories || defaultCategories;
    const contentLower = content.toLowerCase();
    // const contentWords = contentLower.match(/\b\w+\b/g) || [];

    const scores: Record<string, number> = {};
    const details: Record<string, any> = {};

    Object.entries(categories).forEach(([category, config]: [string, any]) => {
      const { keywords, weight = 1.0 } = config;
      let score = 0;
      const foundKeywords: string[] = [];

      keywords.forEach((keyword: string) => {
        const occurrences = (contentLower.match(new RegExp(`\\b${keyword}\\b`, 'g')) || []).length;
        if (occurrences > 0) {
          score += occurrences * weight;
          foundKeywords.push(keyword);
        }
      });

      scores[category] = score;
      details[category] = {
        score,
        foundKeywords,
        keywordCount: foundKeywords.length,
        totalOccurrences: foundKeywords.reduce(
          (sum, kw) => sum + (contentLower.match(new RegExp(`\\b${kw}\\b`, 'g')) || []).length,
          0
        ),
      };
    });

    // Find top categories
    const sortedCategories = Object.entries(scores)
      .sort(([, a], [, b]) => b - a)
      .filter(([, score]) => score > 0);

    const topCategory = sortedCategories[0]?.[0] || 'unknown';
    const topScore = sortedCategories[0]?.[1] || 0;

    // Calculate confidence
    const totalScore = Object.values(scores).reduce((sum, score) => sum + score, 0);
    const confidence = totalScore > 0 ? topScore / totalScore : 0;

    const result: any = {
      category: topCategory,
      alternativeCategories: sortedCategories
        .slice(1, 3)
        .map(([cat, score]) => ({ category: cat, score })),
      details,
    };

    if (includeConfidence) {
      result.confidence = confidence;
      result.totalScore = totalScore;
    }

    return result;
  }

  private assessDocumentQuality(content: string, options: Record<string, unknown> = {}): object {
    const { checkReadability = true, checkCompleteness = true } = options;

    const words = content.match(/\b\w+\b/g) || [];
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);

    // Basic quality metrics
    const metrics = {
      wordCount: words.length,
      sentenceCount: sentences.length,
      paragraphCount: paragraphs.length,
      averageWordsPerSentence: sentences.length > 0 ? words.length / sentences.length : 0,
      averageSentencesPerParagraph:
        paragraphs.length > 0 ? sentences.length / paragraphs.length : 0,
    };

    const quality: any = {
      metrics,
      issues: [] as string[],
      score: 100, // Start with perfect score and deduct
    };

    // Check for quality issues
    if (metrics.wordCount < 50) {
      quality.issues.push('Document is very short');
      quality.score -= 20;
    }

    if (metrics.averageWordsPerSentence > 30) {
      quality.issues.push('Sentences are too long on average');
      quality.score -= 10;
    }

    if (metrics.averageWordsPerSentence < 5) {
      quality.issues.push('Sentences are too short on average');
      quality.score -= 10;
    }

    // Check for spelling/grammar indicators
    const misspellingIndicators = content.match(/\b\w*[0-9]+\w*\b/g) || [];
    if (misspellingIndicators.length > words.length * 0.05) {
      quality.issues.push('Possible OCR errors or misspellings detected');
      quality.score -= 15;
    }

    if (checkReadability) {
      // Simple readability assessment (Flesch-like)
      const avgSentenceLength = metrics.averageWordsPerSentence;
      const complexWords = words.filter(word => word.length > 6).length;
      const complexWordRatio = complexWords / words.length;

      quality.readability = {
        averageSentenceLength: avgSentenceLength,
        complexWordRatio,
        estimatedGradeLevel: Math.max(
          1,
          Math.min(20, avgSentenceLength * 0.4 + complexWordRatio * 100)
        ),
      };
    }

    if (checkCompleteness) {
      // Check for completeness indicators
      const incompleteIndicators = [
        /\[.*\]/g, // Placeholder brackets
        /_{3,}/g, // Underscores for filling
        /\.{3,}/g, // Multiple dots
        /\bTBD\b/gi, // To be determined
        /\bTODO\b/gi, // Todo items
      ];

      const incompleteMatches = incompleteIndicators.reduce(
        (count, pattern) => count + (content.match(pattern) || []).length,
        0
      );

      if (incompleteMatches > 0) {
        quality.issues.push(`${incompleteMatches} incomplete sections detected`);
        quality.score -= incompleteMatches * 5;
      }

      quality.completeness = {
        incompleteIndicators: incompleteMatches,
        estimatedCompleteness: Math.max(0, 100 - incompleteMatches * 10),
      };
    }

    quality.score = Math.max(0, quality.score);
    quality.grade =
      quality.score >= 90
        ? 'A'
        : quality.score >= 80
          ? 'B'
          : quality.score >= 70
            ? 'C'
            : quality.score >= 60
              ? 'D'
              : 'F';

    return quality;
  }

  private async performComprehensiveAnalysis(
    content: string,
    options: Record<string, unknown> = {}
  ): Promise<object> {
    // Perform all analysis types
    const summary = this.generateSummary(content, options);
    const entities = this.extractEntities(content, options);
    const sentiment = this.analyzeSentiment(content, { ...options, includeEmotions: true });
    const classification = this.classifyDocument(content, options);
    const quality = this.assessDocumentQuality(content, options);

    return {
      summary,
      entities,
      sentiment,
      classification,
      quality,
      metadata: {
        analysisDate: new Date(),
        contentLength: content.length,
        analysisVersion: '1.0.0',
      },
    };
  }
}

/**
 * Form Filling Tool for intelligent form completion
 * Enhanced implementation with advanced field mapping and validation
 */
export class FormFillingTool implements EnhancedAITool {
  id = 'form_filler';
  name = 'form_filler';
  description =
    'Intelligently fills form fields based on extracted data, context, and template matching with comprehensive validation and error checking.';

  parameters: ToolParameter[] = [
    {
      name: 'formFields',
      type: 'array',
      description: 'Array of form field definitions with id, name, type, and validation rules',
      required: true,
    },
    {
      name: 'availableData',
      type: 'object',
      description: 'Available data to use for filling fields',
      required: true,
    },
    {
      name: 'documentContext',
      type: 'string',
      description: 'Additional document context for field mapping',
      required: false,
    },
    {
      name: 'template',
      type: 'object',
      description: 'Form template with predefined mappings',
      required: false,
    },
    {
      name: 'validationRules',
      type: 'object',
      description: 'Custom validation rules for fields',
      required: false,
    },
  ];

  metadata: ToolMetadata = {
    category: ToolCategory.FORM_FILLING,
    version: '1.0.0',
    author: 'AI Document Processor',
    tags: ['form', 'filling', 'mapping', 'validation', 'template'],
    requirements: ['chromadb'],
  };

  async execute(input: ToolInput): Promise<ToolOutput> {
    try {
      const {
        formFields,
        availableData,
        documentContext,
        template,
        validationRules = {},
      } = input.parameters as {
        formFields?: unknown[];
        availableData?: Record<string, unknown>;
        documentContext?: string;
        template?: Record<string, unknown>;
        validationRules?: Record<string, unknown>;
      };

      if (!formFields || !Array.isArray(formFields)) {
        return {
          result: null,
          success: false,
          error: 'formFields must be provided as an array',
        };
      }

      if (!availableData || typeof availableData !== 'object') {
        return {
          result: null,
          success: false,
          error: 'availableData must be provided as an object',
        };
      }

      const filledFields: Record<string, any> = {};
      const suggestions: string[] = [];
      const validationErrors: string[] = [];
      const confidence: Record<string, number> = {};

      for (const field of formFields as Array<{
        id: string;
        name: string;
        type?: string;
        required?: boolean;
        validation?: Record<string, unknown>;
      }>) {
        const mappingResult = await this.findValueForField(
          field,
          availableData,
          documentContext,
          template
        );

        if (mappingResult.value !== null) {
          // Validate the value
          const validationResult = this.validateFieldValue(
            field,
            mappingResult.value,
            validationRules
          );

          if (validationResult.isValid) {
            filledFields[field.id] = mappingResult.value;
            confidence[field.id] = mappingResult.confidence;
          } else {
            validationErrors.push(`Field '${field.name}': ${validationResult.error}`);
            if (field.required) {
              suggestions.push(
                `Required field '${field.name}' has validation errors: ${validationResult.error}`
              );
            }
          }
        } else if (field.required) {
          suggestions.push(`Required field '${field.name}' could not be filled automatically`);
        }
      }

      // Calculate overall confidence
      const overallConfidence =
        Object.values(confidence).length > 0
          ? Object.values(confidence).reduce((sum, conf) => sum + conf, 0) /
            Object.values(confidence).length
          : 0;

      logger.info('Form filling completed', {
        totalFields: formFields.length,
        filledFields: Object.keys(filledFields).length,
        suggestions: suggestions.length,
        validationErrors: validationErrors.length,
        overallConfidence,
      });

      return {
        result: {
          filledFields,
          suggestions,
          validationErrors,
          confidence,
          summary: `Filled ${Object.keys(filledFields).length} out of ${formFields.length} fields with ${overallConfidence.toFixed(2)} average confidence`,
          overallConfidence,
          completionRate: Object.keys(filledFields).length / formFields.length,
        },
        success: true,
        metadata: {
          totalFields: formFields.length,
          filledCount: Object.keys(filledFields).length,
          suggestionCount: suggestions.length,
          validationErrorCount: validationErrors.length,
          overallConfidence,
          timestamp: new Date(),
        },
      };
    } catch (error) {
      logger.error('Form filling tool error', { input, error });
      return {
        result: null,
        success: false,
        error: `Form filling failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  async validate(input: ToolInput): Promise<boolean> {
    const { formFields, availableData } = input.parameters as {
      formFields?: unknown[];
      availableData?: Record<string, unknown>;
    };

    if (!formFields || !Array.isArray(formFields)) {
      return false;
    }

    if (!availableData || typeof availableData !== 'object') {
      return false;
    }

    // Validate form field structure
    return formFields.every(
      field => typeof field === 'object' && field !== null && 'id' in field && 'name' in field
    );
  }

  getCapabilities(): string[] {
    return [
      'form_field_detection',
      'intelligent_mapping',
      'data_validation',
      'template_matching',
      'constraint_checking',
      'confidence_scoring',
    ];
  }

  getRequirements(): string[] {
    return ['chromadb'];
  }

  private async findValueForField(
    field: {
      id: string;
      name: string;
      type?: string;
      label?: string;
      validation?: Record<string, unknown>;
    },
    availableData: Record<string, unknown>,
    context?: string,
    template?: Record<string, unknown>
  ): Promise<{ value: unknown; confidence: number; source: string }> {
    const fieldName = field.name.toLowerCase();
    const fieldLabel = field.label?.toLowerCase() || '';
    const fieldType = field.type?.toLowerCase() || 'text';

    // 1. Check template mappings first (highest confidence)
    if (template && template[field.id]) {
      const templateMapping = template[field.id] as string;
      if (availableData[templateMapping]) {
        return {
          value: availableData[templateMapping],
          confidence: 0.95,
          source: 'template_mapping',
        };
      }
    }

    // 2. Direct field name matching (high confidence)
    if (availableData[field.name]) {
      return {
        value: availableData[field.name],
        confidence: 0.9,
        source: 'direct_match',
      };
    }

    // 3. Enhanced fuzzy matching with type-aware patterns
    const typePatterns = this.getFieldPatterns(fieldType);
    const allPatterns = {
      ...typePatterns,
      // General patterns
      name: ['name', 'full_name', 'fullname', 'first_name', 'last_name', 'fname', 'lname'],
      email: ['email', 'email_address', 'e_mail', 'mail', 'email_addr'],
      phone: ['phone', 'telephone', 'phone_number', 'tel', 'mobile', 'cell'],
      address: ['address', 'street', 'street_address', 'addr', 'location'],
      city: ['city', 'town', 'municipality'],
      state: ['state', 'province', 'region'],
      zip: ['zip', 'postal_code', 'zipcode', 'postcode'],
      ssn: ['ssn', 'social_security', 'social_security_number', 'sin'],
      date: ['date', 'birth_date', 'dob', 'birthday', 'born'],
      income: ['income', 'salary', 'wage', 'earnings', 'gross_income'],
      tax: ['tax', 'taxes', 'tax_amount', 'withholding'],
    };

    // Find best pattern match
    let bestMatch: { value: unknown; confidence: number; source: string } | null = null;

    for (const [category, variations] of Object.entries(allPatterns)) {
      if (variations.some(v => fieldName.includes(v) || fieldLabel.includes(v))) {
        // Look for matching data with semantic similarity
        for (const [key, value] of Object.entries(availableData)) {
          const keyLower = key.toLowerCase();
          const similarity = this.calculateSimilarity([fieldName, fieldLabel].join(' '), keyLower);

          if (similarity > 0.6 || variations.some(v => keyLower.includes(v))) {
            const confidence = Math.min(0.85, 0.5 + similarity * 0.35);
            if (!bestMatch || confidence > bestMatch.confidence) {
              bestMatch = {
                value,
                confidence,
                source: `fuzzy_match_${category}`,
              };
            }
          }
        }
      }
    }

    if (bestMatch) {
      return bestMatch;
    }

    // 4. Context-based extraction (medium confidence)
    if (context) {
      const contextValue = this.extractFromContext(fieldName, fieldLabel, context, fieldType);
      if (contextValue) {
        return {
          value: contextValue,
          confidence: 0.7,
          source: 'context_extraction',
        };
      }
    }

    // 5. Semantic search in knowledge base (lower confidence)
    try {
      const searchQuery = `${field.name} ${field.label || ''} ${fieldType}`.trim();
      const searchResults = await chromaKnowledgeBase.semanticSearch(
        searchQuery,
        'extracted_data',
        3,
        0.6
      );

      if (searchResults.length > 0) {
        const bestResult = searchResults[0];
        if (bestResult) {
          return {
            value: bestResult.item.content,
            confidence: Math.min(0.6, bestResult.relevance),
            source: 'semantic_search',
          };
        }
      }
    } catch (error) {
      logger.warn('Semantic search failed during form filling', { error });
    }

    return { value: null, confidence: 0, source: 'not_found' };
  }

  private getFieldPatterns(fieldType: string): Record<string, string[]> {
    const patterns: Record<string, string[]> = {
      email: ['email', 'e_mail', 'mail', 'email_address'],
      phone: ['phone', 'tel', 'telephone', 'mobile', 'cell'],
      date: ['date', 'time', 'when', 'day', 'month', 'year'],
      number: ['amount', 'value', 'count', 'quantity', 'num'],
      currency: ['price', 'cost', 'amount', 'fee', 'charge', 'payment'],
      text: ['name', 'title', 'description', 'comment', 'note'],
      url: ['url', 'link', 'website', 'site', 'web'],
      password: ['password', 'pass', 'pwd', 'secret'],
    };

    return { [fieldType]: patterns[fieldType] || [] };
  }

  private calculateSimilarity(str1: string, str2: string): number {
    // Simple Jaccard similarity
    const set1 = new Set(str1.toLowerCase().split(/\W+/));
    const set2 = new Set(str2.toLowerCase().split(/\W+/));

    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);

    return union.size > 0 ? intersection.size / union.size : 0;
  }

  private extractFromContext(
    fieldName: string,
    fieldLabel: string,
    context: string,
    fieldType: string
  ): string | null {
    const searchTerms = [fieldName, fieldLabel].filter(Boolean);

    // Type-specific extraction patterns
    const typePatterns: Record<string, RegExp[]> = {
      email: [/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g],
      phone: [/\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g],
      date: [/\b\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}\b/g],
      currency: [/\$\d+(?:,\d{3})*(?:\.\d{2})?/g],
      ssn: [/\b\d{3}-\d{2}-\d{4}\b/g],
    };

    // Try type-specific patterns first
    if (typePatterns[fieldType]) {
      for (const pattern of typePatterns[fieldType]) {
        const matches = context.match(pattern);
        if (matches && matches.length > 0) {
          return matches[0];
        }
      }
    }

    // General context extraction
    for (const term of searchTerms) {
      const patterns = [
        new RegExp(`${term}[:\\s]+([^\\n\\r,;]+)`, 'i'),
        new RegExp(`${term}\\s*=\\s*([^\\n\\r,;]+)`, 'i'),
        new RegExp(`${term}\\s*-\\s*([^\\n\\r,;]+)`, 'i'),
      ];

      for (const pattern of patterns) {
        const match = pattern.exec(context);
        if (match?.[1]) {
          return match[1].trim();
        }
      }
    }

    return null;
  }

  private validateFieldValue(
    field: {
      type?: string;
      validation?: Record<string, unknown>;
    },
    value: unknown,
    _customRules: Record<string, unknown>
  ): { isValid: boolean; error?: string } {
    const fieldType = field.type?.toLowerCase() || 'text';
    const validation = field.validation || {};

    // Type validation
    switch (fieldType) {
      case 'email':
        if (typeof value === 'string' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          return { isValid: false, error: 'Invalid email format' };
        }
        break;
      case 'phone':
        if (
          typeof value === 'string' &&
          !/^\d{3}[-.]?\d{3}[-.]?\d{4}$/.test(value.replace(/\s/g, ''))
        ) {
          return { isValid: false, error: 'Invalid phone number format' };
        }
        break;
      case 'number':
        if (typeof value !== 'number' && (typeof value !== 'string' || isNaN(Number(value)))) {
          return { isValid: false, error: 'Value must be a number' };
        }
        break;
      case 'date':
        if (typeof value === 'string' && isNaN(Date.parse(value))) {
          return { isValid: false, error: 'Invalid date format' };
        }
        break;
    }

    // Custom validation rules
    if (validation.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
      return { isValid: false, error: 'Field is required' };
    }

    if (
      validation.minLength &&
      typeof value === 'string' &&
      value.length < (validation.minLength as number)
    ) {
      return { isValid: false, error: `Minimum length is ${validation.minLength}` };
    }

    if (
      validation.maxLength &&
      typeof value === 'string' &&
      value.length > (validation.maxLength as number)
    ) {
      return { isValid: false, error: `Maximum length is ${validation.maxLength}` };
    }

    if (validation.pattern && typeof value === 'string') {
      const pattern = new RegExp(validation.pattern as string);
      if (!pattern.test(value)) {
        return { isValid: false, error: 'Value does not match required pattern' };
      }
    }

    return { isValid: true };
  }
}

/**
 * Knowledge Base Search Tool for semantic information retrieval
 * Enhanced implementation with advanced search capabilities and result ranking
 */
export class KnowledgeSearchTool implements EnhancedAITool {
  id = 'knowledge_search';
  name = 'knowledge_search';
  description =
    'Searches the knowledge base using semantic search with advanced filtering, ranking, and contextual retrieval capabilities.';

  parameters: ToolParameter[] = [
    {
      name: 'query',
      type: 'string',
      description: 'Search query for semantic search',
      required: true,
    },
    {
      name: 'collection',
      type: 'string',
      description: 'Collection name to search in',
      required: false,
      default: 'knowledge_base',
    },
    {
      name: 'limit',
      type: 'number',
      description: 'Maximum number of results to return',
      required: false,
      default: 5,
    },
    {
      name: 'threshold',
      type: 'number',
      description: 'Minimum relevance threshold (0-1)',
      required: false,
      default: 0.7,
    },
    {
      name: 'filters',
      type: 'object',
      description: 'Additional filters for search (category, tags, etc.)',
      required: false,
    },
    {
      name: 'includeMetadata',
      type: 'boolean',
      description: 'Include metadata in results',
      required: false,
      default: true,
    },
  ];

  metadata: ToolMetadata = {
    category: ToolCategory.KNOWLEDGE_BASE,
    version: '1.0.0',
    author: 'AI Document Processor',
    tags: ['search', 'knowledge', 'semantic', 'retrieval', 'chromadb'],
    requirements: ['chromadb'],
  };

  async execute(input: ToolInput): Promise<ToolOutput> {
    try {
      const {
        query,
        collection = 'knowledge_base',
        limit = 5,
        threshold = 0.7,
        filters = {},
        includeMetadata = true,
      } = input.parameters as {
        query?: string;
        collection?: string;
        limit?: number;
        threshold?: number;
        filters?: Record<string, unknown>;
        includeMetadata?: boolean;
      };

      if (!query || typeof query !== 'string') {
        return {
          result: null,
          success: false,
          error: 'Query must be provided as a string',
        };
      }

      // Perform semantic search
      const results = await chromaKnowledgeBase.semanticSearch(query, collection, limit, threshold);

      // Apply additional filters if provided
      const filteredResults = this.applyFilters(results, filters);

      // Rank and enhance results
      const enhancedResults = await this.enhanceResults(filteredResults, query, includeMetadata);

      if (enhancedResults.length === 0) {
        return {
          result: {
            message: `No relevant information found for query: "${query}"`,
            query,
            collection,
            resultsCount: 0,
            searchMetadata: {
              originalResultCount: results.length,
              filteredResultCount: 0,
              threshold,
              timestamp: new Date(),
            },
          },
          success: true,
          metadata: {
            query,
            collection,
            resultsCount: 0,
            threshold,
          },
        };
      }

      // Format results for different output types
      const formattedResults = this.formatResults(enhancedResults, includeMetadata);

      logger.info('Knowledge search completed', {
        query,
        collection,
        originalResults: results.length,
        filteredResults: filteredResults.length,
        finalResults: enhancedResults.length,
        threshold,
      });

      return {
        result: {
          results: formattedResults,
          summary: `Found ${enhancedResults.length} relevant results for "${query}"`,
          query,
          collection,
          resultsCount: enhancedResults.length,
          searchMetadata: {
            originalResultCount: results.length,
            filteredResultCount: filteredResults.length,
            finalResultCount: enhancedResults.length,
            averageRelevance:
              enhancedResults.reduce((sum, r) => sum + r.relevance, 0) / enhancedResults.length,
            threshold,
            timestamp: new Date(),
          },
        },
        success: true,
        metadata: {
          query,
          collection,
          resultsCount: enhancedResults.length,
          averageRelevance:
            enhancedResults.reduce((sum, r) => sum + r.relevance, 0) / enhancedResults.length,
          timestamp: new Date(),
        },
      };
    } catch (error) {
      logger.error('Knowledge search tool error', { input, error });
      return {
        result: null,
        success: false,
        error: `Knowledge search failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  async validate(input: ToolInput): Promise<boolean> {
    const { query, limit, threshold } = input.parameters as {
      query?: string;
      limit?: number;
      threshold?: number;
    };

    if (!query || typeof query !== 'string' || query.trim().length === 0) {
      return false;
    }

    if (limit !== undefined && (typeof limit !== 'number' || limit < 1 || limit > 100)) {
      return false;
    }

    if (
      threshold !== undefined &&
      (typeof threshold !== 'number' || threshold < 0 || threshold > 1)
    ) {
      return false;
    }

    return true;
  }

  getCapabilities(): string[] {
    return [
      'semantic_search',
      'knowledge_retrieval',
      'result_ranking',
      'contextual_search',
      'metadata_filtering',
      'relevance_scoring',
    ];
  }

  getRequirements(): string[] {
    return ['chromadb'];
  }

  private applyFilters(
    results: Array<{ item: any; relevance: number }>,
    filters: Record<string, unknown>
  ): Array<{ item: any; relevance: number }> {
    if (!filters || Object.keys(filters).length === 0) {
      return results;
    }

    return results.filter(result => {
      const metadata = result.item.metadata || {};

      for (const [key, value] of Object.entries(filters)) {
        if (key === 'category' && metadata.category !== value) {
          return false;
        }
        if (key === 'tags' && Array.isArray(value)) {
          const itemTags = metadata.tags || [];
          if (!value.some(tag => itemTags.includes(tag))) {
            return false;
          }
        }
        if (key === 'minDate' && metadata.createdAt) {
          if (new Date(metadata.createdAt) < new Date(value as string)) {
            return false;
          }
        }
        if (key === 'maxDate' && metadata.createdAt) {
          if (new Date(metadata.createdAt) > new Date(value as string)) {
            return false;
          }
        }
        if (key === 'source' && metadata.source !== value) {
          return false;
        }
      }

      return true;
    });
  }

  private async enhanceResults(
    results: Array<{ item: any; relevance: number }>,
    query: string,
    includeMetadata: boolean
  ): Promise<Array<{ item: any; relevance: number; enhancedScore: number; context?: string }>> {
    return results
      .map(result => {
        // Calculate enhanced score based on multiple factors
        let enhancedScore = result.relevance;

        // Boost score for exact matches
        const content = result.item.content?.toLowerCase() || '';
        const queryLower = query.toLowerCase();
        if (content.includes(queryLower)) {
          enhancedScore += 0.1;
        }

        // Boost score for recent content
        if (result.item.metadata?.createdAt) {
          const age = Date.now() - new Date(result.item.metadata.createdAt).getTime();
          const daysSinceCreation = age / (1000 * 60 * 60 * 24);
          if (daysSinceCreation < 30) {
            enhancedScore += 0.05;
          }
        }

        // Boost score for high-confidence content
        if (result.item.metadata?.confidence && result.item.metadata.confidence > 0.8) {
          enhancedScore += 0.05;
        }

        // Add context if available
        let context: string | undefined;
        if (includeMetadata && result.item.metadata) {
          const contextParts = [];
          if (result.item.metadata.source) {
            contextParts.push(`Source: ${result.item.metadata.source}`);
          }
          if (result.item.metadata.category) {
            contextParts.push(`Category: ${result.item.metadata.category}`);
          }
          if (result.item.metadata.createdAt) {
            contextParts.push(
              `Created: ${new Date(result.item.metadata.createdAt).toLocaleDateString()}`
            );
          }
          context = contextParts.join(' | ');
        }

        const resultObj: { item: any; relevance: number; enhancedScore: number; context?: string } =
          {
            ...result,
            enhancedScore: Math.min(1.0, enhancedScore),
          };

        if (context) {
          resultObj.context = context;
        }

        return resultObj;
      })
      .sort((a, b) => b.enhancedScore - a.enhancedScore);
  }

  private formatResults(
    results: Array<{ item: any; relevance: number; enhancedScore: number; context?: string }>,
    includeMetadata: boolean
  ): Array<{
    content: string;
    relevance: number;
    score: number;
    metadata?: any;
    context?: string;
  }> {
    return results.map((result, index) => {
      const formatted: any = {
        rank: index + 1,
        content: result.item.content || '',
        relevance: Number(result.relevance.toFixed(3)),
        score: Number(result.enhancedScore.toFixed(3)),
      };

      if (includeMetadata && result.item.metadata) {
        formatted.metadata = result.item.metadata;
      }

      if (result.context) {
        formatted.context = result.context;
      }

      return formatted;
    });
  }
}

// Export all tools for easy access
export const AI_TOOLS = {
  calculator: CalculatorTool,
  documentAnalyzer: DocumentAnalysisTool,
  formFiller: FormFillingTool,
  knowledgeSearch: KnowledgeSearchTool,
};

export const createAITools = (): EnhancedAITool[] => [
  new CalculatorTool(),
  new DocumentAnalysisTool(),
  new FormFillingTool(),
  new KnowledgeSearchTool(),
];

export const getToolByName = (name: string): EnhancedAITool | undefined => {
  const tools = createAITools();
  return tools.find(tool => tool.name === name);
};

// Create and export singleton instance
export const aiToolsManager = new AIToolsManager();

// Export registry and executor for direct access
export const toolRegistry = aiToolsManager.getRegistry();
export const toolExecutor = aiToolsManager.getExecutor();

import { createLogger } from '../utils/logger';
import { CalculationEngine } from './CalculationEngine';

const logger = createLogger('FormulaEditor');

export interface FormulaEditorOptions {
  enableSyntaxHighlighting: boolean;
  enableAutocomplete: boolean;
  enableValidation: boolean;
  enableTesting: boolean;
  maxFormulaLength: number;
  autoSaveInterval: number;
}

export interface FormulaSuggestion {
  text: string;
  type: 'function' | 'constant' | 'variable' | 'operator' | 'keyword';
  description: string;
  insertText: string;
  detail?: string;
  documentation?: string;
  parameters?: FormulaParameter[];
}

export interface FormulaParameter {
  name: string;
  type: string;
  description: string;
  required: boolean;
  defaultValue?: any;
}

export interface SyntaxHighlightToken {
  start: number;
  end: number;
  type: 'function' | 'constant' | 'variable' | 'operator' | 'number' | 'string' | 'error';
  value: string;
}

export interface FormulaValidationError {
  start: number;
  end: number;
  message: string;
  severity: 'error' | 'warning' | 'info';
  code: string;
  suggestions?: string[];
}

export interface FormulaTestCase {
  id: string;
  name: string;
  formula: string;
  variables: Record<string, any>;
  expectedResult: any;
  tolerance?: number;
  description?: string;
}

export interface FormulaTestResult {
  testCase: FormulaTestCase;
  passed: boolean;
  actualResult: any;
  expectedResult: any;
  error?: string;
  executionTime: number;
}

export interface FormulaDocumentation {
  name: string;
  category: string;
  description: string;
  syntax: string;
  parameters: FormulaParameter[];
  examples: FormulaExample[];
  seeAlso: string[];
}

export interface FormulaExample {
  formula: string;
  description: string;
  variables?: Record<string, any>;
  result: any;
}

export interface FormulaSession {
  id: string;
  formula: string;
  variables: Record<string, any>;
  lastModified: Date;
  validationErrors: FormulaValidationError[];
  testCases: FormulaTestCase[];
}

/**
 * Formula Editor with syntax highlighting, autocomplete, validation, and testing
 * Provides comprehensive formula editing capabilities with real-time feedback
 */
export class FormulaEditor {
  private calculationEngine: CalculationEngine;
  private options: Required<FormulaEditorOptions>;
  private functionDocumentation: Map<string, FormulaDocumentation> = new Map();
  private sessions: Map<string, FormulaSession> = new Map();
  private autoSaveTimer: NodeJS.Timeout | null = null;

  constructor(calculationEngine: CalculationEngine, options: Partial<FormulaEditorOptions> = {}) {
    this.calculationEngine = calculationEngine;
    this.options = {
      enableSyntaxHighlighting: options.enableSyntaxHighlighting ?? true,
      enableAutocomplete: options.enableAutocomplete ?? true,
      enableValidation: options.enableValidation ?? true,
      enableTesting: options.enableTesting ?? true,
      maxFormulaLength: options.maxFormulaLength ?? 10000,
      autoSaveInterval: options.autoSaveInterval ?? 5000,
    };

    this.initializeFunctionDocumentation();
    this.startAutoSave();

    logger.info('FormulaEditor initialized', {
      options: this.options,
    });
  }

  /**
   * Create new formula session
   */
  public createSession(formula: string = '', variables: Record<string, any> = {}): string {
    const sessionId = this.generateSessionId();

    const session: FormulaSession = {
      id: sessionId,
      formula,
      variables,
      lastModified: new Date(),
      validationErrors: [],
      testCases: [],
    };

    this.sessions.set(sessionId, session);

    // Perform initial validation
    if (this.options.enableValidation && formula) {
      this.validateFormula(sessionId);
    }

    logger.debug('Formula session created', { sessionId, formula });
    return sessionId;
  }

  /**
   * Update formula in session
   */
  public async updateFormula(sessionId: string, formula: string): Promise<void> {
    const session = this.getSession(sessionId);
    if (!session) {
      throw new Error(`Session not found: ${sessionId}`);
    }

    if (formula.length > this.options.maxFormulaLength) {
      throw new Error(
        `Formula exceeds maximum length of ${this.options.maxFormulaLength} characters`
      );
    }

    session.formula = formula;
    session.lastModified = new Date();

    // Perform validation
    if (this.options.enableValidation) {
      await this.validateFormula(sessionId);
    }

    logger.debug('Formula updated', { sessionId, formula });
  }

  /**
   * Get autocomplete suggestions
   */
  public getAutocompleteSuggestions(
    sessionId: string,
    position: number,
    _context?: string
  ): FormulaSuggestion[] {
    if (!this.options.enableAutocomplete) {
      return [];
    }

    const session = this.getSession(sessionId);
    if (!session) return [];

    const suggestions: FormulaSuggestion[] = [];
    const formula = session.formula;
    const beforeCursor = formula.substring(0, position);

    // Get current word being typed
    const wordMatch = beforeCursor.match(/[a-zA-Z_][a-zA-Z0-9_]*$/);
    const currentWord = wordMatch ? wordMatch[0] : '';

    // Function suggestions
    const functions = this.calculationEngine.getAvailableFunctions();
    for (const func of functions) {
      if (func.toLowerCase().startsWith(currentWord.toLowerCase())) {
        const documentation = this.functionDocumentation.get(func);
        const suggestion: FormulaSuggestion = {
          text: func,
          type: 'function',
          description: documentation?.description || `Function: ${func}`,
          insertText: `${func}()`,
          detail: documentation?.syntax || func,
        };

        if (documentation?.description) {
          suggestion.documentation = documentation.description;
        }

        if (documentation?.parameters) {
          suggestion.parameters = documentation.parameters;
        }

        suggestions.push(suggestion);
      }
    }

    // Constant suggestions
    const constants = this.calculationEngine.getAvailableConstants();
    for (const [name, value] of Object.entries(constants)) {
      if (name.toLowerCase().startsWith(currentWord.toLowerCase())) {
        suggestions.push({
          text: name,
          type: 'constant',
          description: `Constant: ${name} = ${value}`,
          insertText: name,
          detail: `${name}: ${value}`,
        });
      }
    }

    // Variable suggestions
    for (const [name, value] of Object.entries(session.variables)) {
      if (name.toLowerCase().startsWith(currentWord.toLowerCase())) {
        suggestions.push({
          text: name,
          type: 'variable',
          description: `Variable: ${name} = ${value}`,
          insertText: name,
          detail: `${name}: ${typeof value} = ${value}`,
        });
      }
    }

    // Operator suggestions
    const operators = ['+', '-', '*', '/', '^', '(', ')', '=', '<', '>', '<=', '>=', '!='];
    for (const op of operators) {
      if (op.startsWith(currentWord)) {
        suggestions.push({
          text: op,
          type: 'operator',
          description: `Operator: ${op}`,
          insertText: op,
        });
      }
    }

    return suggestions.sort((a, b) => a.text.localeCompare(b.text));
  }

  /**
   * Get syntax highlighting tokens
   */
  public getSyntaxHighlightTokens(sessionId: string): SyntaxHighlightToken[] {
    if (!this.options.enableSyntaxHighlighting) {
      return [];
    }

    const session = this.getSession(sessionId);
    if (!session) return [];

    const tokens: SyntaxHighlightToken[] = [];
    const formula = session.formula;

    // Tokenize the formula
    const tokenPatterns = [
      { type: 'function' as const, pattern: /\b[a-zA-Z_][a-zA-Z0-9_]*(?=\s*\()/g },
      { type: 'number' as const, pattern: /\b\d+\.?\d*\b/g },
      { type: 'string' as const, pattern: /"[^"]*"/g },
      { type: 'operator' as const, pattern: /[+\-*/^=<>!()]/g },
      { type: 'variable' as const, pattern: /\b[a-zA-Z_][a-zA-Z0-9_]*\b/g },
    ];

    for (const { type, pattern } of tokenPatterns) {
      let match;
      while ((match = pattern.exec(formula)) !== null) {
        // Check if this token overlaps with existing tokens
        const overlaps = tokens.some(
          token => match!.index < token.end && match!.index + match![0].length > token.start
        );

        if (!overlaps) {
          tokens.push({
            start: match.index,
            end: match.index + match[0].length,
            type,
            value: match[0],
          });
        }
      }
    }

    // Sort tokens by position
    tokens.sort((a, b) => a.start - b.start);

    return tokens;
  }

  /**
   * Validate formula
   */
  public async validateFormula(sessionId: string): Promise<FormulaValidationError[]> {
    const session = this.getSession(sessionId);
    if (!session) {
      throw new Error(`Session not found: ${sessionId}`);
    }

    const errors: FormulaValidationError[] = [];

    try {
      // Use calculation engine validation
      const validation = await this.calculationEngine.validateExpression(
        session.formula,
        session.variables
      );

      // Convert validation errors to editor format
      for (const error of validation.errors) {
        errors.push({
          start: 0,
          end: session.formula.length,
          message: error,
          severity: 'error',
          code: 'VALIDATION_ERROR',
        });
      }

      for (const warning of validation.warnings) {
        errors.push({
          start: 0,
          end: session.formula.length,
          message: warning,
          severity: 'warning',
          code: 'VALIDATION_WARNING',
        });
      }

      // Additional editor-specific validations
      this.performEditorValidations(session, errors);
    } catch (error) {
      errors.push({
        start: 0,
        end: session.formula.length,
        message: error instanceof Error ? error.message : 'Unknown validation error',
        severity: 'error',
        code: 'SYNTAX_ERROR',
      });
    }

    session.validationErrors = errors;
    logger.debug('Formula validation completed', {
      sessionId,
      errorCount: errors.length,
    });

    return errors;
  }

  /**
   * Test formula with test cases
   */
  public async testFormula(
    sessionId: string,
    testCases?: FormulaTestCase[]
  ): Promise<FormulaTestResult[]> {
    if (!this.options.enableTesting) {
      throw new Error('Formula testing is disabled');
    }

    const session = this.getSession(sessionId);
    if (!session) {
      throw new Error(`Session not found: ${sessionId}`);
    }

    const cases = testCases || session.testCases;
    const results: FormulaTestResult[] = [];

    for (const testCase of cases) {
      const startTime = Date.now();

      try {
        const calculationResult = await this.calculationEngine.evaluate(
          testCase.formula,
          testCase.variables
        );

        const actualResult = calculationResult.result;
        const passed = this.compareResults(
          actualResult,
          testCase.expectedResult,
          testCase.tolerance
        );

        results.push({
          testCase,
          passed,
          actualResult,
          expectedResult: testCase.expectedResult,
          executionTime: Date.now() - startTime,
        });
      } catch (error) {
        results.push({
          testCase,
          passed: false,
          actualResult: null,
          expectedResult: testCase.expectedResult,
          error: error instanceof Error ? error.message : 'Unknown error',
          executionTime: Date.now() - startTime,
        });
      }
    }

    logger.info('Formula testing completed', {
      sessionId,
      totalTests: results.length,
      passed: results.filter(r => r.passed).length,
      failed: results.filter(r => !r.passed).length,
    });

    return results;
  }

  /**
   * Add test case to session
   */
  public addTestCase(sessionId: string, testCase: Omit<FormulaTestCase, 'id'>): string {
    const session = this.getSession(sessionId);
    if (!session) {
      throw new Error(`Session not found: ${sessionId}`);
    }

    const testCaseWithId: FormulaTestCase = {
      ...testCase,
      id: this.generateTestCaseId(),
    };

    session.testCases.push(testCaseWithId);
    session.lastModified = new Date();

    logger.debug('Test case added', { sessionId, testCaseId: testCaseWithId.id });
    return testCaseWithId.id;
  }

  /**
   * Get function documentation
   */
  public getFunctionDocumentation(functionName: string): FormulaDocumentation | null {
    return this.functionDocumentation.get(functionName) || null;
  }

  /**
   * Get all available function documentation
   */
  public getAllFunctionDocumentation(): FormulaDocumentation[] {
    return Array.from(this.functionDocumentation.values());
  }

  /**
   * Get session
   */
  public getSession(sessionId: string): FormulaSession | null {
    return this.sessions.get(sessionId) || null;
  }

  /**
   * Delete session
   */
  public deleteSession(sessionId: string): boolean {
    const deleted = this.sessions.delete(sessionId);
    if (deleted) {
      logger.debug('Formula session deleted', { sessionId });
    }
    return deleted;
  }

  /**
   * Initialize function documentation
   */
  private initializeFunctionDocumentation(): void {
    // Mathematical functions
    this.addFunctionDoc(
      'abs',
      'Math',
      'Returns the absolute value of a number',
      'abs(x)',
      [{ name: 'x', type: 'number', description: 'Input number', required: true }],
      [
        { formula: 'abs(-5)', description: 'Absolute value of -5', result: 5 },
        { formula: 'abs(3.14)', description: 'Absolute value of 3.14', result: 3.14 },
      ]
    );

    this.addFunctionDoc(
      'sqrt',
      'Math',
      'Returns the square root of a number',
      'sqrt(x)',
      [
        {
          name: 'x',
          type: 'number',
          description: 'Input number (must be non-negative)',
          required: true,
        },
      ],
      [
        { formula: 'sqrt(16)', description: 'Square root of 16', result: 4 },
        { formula: 'sqrt(2)', description: 'Square root of 2', result: 1.414 },
      ]
    );

    this.addFunctionDoc(
      'pow',
      'Math',
      'Returns base raised to the power of exponent',
      'pow(base, exponent)',
      [
        { name: 'base', type: 'number', description: 'Base number', required: true },
        { name: 'exponent', type: 'number', description: 'Exponent', required: true },
      ],
      [
        { formula: 'pow(2, 3)', description: '2 to the power of 3', result: 8 },
        { formula: 'pow(10, 2)', description: '10 squared', result: 100 },
      ]
    );

    // Financial functions
    this.addFunctionDoc(
      'compound_interest',
      'Financial',
      'Calculates compound interest',
      'compound_interest(principal, rate, frequency, time)',
      [
        { name: 'principal', type: 'number', description: 'Initial amount', required: true },
        {
          name: 'rate',
          type: 'number',
          description: 'Annual interest rate (as decimal)',
          required: true,
        },
        {
          name: 'frequency',
          type: 'number',
          description: 'Compounding frequency per year',
          required: true,
        },
        { name: 'time', type: 'number', description: 'Time in years', required: true },
      ],
      [
        {
          formula: 'compound_interest(1000, 0.05, 12, 5)',
          description: '$1000 at 5% compounded monthly for 5 years',
          result: 1283.36,
        },
      ]
    );

    this.addFunctionDoc(
      'simple_interest',
      'Financial',
      'Calculates simple interest',
      'simple_interest(principal, rate, time)',
      [
        { name: 'principal', type: 'number', description: 'Initial amount', required: true },
        {
          name: 'rate',
          type: 'number',
          description: 'Annual interest rate (as decimal)',
          required: true,
        },
        { name: 'time', type: 'number', description: 'Time in years', required: true },
      ],
      [
        {
          formula: 'simple_interest(1000, 0.05, 5)',
          description: '$1000 at 5% simple interest for 5 years',
          result: 1250,
        },
      ]
    );

    // Statistical functions
    this.addFunctionDoc(
      'mean',
      'Statistics',
      'Calculates the arithmetic mean',
      'mean(values)',
      [{ name: 'values', type: 'array', description: 'Array of numbers', required: true }],
      [{ formula: 'mean([1, 2, 3, 4, 5])', description: 'Mean of 1,2,3,4,5', result: 3 }]
    );

    this.addFunctionDoc(
      'median',
      'Statistics',
      'Calculates the median value',
      'median(values)',
      [{ name: 'values', type: 'array', description: 'Array of numbers', required: true }],
      [{ formula: 'median([1, 2, 3, 4, 5])', description: 'Median of 1,2,3,4,5', result: 3 }]
    );

    logger.debug('Function documentation initialized', {
      functionCount: this.functionDocumentation.size,
    });
  }

  /**
   * Add function documentation
   */
  private addFunctionDoc(
    name: string,
    category: string,
    description: string,
    syntax: string,
    parameters: FormulaParameter[],
    examples: FormulaExample[],
    seeAlso: string[] = []
  ): void {
    this.functionDocumentation.set(name, {
      name,
      category,
      description,
      syntax,
      parameters,
      examples,
      seeAlso,
    });
  }

  /**
   * Perform editor-specific validations
   */
  private performEditorValidations(
    session: FormulaSession,
    errors: FormulaValidationError[]
  ): void {
    const formula = session.formula;

    // Check for unmatched parentheses
    let openParens = 0;
    for (let i = 0; i < formula.length; i++) {
      if (formula[i] === '(') {
        openParens++;
      } else if (formula[i] === ')') {
        openParens--;
        if (openParens < 0) {
          errors.push({
            start: i,
            end: i + 1,
            message: 'Unmatched closing parenthesis',
            severity: 'error',
            code: 'UNMATCHED_PAREN',
          });
        }
      }
    }

    if (openParens > 0) {
      errors.push({
        start: formula.length - 1,
        end: formula.length,
        message: `${openParens} unmatched opening parenthesis(es)`,
        severity: 'error',
        code: 'UNMATCHED_PAREN',
      });
    }

    // Check for empty formula
    if (formula.trim() === '') {
      errors.push({
        start: 0,
        end: 0,
        message: 'Formula cannot be empty',
        severity: 'warning',
        code: 'EMPTY_FORMULA',
      });
    }

    // Check for consecutive operators
    const consecutiveOpPattern = /[+\-*/^]{2,}/g;
    let match;
    while ((match = consecutiveOpPattern.exec(formula)) !== null) {
      errors.push({
        start: match.index,
        end: match.index + match[0].length,
        message: 'Consecutive operators are not allowed',
        severity: 'error',
        code: 'CONSECUTIVE_OPERATORS',
        suggestions: ['Remove extra operators', 'Add parentheses if needed'],
      });
    }

    // Check for undefined variables
    const variablePattern = /\b[a-zA-Z_][a-zA-Z0-9_]*\b/g;
    const functions = this.calculationEngine.getAvailableFunctions();
    const constants = Object.keys(this.calculationEngine.getAvailableConstants());

    while ((match = variablePattern.exec(formula)) !== null) {
      const variable = match[0];
      if (
        !functions.includes(variable) &&
        !constants.includes(variable) &&
        !(variable in session.variables)
      ) {
        errors.push({
          start: match.index,
          end: match.index + variable.length,
          message: `Undefined variable: ${variable}`,
          severity: 'warning',
          code: 'UNDEFINED_VARIABLE',
          suggestions: [
            `Define variable '${variable}'`,
            `Use available variables: ${Object.keys(session.variables).join(', ')}`,
          ],
        });
      }
    }
  }

  /**
   * Compare test results with tolerance
   */
  private compareResults(actual: any, expected: any, tolerance: number = 1e-10): boolean {
    if (typeof actual === 'number' && typeof expected === 'number') {
      return Math.abs(actual - expected) <= tolerance;
    }

    if (Array.isArray(actual) && Array.isArray(expected)) {
      if (actual.length !== expected.length) return false;
      return actual.every((val, index) => this.compareResults(val, expected[index], tolerance));
    }

    return actual === expected;
  }

  /**
   * Generate session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate test case ID
   */
  private generateTestCaseId(): string {
    return `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Start auto-save timer
   */
  private startAutoSave(): void {
    if (this.options.autoSaveInterval > 0) {
      this.autoSaveTimer = setInterval(() => {
        this.performAutoSave();
      }, this.options.autoSaveInterval);
    }
  }

  /**
   * Perform auto-save of sessions
   */
  private performAutoSave(): void {
    // In a real implementation, this would save sessions to persistent storage
    logger.debug('Auto-save performed', {
      sessionCount: this.sessions.size,
    });
  }

  /**
   * Cleanup resources
   */
  public dispose(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = null;
    }

    this.sessions.clear();
    this.functionDocumentation.clear();

    logger.info('FormulaEditor disposed');
  }
}

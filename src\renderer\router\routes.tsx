import React, { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import { PageSuspenseWrapper } from '../components/common/SuspenseWrapper';

// Lazy load components for code splitting
const Dashboard = lazy(() => import('../pages/Dashboard'));
const DocumentViewer = lazy(() => import('../pages/DocumentViewer'));
const FormEditor = lazy(() => import('../pages/FormEditor'));
const Timeline = lazy(() => import('../pages/Timeline'));
const KnowledgeBase = lazy(() => import('../pages/KnowledgeBase'));
const Settings = lazy(() => import('../pages/Settings'));
const NotFound = lazy(() => import('../pages/NotFound'));

// Route configuration with nested routing
export const routes: RouteObject[] = [
  {
    path: '/',
    element: (
      <PageSuspenseWrapper loadingText="Loading dashboard...">
        <Dashboard />
      </PageSuspenseWrapper>
    ),
    index: true,
  },
  {
    path: '/documents',
    children: [
      {
        index: true,
        element: (
          <PageSuspenseWrapper loadingText="Loading documents...">
            <Dashboard />
          </PageSuspenseWrapper>
        ),
      },
      {
        path: ':documentId',
        element: (
          <PageSuspenseWrapper loadingText="Loading document...">
            <DocumentViewer />
          </PageSuspenseWrapper>
        ),
      },
      {
        path: ':documentId/edit',
        element: (
          <PageSuspenseWrapper loadingText="Loading editor...">
            <FormEditor />
          </PageSuspenseWrapper>
        ),
      },
    ],
  },
  {
    path: '/timeline',
    element: (
      <PageSuspenseWrapper loadingText="Loading timeline...">
        <Timeline />
      </PageSuspenseWrapper>
    ),
  },
  {
    path: '/knowledge',
    element: (
      <PageSuspenseWrapper loadingText="Loading knowledge base...">
        <KnowledgeBase />
      </PageSuspenseWrapper>
    ),
  },
  {
    path: '/settings',
    children: [
      {
        index: true,
        element: (
          <PageSuspenseWrapper loadingText="Loading settings...">
            <Settings />
          </PageSuspenseWrapper>
        ),
      },
      {
        path: 'ai',
        element: (
          <PageSuspenseWrapper loadingText="Loading AI settings...">
            <Settings />
          </PageSuspenseWrapper>
        ),
      },
      {
        path: 'appearance',
        element: (
          <PageSuspenseWrapper loadingText="Loading appearance settings...">
            <Settings />
          </PageSuspenseWrapper>
        ),
      },
      {
        path: 'advanced',
        element: (
          <PageSuspenseWrapper loadingText="Loading advanced settings...">
            <Settings />
          </PageSuspenseWrapper>
        ),
      },
    ],
  },
  {
    path: '*',
    element: (
      <PageSuspenseWrapper loadingText="Loading page...">
        <NotFound />
      </PageSuspenseWrapper>
    ),
  },
];

// Route metadata for navigation and breadcrumbs
export interface RouteMetadata {
  title: string;
  description?: string;
  icon?: string;
  requiresAuth?: boolean;
  breadcrumb?: string;
  keywords?: string[];
}

export const routeMetadata: Record<string, RouteMetadata> = {
  '/': {
    title: 'Dashboard',
    description: 'Main dashboard with overview and quick actions',
    icon: 'dashboard',
    breadcrumb: 'Home',
    keywords: ['dashboard', 'home', 'overview'],
  },
  '/documents': {
    title: 'Documents',
    description: 'Document management and processing',
    icon: 'description',
    breadcrumb: 'Documents',
    keywords: ['documents', 'files', 'processing'],
  },
  '/documents/:documentId': {
    title: 'Document Viewer',
    description: 'View and analyze documents',
    icon: 'visibility',
    breadcrumb: 'View Document',
    keywords: ['view', 'document', 'analyze'],
  },
  '/documents/:documentId/edit': {
    title: 'Form Editor',
    description: 'Edit and fill document forms',
    icon: 'edit',
    breadcrumb: 'Edit Document',
    keywords: ['edit', 'form', 'fill'],
  },
  '/timeline': {
    title: 'Timeline',
    description: 'Version history and document timeline',
    icon: 'timeline',
    breadcrumb: 'Timeline',
    keywords: ['timeline', 'history', 'versions'],
  },
  '/knowledge': {
    title: 'Knowledge Base',
    description: 'Search and manage extracted knowledge',
    icon: 'library_books',
    breadcrumb: 'Knowledge Base',
    keywords: ['knowledge', 'search', 'database'],
  },
  '/settings': {
    title: 'Settings',
    description: 'Application settings and preferences',
    icon: 'settings',
    breadcrumb: 'Settings',
    keywords: ['settings', 'preferences', 'configuration'],
  },
  '/settings/ai': {
    title: 'AI Settings',
    description: 'AI model configuration and preferences',
    icon: 'smart_toy',
    breadcrumb: 'AI Settings',
    keywords: ['ai', 'models', 'configuration'],
  },
  '/settings/appearance': {
    title: 'Appearance',
    description: 'Theme and appearance settings',
    icon: 'palette',
    breadcrumb: 'Appearance',
    keywords: ['theme', 'appearance', 'ui'],
  },
  '/settings/advanced': {
    title: 'Advanced Settings',
    description: 'Advanced configuration options',
    icon: 'tune',
    breadcrumb: 'Advanced',
    keywords: ['advanced', 'configuration', 'expert'],
  },
};

// Navigation items for sidebar
export interface NavigationItem {
  path: string;
  label: string;
  icon: string;
  children?: NavigationItem[];
  badge?: string;
  disabled?: boolean;
}

export const navigationItems: NavigationItem[] = [
  {
    path: '/',
    label: 'Dashboard',
    icon: 'dashboard',
  },
  {
    path: '/documents',
    label: 'Documents',
    icon: 'description',
  },
  {
    path: '/timeline',
    label: 'Timeline',
    icon: 'timeline',
  },
  {
    path: '/knowledge',
    label: 'Knowledge Base',
    icon: 'library_books',
  },
  {
    path: '/settings',
    label: 'Settings',
    icon: 'settings',
    children: [
      {
        path: '/settings/ai',
        label: 'AI Configuration',
        icon: 'smart_toy',
      },
      {
        path: '/settings/appearance',
        label: 'Appearance',
        icon: 'palette',
      },
      {
        path: '/settings/advanced',
        label: 'Advanced',
        icon: 'tune',
      },
    ],
  },
];

export default routes;

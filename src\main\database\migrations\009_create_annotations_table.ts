import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create annotations table for document annotations
  await knex.schema.createTable('annotations', (table) => {
    // Primary key
    table.string('id', 36).primary().notNullable().comment('UUID primary key');

    // Document reference
    table.string('document_id', 36).notNullable().comment('Reference to documents table');
    table.foreign('document_id').references('id').inTable('documents').onDelete('CASCADE');

    // Annotation classification
    table.string('annotation_type', 100).notNullable().comment('Type of annotation (highlight, comment, signature, etc.)');
    table.string('category', 100).comment('Annotation category (review, approval, correction, etc.)');
    table.string('subcategory', 100).comment('Annotation subcategory');

    // Content and data
    table.text('content').comment('Annotation content (text, comment, etc.)');
    table.json('annotation_data').comment('Structured annotation data');
    table.binary('binary_data').comment('Binary data for signatures, drawings, etc.');
    table.string('data_format', 50).comment('Format of binary data (svg, png, pdf, etc.)');
    table.integer('data_size').unsigned().comment('Size of binary data in bytes');

    // Position and coordinates
    table.integer('page_number').unsigned().comment('Page number where annotation is located');
    table.json('coordinates').comment('Position coordinates (x, y, width, height)');
    table.json('bounding_box').comment('Detailed bounding box information');
    table.decimal('rotation', 8, 4).comment('Rotation angle in degrees');
    table.decimal('scale_factor', 8, 4).defaultTo(1.0).comment('Scale factor for the annotation');

    // Visual properties
    table.string('color', 20).comment('Annotation color (hex, rgb, or named color)');
    table.string('background_color', 20).comment('Background color');
    table.string('border_color', 20).comment('Border color');
    table.integer('border_width').unsigned().comment('Border width in pixels');
    table.decimal('opacity', 3, 2).defaultTo(1.0).comment('Opacity (0.0 to 1.0)');
    table.string('font_family', 100).comment('Font family for text annotations');
    table.integer('font_size').unsigned().comment('Font size for text annotations');
    table.string('font_style', 50).comment('Font style (normal, italic, bold, etc.)');

    // User and temporal information
    table.string('created_by', 100).notNullable().comment('User who created the annotation');
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable().comment('Annotation creation timestamp');
    table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable().comment('Last update timestamp');
    table.string('updated_by', 100).comment('User who last updated the annotation');

    // Status and workflow
    table.string('status', 50).defaultTo('active').notNullable().comment('Annotation status');
    table.boolean('is_resolved').defaultTo(false).notNullable().comment('Whether annotation has been resolved');
    table.timestamp('resolved_at').comment('When annotation was resolved');
    table.string('resolved_by', 100).comment('Who resolved the annotation');
    table.text('resolution_notes').comment('Notes about the resolution');

    // Collaboration and threading
    table.string('parent_annotation_id', 36).comment('Parent annotation for replies/threads');
    table.foreign('parent_annotation_id').references('id').inTable('annotations').onDelete('CASCADE');
    table.json('thread_participants').comment('Array of users participating in thread');
    table.integer('reply_count').defaultTo(0).notNullable().comment('Number of replies to this annotation');
    table.timestamp('last_reply_at').comment('Timestamp of last reply');

    // Permissions and visibility
    table.json('permissions').comment('Annotation-specific permissions');
    table.boolean('is_public').defaultTo(true).notNullable().comment('Whether annotation is visible to all users');
    table.json('visible_to_users').comment('Array of users who can see this annotation');
    table.boolean('is_system_annotation').defaultTo(false).notNullable().comment('Whether this is a system-generated annotation');

    // Metadata and context
    table.json('metadata').comment('Additional annotation metadata');
    table.json('tags').comment('Array of tags for categorization');
    table.text('context').comment('Context information about the annotation');
    table.string('source', 100).comment('Source of the annotation (manual, ai, import, etc.)');
    table.decimal('confidence', 5, 4).comment('Confidence score for AI-generated annotations');

    // Validation and quality
    table.boolean('is_validated').defaultTo(false).notNullable().comment('Whether annotation has been validated');
    table.timestamp('validated_at').comment('When annotation was validated');
    table.string('validated_by', 100).comment('Who validated the annotation');
    table.decimal('quality_score', 5, 4).comment('Quality score of the annotation');

    // Version control
    table.string('version', 50).defaultTo('1.0').notNullable().comment('Annotation version');
    table.json('version_history').comment('History of annotation changes');
    table.string('original_annotation_id', 36).comment('Original annotation ID if this is a version');

    // Constraints
    table.check('page_number > 0', [], 'page_number_positive');
    table.check('data_size >= 0', [], 'data_size_positive');
    table.check('border_width >= 0', [], 'border_width_positive');
    table.check('font_size > 0', [], 'font_size_positive');
    table.check('opacity >= 0 AND opacity <= 1', [], 'opacity_range');
    table.check('scale_factor > 0', [], 'scale_factor_positive');
    table.check('reply_count >= 0', [], 'reply_count_positive');
    table.check('confidence IS NULL OR (confidence >= 0 AND confidence <= 1)', [], 'confidence_range');
    table.check('quality_score IS NULL OR (quality_score >= 0 AND quality_score <= 1)', [], 'quality_score_range');
    table.check("annotation_type IN ('highlight', 'comment', 'note', 'signature', 'stamp', 'drawing', 'shape', 'text', 'image', 'link', 'bookmark', 'redaction', 'other')", [], 'valid_annotation_type');
    table.check("status IN ('active', 'inactive', 'pending', 'approved', 'rejected', 'archived')", [], 'valid_status');
    table.check("data_format IN ('svg', 'png', 'jpg', 'pdf', 'json', 'xml', 'text', 'other') OR data_format IS NULL", [], 'valid_data_format');
    table.check("source IN ('manual', 'ai', 'import', 'system', 'api', 'other')", [], 'valid_source');

    // JSON validation
    table.check("json_valid(annotation_data) OR annotation_data IS NULL", [], 'valid_annotation_data');
    table.check("json_valid(coordinates) OR coordinates IS NULL", [], 'valid_coordinates');
    table.check("json_valid(bounding_box) OR bounding_box IS NULL", [], 'valid_bounding_box');
    table.check("json_valid(thread_participants) OR thread_participants IS NULL", [], 'valid_thread_participants');
    table.check("json_valid(permissions) OR permissions IS NULL", [], 'valid_permissions');
    table.check("json_valid(visible_to_users) OR visible_to_users IS NULL", [], 'valid_visible_to_users');
    table.check("json_valid(metadata) OR metadata IS NULL", [], 'valid_metadata');
    table.check("json_valid(tags) OR tags IS NULL", [], 'valid_tags');
    table.check("json_valid(version_history) OR version_history IS NULL", [], 'valid_version_history');

    // Indexes for performance
    table.index(['document_id'], 'idx_annotations_document');
    table.index(['annotation_type'], 'idx_annotations_type');
    table.index(['category'], 'idx_annotations_category');
    table.index(['created_by'], 'idx_annotations_created_by');
    table.index(['created_at'], 'idx_annotations_created_at');
    table.index(['updated_at'], 'idx_annotations_updated_at');
    table.index(['status'], 'idx_annotations_status');
    table.index(['is_resolved'], 'idx_annotations_resolved');
    table.index(['page_number'], 'idx_annotations_page');
    table.index(['parent_annotation_id'], 'idx_annotations_parent');
    table.index(['is_public'], 'idx_annotations_public');
    table.index(['is_system_annotation'], 'idx_annotations_system');
    table.index(['source'], 'idx_annotations_source');
    table.index(['is_validated'], 'idx_annotations_validated');
    table.index(['original_annotation_id'], 'idx_annotations_original');

    // Composite indexes for common queries
    table.index(['document_id', 'annotation_type'], 'idx_annotations_doc_type');
    table.index(['document_id', 'page_number'], 'idx_annotations_doc_page');
    table.index(['document_id', 'created_by'], 'idx_annotations_doc_user');
    table.index(['created_by', 'created_at'], 'idx_annotations_user_created');
    table.index(['status', 'is_resolved'], 'idx_annotations_status_resolved');
    table.index(['annotation_type', 'status'], 'idx_annotations_type_status');
    table.index(['parent_annotation_id', 'created_at'], 'idx_annotations_parent_created');
    table.index(['is_public', 'status'], 'idx_annotations_public_status');
  });

  // Create trigger to update updated_at timestamp
  await knex.raw(`
    CREATE TRIGGER update_annotations_updated_at
    AFTER UPDATE ON annotations
    FOR EACH ROW
    BEGIN
      UPDATE annotations SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END
  `);

  // Create trigger to update reply count on parent annotations
  await knex.raw(`
    CREATE TRIGGER update_annotation_reply_count_insert
    AFTER INSERT ON annotations
    FOR EACH ROW
    WHEN NEW.parent_annotation_id IS NOT NULL
    BEGIN
      UPDATE annotations
      SET reply_count = reply_count + 1,
          last_reply_at = CURRENT_TIMESTAMP
      WHERE id = NEW.parent_annotation_id;
    END
  `);

  await knex.raw(`
    CREATE TRIGGER update_annotation_reply_count_delete
    AFTER DELETE ON annotations
    FOR EACH ROW
    WHEN OLD.parent_annotation_id IS NOT NULL
    BEGIN
      UPDATE annotations
      SET reply_count = reply_count - 1
      WHERE id = OLD.parent_annotation_id;
    END
  `);

  // Create full-text search virtual table for annotation content
  await knex.raw(`
    CREATE VIRTUAL TABLE IF NOT EXISTS annotations_fts USING fts5(
      id UNINDEXED,
      document_id UNINDEXED,
      content,
      context,
      resolution_notes,
      tags,
      metadata,
      content='annotations',
      content_rowid='rowid'
    )
  `);

  // Create triggers to keep FTS table in sync
  await knex.raw(`
    CREATE TRIGGER annotations_fts_insert AFTER INSERT ON annotations BEGIN
      INSERT INTO annotations_fts(id, document_id, content, context, resolution_notes, tags, metadata)
      VALUES (new.id, new.document_id, new.content, new.context, new.resolution_notes, new.tags, new.metadata);
    END
  `);

  await knex.raw(`
    CREATE TRIGGER annotations_fts_delete AFTER DELETE ON annotations BEGIN
      DELETE FROM annotations_fts WHERE id = old.id;
    END
  `);

  await knex.raw(`
    CREATE TRIGGER annotations_fts_update AFTER UPDATE ON annotations BEGIN
      DELETE FROM annotations_fts WHERE id = old.id;
      INSERT INTO annotations_fts(id, document_id, content, context, resolution_notes, tags, metadata)
      VALUES (new.id, new.document_id, new.content, new.context, new.resolution_notes, new.tags, new.metadata);
    END
  `);

  // Create view for active annotations
  await knex.raw(`
    CREATE VIEW active_annotations AS
    SELECT
      a.*,
      d.name as document_name,
      d.type as document_type
    FROM annotations a
    JOIN documents d ON a.document_id = d.id
    WHERE a.status = 'active' AND a.is_resolved = 0
  `);

  // Create view for annotation threads
  await knex.raw(`
    CREATE VIEW annotation_threads AS
    SELECT
      parent.id as thread_id,
      parent.content as thread_content,
      parent.created_by as thread_creator,
      parent.created_at as thread_created_at,
      parent.reply_count,
      parent.last_reply_at,
      child.id as reply_id,
      child.content as reply_content,
      child.created_by as reply_creator,
      child.created_at as reply_created_at,
      parent.document_id,
      d.name as document_name
    FROM annotations parent
    LEFT JOIN annotations child ON child.parent_annotation_id = parent.id
    JOIN documents d ON parent.document_id = d.id
    WHERE parent.parent_annotation_id IS NULL
    ORDER BY parent.created_at DESC, child.created_at ASC
  `);

  // Create view for annotation statistics by document
  await knex.raw(`
    CREATE VIEW document_annotation_stats AS
    SELECT
      d.id as document_id,
      d.name as document_name,
      d.type as document_type,
      COUNT(a.id) as total_annotations,
      COUNT(CASE WHEN a.status = 'active' THEN 1 END) as active_annotations,
      COUNT(CASE WHEN a.is_resolved = 1 THEN 1 END) as resolved_annotations,
      COUNT(CASE WHEN a.annotation_type = 'comment' THEN 1 END) as comments,
      COUNT(CASE WHEN a.annotation_type = 'highlight' THEN 1 END) as highlights,
      COUNT(CASE WHEN a.annotation_type = 'signature' THEN 1 END) as signatures,
      COUNT(DISTINCT a.created_by) as unique_annotators,
      MAX(a.created_at) as last_annotation_time,
      COUNT(CASE WHEN a.parent_annotation_id IS NULL THEN 1 END) as thread_count,
      SUM(a.reply_count) as total_replies
    FROM documents d
    LEFT JOIN annotations a ON d.id = a.document_id
    GROUP BY d.id, d.name, d.type
  `);

  // Create view for user annotation activity
  await knex.raw(`
    CREATE VIEW user_annotation_activity AS
    SELECT
      created_by as user_id,
      COUNT(*) as total_annotations,
      COUNT(CASE WHEN status = 'active' THEN 1 END) as active_annotations,
      COUNT(CASE WHEN is_resolved = 1 THEN 1 END) as resolved_annotations,
      COUNT(DISTINCT annotation_type) as unique_annotation_types,
      COUNT(DISTINCT document_id) as documents_annotated,
      MIN(created_at) as first_annotation,
      MAX(created_at) as last_annotation,
      COUNT(CASE WHEN created_at >= datetime('now', '-7 days') THEN 1 END) as annotations_last_week,
      COUNT(CASE WHEN parent_annotation_id IS NOT NULL THEN 1 END) as replies_made,
      SUM(reply_count) as replies_received
    FROM annotations
    WHERE created_by IS NOT NULL
    GROUP BY created_by
    ORDER BY total_annotations DESC
  `);

  // Create view for annotations requiring attention
  await knex.raw(`
    CREATE VIEW annotations_requiring_attention AS
    SELECT
      a.*,
      d.name as document_name,
      CASE
        WHEN a.is_validated = 0 AND a.source = 'ai' THEN 'AI Annotation Needs Validation'
        WHEN a.status = 'pending' THEN 'Pending Approval'
        WHEN a.annotation_type = 'comment' AND a.is_resolved = 0 AND a.created_at < datetime('now', '-7 days') THEN 'Unresolved Comment'
        WHEN a.reply_count > 0 AND a.last_reply_at > a.updated_at THEN 'Has New Replies'
        WHEN a.quality_score IS NOT NULL AND a.quality_score < 0.6 THEN 'Low Quality'
        ELSE 'Other'
      END as attention_reason
    FROM annotations a
    JOIN documents d ON a.document_id = d.id
    WHERE a.status = 'active'
      AND (
        (a.is_validated = 0 AND a.source = 'ai')
        OR a.status = 'pending'
        OR (a.annotation_type = 'comment' AND a.is_resolved = 0 AND a.created_at < datetime('now', '-7 days'))
        OR (a.reply_count > 0 AND a.last_reply_at > a.updated_at)
        OR (a.quality_score IS NOT NULL AND a.quality_score < 0.6)
      )
    ORDER BY
      CASE attention_reason
        WHEN 'Pending Approval' THEN 1
        WHEN 'Has New Replies' THEN 2
        WHEN 'AI Annotation Needs Validation' THEN 3
        WHEN 'Unresolved Comment' THEN 4
        WHEN 'Low Quality' THEN 5
        ELSE 6
      END,
      a.created_at DESC
  `);
}

export async function down(knex: Knex): Promise<void> {
  // Drop triggers first
  await knex.raw('DROP TRIGGER IF EXISTS annotations_fts_update');
  await knex.raw('DROP TRIGGER IF EXISTS annotations_fts_delete');
  await knex.raw('DROP TRIGGER IF EXISTS annotations_fts_insert');
  await knex.raw('DROP TRIGGER IF EXISTS update_annotation_reply_count_delete');
  await knex.raw('DROP TRIGGER IF EXISTS update_annotation_reply_count_insert');
  await knex.raw('DROP TRIGGER IF EXISTS update_annotations_updated_at');

  // Drop FTS table
  await knex.raw('DROP TABLE IF EXISTS annotations_fts');

  // Drop views
  await knex.raw('DROP VIEW IF EXISTS annotations_requiring_attention');
  await knex.raw('DROP VIEW IF EXISTS user_annotation_activity');
  await knex.raw('DROP VIEW IF EXISTS document_annotation_stats');
  await knex.raw('DROP VIEW IF EXISTS annotation_threads');
  await knex.raw('DROP VIEW IF EXISTS active_annotations');

  // Drop main table
  await knex.schema.dropTableIfExists('annotations');
}

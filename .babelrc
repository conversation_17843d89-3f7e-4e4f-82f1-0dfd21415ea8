{"presets": [["@babel/preset-env", {"targets": {"electron": "37.2.5"}, "useBuiltIns": "usage", "corejs": 3, "modules": false}], ["@babel/preset-react", {"runtime": "automatic", "development": true}], ["@babel/preset-typescript", {"allowNamespaces": true, "allowDeclareFields": true}]], "plugins": ["@babel/plugin-transform-class-properties", "@babel/plugin-transform-object-rest-spread", "@babel/plugin-syntax-dynamic-import"], "env": {"development": {"plugins": []}, "production": {"plugins": ["transform-react-remove-prop-types"]}, "test": {"presets": [["@babel/preset-env", {"targets": {"node": "current"}}]]}}}
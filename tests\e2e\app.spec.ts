import { test, expect } from '@playwright/test';

test.describe('AI Document Processor', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('/');
  });

  test('should display the welcome screen', async ({ page }) => {
    // Check if the main title is visible
    await expect(page.locator('h1')).toContainText('AI Document Processor');
    
    // Check if the description is visible
    await expect(page.locator('text=Enterprise-grade AI-powered document processing')).toBeVisible();
    
    // Check if the main action buttons are present
    await expect(page.locator('button:has-text("Open Document")')).toBeVisible();
    await expect(page.locator('button:has-text("Create New Project")')).toBeVisible();
  });

  test('should have proper window title', async ({ page }) => {
    await expect(page).toHaveTitle(/AI Document Processor/);
  });

  test('should display version information', async ({ page }) => {
    await expect(page.locator('text=Version 1.0.0')).toBeVisible();
  });

  test('should have sidebar with explorer', async ({ page }) => {
    // Check if sidebar is visible
    await expect(page.locator('text=Explorer')).toBeVisible();
    
    // Check if "No project loaded" message is shown
    await expect(page.locator('text=No project loaded')).toBeVisible();
  });

  test('should have status bar', async ({ page }) => {
    // Check if status bar is visible
    await expect(page.locator('text=Ready')).toBeVisible();
    await expect(page.locator('text=AI Document Processor').last()).toBeVisible();
  });

  test('should have responsive layout', async ({ page }) => {
    // Test different viewport sizes
    await page.setViewportSize({ width: 1200, height: 800 });
    await expect(page.locator('h1')).toBeVisible();
    
    await page.setViewportSize({ width: 800, height: 600 });
    await expect(page.locator('h1')).toBeVisible();
  });

  test('should handle button interactions', async ({ page }) => {
    // Test Open Document button
    const openDocButton = page.locator('button:has-text("Open Document")');
    await expect(openDocButton).toBeEnabled();
    
    // Test Create New Project button
    const newProjectButton = page.locator('button:has-text("Create New Project")');
    await expect(newProjectButton).toBeEnabled();
    
    // Click buttons (they won't do anything yet, but should not crash)
    await openDocButton.click();
    await newProjectButton.click();
  });

  test('should have proper accessibility attributes', async ({ page }) => {
    // Check for proper heading hierarchy
    const h1 = page.locator('h1');
    await expect(h1).toBeVisible();
    
    // Check for proper button labels
    await expect(page.locator('button:has-text("Open Document")')).toHaveAttribute('class', /btn/);
    await expect(page.locator('button:has-text("Create New Project")')).toHaveAttribute('class', /btn/);
  });

  test('should load without JavaScript errors', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Allow for some expected development warnings
    const criticalErrors = errors.filter(error => 
      !error.includes('Warning:') && 
      !error.includes('DevTools')
    );
    
    expect(criticalErrors).toHaveLength(0);
  });
});
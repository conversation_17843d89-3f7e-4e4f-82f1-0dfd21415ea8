import Database from 'better-sqlite3';
import { app } from 'electron';
import fs from 'fs-extra';
import path from 'path';
import { logger } from '../utils/logger';

export interface DatabaseConfig {
  filename: string;
  readonly: boolean;
  fileMustExist: boolean;
  timeout: number;
  verbose?: (message?: any, ...additionalArgs: any[]) => void;
  nativeBinding?: string;
}

export interface ConnectionPoolOptions {
  maxConnections: number;
  idleTimeout: number;
  acquireTimeout: number;
}

export interface TransactionOptions {
  immediate?: boolean;
  deferred?: boolean;
  exclusive?: boolean;
}

class DatabaseConnection {
  private static instance: DatabaseConnection;
  private db: Database.Database | null = null;
  private config: DatabaseConfig;
  private poolOptions: ConnectionPoolOptions;
  private connectionPool: Database.Database[] = [];
  private activeConnections = 0;
  private healthCheckInterval: NodeJS.Timeout | null = null;

  private constructor() {
    const userDataPath = app.getPath('userData');
    const dbPath = path.join(userDataPath, 'ai-document-processor.db');

    // Ensure database directory exists
    fs.ensureDirSync(path.dirname(dbPath));

    this.config = {
      filename: dbPath,
      readonly: false,
      fileMustExist: false,
      timeout: 10000,
      ...(process.env.NODE_ENV === 'development' ? { verbose: logger.debug } : {}),
    };

    this.poolOptions = {
      maxConnections: 10,
      idleTimeout: 30000, // 30 seconds
      acquireTimeout: 5000, // 5 seconds
    };
  }

  public static getInstance(): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new DatabaseConnection();
    }
    return DatabaseConnection.instance;
  }

  public async initialize(): Promise<void> {
    try {
      logger.info('Initializing database connection', { path: this.config.filename });

      // Create main database connection
      this.db = new Database(this.config.filename, this.config);

      // Configure SQLite for better performance and reliability
      this.db.pragma('journal_mode = WAL'); // Write-Ahead Logging for better concurrency
      this.db.pragma('synchronous = NORMAL'); // Balance between safety and performance
      this.db.pragma('cache_size = 10000'); // 10MB cache
      this.db.pragma('temp_store = memory'); // Store temporary tables in memory
      this.db.pragma('mmap_size = 268435456'); // 256MB memory-mapped I/O
      this.db.pragma('foreign_keys = ON'); // Enable foreign key constraints

      // Test the connection
      const result = this.db.prepare('SELECT 1 as test').get() as { test: number } | undefined;
      if (!result || result.test !== 1) {
        throw new Error('Database connection test failed');
      }

      // Initialize connection pool
      await this.initializeConnectionPool();

      // Start health check monitoring
      this.startHealthCheck();

      logger.info('Database connection initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize database connection', { error });
      throw error;
    }
  }

  private async initializeConnectionPool(): Promise<void> {
    try {
      // Pre-create a few connections for the pool
      const initialConnections = Math.min(3, this.poolOptions.maxConnections);

      for (let i = 0; i < initialConnections; i++) {
        const poolConnection = new Database(this.config.filename, {
          ...this.config,
          readonly: false,
        });

        // Configure each pool connection
        poolConnection.pragma('journal_mode = WAL');
        poolConnection.pragma('synchronous = NORMAL');
        poolConnection.pragma('foreign_keys = ON');

        this.connectionPool.push(poolConnection);
      }

      logger.info(`Connection pool initialized with ${initialConnections} connections`);
    } catch (error) {
      logger.error('Failed to initialize connection pool', { error });
      throw error;
    }
  }

  public getConnection(): Database.Database {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
    return this.db;
  }

  public async acquirePoolConnection(): Promise<Database.Database> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Connection acquisition timeout'));
      }, this.poolOptions.acquireTimeout);

      // Try to get an available connection from pool
      if (this.connectionPool.length > 0) {
        const connection = this.connectionPool.pop()!;
        this.activeConnections++;
        clearTimeout(timeout);
        resolve(connection);
        return;
      }

      // Create new connection if under limit
      if (this.activeConnections < this.poolOptions.maxConnections) {
        try {
          const newConnection = new Database(this.config.filename, {
            ...this.config,
            readonly: false,
          });

          newConnection.pragma('journal_mode = WAL');
          newConnection.pragma('synchronous = NORMAL');
          newConnection.pragma('foreign_keys = ON');

          this.activeConnections++;
          clearTimeout(timeout);
          resolve(newConnection);
        } catch (error) {
          clearTimeout(timeout);
          reject(error);
        }
        return;
      }

      // Wait for a connection to become available
      const checkForConnection = () => {
        if (this.connectionPool.length > 0) {
          const connection = this.connectionPool.pop()!;
          this.activeConnections++;
          clearTimeout(timeout);
          resolve(connection);
        } else {
          setTimeout(checkForConnection, 100);
        }
      };

      checkForConnection();
    });
  }

  public releasePoolConnection(connection: Database.Database): void {
    this.activeConnections--;

    // Return connection to pool if under idle limit
    if (this.connectionPool.length < Math.floor(this.poolOptions.maxConnections / 2)) {
      this.connectionPool.push(connection);
    } else {
      // Close excess connections
      try {
        connection.close();
      } catch (error) {
        logger.warn('Error closing excess pool connection', { error });
      }
    }
  }

  public async transaction<T>(
    callback: (db: Database.Database) => T,
    options: TransactionOptions = {}
  ): Promise<T> {
    const connection = await this.acquirePoolConnection();

    try {
      // Determine transaction type
      let transactionType = 'BEGIN';
      if (options.immediate) transactionType = 'BEGIN IMMEDIATE';
      else if (options.exclusive) transactionType = 'BEGIN EXCLUSIVE';
      else if (options.deferred) transactionType = 'BEGIN DEFERRED';

      connection.prepare(transactionType).run();

      try {
        const result = callback(connection);
        connection.prepare('COMMIT').run();
        return result;
      } catch (error) {
        connection.prepare('ROLLBACK').run();
        throw error;
      }
    } finally {
      this.releasePoolConnection(connection);
    }
  }

  public async healthCheck(): Promise<boolean> {
    try {
      if (!this.db) {
        return false;
      }

      // Test main connection
      const result = this.db.prepare('SELECT 1 as health').get() as { health: number } | undefined;
      if (!result || result.health !== 1) {
        return false;
      }

      // Check database integrity
      const integrityResult = this.db.prepare('PRAGMA integrity_check').get() as {
        integrity_check: string;
      };
      if (integrityResult.integrity_check !== 'ok') {
        logger.warn('Database integrity check failed', { result: integrityResult });
        return false;
      }

      // Check WAL mode is active
      const walResult = this.db.prepare('PRAGMA journal_mode').get() as { journal_mode: string };
      if (walResult.journal_mode !== 'wal') {
        logger.warn('Database not in WAL mode', { mode: walResult.journal_mode });
      }

      return true;
    } catch (error) {
      logger.error('Database health check failed', { error });
      return false;
    }
  }

  private startHealthCheck(): void {
    this.healthCheckInterval = setInterval(async () => {
      const isHealthy = await this.healthCheck();
      if (!isHealthy) {
        logger.error('Database health check failed, attempting recovery');
        await this.attemptRecovery();
      }
    }, 60000); // Check every minute
  }

  private async attemptRecovery(): Promise<void> {
    try {
      logger.info('Attempting database recovery');

      // Close current connection
      if (this.db) {
        this.db.close();
        this.db = null;
      }

      // Close all pool connections
      this.connectionPool.forEach(conn => {
        try {
          conn.close();
        } catch (error) {
          logger.warn('Error closing pool connection during recovery', { error });
        }
      });
      this.connectionPool = [];
      this.activeConnections = 0;

      // Wait a moment before reconnecting
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Reinitialize connection
      await this.initialize();

      logger.info('Database recovery completed successfully');
    } catch (error) {
      logger.error('Database recovery failed', { error });
      throw error;
    }
  }

  public async backup(backupPath: string): Promise<void> {
    try {
      if (!this.db) {
        throw new Error('Database not initialized');
      }

      logger.info('Starting database backup', { backupPath });

      // Ensure backup directory exists
      fs.ensureDirSync(path.dirname(backupPath));

      // Use SQLite backup API for consistent backup
      await this.db.backup(backupPath);

      logger.info('Database backup completed successfully', { backupPath });
    } catch (error) {
      logger.error('Database backup failed', { error, backupPath });
      throw error;
    }
  }

  public async close(): Promise<void> {
    try {
      logger.info('Closing database connections');

      // Stop health check
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }

      // Close pool connections
      this.connectionPool.forEach(conn => {
        try {
          conn.close();
        } catch (error) {
          logger.warn('Error closing pool connection', { error });
        }
      });
      this.connectionPool = [];
      this.activeConnections = 0;

      // Close main connection
      if (this.db) {
        this.db.close();
        this.db = null;
      }

      logger.info('Database connections closed successfully');
    } catch (error) {
      logger.error('Error closing database connections', { error });
      throw error;
    }
  }

  // Utility methods for common operations
  public getDbPath(): string {
    return this.config.filename;
  }

  public getDbSize(): number {
    try {
      const stats = fs.statSync(this.config.filename);
      return stats.size;
    } catch (error) {
      logger.warn('Could not get database size', { error });
      return 0;
    }
  }

  public async vacuum(): Promise<void> {
    try {
      if (!this.db) {
        throw new Error('Database not initialized');
      }

      logger.info('Starting database vacuum');
      this.db.prepare('VACUUM').run();
      logger.info('Database vacuum completed');
    } catch (error) {
      logger.error('Database vacuum failed', { error });
      throw error;
    }
  }

  public async analyze(): Promise<void> {
    try {
      if (!this.db) {
        throw new Error('Database not initialized');
      }

      logger.info('Starting database analyze');
      this.db.prepare('ANALYZE').run();
      logger.info('Database analyze completed');
    } catch (error) {
      logger.error('Database analyze failed', { error });
      throw error;
    }
  }
}

// Export singleton instance
export const dbConnection = DatabaseConnection.getInstance();

// Export types and interfaces
export { Database };
export type { DatabaseConnection };

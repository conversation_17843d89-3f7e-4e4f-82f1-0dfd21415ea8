{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2022", "module": "CommonJS", "moduleResolution": "node", "lib": ["ES2022"], "jsx": "preserve", "noEmit": false, "composite": true, "outDir": "./dist/main", "rootDir": "./src", "types": ["node", "electron"]}, "include": ["src/main/**/*", "src/shared/**/*", "src/preload/**/*"], "exclude": ["src/renderer/**/*", "node_modules", "dist", "tests/**/*", "**/__tests__/**/*", "**/*.test.ts"]}
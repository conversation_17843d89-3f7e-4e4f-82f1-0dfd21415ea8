import React from 'react';
import { useParams } from 'react-router-dom';
import { useAppNavigation } from '../router/RouterProvider';

const DocumentViewer: React.FC = () => {
  const { documentId } = useParams<{ documentId: string }>();
  const { navigate } = useAppNavigation();

  return (
    <div className="min-h-screen bg-base-100 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <button
            className="btn btn-ghost btn-sm mb-4"
            onClick={() => navigate('/documents')}
          >
            ← Back to Documents
          </button>
          <h1 className="text-3xl font-bold text-base-content">Document Viewer</h1>
          <p className="text-base-content/70">Viewing document: {documentId}</p>
        </div>

        <div className="bg-base-200 rounded-lg p-8 text-center">
          <div className="w-16 h-16 bg-info/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-8 h-8 text-info"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
              />
            </svg>
          </div>
          <h2 className="text-xl font-semibold mb-2">Document Viewer</h2>
          <p className="text-base-content/70">
            This page will display document content with AI-powered analysis and annotation tools.
          </p>
        </div>
      </div>
    </div>
  );
};

export default DocumentViewer;

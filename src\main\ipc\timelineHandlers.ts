import { ipc<PERSON><PERSON>, <PERSON><PERSON>erWindow } from 'electron';
import { TimelineManager } from '../services/TimelineManager';
import {
  TimelineEntry,
  TimelineQuery,
  CheckpointId,
  ApplicationState,
  DiffResult,
  VisualDiff,
  Branch,
  BranchId,
  UndoRedoState,
  MergeResult,
  DocumentVersion,
} from '../../shared/types/Timeline';

export class TimelineIPCHandlers {
  private timelineManager: TimelineManager;
  private mainWindow: BrowserWindow | null = null;

  constructor(timelineManager: TimelineManager) {
    this.timelineManager = timelineManager;
    this.setupHandlers();
  }

  setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
  }

  private setupHandlers(): void {
    // Timeline query operations
    ipcMain.handle(
      'timeline:getTimeline',
      async (_event, query?: TimelineQuery): Promise<TimelineEntry[]> => {
        try {
          return await this.timelineManager.getTimeline(query);
        } catch (error) {
          console.error('Failed to get timeline:', error);
          throw error;
        }
      }
    );

    ipcMain.handle('timeline:getBranches', async (): Promise<Branch[]> => {
      try {
        return await this.timelineManager.getBranches();
      } catch (error) {
        console.error('Failed to get branches:', error);
        throw error;
      }
    });

    ipcMain.handle('timeline:getUndoRedoState', async (): Promise<UndoRedoState> => {
      try {
        return this.timelineManager.getUndoRedoState();
      } catch (error) {
        console.error('Failed to get undo/redo state:', error);
        throw error;
      }
    });

    ipcMain.handle('timeline:getCurrentBranch', async (): Promise<string> => {
      try {
        return this.timelineManager.getCurrentBranch();
      } catch (error) {
        console.error('Failed to get current branch:', error);
        throw error;
      }
    });

    ipcMain.handle(
      'timeline:getDocumentHistory',
      async (_event, documentId: string): Promise<DocumentVersion[]> => {
        try {
          return await this.timelineManager.getDocumentHistory(documentId);
        } catch (error) {
          console.error('Failed to get document history:', error);
          throw error;
        }
      }
    );

    // Checkpoint operations
    ipcMain.handle(
      'timeline:createCheckpoint',
      async (_event, state: ApplicationState, description?: string): Promise<CheckpointId> => {
        try {
          const checkpointId = await this.timelineManager.createCheckpoint(state, description);
          this.notifyTimelineUpdate();
          return checkpointId;
        } catch (error) {
          console.error('Failed to create checkpoint:', error);
          throw error;
        }
      }
    );

    ipcMain.handle(
      'timeline:restoreCheckpoint',
      async (_event, checkpointId: CheckpointId): Promise<ApplicationState> => {
        try {
          const state = await this.timelineManager.restoreCheckpoint(checkpointId);
          this.notifyTimelineUpdate();
          return state;
        } catch (error) {
          console.error('Failed to restore checkpoint:', error);
          throw error;
        }
      }
    );

    // Undo/Redo operations
    ipcMain.handle('timeline:undo', async (): Promise<ApplicationState | null> => {
      try {
        const state = await this.timelineManager.undo();
        this.notifyTimelineUpdate();
        return state;
      } catch (error) {
        console.error('Failed to undo:', error);
        throw error;
      }
    });

    ipcMain.handle('timeline:redo', async (): Promise<ApplicationState | null> => {
      try {
        const state = await this.timelineManager.redo();
        this.notifyTimelineUpdate();
        return state;
      } catch (error) {
        console.error('Failed to redo:', error);
        throw error;
      }
    });

    // Branch operations
    ipcMain.handle(
      'timeline:createBranch',
      async (
        _event,
        fromCheckpoint: CheckpointId,
        name: string,
        description?: string
      ): Promise<BranchId> => {
        try {
          const branchId = await this.timelineManager.createBranch(
            fromCheckpoint,
            name,
            description
          );
          this.notifyTimelineUpdate();
          return branchId;
        } catch (error) {
          console.error('Failed to create branch:', error);
          throw error;
        }
      }
    );

    ipcMain.handle('timeline:switchBranch', async (_event, branchId: BranchId): Promise<void> => {
      try {
        await this.timelineManager.switchBranch(branchId);
        this.notifyTimelineUpdate();
      } catch (error) {
        console.error('Failed to switch branch:', error);
        throw error;
      }
    });

    ipcMain.handle(
      'timeline:mergeBranches',
      async (_event, sourceBranch: BranchId, targetBranch: BranchId): Promise<MergeResult> => {
        try {
          const result = await this.timelineManager.mergeBranches(sourceBranch, targetBranch);
          this.notifyTimelineUpdate();
          return result;
        } catch (error) {
          console.error('Failed to merge branches:', error);
          throw error;
        }
      }
    );

    // Diff operations
    ipcMain.handle(
      'timeline:createDiff',
      async (_event, before: ApplicationState, after: ApplicationState): Promise<DiffResult> => {
        try {
          return await this.timelineManager.createDiff(before, after);
        } catch (error) {
          console.error('Failed to create diff:', error);
          throw error;
        }
      }
    );

    ipcMain.handle(
      'timeline:createVisualDiff',
      async (_event, before: any, after: any): Promise<VisualDiff> => {
        try {
          return await this.timelineManager.createVisualDiff(before, after);
        } catch (error) {
          console.error('Failed to create visual diff:', error);
          throw error;
        }
      }
    );

    // Export operations
    ipcMain.handle(
      'timeline:exportTimeline',
      async (_event, format: 'json' | 'git'): Promise<Buffer> => {
        try {
          return await this.timelineManager.exportTimeline(format);
        } catch (error) {
          console.error('Failed to export timeline:', error);
          throw error;
        }
      }
    );

    // Timeline entry operations
    ipcMain.handle(
      'timeline:addEntry',
      async (_event, entry: Partial<TimelineEntry>): Promise<string> => {
        try {
          const entryId = await this.timelineManager.addTimelineEntry(entry);
          this.notifyTimelineUpdate();
          return entryId;
        } catch (error) {
          console.error('Failed to add timeline entry:', error);
          throw error;
        }
      }
    );
  }

  private notifyTimelineUpdate(): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('timeline:updated');
    }
  }

  // Public method to manually trigger timeline update notification
  public triggerTimelineUpdate(): void {
    this.notifyTimelineUpdate();
  }

  // Cleanup method to remove all handlers
  public cleanup(): void {
    const handlers = [
      'timeline:getTimeline',
      'timeline:getBranches',
      'timeline:getUndoRedoState',
      'timeline:getCurrentBranch',
      'timeline:getDocumentHistory',
      'timeline:createCheckpoint',
      'timeline:restoreCheckpoint',
      'timeline:undo',
      'timeline:redo',
      'timeline:createBranch',
      'timeline:switchBranch',
      'timeline:mergeBranches',
      'timeline:createDiff',
      'timeline:createVisualDiff',
      'timeline:exportTimeline',
      'timeline:addEntry',
    ];

    handlers.forEach(handler => {
      ipcMain.removeHandler(handler);
    });
  }
}

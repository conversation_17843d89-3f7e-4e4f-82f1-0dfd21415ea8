{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "useDefineForClassFields": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/main/*": ["src/main/*"], "@/renderer/*": ["src/renderer/*"], "@/shared/*": ["src/shared/*"], "@/preload/*": ["src/preload/*"]}, "types": ["node", "jest", "@testing-library/jest-dom"]}, "include": ["tests/**/*", "config/**/*", "scripts/**/*", "global.d.ts", "playwright.config.ts", "src/types/**/*"], "exclude": ["node_modules", "dist", "build", "public", "coverage", "logs", "tests"], "references": [{"path": "./tsconfig.main.json"}, {"path": "./tsconfig.renderer.json"}]}
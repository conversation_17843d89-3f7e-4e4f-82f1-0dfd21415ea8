import React, { useEffect } from 'react';
import { logger } from '../../services/LoggingService';
import { errorAnalytics } from '../../services/ErrorAnalyticsService';
import { useNotification } from '../providers/NotificationProvider';

interface GlobalErrorHandlerProps {
  children: React.ReactNode;
}

/**
 * Global error handler component that captures and processes all application errors
 */
export const GlobalErrorHandler: React.FC<GlobalErrorHandlerProps> = ({ children }) => {
  const { error: showError } = useNotification();

  useEffect(() => {
    // Enhanced global error handler
    const handleError = (event: ErrorEvent) => {
      const errorInfo = {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent,
      };

      logger.error('Global JavaScript Error', errorInfo, 'GlobalErrorHandler');

      // Show user-friendly error message
      if (event.error?.name !== 'ChunkLoadError') {
        showError('An unexpected error occurred. The development team has been notified.');
      }

      // Prevent default browser error handling
      event.preventDefault();
    };

    // Enhanced unhandled promise rejection handler
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const errorInfo = {
        reason: event.reason?.toString(),
        stack: event.reason?.stack,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent,
      };

      logger.error('Unhandled Promise Rejection', errorInfo, 'GlobalErrorHandler');

      // Show user-friendly error message
      showError('A background operation failed. Please try again.');

      // Prevent default browser handling
      event.preventDefault();
    };

    // Resource loading error handler
    const handleResourceError = (event: Event) => {
      const target = event.target as HTMLElement;
      const errorInfo = {
        tagName: target?.tagName,
        src: (target as any)?.src || (target as any)?.href,
        timestamp: new Date().toISOString(),
        url: window.location.href,
      };

      logger.warn('Resource Loading Error', errorInfo, 'GlobalErrorHandler');
    };

    // Network status monitoring
    const handleOnline = () => {
      logger.info('Network Connection Restored', {}, 'GlobalErrorHandler');
    };

    const handleOffline = () => {
      logger.warn('Network Connection Lost', {}, 'GlobalErrorHandler');
      showError('Network connection lost. Some features may not work properly.');
    };

    // Performance monitoring
    const handlePerformanceIssue = () => {
      if (typeof window.performance !== 'undefined') {
        const memory = (window.performance as any).memory;
        if (memory) {
          const memoryUsage = {
            used: memory.usedJSHeapSize,
            total: memory.totalJSHeapSize,
            limit: memory.jsHeapSizeLimit,
            percentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
          };

          if (memoryUsage.percentage > 80) {
            logger.warn('High Memory Usage Detected', memoryUsage, 'GlobalErrorHandler');
            showError('High memory usage detected. Consider closing some documents.');
          }
        }
      }
    };

    // Security monitoring
    const handleSecurityError = (event: SecurityPolicyViolationEvent) => {
      const errorInfo = {
        violatedDirective: event.violatedDirective,
        blockedURI: event.blockedURI,
        documentURI: event.documentURI,
        effectiveDirective: event.effectiveDirective,
        originalPolicy: event.originalPolicy,
        timestamp: new Date().toISOString(),
      };

      logger.error('Content Security Policy Violation', errorInfo, 'GlobalErrorHandler');
    };

    // Add event listeners
    window.addEventListener('error', handleError, true);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleResourceError, true);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    window.addEventListener('securitypolicyviolation', handleSecurityError);

    // Performance monitoring interval
    const performanceInterval = setInterval(handlePerformanceIssue, 30000); // Check every 30 seconds

    // Cleanup function
    return () => {
      window.removeEventListener('error', handleError, true);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleResourceError, true);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      window.removeEventListener('securitypolicyviolation', handleSecurityError);
      clearInterval(performanceInterval);
    };
  }, [showError]);

  // Monitor React errors through error boundary
  useEffect(() => {
    const checkErrorLogs = () => {
      const logs = logger.getLogs();
      const recentErrors = logs.filter(log => {
        const logTime = new Date(log.timestamp).getTime();
        const now = Date.now();
        return now - logTime < 60000; // Last minute
      });

      if (recentErrors.length > 10) {
        logger.warn('High Error Rate Detected', {
          errorCount: recentErrors.length,
          timeWindow: '1 minute',
        }, 'GlobalErrorHandler');
      }
    };

    const errorCheckInterval = setInterval(checkErrorLogs, 60000); // Check every minute

    return () => clearInterval(errorCheckInterval);
  }, []);

  return <>{children}</>;
};

/**
 * Error reporting utility component
 */
export const ErrorReporter: React.FC = () => {
  const generateErrorReport = () => {
    const logs = logger.getLogs();
    const analysis = errorAnalytics.analyzeErrors(logs);
    const report = errorAnalytics.generateReport(analysis);
    
    // Copy to clipboard
    navigator.clipboard.writeText(report).then(() => {
      console.log('Error report copied to clipboard');
    }).catch(() => {
      console.error('Failed to copy error report');
    });
  };

  const clearErrorLogs = () => {
    logger.clearLogs();
    console.log('Error logs cleared');
  };

  const exportErrorLogs = () => {
    const logs = logger.exportLogs();
    const blob = new Blob([logs], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `error-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Only show in development mode
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="dropdown dropdown-top dropdown-end">
        <label tabIndex={0} className="btn btn-ghost btn-sm">
          🐛 Debug
        </label>
        <ul
          tabIndex={0}
          className="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-52"
        >
          <li>
            <button onClick={generateErrorReport}>
              📋 Copy Error Report
            </button>
          </li>
          <li>
            <button onClick={exportErrorLogs}>
              💾 Export Error Logs
            </button>
          </li>
          <li>
            <button onClick={clearErrorLogs}>
              🗑️ Clear Error Logs
            </button>
          </li>
          <li>
            <button onClick={() => console.log('Current logs:', logger.getLogs())}>
              📊 Log Current Errors
            </button>
          </li>
          <li>
            <button onClick={() => {
              throw new Error('Test error for debugging');
            }}>
              ⚠️ Test Error
            </button>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default GlobalErrorHandler;

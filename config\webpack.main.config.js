const path = require('path');
const webpack = require('webpack');

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';

  return {
  mode: argv.mode || 'development',
  entry: './src/main/index.ts',
  target: 'electron-main',
  devtool: 'source-map',
  
  module: {
    rules: [
      {
        test: /\.(ts|js)$/,
        exclude: /node_modules/,
        use: {
          loader: 'ts-loader',
          options: {
            configFile: 'tsconfig.main.json',
            transpileOnly: true // Faster builds in development
          }
        }
      },
      {
        test: /\.node$/,
        use: 'node-loader'
      }
    ]
  },
  
  resolve: {
    extensions: ['.ts', '.js', '.node'],
    alias: {
      '@': path.resolve(__dirname, '../src'),
      '@/main': path.resolve(__dirname, '../src/main'),
      '@/shared': path.resolve(__dirname, '../src/shared'),
      '@/preload': path.resolve(__dirname, '../src/preload')
    }
  },
  
  plugins: [
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify(isProduction ? 'production' : 'development'),
      'process.env.ELECTRON_IS_DEV': JSON.stringify(!isProduction)
    })
  ],
  
  output: {
    path: path.resolve(__dirname, '../dist/main'),
    filename: 'index.js',
    clean: true
  },
  
  externals: {
    // Exclude native modules from bundling
    'better-sqlite3': 'commonjs better-sqlite3',
    'sqlite3': 'commonjs sqlite3',
    'sharp': 'commonjs sharp',
    'canvas': 'commonjs canvas',
    'fsevents': 'commonjs fsevents'
  },
  
  node: {
    __dirname: false,
    __filename: false
  },
  
  optimization: {
    minimize: isProduction
  }
};
};
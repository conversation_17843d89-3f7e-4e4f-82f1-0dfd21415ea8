#!/usr/bin/env node

/**
 * Development Environment Setup Script
 *
 * This script helps new developers set up their development environment
 * for the AI Document Processor project.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`[${step}] ${message}`, colors.cyan);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function checkNodeVersion() {
  logStep('1/8', 'Checking Node.js version...');

  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

  if (majorVersion < 18) {
    logError(`Node.js version ${nodeVersion} is not supported. Please install Node.js 18 or higher.`);
    process.exit(1);
  }

  logSuccess(`Node.js version ${nodeVersion} is supported.`);
}

function checkNpmVersion() {
  logStep('2/8', 'Checking npm version...');

  try {
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
    const majorVersion = parseInt(npmVersion.split('.')[0]);

    if (majorVersion < 9) {
      logWarning(`npm version ${npmVersion} is older than recommended. Consider upgrading to npm 9+.`);
    } else {
      logSuccess(`npm version ${npmVersion} is supported.`);
    }
  } catch (error) {
    logError('npm is not installed or not accessible.');
    process.exit(1);
  }
}

function installDependencies() {
  logStep('3/8', 'Installing dependencies...');

  try {
    execSync('npm ci', { stdio: 'inherit' });
    logSuccess('Dependencies installed successfully.');
  } catch (error) {
    logError('Failed to install dependencies.');
    process.exit(1);
  }
}

function setupEnvironmentFile() {
  logStep('4/8', 'Setting up environment file...');

  const envPath = path.join(process.cwd(), '.env');
  const envExamplePath = path.join(process.cwd(), '.env.example');

  if (!fs.existsSync(envPath)) {
    if (fs.existsSync(envExamplePath)) {
      fs.copyFileSync(envExamplePath, envPath);
      logSuccess('Created .env file from .env.example template.');
      logWarning('Please update the .env file with your actual configuration values.');
    } else {
      logWarning('.env.example file not found. Please create .env file manually.');
    }
  } else {
    logSuccess('.env file already exists.');
  }
}

function setupDatabase() {
  logStep('5/8', 'Setting up database...');

  try {
    // Create data directory if it doesn't exist
    const dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
      logSuccess('Created data directory.');
    }

    // Run database migrations
    execSync('npm run db:migrate', { stdio: 'inherit' });
    logSuccess('Database migrations completed.');
  } catch (error) {
    logError('Failed to set up database.');
    logWarning('You may need to run "npm run db:migrate" manually later.');
  }
}

function setupGitHooks() {
  logStep('6/8', 'Setting up Git hooks...');

  try {
    execSync('npm run prepare', { stdio: 'inherit' });
    logSuccess('Git hooks installed successfully.');
  } catch (error) {
    logWarning('Failed to install Git hooks. Pre-commit checks may not work.');
  }
}

function buildProject() {
  logStep('7/8', 'Building project...');

  try {
    execSync('npm run build:css', { stdio: 'inherit' });
    logSuccess('CSS build completed.');

    execSync('npm run type-check', { stdio: 'inherit' });
    logSuccess('Type checking passed.');
  } catch (error) {
    logWarning('Build step failed. You may need to fix compilation errors.');
  }
}

function runTests() {
  logStep('8/8', 'Running tests...');

  try {
    execSync('npm run test -- --passWithNoTests', { stdio: 'inherit' });
    logSuccess('All tests passed.');
  } catch (error) {
    logWarning('Some tests failed. Please check the test output.');
  }
}

function printNextSteps() {
  log('\n' + '='.repeat(60), colors.bright);
  log('🎉 Development environment setup complete!', colors.green + colors.bright);
  log('='.repeat(60), colors.bright);

  log('\nNext steps:', colors.bright);
  log('1. Update your .env file with actual API keys and configuration', colors.yellow);
  log('2. Start the development server: npm run dev', colors.cyan);
  log('3. Open VS Code and install recommended extensions', colors.cyan);
  log('4. Read the documentation in the docs/ directory', colors.cyan);

  log('\nUseful commands:', colors.bright);
  log('• npm run dev          - Start development server', colors.blue);
  log('• npm run test         - Run tests', colors.blue);
  log('• npm run lint         - Lint and fix code', colors.blue);
  log('• npm run type-check   - Check TypeScript types', colors.blue);
  log('• npm run db:migrate   - Run database migrations', colors.blue);
  log('• npm run clean        - Clean build artifacts', colors.blue);

  log('\nFor more information, see README.md', colors.magenta);
  log('');
}

function main() {
  log('🚀 AI Document Processor - Development Setup', colors.bright + colors.cyan);
  log('This script will set up your development environment.\n');

  try {
    checkNodeVersion();
    checkNpmVersion();
    installDependencies();
    setupEnvironmentFile();
    setupDatabase();
    setupGitHooks();
    buildProject();
    runTests();
    printNextSteps();
  } catch (error) {
    logError(`Setup failed: ${error.message}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  checkNodeVersion,
  checkNpmVersion,
  installDependencies,
  setupEnvironmentFile,
  setupDatabase,
  setupGitHooks,
  buildProject,
  runTests
};

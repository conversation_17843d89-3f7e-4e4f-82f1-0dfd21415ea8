const path = require('path');

module.exports = {
  mode: process.env.NODE_ENV || 'development',
  entry: './src/preload/index.ts',
  target: 'electron-preload',
  devtool: 'source-map',
  
  module: {
    rules: [
      {
        test: /\.(ts|js)$/,
        exclude: /node_modules/,
        use: {
          loader: 'ts-loader',
          options: {
            configFile: 'tsconfig.main.json',
            transpileOnly: true
          }
        }
      }
    ]
  },
  
  resolve: {
    extensions: ['.ts', '.js'],
    alias: {
      '@': path.resolve(__dirname, '../src'),
      '@/shared': path.resolve(__dirname, '../src/shared'),
      '@/preload': path.resolve(__dirname, '../src/preload')
    }
  },
  
  output: {
    path: path.resolve(__dirname, '../dist/preload'),
    filename: 'index.js',
    clean: true
  },
  
  node: {
    __dirname: false,
    __filename: false
  }
};
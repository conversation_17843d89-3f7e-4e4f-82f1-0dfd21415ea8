/**
 * PDF-related type definitions
 */

// PDF.js types
export interface PDFTextItem {
  str: string;
  dir: string;
  width: number;
  height: number;
  transform: number[];
  fontName: string;
  hasEOL: boolean;
}

export interface PDFViewport {
  width: number;
  height: number;
  scale: number;
  rotation: number;
  offsetX: number;
  offsetY: number;
  transform: number[];
}

export interface PDFTextContent {
  items: PDFTextItem[];
  styles: Record<string, any>;
}

export interface PDFPageProxy {
  getTextContent(): Promise<PDFTextContent>;
  getViewport(options: { scale: number }): PDFViewport;
  render(options: any): any;
  pageNumber: number;
}

export interface PDFDocumentProxy {
  numPages: number;
  getPage(pageNumber: number): Promise<PDFPageProxy>;
  getMetadata(): Promise<any>;
  destroy(): void;
}

// PDF-lib types
export interface PDFLibPage {
  getSize(): { width: number; height: number };
  drawText(text: string, options?: any): void;
  drawRectangle(options: any): void;
  drawCircle(options: any): void;
  drawLine(options: any): void;
  setFont(font: any): void;
  setFontSize(size: number): void;
  setFontColor(color: any): void;
}

export interface PDFLibDocument {
  getPages(): PDFLibPage[];
  addPage(): PDFLibPage;
  embedFont(font: any): Promise<any>;
  save(): Promise<Uint8Array>;
  copyPages(doc: PDFLibDocument, pageIndices: number[]): Promise<PDFLibPage[]>;
}

// Font and styling types
export interface FontInfo {
  name: string;
  size: number;
  bold: boolean;
  italic: boolean;
  color: string;
}

export interface TextStyling {
  font: FontInfo;
  alignment: 'left' | 'center' | 'right' | 'justify';
  lineHeight: number;
  letterSpacing: number;
  wordSpacing: number;
}

// Table cell types for PDF processing
export interface PDFTableCell {
  text: string;
  bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  style?: TextStyling;
  rowSpan?: number;
  colSpan?: number;
}

export interface PDFTableRow {
  cells: PDFTableCell[];
  bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface PDFTable {
  rows: PDFTableRow[];
  bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  pageNumber: number;
}

// Merge options
export interface PDFMergeOptions {
  title?: string;
  author?: string;
  subject?: string;
  keywords?: string[];
  creator?: string;
  producer?: string;
}

// Conversion options
export interface PDFToImageOptions {
  format: 'png' | 'jpeg' | 'webp';
  quality?: number;
  scale?: number;
  pages?: number[];
  background?: string;
}

// Form field types
export interface PDFFormField {
  name: string;
  type: 'text' | 'checkbox' | 'radio' | 'select' | 'signature';
  value: string | boolean;
  required: boolean;
  readOnly: boolean;
  bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
    pageNumber: number;
  };
  options?: string[]; // For select fields
}

// Annotation types
export interface PDFAnnotationBase {
  id: string;
  type: string;
  pageNumber: number;
  bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  author?: string;
  createdDate?: Date;
  modifiedDate?: Date;
}

export interface PDFHighlightAnnotation extends PDFAnnotationBase {
  type: 'highlight';
  color: string;
  opacity: number;
  text: string;
}

export interface PDFNoteAnnotation extends PDFAnnotationBase {
  type: 'note';
  content: string;
  icon: string;
}

export interface PDFStampAnnotation extends PDFAnnotationBase {
  type: 'stamp';
  stampType: string;
  text?: string;
}

export type PDFAnnotationType = PDFHighlightAnnotation | PDFNoteAnnotation | PDFStampAnnotation;

// Security and permissions
export interface PDFSecurity {
  isEncrypted: boolean;
  permissions: {
    print: boolean;
    modify: boolean;
    copy: boolean;
    annotate: boolean;
    fillForms: boolean;
    extractText: boolean;
    assemble: boolean;
    printHighQuality: boolean;
  };
}

// OCR types
export interface OCRResult {
  text: string;
  confidence: number;
  words: Array<{
    text: string;
    confidence: number;
    bounds: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  }>;
  lines: Array<{
    text: string;
    confidence: number;
    bounds: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
    words: number[];
  }>;
  paragraphs: Array<{
    text: string;
    confidence: number;
    bounds: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
    lines: number[];
  }>;
}

// Processing options
export interface PDFProcessingOptions {
  extractText: boolean;
  extractImages: boolean;
  extractTables: boolean;
  extractForms: boolean;
  extractAnnotations: boolean;
  performOCR: boolean;
  ocrLanguage?: string;
  imageQuality?: number;
  preserveFormatting: boolean;
}

// Error types
export interface PDFProcessingError {
  code: string;
  message: string;
  details?: any;
  pageNumber?: number;
}

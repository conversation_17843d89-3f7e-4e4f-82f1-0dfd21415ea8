import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import {
  TimelineEntry,
  TimelineFilter,
  TimelineSortField,
  SortDirection,
  Branch,
  UndoRedoState,
  CheckpointId,
  BranchId,
  DiffR<PERSON>ult,
  VisualDiff,
  TimelineViewMode,
  TimelineDisplayOptions,
} from '../../shared/types/Timeline';

export interface TimelineState {
  // Data
  entries: TimelineEntry[];
  branches: Branch[];
  undoRedoState: UndoRedoState;
  selectedEntry: TimelineEntry | null;
  selectedBranch: Branch | null;

  // UI State
  filter: TimelineFilter;
  sort: { field: TimelineSortField; direction: SortDirection };
  viewMode: TimelineViewMode;
  displayOptions: TimelineDisplayOptions;
  searchTerm: string;

  // Loading states
  isLoading: boolean;
  isCreatingCheckpoint: boolean;
  isUndoing: boolean;
  isRedoing: boolean;
  isSwitchingBranch: boolean;

  // Error states
  error: string | null;

  // Diff viewer state
  currentDiff: DiffResult | null;
  currentVisualDiff: VisualDiff | null;
  showDiffViewer: boolean;

  // Panel states
  showTimelinePanel: boolean;
  showBranchPanel: boolean;
  showDiffPanel: boolean;
  timelinePanelWidth: number;

  // Actions
  setEntries: (entries: TimelineEntry[]) => void;
  setBranches: (branches: Branch[]) => void;
  setUndoRedoState: (state: UndoRedoState) => void;
  setSelectedEntry: (entry: TimelineEntry | null) => void;
  setSelectedBranch: (branch: Branch | null) => void;

  // Filter and sort actions
  setFilter: (filter: TimelineFilter) => void;
  updateFilter: (filter: Partial<TimelineFilter>) => void;
  clearFilter: () => void;
  setSort: (field: TimelineSortField, direction: SortDirection) => void;
  setSearchTerm: (term: string) => void;

  // UI actions
  setViewMode: (mode: TimelineViewMode) => void;
  setDisplayOptions: (options: Partial<TimelineDisplayOptions>) => void;

  // Loading actions
  setLoading: (loading: boolean) => void;
  setCreatingCheckpoint: (creating: boolean) => void;
  setUndoing: (undoing: boolean) => void;
  setRedoing: (redoing: boolean) => void;
  setSwitchingBranch: (switching: boolean) => void;

  // Error actions
  setError: (error: string | null) => void;
  clearError: () => void;

  // Diff actions
  setCurrentDiff: (diff: DiffResult | null) => void;
  setCurrentVisualDiff: (diff: VisualDiff | null) => void;
  setShowDiffViewer: (show: boolean) => void;

  // Panel actions
  setShowTimelinePanel: (show: boolean) => void;
  setShowBranchPanel: (show: boolean) => void;
  setShowDiffPanel: (show: boolean) => void;
  setTimelinePanelWidth: (width: number) => void;

  // Utility actions
  reset: () => void;
  addEntry: (entry: TimelineEntry) => void;
  updateEntry: (id: string, updates: Partial<TimelineEntry>) => void;
  removeEntry: (id: string) => void;
}

const initialState = {
  // Data
  entries: [],
  branches: [],
  undoRedoState: {
    undoStack: [],
    redoStack: [],
    currentPosition: 0,
    maxStackSize: 100,
    canUndo: false,
    canRedo: false,
  },
  selectedEntry: null,
  selectedBranch: null,

  // UI State
  filter: {},
  sort: { field: TimelineSortField.CREATED_AT, direction: SortDirection.DESC },
  viewMode: TimelineViewMode.LIST,
  displayOptions: {
    showLineNumbers: true,
    showTimestamps: true,
    showUserInfo: true,
    showTags: true,
    showAffectedDocuments: true,
    groupByDate: false,
    compactMode: false,
    highlightChanges: true,
  },
  searchTerm: '',

  // Loading states
  isLoading: false,
  isCreatingCheckpoint: false,
  isUndoing: false,
  isRedoing: false,
  isSwitchingBranch: false,

  // Error states
  error: null,

  // Diff viewer state
  currentDiff: null,
  currentVisualDiff: null,
  showDiffViewer: false,

  // Panel states
  showTimelinePanel: true,
  showBranchPanel: true,
  showDiffPanel: false,
  timelinePanelWidth: 300,
};

export const useTimelineStore = create<TimelineState>()(
  subscribeWithSelector(
    immer((set, get) => ({
      ...initialState,

      // Data actions
      setEntries: entries =>
        set(state => {
          state.entries = entries;
        }),

      setBranches: branches =>
        set(state => {
          state.branches = branches;
          // Update selected branch if it's no longer available
          if (state.selectedBranch && !branches.find(b => b.id === state.selectedBranch!.id)) {
            state.selectedBranch = branches.find(b => b.isActive) || null;
          }
        }),

      setUndoRedoState: undoRedoState =>
        set(state => {
          state.undoRedoState = undoRedoState;
        }),

      setSelectedEntry: entry =>
        set(state => {
          state.selectedEntry = entry;
        }),

      setSelectedBranch: branch =>
        set(state => {
          state.selectedBranch = branch;
        }),

      // Filter and sort actions
      setFilter: filter =>
        set(state => {
          state.filter = filter;
        }),

      updateFilter: filterUpdate =>
        set(state => {
          state.filter = { ...state.filter, ...filterUpdate };
        }),

      clearFilter: () =>
        set(state => {
          state.filter = {};
        }),

      setSort: (field, direction) =>
        set(state => {
          state.sort = { field, direction };
        }),

      setSearchTerm: term =>
        set(state => {
          state.searchTerm = term;
        }),

      // UI actions
      setViewMode: mode =>
        set(state => {
          state.viewMode = mode;
        }),

      setDisplayOptions: options =>
        set(state => {
          state.displayOptions = { ...state.displayOptions, ...options };
        }),

      // Loading actions
      setLoading: loading =>
        set(state => {
          state.isLoading = loading;
        }),

      setCreatingCheckpoint: creating =>
        set(state => {
          state.isCreatingCheckpoint = creating;
        }),

      setUndoing: undoing =>
        set(state => {
          state.isUndoing = undoing;
        }),

      setRedoing: redoing =>
        set(state => {
          state.isRedoing = redoing;
        }),

      setSwitchingBranch: switching =>
        set(state => {
          state.isSwitchingBranch = switching;
        }),

      // Error actions
      setError: error =>
        set(state => {
          state.error = error;
        }),

      clearError: () =>
        set(state => {
          state.error = null;
        }),

      // Diff actions
      setCurrentDiff: diff =>
        set(state => {
          state.currentDiff = diff;
          if (diff) {
            state.showDiffViewer = true;
          }
        }),

      setCurrentVisualDiff: diff =>
        set(state => {
          state.currentVisualDiff = diff;
          if (diff) {
            state.showDiffViewer = true;
          }
        }),

      setShowDiffViewer: show =>
        set(state => {
          state.showDiffViewer = show;
          if (!show) {
            state.currentDiff = null;
            state.currentVisualDiff = null;
          }
        }),

      // Panel actions
      setShowTimelinePanel: show =>
        set(state => {
          state.showTimelinePanel = show;
        }),

      setShowBranchPanel: show =>
        set(state => {
          state.showBranchPanel = show;
        }),

      setShowDiffPanel: show =>
        set(state => {
          state.showDiffPanel = show;
        }),

      setTimelinePanelWidth: width =>
        set(state => {
          state.timelinePanelWidth = Math.max(200, Math.min(600, width));
        }),

      // Utility actions
      reset: () => set(() => ({ ...initialState })),

      addEntry: entry =>
        set(state => {
          state.entries.unshift(entry); // Add to beginning for chronological order
        }),

      updateEntry: (id, updates) =>
        set(state => {
          const index = state.entries.findIndex(entry => entry.id === id);
          if (index !== -1) {
            state.entries[index] = { ...state.entries[index], ...updates };
          }
        }),

      removeEntry: id =>
        set(state => {
          state.entries = state.entries.filter(entry => entry.id !== id);
        }),
    }))
  )
);

// Selectors for computed values
export const useTimelineSelectors = () => {
  const store = useTimelineStore();

  return {
    // Filtered and sorted entries
    filteredEntries: () => {
      let entries = [...store.entries];

      // Apply search filter
      if (store.searchTerm) {
        entries = entries.filter(
          entry =>
            entry.description.toLowerCase().includes(store.searchTerm.toLowerCase()) ||
            entry.action.toLowerCase().includes(store.searchTerm.toLowerCase())
        );
      }

      // Apply type filter
      if (store.filter.types?.length) {
        entries = entries.filter(entry => store.filter.types!.includes(entry.type));
      }

      // Apply action filter
      if (store.filter.actions?.length) {
        entries = entries.filter(entry => store.filter.actions!.includes(entry.action));
      }

      // Apply date range filter
      if (store.filter.dateRange) {
        entries = entries.filter(entry => {
          const entryDate = entry.createdAt;
          return (
            entryDate >= store.filter.dateRange!.start && entryDate <= store.filter.dateRange!.end
          );
        });
      }

      // Apply user filter
      if (store.filter.users?.length) {
        entries = entries.filter(entry => store.filter.users!.includes(entry.userId || ''));
      }

      // Apply priority filter
      if (store.filter.priority?.length) {
        entries = entries.filter(entry => store.filter.priority!.includes(entry.metadata.priority));
      }

      // Apply sorting
      entries.sort((a, b) => {
        let aValue: any;
        let bValue: any;

        switch (store.sort.field) {
          case TimelineSortField.CREATED_AT:
            aValue = a.createdAt.getTime();
            bValue = b.createdAt.getTime();
            break;
          case TimelineSortField.TYPE:
            aValue = a.type;
            bValue = b.type;
            break;
          case TimelineSortField.ACTION:
            aValue = a.action;
            bValue = b.action;
            break;
          case TimelineSortField.USER:
            aValue = a.userId || '';
            bValue = b.userId || '';
            break;
          case TimelineSortField.PRIORITY:
            aValue = a.metadata.priority;
            bValue = b.metadata.priority;
            break;
          case TimelineSortField.PROCESSING_TIME:
            aValue = a.processingTime;
            bValue = b.processingTime;
            break;
          default:
            aValue = a.createdAt.getTime();
            bValue = b.createdAt.getTime();
        }

        if (store.sort.direction === SortDirection.ASC) {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        } else {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        }
      });

      return entries;
    },

    // Current active branch
    currentBranch: () => {
      return store.branches.find(branch => branch.isActive) || null;
    },

    // Protected branches
    protectedBranches: () => {
      return store.branches.filter(branch => branch.isProtected);
    },

    // Active branches (not archived)
    activeBranches: () => {
      return store.branches.filter(branch => !branch.metadata.tags.includes('archived'));
    },

    // Timeline statistics
    timelineStats: () => {
      const entries = store.entries;
      const totalEntries = entries.length;
      const entriesByType = entries.reduce(
        (acc, entry) => {
          acc[entry.type] = (acc[entry.type] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      );

      const entriesByUser = entries.reduce(
        (acc, entry) => {
          const user = entry.userId || 'System';
          acc[user] = (acc[user] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      );

      return {
        totalEntries,
        entriesByType,
        entriesByUser,
        oldestEntry: entries.length > 0 ? entries[entries.length - 1].createdAt : null,
        newestEntry: entries.length > 0 ? entries[0].createdAt : null,
      };
    },
  };
};

// Persistence middleware for timeline store
export const persistTimelineStore = () => {
  const store = useTimelineStore.getState();

  // Save to localStorage
  const persistedState = {
    filter: store.filter,
    sort: store.sort,
    viewMode: store.viewMode,
    displayOptions: store.displayOptions,
    showTimelinePanel: store.showTimelinePanel,
    showBranchPanel: store.showBranchPanel,
    showDiffPanel: store.showDiffPanel,
    timelinePanelWidth: store.timelinePanelWidth,
  };

  localStorage.setItem('timeline-store', JSON.stringify(persistedState));
};

// Load persisted state
export const loadPersistedTimelineStore = () => {
  try {
    const persistedState = localStorage.getItem('timeline-store');
    if (persistedState) {
      const parsed = JSON.parse(persistedState);
      const store = useTimelineStore.getState();

      // Apply persisted state
      store.setFilter(parsed.filter || {});
      store.setSort(
        parsed.sort?.field || TimelineSortField.CREATED_AT,
        parsed.sort?.direction || SortDirection.DESC
      );
      store.setViewMode(parsed.viewMode || TimelineViewMode.LIST);
      store.setDisplayOptions(parsed.displayOptions || {});
      store.setShowTimelinePanel(parsed.showTimelinePanel ?? true);
      store.setShowBranchPanel(parsed.showBranchPanel ?? true);
      store.setShowDiffPanel(parsed.showDiffPanel ?? false);
      store.setTimelinePanelWidth(parsed.timelinePanelWidth || 300);
    }
  } catch (error) {
    console.warn('Failed to load persisted timeline store:', error);
  }
};

// Subscribe to store changes for persistence
useTimelineStore.subscribe(
  state => ({
    filter: state.filter,
    sort: state.sort,
    viewMode: state.viewMode,
    displayOptions: state.displayOptions,
    showTimelinePanel: state.showTimelinePanel,
    showBranchPanel: state.showBranchPanel,
    showDiffPanel: state.showDiffPanel,
    timelinePanelWidth: state.timelinePanelWidth,
  }),
  persistTimelineStore,
  { equalityFn: (a, b) => JSON.stringify(a) === JSON.stringify(b) }
);

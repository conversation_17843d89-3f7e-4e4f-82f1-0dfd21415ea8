import { Component, ErrorInfo, ReactNode } from 'react';
import { toast } from 'react-hot-toast';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId?: string;
}

/**
 * Enhanced Error Boundary component with detailed error reporting,
 * user-friendly fallback UI, and error recovery options.
 */
export class ErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Generate unique error ID for tracking
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    return {
      hasError: true,
      error,
      errorId,
    };
  }

  override componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { onError } = this.props;

    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    // Store error info in state
    this.setState({ errorInfo });

    // Call custom error handler if provided
    if (onError) {
      onError(error, errorInfo);
    }

    // Send error to main process for logging
    if (window.electronAPI?.logToMain) {
      window.electronAPI.logToMain('error', 'React Error Boundary', {
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack,
        },
        errorInfo: {
          componentStack: errorInfo.componentStack,
        },
        errorId: this.state.errorId,
        retryCount: this.retryCount,
        timestamp: new Date().toISOString(),
      });
    }

    // Show user-friendly error notification
    if (this.state.errorId) {
      toast.error('An unexpected error occurred. Please try again.', {
        id: this.state.errorId,
        duration: 5000,
      });
    } else {
      toast.error('An unexpected error occurred. Please try again.', {
        duration: 5000,
      });
    }
  }

  handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      this.setState({
        hasError: false,
        error: undefined,
        errorInfo: undefined,
        errorId: undefined,
      });

      toast.success(`Retrying... (${this.retryCount}/${this.maxRetries})`, {
        duration: 2000,
      });
    } else {
      toast.error('Maximum retry attempts reached. Please reload the application.', {
        duration: 8000,
      });
    }
  };

  handleReload = () => {
    window.location.reload();
  };

  handleReportError = () => {
    const { error, errorInfo, errorId } = this.state;

    // Create error report
    const errorReport = {
      errorId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      error: {
        name: error?.name,
        message: error?.message,
        stack: error?.stack,
      },
      errorInfo: {
        componentStack: errorInfo?.componentStack,
      },
      retryCount: this.retryCount,
    };

    // Copy to clipboard
    navigator.clipboard
      .writeText(JSON.stringify(errorReport, null, 2))
      .then(() => {
        toast.success('Error report copied to clipboard', { duration: 3000 });
      })
      .catch(() => {
        toast.error('Failed to copy error report', { duration: 3000 });
      });
  };

  override render() {
    const { hasError, error, errorId } = this.state;
    const { children, fallback } = this.props;

    if (hasError) {
      // Use custom fallback if provided
      if (fallback) {
        return fallback;
      }

      // Default error UI
      return (
        <div className='min-h-screen flex items-center justify-center bg-base-100 p-4'>
          <div className='max-w-md w-full bg-base-200 rounded-lg shadow-lg p-6'>
            <div className='text-center mb-6'>
              <div className='w-16 h-16 bg-error/10 rounded-full flex items-center justify-center mx-auto mb-4'>
                <svg
                  className='w-8 h-8 text-error'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'
                  />
                </svg>
              </div>
              <h1 className='text-xl font-bold text-error mb-2'>Something went wrong</h1>
              <p className='text-base-content/70 text-sm mb-4'>
                The application encountered an unexpected error and needs to recover.
              </p>
              {errorId && (
                <p className='text-xs text-base-content/50 font-mono'>Error ID: {errorId}</p>
              )}
            </div>

            <div className='space-y-3'>
              {this.retryCount < this.maxRetries && (
                <button className='btn btn-primary btn-block' onClick={this.handleRetry}>
                  Try Again ({this.maxRetries - this.retryCount} attempts left)
                </button>
              )}

              <button className='btn btn-outline btn-block' onClick={this.handleReload}>
                Reload Application
              </button>

              <button className='btn btn-ghost btn-block btn-sm' onClick={this.handleReportError}>
                Copy Error Report
              </button>
            </div>

            {process.env.NODE_ENV === 'development' && error && (
              <details className='mt-6'>
                <summary className='cursor-pointer text-sm text-base-content/70 hover:text-base-content'>
                  Error Details (Development)
                </summary>
                <div className='mt-2 p-3 bg-base-300 rounded text-xs font-mono overflow-auto max-h-40'>
                  <div className='text-error font-bold'>
                    {error.name}: {error.message}
                  </div>
                  {error.stack && (
                    <pre className='mt-2 text-base-content/70 whitespace-pre-wrap'>
                      {error.stack}
                    </pre>
                  )}
                </div>
              </details>
            )}
          </div>
        </div>
      );
    }

    return children;
  }
}

export default ErrorBoundary;

import {
  DocumentContent,
  DocumentImage,
  DocumentPage,
  DocumentTable,
  DocumentType,
  ExtractedData,
  ExtractedDataType,
  ExtractionMethod,
  OCRResult,
  ProcessingOptions,
  ValidationResult,
} from '../../shared/types/Document';
import { logger } from '../utils/logger';
import { DocumentProcessor } from './DocumentProcessor';
import { ImageProcessor } from './ImageProcessor';
import { OCREngine } from './OCREngine';

/**
 * Enhanced Image Processor that combines image processing with OCR capabilities
 * Handles image documents, scanned PDFs, and performs advanced OCR operations
 */
export class EnhancedImageProcessor extends DocumentProcessor {
  private ocrEngine: OCREngine;
  private imageProcessor: ImageProcessor;

  constructor() {
    super('EnhancedImageProcessor', {
      supportedTypes: [DocumentType.IMAGE],
      canExtractText: true,
      canExtractImages: true,
      canExtractTables: true,
      canDetectFormFields: true,
      canPerformOCR: true,
      canPreserveFormatting: false,
      maxFileSize: 50 * 1024 * 1024, // 50MB
      supportedEncodings: ['utf-8'],
      requiresNetwork: false,
      processingTimeEstimate: 10, // seconds per MB
    });

    this.ocrEngine = new OCREngine({
      maxWorkers: 2,
      cacheEnabled: true,
      cacheTTL: 3600,
      defaultLanguage: 'eng',
      supportedLanguages: ['eng', 'spa', 'fra', 'deu'],
    });

    this.imageProcessor = new ImageProcessor();
  }

  /**
   * Initialize the processor
   */
  public async initialize(): Promise<void> {
    await this.ocrEngine.initialize();
    logger.info('Enhanced Image Processor initialized');
  }

  /**
   * Extract text content from image using OCR
   */
  public async extractText(buffer: Buffer, options: ProcessingOptions): Promise<string> {
    try {
      // Preprocess image for better OCR results
      const preprocessedBuffer = await this.imageProcessor.enhanceForOCR(buffer);

      // Perform OCR
      const ocrResult = await this.ocrEngine.extractTextFromImage(preprocessedBuffer, {
        language: options.ocrLanguage || 'eng',
        enhanceImage: !options.enhanceImages,
        minimumConfidence: 60,
      });

      return ocrResult.text || '';
    } catch (error) {
      logger.error('Error extracting text from image:', error);
      return '';
    }
  }

  /**
   * Extract structured data from image
   */
  public async extractStructuredData(
    buffer: Buffer,
    options: ProcessingOptions
  ): Promise<ExtractedData[]> {
    // This is the main processing method - just call it and extract the data
    const result = await this.processImageDocument(buffer, options);
    return result.extractedData || [];
  }

  /**
   * Process image document with OCR and advanced analysis
   */
  private async processImageDocument(
    buffer: Buffer,
    options: ProcessingOptions
  ): Promise<{
    extractedData: ExtractedData[];
    documentContent: DocumentContent;
    processingTime: number;
    confidence: number;
  }> {
    const startTime = Date.now();

    try {
      logger.info('Starting enhanced image processing', {
        bufferSize: buffer.length,
        options,
      });

      const extractedData: ExtractedData[] = [];
      const documentContent: DocumentContent = {
        text: '',
        pages: [],
        formFields: [],
        images: [],
        tables: [],
      };

      // Step 1: Enhance image for better OCR
      let processedImageBuffer = buffer;
      if (options.enhanceImages) {
        const imageProcessResult = await this.imageProcessor.processImage(
          buffer,
          this.imageProcessor.getOCROptimizedOptions()
        );

        if (imageProcessResult.success) {
          processedImageBuffer = Buffer.from(imageProcessResult.processedImage.data);

          // Add image enhancement data
          extractedData.push({
            id: `enhancement_${Date.now()}`,
            documentId: '',
            type: ExtractedDataType.IMAGE,
            content: {
              enhancements: imageProcessResult.enhancements,
              originalSize: imageProcessResult.originalImage,
              processedSize: imageProcessResult.processedImage,
            },
            confidence: 1.0,
            extractionMethod: ExtractionMethod.AI_ANALYSIS,
            createdAt: new Date(),
          });
        }
      }

      // Step 2: Perform OCR if requested
      let ocrResult: OCRResult | null = null;
      if (options.performOCR) {
        ocrResult = await this.ocrEngine.extractTextFromImage(processedImageBuffer, {
          language: options.ocrLanguage || 'eng',
          enhanceImage: !options.enhanceImages, // Don't enhance twice
          minimumConfidence: 60,
        });

        documentContent.text = ocrResult.text;

        // Create document page from OCR result
        const page: DocumentPage = {
          pageNumber: 1,
          text: ocrResult.text,
          width: 0, // Will be set from image metadata
          height: 0, // Will be set from image metadata
          annotations: [],
        };

        documentContent.pages = [page];

        // Add OCR text data
        extractedData.push({
          id: `ocr_text_${Date.now()}`,
          documentId: '',
          type: ExtractedDataType.TEXT,
          content: {
            text: ocrResult.text,
            confidence: ocrResult.confidence,
            language: ocrResult.language,
            words: ocrResult.words,
            lines: ocrResult.lines,
            paragraphs: ocrResult.paragraphs,
          },
          confidence: ocrResult.confidence / 100,
          extractionMethod: ExtractionMethod.OCR,
          createdAt: new Date(),
        });
      }

      // Step 3: Extract tables if requested
      if (options.extractTables && ocrResult) {
        const tableData = await this.ocrEngine.extractTabularData(processedImageBuffer);

        if (tableData.rows.length > 0) {
          const documentTable: DocumentTable = {
            id: `table_${Date.now()}`,
            pageNumber: 1,
            bounds: tableData.bounds,
            rows: tableData.rows,
            headers: tableData.headers || [],
          };

          documentContent.tables = [documentTable];

          extractedData.push({
            id: `table_${Date.now()}`,
            documentId: '',
            type: ExtractedDataType.TABLE,
            content: tableData,
            confidence: tableData.confidence / 100,
            extractionMethod: ExtractionMethod.OCR,
            coordinates: tableData.bounds,
            createdAt: new Date(),
          });
        }
      }

      // Step 4: Detect form fields if requested
      if (options.detectFormFields) {
        const formFields = await this.ocrEngine.extractFormFields(processedImageBuffer);

        if (formFields.length > 0) {
          documentContent.formFields = formFields;

          extractedData.push({
            id: `forms_${Date.now()}`,
            documentId: '',
            type: ExtractedDataType.FORM_FIELD,
            content: { fields: formFields },
            confidence: 0.8, // Default confidence for form detection
            extractionMethod: ExtractionMethod.OCR,
            createdAt: new Date(),
          });
        }
      }

      // Step 5: Extract handwriting and signatures
      const handwritingResult = await this.ocrEngine.extractHandwriting(processedImageBuffer);

      if (
        handwritingResult.handwritingRegions.length > 0 ||
        handwritingResult.signatureRegions.length > 0
      ) {
        extractedData.push({
          id: `handwriting_${Date.now()}`,
          documentId: '',
          type: ExtractedDataType.TEXT,
          content: {
            handwritingRegions: handwritingResult.handwritingRegions,
            signatureRegions: handwritingResult.signatureRegions,
          },
          confidence: handwritingResult.confidence / 100,
          extractionMethod: ExtractionMethod.OCR,
          createdAt: new Date(),
        });
      }

      // Step 6: Detect advanced tables with cell structure
      const advancedTables = await this.ocrEngine.detectAdvancedTables(processedImageBuffer);

      if (advancedTables.tables.length > 0) {
        for (const table of advancedTables.tables) {
          extractedData.push({
            id: `advanced_table_${Date.now()}_${Math.random()}`,
            documentId: '',
            type: ExtractedDataType.TABLE,
            content: {
              structure: table,
              cellCount: table.cells.length,
              rowCount: table.rows,
              columnCount: table.columns,
            },
            confidence: table.confidence,
            extractionMethod: ExtractionMethod.AI_ANALYSIS,
            coordinates: table.bounds,
            createdAt: new Date(),
          });
        }
      }

      // Step 7: Create coordinate mapping if OCR was performed
      if (ocrResult) {
        const coordinateMapping = await this.ocrEngine.createCoordinateMapping(
          processedImageBuffer,
          ocrResult
        );

        extractedData.push({
          id: `coordinates_${Date.now()}`,
          documentId: '',
          type: ExtractedDataType.TEXT,
          content: { coordinateMapping },
          confidence: 1.0,
          extractionMethod: ExtractionMethod.AI_ANALYSIS,
          createdAt: new Date(),
        });
      }

      // Step 8: Store original image data
      const imageMetadata = await this.imageProcessor.getImageData(buffer);
      const documentImage: DocumentImage = {
        id: `image_${Date.now()}`,
        name: 'processed_image',
        type: imageMetadata.format,
        width: imageMetadata.width,
        height: imageMetadata.height,
        data: imageMetadata.data,
        pageNumber: 1,
        bounds: {
          x: 0,
          y: 0,
          width: imageMetadata.width,
          height: imageMetadata.height,
          pageNumber: 1,
        },
      };

      documentContent.images = [documentImage];

      // Update page dimensions
      if (documentContent.pages.length > 0 && documentContent.pages[0]) {
        documentContent.pages[0].width = imageMetadata.width;
        documentContent.pages[0].height = imageMetadata.height;
      }

      const processingTime = Date.now() - startTime;
      const confidence =
        extractedData.length > 0
          ? extractedData.reduce((sum, data) => sum + data.confidence, 0) / extractedData.length
          : 0;

      logger.info('Enhanced image processing completed', {
        processingTime,
        extractedDataCount: extractedData.length,
        confidence,
        textLength: documentContent.text.length,
        tablesFound: documentContent.tables?.length || 0,
        formFieldsFound: documentContent.formFields?.length || 0,
      });

      // Document processing completed successfully

      // Validate extraction (but don't store result since it's not used)
      await this.validateExtraction(extractedData);

      return {
        extractedData,
        documentContent,
        processingTime,
        confidence,
      };
    } catch (error) {
      logger.error('Enhanced image processing failed', { error });

      return {
        extractedData: [],
        documentContent: {
          text: '',
          pages: [],
          formFields: [],
          images: [],
          tables: [],
        },
        processingTime: Date.now() - startTime,
        confidence: 0,
      };
    }
  }

  /**
   * Validate extracted data
   */
  public async validateExtraction(extractedData: ExtractedData[]): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    for (const data of extractedData) {
      const result: ValidationResult = {
        fieldId: data.id,
        isValid: true,
        errors: [],
        warnings: [],
      };

      // Check confidence threshold
      if (data.confidence < 0.5) {
        result.warnings.push({
          code: 'LOW_CONFIDENCE',
          message: `Low confidence score: ${data.confidence}`,
          severity: 'warning' as const,
        });
      }

      // Check if content is empty
      if (!data.content || (typeof data.content === 'string' && data.content.trim() === '')) {
        result.isValid = false;
        result.errors.push({
          code: 'EMPTY_CONTENT',
          message: 'Extracted content is empty',
          severity: 'error',
        });
      }

      results.push(result);
    }

    return results;
  }

  /**
   * Cleanup resources
   */
  public async cleanup(): Promise<void> {
    await this.ocrEngine.cleanup();
    logger.info('Enhanced Image Processor cleaned up');
  }
}

const { FusesPlugin } = require('@electron-forge/plugin-fuses');
const { FuseV1Options, FuseVersion } = require('@electron/fuses');
const path = require('path');

module.exports = {
  packagerConfig: {
    asar: true,
    name: 'AI Document Processor',
    executableName: 'ai-document-processor',
    appBundleId: 'com.hepzceo.ai-document-processor',
    appCategoryType: 'public.app-category.productivity',
    icon: path.resolve(__dirname, 'assets/icons/icon'),
    ignore: [
      /^\/src\//,
      /^\/tests\//,
      /^\/docs\//,
      /^\/\.git/,
      /^\/\.vscode/,
      /^\/\.kiro/,
      /node_modules\/.*\.d\.ts$/,
      /node_modules\/.*\.map$/,
    ],
    extraResource: [
      {
        from: path.resolve(__dirname, 'assets'),
        to: 'assets',
      },
    ],
    protocols: [
      {
        name: 'AI Document Processor',
        schemes: ['ai-doc-processor'],
      },
    ],
  },

  rebuildConfig: {
    buildPath: path.resolve(__dirname, '.tmp'),
    onlyModules: ['better-sqlite3', 'sqlite3', 'sharp'],
  },

  makers: [
    {
      name: '@electron-forge/maker-squirrel',
      config: {
        name: 'ai-document-processor',
        authors: 'hepzceo',
        description: 'Enterprise-grade AI-powered document processing system',
        setupIcon: path.resolve(__dirname, 'assets/icons/icon.ico'),
        loadingGif: path.resolve(__dirname, 'assets/icons/loading.gif'),
        certificateFile: process.env.WINDOWS_CERTIFICATE_FILE,
        certificatePassword: process.env.WINDOWS_CERTIFICATE_PASSWORD,
      },
    },
    {
      name: '@electron-forge/maker-zip',
      platforms: ['darwin'],
      config: {
        name: 'AI Document Processor',
      },
    },
    {
      name: '@electron-forge/maker-deb',
      config: {
        name: 'ai-document-processor',
        productName: 'AI Document Processor',
        genericName: 'Document Processor',
        description:
          'Enterprise-grade AI-powered document processing and intelligent paperwork management system',
        categories: ['Office', 'Productivity'],
        maintainer: 'hepzceo <<EMAIL>>',
        homepage: 'https://github.com/hepzceo/ai-document-processor',
        icon: path.resolve(__dirname, 'assets/icons/icon.png'),
      },
    },
    {
      name: '@electron-forge/maker-rpm',
      config: {
        name: 'ai-document-processor',
        productName: 'AI Document Processor',
        genericName: 'Document Processor',
        description:
          'Enterprise-grade AI-powered document processing and intelligent paperwork management system',
        categories: ['Office', 'Productivity'],
        maintainer: 'hepzceo <<EMAIL>>',
        homepage: 'https://github.com/hepzceo/ai-document-processor',
        icon: path.resolve(__dirname, 'assets/icons/icon.png'),
      },
    },
  ],

  plugins: [
    {
      name: '@electron-forge/plugin-auto-unpack-natives',
      config: {
        unpackNativeModules: ['better-sqlite3', 'sqlite3', 'sharp', 'canvas'],
      },
    },
    // Fuses are used to enable/disable various Electron functionality
    // at package time, before code signing the application
    new FusesPlugin({
      version: FuseVersion.V1,
      [FuseV1Options.RunAsNode]: false,
      [FuseV1Options.EnableCookieEncryption]: true,
      [FuseV1Options.EnableNodeOptionsEnvironmentVariable]: false,
      [FuseV1Options.EnableNodeCliInspectArguments]: false,
      [FuseV1Options.EnableEmbeddedAsarIntegrityValidation]: true,
      [FuseV1Options.OnlyLoadAppFromAsar]: true,
      [FuseV1Options.LoadBrowserProcessSpecificV8Snapshot]: false,
    }),
  ],

  buildIdentifier: process.env.IS_BETA ? 'beta' : 'prod',

  publishers: [
    {
      name: '@electron-forge/publisher-github',
      config: {
        repository: {
          owner: 'hepzceo',
          name: 'ai-document-processor',
        },
        prerelease: process.env.IS_BETA === 'true',
        draft: true,
      },
    },
  ],

  hooks: {
    packageAfterCopy: async (_config, buildPath) => {
      console.log('Packaging completed for:', buildPath);
    },
    packageAfterPrune: async (_config, buildPath) => {
      console.log('Dependencies pruned for:', buildPath);
    },
  },
};

import React from 'react';
import { AppRouterProvider } from './router/RouterProvider';

// Type assertion for electronAPI
declare global {
  interface Window {
    electronAPI: {
      logToMain: (level: string, message: string, data?: any, stack?: string) => Promise<void>;
      // Add other methods as needed
    };
  }
}

const App: React.FC = () => {
  // Initialize application
  React.useEffect(() => {
    console.log('App component mounted successfully!');

    // Test the electronAPI
    if (window.electronAPI) {
      console.log('electronAPI is available');
      window.electronAPI.logToMain('info', 'App component initialized with React Router', {
        component: 'App',
        timestamp: new Date().toISOString(),
        features: ['React Router', 'Error Boundaries', 'Code Splitting'],
      });
    } else {
      console.error('electronAPI is not available');
    }

    // Set initial document title
    document.title = 'AI Document Processor';
  }, []);

  return <AppRouterProvider />;
};

export default App;

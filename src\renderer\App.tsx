import React from 'react';

// Type assertion for electronAPI
declare global {
  interface Window {
    electronAPI: {
      logToMain: (level: string, message: string, data?: any, stack?: string) => Promise<void>;
      // Add other methods as needed
    };
  }
}

const App: React.FC = () => {
  // Test console logs
  React.useEffect(() => {
    console.log('App component mounted successfully!');
    console.info('This is an info message from <PERSON><PERSON>');
    console.warn('This is a warning message from <PERSON><PERSON>');

    // Test the electronAPI
    if (window.electronAPI) {
      console.log('electronAPI is available');
      window.electronAPI.logToMain('info', 'Direct log from App component', {
        component: 'App',
        timestamp: new Date().toISOString(),
      });
    } else {
      console.error('electronAPI is not available');
    }

    // Add a visible indicator that <PERSON>act is working
    document.title = 'AI Document Processor - React Loaded';

    // Test error logging after a delay to ensure console override is active
    setTimeout(() => {
      console.error('Test error message from <PERSON><PERSON>');
      console.log('Testing console.log after override injection');

      // Test unhandled promise rejection
      setTimeout(() => {
        Promise.reject(new Error('Test unhandled promise rejection'));
      }, 1000);

      // Test JavaScript error
      setTimeout(() => {
        try {
          throw new Error('Test JavaScript error');
        } catch (error) {
          console.error('Caught error:', error);
        }
      }, 2000);
    }, 3000);
  }, []);

  return (
    <div className='min-h-screen bg-base-100 text-base-content'>
      {/* Title Bar */}
      <div className='h-8 bg-base-200 flex items-center justify-between px-4 drag-region'>
        <div className='flex items-center space-x-2'>
          <div className='w-3 h-3 rounded-full bg-error'></div>
          <div className='w-3 h-3 rounded-full bg-warning'></div>
          <div className='w-3 h-3 rounded-full bg-success'></div>
        </div>
        <div className='text-sm font-medium'>AI Document Processor</div>
        <div className='w-16'></div>
      </div>

      {/* Main Application */}
      <div className='flex h-[calc(100vh-2rem)]'>
        {/* Sidebar */}
        <div className='w-64 bg-base-200 border-r border-base-300'>
          <div className='p-4'>
            <h2 className='text-lg font-semibold mb-4'>Explorer</h2>
            <div className='space-y-2'>
              <div className='text-sm text-base-content/70'>No project loaded</div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className='flex-1 flex flex-col'>
          {/* Tab Bar */}
          <div className='h-10 bg-base-200 border-b border-base-300 flex items-center px-4'>
            <div className='text-sm text-base-content/70'>Welcome to AI Document Processor</div>
          </div>

          {/* Content Area */}
          <div className='flex-1 flex items-center justify-center bg-base-100'>
            <div className='text-center max-w-md'>
              <div className='mb-8'>
                <div className='w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4'>
                  <svg
                    className='w-8 h-8 text-primary'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
                    />
                  </svg>
                </div>
                <h1 className='text-2xl font-bold text-base-content mb-2'>AI Document Processor</h1>
                <p className='text-base-content/70 mb-6'>
                  Enterprise-grade AI-powered document processing and intelligent paperwork
                  management system
                </p>
              </div>

              <div className='space-y-3'>
                <button
                  className='btn btn-primary btn-block'
                  onClick={() => {
                    console.log('Open Document clicked');
                    window.electronAPI.logToMain('info', 'User clicked Open Document button', {
                      component: 'App',
                      action: 'openDocument',
                    });
                  }}
                >
                  Open Document
                </button>
                <button
                  className='btn btn-outline btn-block'
                  onClick={() => {
                    console.log('Create New Project clicked');
                    window.electronAPI.logToMain('info', 'User clicked Create New Project button', {
                      component: 'App',
                      action: 'createProject',
                    });
                  }}
                >
                  Create New Project
                </button>
              </div>

              <div className='mt-8 text-xs text-base-content/50'>
                Version 1.0.0 • Built with Electron & React
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Status Bar */}
      <div className='h-6 bg-base-200 border-t border-base-300 flex items-center justify-between px-4 text-xs text-base-content/70'>
        <div>Ready</div>
        <div>AI Document Processor</div>
      </div>
    </div>
  );
};

export default App;

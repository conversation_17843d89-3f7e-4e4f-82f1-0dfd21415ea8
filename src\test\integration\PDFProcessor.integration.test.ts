import { describe, it, expect, beforeEach } from '@jest/globals';
import { DocumentProcessorFactory } from '../../main/services/DocumentProcessorFactory';
import { PDFProcessor } from '../../main/services/PDFProcessor';
import { DocumentType } from '../../shared/types/Document';

describe('PDFProcessor Integration Tests', () => {
  let factory: DocumentProcessorFactory;

  beforeEach(() => {
    factory = new DocumentProcessorFactory();
  });

  describe('Factory Integration', () => {
    it('should register PDFProcessor in factory', () => {
      const processors = factory.getAvailableProcessors();
      const pdfProcessor = processors.find(p => p.name === 'pdf-processor');
      
      expect(pdfProcessor).toBeDefined();
      expect(pdfProcessor?.capabilities.supportedTypes).toContain(DocumentType.PDF);
    });

    it('should create PDFProcessor instance from factory', () => {
      const processor = factory.createProcessor('pdf-processor');
      
      expect(processor).toBeInstanceOf(PDFProcessor);
    });

    it('should get processor for PDF document type', () => {
      const processor = factory.getProcessorForType(DocumentType.PDF);
      
      expect(processor).toBeInstanceOf(PDFProcessor);
    });

    it('should have correct capabilities registered', () => {
      const processors = factory.getAvailableProcessors();
      const pdfProcessor = processors.find(p => p.name === 'pdf-processor');
      
      expect(pdfProcessor?.capabilities).toEqual({
        name: 'PDFProcessor',
        version: '1.0.0',
        supportedTypes: [DocumentType.PDF],
        canExtractText: true,
        canExtractImages: true,
        canExtractTables: true,
        canDetectFormFields: true,
        canGenerateDocument: true,
        canModifyDocument: true,
        canExtractMetadata: true,
        canPerformOCR: false,
        canPreserveFormatting: true,
        maxFileSize: 100 * 1024 * 1024, // 100MB
        supportedEncodings: ['utf-8', 'latin1'],
        requiresNetwork: false,
        processingTimeEstimate: 2, // seconds per MB
      });
    });

    it('should have correct metadata', () => {
      const processors = factory.getAvailableProcessors();
      const pdfProcessor = processors.find(p => p.name === 'pdf-processor');
      
      expect(pdfProcessor?.metadata).toEqual({
        name: 'PDF Processor',
        version: '1.0.0',
        description: 'Advanced PDF processing with text extraction, form handling, and generation capabilities',
        author: 'AI Document Processor Team',
        supportedFormats: ['pdf'],
        performanceRating: 9,
        accuracyRating: 9,
        resourceUsage: {
          memoryUsage: 'medium',
          cpuUsage: 'medium',
          diskUsage: 'low',
          networkRequired: false,
          estimatedProcessingTime: 2, // seconds per MB
        },
        dependencies: ['pdfjs-dist', 'pdfkit', 'pdf-lib'],
        lastUpdated: expect.any(Date),
      });
    });
  });

  describe('Processor Selection', () => {
    it('should select PDFProcessor for .pdf files', () => {
      const processor = factory.getProcessorForFile('document.pdf');
      expect(processor).toBeInstanceOf(PDFProcessor);
    });

    it('should select PDFProcessor for PDF MIME type', () => {
      const processor = factory.getProcessorForMimeType('application/pdf');
      expect(processor).toBeInstanceOf(PDFProcessor);
    });

    it('should not select PDFProcessor for non-PDF files', () => {
      const processor = factory.getProcessorForFile('document.docx');
      expect(processor).not.toBeInstanceOf(PDFProcessor);
    });
  });

  describe('Performance and Capabilities', () => {
    it('should report correct performance characteristics', () => {
      const processors = factory.getAvailableProcessors();
      const pdfProcessor = processors.find(p => p.name === 'pdf-processor');
      
      expect(pdfProcessor?.metadata.performanceRating).toBeGreaterThanOrEqual(8);
      expect(pdfProcessor?.metadata.accuracyRating).toBeGreaterThanOrEqual(8);
      expect(pdfProcessor?.capabilities.maxFileSize).toBeGreaterThan(50 * 1024 * 1024); // At least 50MB
    });

    it('should support all required PDF operations', () => {
      const processors = factory.getAvailableProcessors();
      const pdfProcessor = processors.find(p => p.name === 'pdf-processor');
      
      expect(pdfProcessor?.capabilities.canExtractText).toBe(true);
      expect(pdfProcessor?.capabilities.canExtractImages).toBe(true);
      expect(pdfProcessor?.capabilities.canDetectFormFields).toBe(true);
      expect(pdfProcessor?.capabilities.canGenerateDocument).toBe(true);
      expect(pdfProcessor?.capabilities.canModifyDocument).toBe(true);
      expect(pdfProcessor?.capabilities.canExtractMetadata).toBe(true);
      expect(pdfProcessor?.capabilities.canPreserveFormatting).toBe(true);
    });

    it('should have reasonable processing time estimates', () => {
      const processors = factory.getAvailableProcessors();
      const pdfProcessor = processors.find(p => p.name === 'pdf-processor');
      
      expect(pdfProcessor?.capabilities.processingTimeEstimate).toBeLessThanOrEqual(5); // Max 5 seconds per MB
      expect(pdfProcessor?.metadata.resourceUsage.estimatedProcessingTime).toBeLessThanOrEqual(5);
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle processor creation errors gracefully', () => {
      expect(() => {
        factory.createProcessor('non-existent-processor');
      }).toThrow('Processor not found: non-existent-processor');
    });

    it('should handle unsupported document types', () => {
      const processor = factory.getProcessorForType('UNSUPPORTED' as DocumentType);
      expect(processor).toBeNull();
    });

    it('should validate processor capabilities', () => {
      const processors = factory.getAvailableProcessors();
      const pdfProcessor = processors.find(p => p.name === 'pdf-processor');
      
      // Ensure all required fields are present
      expect(pdfProcessor?.capabilities.supportedTypes).toBeDefined();
      expect(pdfProcessor?.capabilities.supportedTypes.length).toBeGreaterThan(0);
      expect(pdfProcessor?.metadata.dependencies).toBeDefined();
      expect(pdfProcessor?.metadata.dependencies.length).toBeGreaterThan(0);
    });
  });

  describe('Processor Lifecycle', () => {
    it('should create multiple processor instances', () => {
      const processor1 = factory.createProcessor('pdf-processor');
      const processor2 = factory.createProcessor('pdf-processor');
      
      expect(processor1).toBeInstanceOf(PDFProcessor);
      expect(processor2).toBeInstanceOf(PDFProcessor);
      expect(processor1).not.toBe(processor2); // Different instances
    });

    it('should maintain processor registry integrity', () => {
      const initialCount = factory.getAvailableProcessors().length;
      
      // Creating processors shouldn't affect registry
      factory.createProcessor('pdf-processor');
      factory.createProcessor('pdf-processor');
      
      const finalCount = factory.getAvailableProcessors().length;
      expect(finalCount).toBe(initialCount);
    });
  });

  describe('Configuration Validation', () => {
    it('should have valid processor configuration', () => {
      const processors = factory.getAvailableProcessors();
      const pdfProcessor = processors.find(p => p.name === 'pdf-processor');
      
      // Validate configuration structure
      expect(pdfProcessor?.isEnabled).toBe(true);
      expect(pdfProcessor?.priority).toBeGreaterThan(0);
      expect(pdfProcessor?.capabilities.name).toBe('PDFProcessor');
      expect(pdfProcessor?.capabilities.version).toMatch(/^\d+\.\d+\.\d+$/);
    });

    it('should have consistent metadata and capabilities', () => {
      const processors = factory.getAvailableProcessors();
      const pdfProcessor = processors.find(p => p.name === 'pdf-processor');
      
      // Ensure metadata and capabilities are consistent
      expect(pdfProcessor?.metadata.name).toBe('PDF Processor');
      expect(pdfProcessor?.capabilities.name).toBe('PDFProcessor');
      expect(pdfProcessor?.metadata.version).toBe(pdfProcessor?.capabilities.version);
    });
  });
});

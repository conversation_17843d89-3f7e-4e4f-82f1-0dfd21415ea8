// Node.js environment setup for Jest tests
import { setImmediate, clearImmediate } from 'timers';

// Make Node.js globals available in test environment
global.setImmediate = setImmediate;
global.clearImmediate = clearImmediate;

// Mock Electron APIs that might be used in tests
global.require = require;

// Set up any other Node.js specific globals or polyfills needed for testing
process.env.NODE_ENV = 'test';

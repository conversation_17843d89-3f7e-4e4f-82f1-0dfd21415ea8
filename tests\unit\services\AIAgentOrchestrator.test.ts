import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import {
  AIAgentOrchestrator,
  AgentTask,
  aiAgentOrchestrator,
} from '../../../src/main/services/AIAgentOrchestrator';
import { AgentType, AICapability } from '../../../src/shared/types/AI';

// Mock dependencies
jest.mock('../../../src/main/utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

jest.mock('../../../src/main/services/AIModelClient', () => ({
  aiModelClient: {
    performReasoning: jest.fn().mockResolvedValue({
      conclusion: 'Test AI response',
      confidence: 0.85,
      reasoning: 'Test reasoning',
      sources: [],
      processingTime: 1000,
      model: 'gpt-4',
      metadata: {
        totalSteps: 1,
        averageConfidence: 0.85,
        tokensUsed: 100,
        cost: 0.01,
        timestamp: new Date(),
      },
    }),
    startTask: jest.fn().mockReturnValue('task_123'),
    endTask: jest.fn(),
    getSessionMetrics: jest.fn().mockReturnValue({
      totalTokens: 100,
      totalCost: 0.01,
      startTime: new Date(),
    }),
  },
}));

jest.mock('../../../src/main/services/AITools', () => ({
  aiToolsManager: {
    discoverTools: jest.fn().mockReturnValue([
      {
        id: 'calculator',
        name: 'Calculator Tool',
        description: 'Performs calculations',
        category: 'calculation',
        capabilities: ['basic_math'],
      },
      {
        id: 'document_analyzer',
        name: 'Document Analysis Tool',
        description: 'Analyzes documents',
        category: 'analysis',
        capabilities: ['document_analysis'],
      },
    ]),
  },
  toolExecutor: {
    execute: jest.fn().mockResolvedValue({
      result: { value: 42 },
      success: true,
      metadata: { timestamp: new Date() },
    }),
  },
}));

describe('AIAgentOrchestrator', () => {
  let orchestrator: AIAgentOrchestrator;

  beforeEach(() => {
    orchestrator = new AIAgentOrchestrator();
    jest.clearAllMocks();
  });

  describe('Agent Management', () => {
    it('should initialize with default agents', () => {
      const agents = orchestrator.getAllAgents();

      expect(agents.length).toBeGreaterThan(0);
      expect(agents.map(a => a.id)).toContain('document_processor');
      expect(agents.map(a => a.id)).toContain('form_filler');
      expect(agents.map(a => a.id)).toContain('knowledge_manager');
      expect(agents.map(a => a.id)).toContain('analyst');
    });

    it('should register new agents', () => {
      const customAgent = {
        id: 'custom_agent',
        name: 'Custom Agent',
        description: 'A custom test agent',
        type: AgentType.ASSISTANT,
        capabilities: [AICapability.TEXT_GENERATION],
        tools: [],
        configuration: {
          model: 'gpt-3.5-turbo',
          temperature: 0.7,
          maxTokens: 1000,
          systemPrompt: 'You are a helpful assistant',
          tools: [],
          memorySize: 5,
          timeout: 15000,
        },
        isActive: true,
      };

      orchestrator.registerAgent(customAgent);

      const retrievedAgent = orchestrator.getAgent('custom_agent');
      expect(retrievedAgent).toEqual(customAgent);
    });

    it('should get agents by capability', () => {
      const documentAgents = orchestrator.getAgentsByCapability(
        AICapability.DOCUMENT_UNDERSTANDING
      );

      expect(documentAgents.length).toBeGreaterThan(0);
      expect(documentAgents[0].capabilities).toContain(AICapability.DOCUMENT_UNDERSTANDING);
    });
  });

  describe('Agent Selection', () => {
    it('should select appropriate agent for task', () => {
      const task: AgentTask = {
        id: 'test_task',
        type: AgentType.DOCUMENT_PROCESSOR,
        description: 'Analyze a document',
        input: { content: 'Test document content' },
        priority: 'normal',
        requiredCapabilities: ['document_understanding'],
      };

      const selectedAgent = orchestrator.selectAgentForTask(task);

      expect(selectedAgent).toBeTruthy();
      expect(selectedAgent?.type).toBe(AgentType.DOCUMENT_PROCESSOR);
    });

    it('should return null when no suitable agent found', () => {
      const task: AgentTask = {
        id: 'impossible_task',
        type: 'nonexistent_type' as AgentType,
        description: 'Impossible task',
        input: {},
        priority: 'normal',
        requiredCapabilities: ['nonexistent_capability'],
      };

      const selectedAgent = orchestrator.selectAgentForTask(task);
      expect(selectedAgent).toBeNull();
    });

    it('should prefer agents with exact type match', () => {
      const formTask: AgentTask = {
        id: 'form_task',
        type: AgentType.FORM_FILLER,
        description: 'Fill out a form',
        input: { formFields: [] },
        priority: 'normal',
      };

      const selectedAgent = orchestrator.selectAgentForTask(formTask);

      expect(selectedAgent).toBeTruthy();
      expect(selectedAgent?.id).toBe('form_filler');
    });
  });

  describe('Task Execution', () => {
    it('should execute task successfully', async () => {
      const task: AgentTask = {
        id: 'test_execution',
        type: AgentType.ANALYST,
        description: 'Perform a calculation',
        input: { expression: '2 + 2' },
        priority: 'normal',
        timeout: 30000,
      };

      const result = await orchestrator.executeTask(task);

      expect(result.success).toBe(true);
      expect(result.result).toBeTruthy();
      expect(result.totalExecutionTime).toBeGreaterThanOrEqual(0);
      expect(result.metadata).toHaveProperty('agentId');
    });

    it('should handle task execution failure', async () => {
      const task: AgentTask = {
        id: 'failing_task',
        type: 'nonexistent_type' as AgentType,
        description: 'This should fail',
        input: {},
        priority: 'normal',
        requiredCapabilities: ['nonexistent_capability'],
      };

      const result = await orchestrator.executeTask(task);

      expect(result.success).toBe(false);
      expect(result.result).toContain('No suitable agent found');
    });

    it('should record execution history', async () => {
      const task: AgentTask = {
        id: 'history_test',
        type: AgentType.KNOWLEDGE_MANAGER,
        description: 'Test history recording',
        input: { query: 'test query' },
        priority: 'normal',
      };

      await orchestrator.executeTask(task);

      const history = orchestrator.getExecutionHistory();
      expect(history.length).toBeGreaterThan(0);

      const lastExecution = history[history.length - 1];
      expect(lastExecution.task.id).toBe('history_test');
      expect(lastExecution.agentId).toBeTruthy();
    });
  });

  describe('Performance Metrics', () => {
    it('should calculate agent metrics', async () => {
      const task: AgentTask = {
        id: 'metrics_test',
        type: AgentType.ANALYST,
        description: 'Test metrics calculation',
        input: {},
        priority: 'normal',
      };

      await orchestrator.executeTask(task);

      const metrics = orchestrator.getAgentMetrics('analyst');

      expect(metrics.totalExecutions).toBeGreaterThan(0);
      expect(metrics.successRate).toBeGreaterThanOrEqual(0);
      expect(metrics.successRate).toBeLessThanOrEqual(1);
      expect(metrics.averageExecutionTime).toBeGreaterThanOrEqual(0);
    });

    it('should return zero metrics for unknown agent', () => {
      const metrics = orchestrator.getAgentMetrics('nonexistent_agent');

      expect(metrics.totalExecutions).toBe(0);
      expect(metrics.successRate).toBe(0);
      expect(metrics.averageExecutionTime).toBe(0);
      expect(metrics.averageTokensUsed).toBe(0);
      expect(metrics.totalCost).toBe(0);
    });
  });

  describe('History Management', () => {
    it('should clear execution history', async () => {
      const task: AgentTask = {
        id: 'clear_test',
        type: AgentType.ASSISTANT,
        description: 'Test history clearing',
        input: {},
        priority: 'normal',
      };

      await orchestrator.executeTask(task);
      expect(orchestrator.getExecutionHistory().length).toBeGreaterThan(0);

      orchestrator.clearHistory();
      expect(orchestrator.getExecutionHistory().length).toBe(0);
    });
  });

  describe('Integration Tests', () => {
    it('should work with different task priorities', async () => {
      const highPriorityTask: AgentTask = {
        id: 'high_priority',
        type: AgentType.DOCUMENT_PROCESSOR,
        description: 'High priority document analysis',
        input: { content: 'Urgent document' },
        priority: 'urgent',
      };

      const result = await orchestrator.executeTask(highPriorityTask);
      expect(result.success).toBe(true);
    });

    it('should handle tasks with context', async () => {
      const taskWithContext: AgentTask = {
        id: 'context_test',
        type: AgentType.FORM_FILLER,
        description: 'Fill form with context',
        input: { formFields: [] },
        priority: 'normal',
        context: {
          sessionId: 'test_session',
          userId: 'test_user',
          documentIds: ['doc1', 'doc2'],
          knowledgeBaseId: 'kb1',
          preferences: {
            preferredModel: 'gpt-4',
            temperature: 0.3,
            maxTokens: 1000,
            language: 'en',
            responseStyle: 'detailed' as const,
          },
          history: [],
        },
      };

      const result = await orchestrator.executeTask(taskWithContext);
      expect(result.success).toBe(true);
    });
  });

  describe('Singleton Instance', () => {
    it('should provide singleton access', () => {
      expect(aiAgentOrchestrator).toBeInstanceOf(AIAgentOrchestrator);

      const agents = aiAgentOrchestrator.getAllAgents();
      expect(agents.length).toBeGreaterThan(0);
    });

    it('should maintain state across accesses', () => {
      const agentCount1 = aiAgentOrchestrator.getAllAgents().length;
      const agentCount2 = aiAgentOrchestrator.getAllAgents().length;

      expect(agentCount1).toBe(agentCount2);
    });
  });
});

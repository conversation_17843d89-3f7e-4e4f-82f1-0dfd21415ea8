# Development Automation Guide

This document provides comprehensive information about the development
automation setup for the AI Document Processor project.

## Overview

The project includes a complete development automation suite designed to:

- Ensure consistent code quality across all contributors
- Automate repetitive development tasks
- Provide comprehensive tooling for debugging and testing
- Streamline the onboarding process for new developers

## Automated Setup

### Quick Setup for New Developers

```bash
# Clone the repository
git clone https://github.com/hepzceo/ai-document-processor.git
cd ai-document-processor

# Run automated setup
npm run setup
```

The setup script will:

1. ✅ Check Node.js and npm versions
2. ✅ Install all dependencies
3. ✅ Create .env file from template
4. ✅ Set up and migrate database
5. ✅ Install Git hooks
6. ✅ Build CSS and run type checking
7. ✅ Run initial tests
8. ✅ Provide next steps guidance

### Manual Setup Steps

If you prefer manual setup or need to troubleshoot:

```bash
# Install dependencies
npm ci

# Set up environment
cp .env.example .env
# Edit .env with your configuration

# Set up database
npm run db:migrate

# Install Git hooks
npm run prepare

# Build project
npm run build:css
npm run type-check

# Validate setup
npm run validate-setup
```

## Git Hooks (<PERSON><PERSON>)

The project uses <PERSON><PERSON> to manage Git hooks that automatically enforce code
quality.

### Pre-commit Hook

Runs automatically on every `git commit`:

```bash
# What runs on pre-commit:
npx lint-staged          # Lint and format staged files
npm run type-check       # TypeScript type checking
npm run test:unit -- --passWithNoTests --findRelatedTests --bail
```

**Files affected by lint-staged:**

- `*.{ts,tsx,js,jsx}`: ESLint with auto-fix + Prettier formatting
- `*.{ts,tsx}`: TypeScript type checking
- `*.{json,css,md}`: Prettier formatting
- `package.json`: JSON validation + type checking
- Test files: Run related unit tests

### Pre-push Hook

Runs automatically on every `git push`:

```bash
# What runs on pre-push:
npm run test             # Full test suite
npm run test:security    # Security vulnerability tests
npm run test:performance # Performance benchmarks
npm run lint:check       # Linting without auto-fix
npm run format:check     # Format checking without auto-fix
```

### Bypassing Hooks

**⚠️ Not recommended for regular development**

```bash
# Skip pre-commit hooks (emergency only)
git commit --no-verify -m "Emergency commit"

# Skip pre-push hooks (emergency only)
git push --no-verify
```

## Code Quality Automation

### Lint-staged Configuration

The project uses a comprehensive lint-staged setup (`.lintstagedrc.js`):

```javascript
{
  // TypeScript/JavaScript: Lint + Format
  '*.{ts,tsx,js,jsx}': [
    'eslint --fix --max-warnings=0',
    'prettier --write'
  ],

  // TypeScript: Type checking
  '*.{ts,tsx}': [
    () => 'npm run type-check'
  ],

  // Other files: Format only
  '*.{json,css,md}': [
    'prettier --write'
  ],

  // Package.json: Validate + type check
  'package.json': [
    'npm run type-check',
    () => 'node -e "JSON.parse(require(\'fs\').readFileSync(\'package.json\', \'utf8\'))"'
  ],

  // Test files: Run related tests
  '*.{test,spec}.{ts,tsx,js,jsx}': [
    () => 'npm run test:unit -- --passWithNoTests --findRelatedTests --bail'
  ]
}
```

### Manual Quality Checks

```bash
# Run all quality checks
npm run validate

# Individual checks
npm run type-check       # TypeScript type checking
npm run lint            # ESLint with auto-fix
npm run lint:check      # ESLint check only
npm run format          # Prettier with auto-fix
npm run format:check    # Prettier check only
```

## VS Code Integration

### Workspace Configuration

The project includes a complete VS Code workspace setup:

```bash
# Open the workspace for optimal experience
code .vscode/ai-document-processor.code-workspace
```

**Included configurations:**

- **Settings**: Optimized editor settings for the project
- **Extensions**: Recommended extensions with auto-install prompts
- **Tasks**: Pre-configured build, test, and development tasks
- **Launch**: Debug configurations for Electron main/renderer processes
- **Workspace**: Multi-folder workspace with proper exclusions

### Recommended Extensions

The workspace automatically suggests these extensions:

**Essential:**

- `esbenp.prettier-vscode` - Code formatting
- `dbaeumer.vscode-eslint` - Linting
- `ms-vscode.vscode-typescript-next` - TypeScript support
- `bradlc.vscode-tailwindcss` - Tailwind CSS IntelliSense

**Development:**

- `kodetech.electron-snippets` - Electron code snippets
- `orta.vscode-jest` - Jest test runner
- `ms-playwright.playwright` - Playwright test runner
- `eamodio.gitlens` - Git integration

**Quality:**

- `sonarsource.sonarlint-vscode` - Code quality analysis
- `streetsidesoftware.code-spell-checker` - Spell checking
- `usernamehw.errorlens` - Inline error display

### VS Code Tasks

Pre-configured tasks available via `Ctrl+Shift+P` → "Tasks: Run Task":

- **Build All** - Complete production build
- **Start Development** - Start dev server with hot reload
- **Run Tests** - Execute test suite
- **Lint and Fix** - Run ESLint with auto-fix
- **Type Check** - Run TypeScript compiler
- **Database Migrate** - Run database migrations

### Debug Configurations

Available debug configurations:

1. **Debug Electron Main Process** - Debug the Node.js main process
2. **Debug Electron Renderer Process** - Debug the React renderer
3. **Debug Jest Tests** - Debug unit tests
4. **Debug Playwright Tests** - Debug E2E tests
5. **Debug Full Application** - Debug both main and renderer processes

## Development Scripts

### Core Development

```bash
npm run dev              # Start development with hot reload
npm run start            # Start Electron (production build)
npm run build            # Production build
npm run package          # Package for current platform
npm run make             # Create distributable packages
```

### Code Quality

```bash
npm run validate         # Run all quality checks
npm run lint             # ESLint with auto-fix
npm run format           # Prettier formatting
npm run type-check       # TypeScript validation
```

### Testing

```bash
npm run test             # Run all tests
npm run test:unit        # Unit tests only
npm run test:integration # Integration tests only
npm run test:e2e         # End-to-end tests
npm run test:watch       # Watch mode for development
npm run test:coverage    # Generate coverage reports
```

### Database Management

```bash
npm run db:migrate       # Run migrations
npm run db:rollback      # Rollback last migration
npm run db:seed          # Seed with test data
npm run db:reset         # Complete database reset
```

### Maintenance

```bash
npm run clean            # Clean build artifacts
npm run clean:deps       # Clean and reinstall dependencies
npm run clean:all        # Complete cleanup
npm run analyze          # Bundle analysis
```

### CI/CD

```bash
npm run ci               # Full CI pipeline
npm run validate-setup   # Validate development environment
npm run security:audit   # Security audit
npm run deps:check       # Check for outdated dependencies
```

## Environment Configuration

### Environment Variables

The project uses a comprehensive `.env.example` template with:

- **AI Service Configuration**: Azure AI, OpenAI API keys
- **Database Settings**: SQLite path, encryption keys
- **Performance Tuning**: Memory limits, concurrency settings
- **Feature Flags**: Enable/disable specific features
- **Development Settings**: Debug modes, logging levels

### Configuration Files

Key configuration files and their purposes:

- `.env.example` - Environment variables template
- `tsconfig.json` - TypeScript compiler configuration
- `.eslintrc.js` - ESLint rules and settings
- `.prettierrc` - Code formatting rules
- `jest.config.js` - Test runner configuration
- `tailwind.config.js` - CSS framework configuration
- `knexfile.js` - Database migration configuration

## Troubleshooting

### Common Issues

**1. Git hooks not running**

```bash
# Reinstall hooks
npm run prepare
# Check hook files exist
ls -la .husky/
```

**2. Type checking fails**

```bash
# Check TypeScript configuration
npm run type-check
# Update TypeScript if needed
npm update typescript
```

**3. Linting errors**

```bash
# Auto-fix most issues
npm run lint
# Check remaining issues
npm run lint:check
```

**4. Database issues**

```bash
# Reset database completely
npm run db:reset
# Check database file exists
ls -la data/
```

**5. VS Code extensions not working**

```bash
# Open workspace file
code .vscode/ai-document-processor.code-workspace
# Install recommended extensions when prompted
```

### Getting Help

1. **Validate your setup**: `npm run validate-setup`
2. **Check the logs**: Look in `logs/` directory for error details
3. **Clean and rebuild**: `npm run clean:all && npm install && npm run setup`
4. **Check documentation**: Review `docs/` directory for specific topics

## Best Practices

### Development Workflow

1. **Start with setup validation**: `npm run validate-setup`
2. **Create feature branch**: `git checkout -b feature/my-feature`
3. **Develop with hot reload**: `npm run dev`
4. **Test frequently**: `npm run test:watch`
5. **Commit often**: Git hooks ensure quality automatically
6. **Push when ready**: Pre-push hooks run full validation

### Code Quality

- **Let automation handle formatting**: Don't manually format code
- **Fix linting issues promptly**: Don't accumulate warnings
- **Write tests for new features**: Maintain high coverage
- **Use TypeScript strictly**: Avoid `any` types when possible
- **Follow conventional commits**: Use clear, descriptive commit messages

### Performance

- **Use the clean scripts**: Regularly clean build artifacts
- **Monitor bundle size**: Use `npm run analyze` to check bundle size
- **Profile performance**: Use performance tests to catch regressions
- **Optimize dependencies**: Regularly update and audit dependencies

This automation setup ensures consistent, high-quality development across the
entire team while minimizing manual overhead and maximizing developer
productivity.

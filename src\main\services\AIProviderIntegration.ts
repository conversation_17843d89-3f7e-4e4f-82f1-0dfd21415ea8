import { logger } from '../utils/logger';
import { aiProviderConfig, ProviderSettings } from './AIProviderConfig';
import { multiProviderAIClient } from './MultiProviderAIClient';
import { ApiProvider } from '../../shared/ai-config/api';

/**
 * Integration service that bridges the existing ai-config system
 * with our new multi-provider document processing system
 */
export class AIProviderIntegration {
  private initialized = false;

  constructor() {
    // Removed telemetry and tracking code
  }

  /**
   * Initialize the integration system
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // Initialize provider configuration
      await aiProviderConfig.initialize();

      // Set up default providers if none exist
      await this.setupDefaultProviders();

      this.initialized = true;
      logger.info('AI Provider Integration initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize AI Provider Integration', error);
      throw error;
    }
  }

  /**
   * Set up default providers if none are configured
   */
  private async setupDefaultProviders(): Promise<void> {
    const existingProviders = aiProviderConfig.getProviders();

    if (existingProviders.length === 0) {
      logger.info('No providers configured, setting up defaults');

      // Add default OpenAI provider
      await this.addDefaultProvider('openai', {
        name: 'OpenAI',
        type: 'openai',
        models: {
          planMode: 'gpt-4',
          actMode: 'gpt-4',
        },
        priority: 100,
      });

      // Add default Anthropic provider
      await this.addDefaultProvider('anthropic', {
        name: 'Anthropic Claude',
        type: 'anthropic',
        models: {
          planMode: 'claude-3-5-sonnet-20241022',
          actMode: 'claude-3-5-sonnet-20241022',
        },
        priority: 90,
      });

      // Add default OpenRouter provider
      await this.addDefaultProvider('openrouter', {
        name: 'OpenRouter',
        type: 'openrouter',
        models: {
          planMode: 'anthropic/claude-3.5-sonnet',
          actMode: 'anthropic/claude-3.5-sonnet',
        },
        priority: 80,
      });

      // Add default Groq provider
      await this.addDefaultProvider('groq', {
        name: 'Groq',
        type: 'groq',
        models: {
          planMode: 'llama-3.1-70b-versatile',
          actMode: 'llama-3.1-70b-versatile',
        },
        priority: 70,
      });

      // Add default Ollama provider (local)
      await this.addDefaultProvider('ollama', {
        name: 'Ollama (Local)',
        type: 'ollama',
        models: {
          planMode: 'llama3.1',
          actMode: 'llama3.1',
        },
        priority: 60,
        enabled: false, // Disabled by default since it requires local setup
      });

      // Add default LM Studio provider (local)
      await this.addDefaultProvider('lmstudio', {
        name: 'LM Studio (Local)',
        type: 'lmstudio',
        models: {
          planMode: 'local-model',
          actMode: 'local-model',
        },
        priority: 50,
        enabled: false, // Disabled by default since it requires local setup
      });
    }
  }

  /**
   * Add a default provider configuration
   */
  private async addDefaultProvider(
    id: string,
    config: {
      name: string;
      type: ApiProvider;
      models: { planMode: string; actMode: string };
      priority: number;
      enabled?: boolean;
    }
  ): Promise<void> {
    const providerSettings: Omit<ProviderSettings, 'createdAt' | 'updatedAt'> = {
      id,
      name: config.name,
      type: config.type,
      enabled: config.enabled !== false,
      priority: config.priority,
      credentials: {},
      models: config.models,
      settings: {
        temperature: 0.7,
        maxTokens: 4000,
        timeout: 30000,
      },
    };

    try {
      await aiProviderConfig.addProvider(providerSettings);
      logger.info(`Added default provider: ${id}`);
    } catch (error) {
      logger.warn(`Failed to add default provider ${id}`, error);
    }
  }

  /**
   * Start a new task for token tracking
   */
  startTask(taskName: string): string {
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    logger.info(`Started task: ${taskName} (${taskId})`);
    return taskId;
  }

  /**
   * End the current task
   */
  endTask(): void {
    logger.info('Task ended');
  }

  /**
   * Create a completion with automatic token tracking
   */
  async createCompletion(
    systemPrompt: string,
    messages: any[],
    operation: string = 'completion'
  ): Promise<any> {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    const request = {
      id: requestId,
      operation,
      systemPrompt,
      messages,
      temperature: 0.7,
      maxTokens: 4000,
    };

    try {
      const response = await multiProviderAIClient.createCompletion(request);

      logger.info('AI completion successful', {
        operation,
        provider: response.provider,
        model: response.model,
        tokens: response.usage.totalTokens,
        cost: response.usage.cost,
        processingTime: response.processingTime,
      });

      return response;
    } catch (error) {
      logger.error('AI completion failed', { operation, error });
      throw error;
    }
  }

  /**
   * Get current session metrics - removed telemetry
   */
  getSessionMetrics() {
    return { totalRequests: 0, totalTokens: 0, totalCost: 0, startTime: new Date() };
  }

  /**
   * Get task metrics - removed telemetry
   */
  getTaskMetrics(_taskId?: string) {
    return null;
  }

  /**
   * Get provider metrics - removed telemetry
   */
  getProviderMetrics(_provider?: string) {
    return null;
  }

  /**
   * Get available providers
   */
  getAvailableProviders(): ProviderSettings[] {
    return aiProviderConfig.getProviders().filter(p => p.enabled);
  }

  /**
   * Switch to a different provider
   */
  async switchProvider(providerId: string): Promise<void> {
    try {
      await aiProviderConfig.setDefaultProvider(providerId);
      logger.info(`Switched to provider: ${providerId}`);
    } catch (error) {
      logger.error(`Failed to switch to provider ${providerId}`, error);
      throw error;
    }
  }

  /**
   * Test a provider connection
   */
  testProvider(providerId: string): boolean {
    try {
      const result = aiProviderConfig.testProvider(providerId);
      logger.info(`Provider test result for ${providerId}: ${result}`);
      return result;
    } catch (error) {
      logger.error(`Provider test failed for ${providerId}`, error);
      return false;
    }
  }

  /**
   * Add or update a provider
   */
  async configureProvider(
    providerData: Omit<ProviderSettings, 'createdAt' | 'updatedAt'>
  ): Promise<void> {
    try {
      await aiProviderConfig.addProvider(providerData);
      logger.info(`Provider ${providerData.id} configured successfully`);
    } catch (error) {
      logger.error(`Failed to configure provider ${providerData.id}`, error);
      throw error;
    }
  }

  /**
   * Remove a provider
   */
  async removeProvider(providerId: string): Promise<void> {
    try {
      await aiProviderConfig.removeProvider(providerId);
      logger.info(`Provider ${providerId} removed successfully`);
    } catch (error) {
      logger.error(`Failed to remove provider ${providerId}`, error);
      throw error;
    }
  }

  /**
   * Export configuration for backup
   */
  exportConfiguration() {
    return {
      providers: aiProviderConfig.exportConfiguration(),
      metrics: {},
      timestamp: new Date(),
    };
  }

  /**
   * Reset session metrics
   */
  resetSession(): void {
    logger.info('Session metrics reset');
  }

  /**
   * Check if the integration is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Get health status of all providers
   */
  getHealthStatus(): Record<string, boolean> {
    try {
      return multiProviderAIClient.healthCheck();
    } catch (error) {
      logger.error('Health check failed', error);
      return {};
    }
  }
}

// Export singleton instance
export const aiProviderIntegration = new AIProviderIntegration();

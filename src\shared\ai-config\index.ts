import { Anthropic } from '@anthropic-ai/sdk';
import { ApiConfiguration, ModelInfo, QwenApiRegions } from './api';
import { AnthropicHandler } from './providers/anthropic';
import { AwsBedrockHandler } from './providers/bedrock';
import { OpenRouterHandler } from './providers/openrouter';
import { VertexHandler } from './providers/vertex';
import { OpenAiHandler } from './providers/openai';
import { <PERSON>llamaHandler } from './providers/ollama';

import { GeminiHandler } from './providers/gemini';
import { OpenAiNativeHandler } from './providers/openai-native';
import { ApiStream, ApiStreamUsageChunk } from './transform/stream';
import { DeepSeekHandler } from './providers/deepseek';

import { QwenHandler } from './providers/qwen';
import { MistralHandler } from './providers/mistral';

import { XAIHandler } from './providers/xai';

import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from './providers/groq';

// Helper function to create clean option objects without undefined values
function createCleanOptions<T extends Record<string, any>>(options: T): Partial<T> {
  const clean: Partial<T> = {};
  for (const [key, value] of Object.entries(options)) {
    if (value !== undefined) {
      (clean as any)[key] = value;
    }
  }
  return clean;
}

// Mode type definition
type Mode = 'plan' | 'act';

export interface ApiHandler {
  createMessage(systemPrompt: string, messages: Anthropic.Messages.MessageParam[]): ApiStream;
  getModel(): { id: string; info: ModelInfo };
  getApiStreamUsage?(): Promise<ApiStreamUsageChunk | undefined>;
}

export interface SingleCompletionHandler {
  completePrompt(prompt: string): Promise<string>;
}

function createHandlerForProvider(
  apiProvider: string | undefined,
  options: Omit<ApiConfiguration, 'apiProvider'>,
  mode: Mode
): ApiHandler {
  switch (apiProvider) {
    case 'anthropic':
      return new AnthropicHandler({
        apiKey: options.apiKey || '',
        anthropicBaseUrl: options.anthropicBaseUrl || '',
        apiModelId:
          (mode === 'plan' ? options.planModeApiModelId : options.actModeApiModelId) || '',
        thinkingBudgetTokens:
          mode === 'plan'
            ? options.planModeThinkingBudgetTokens || 0
            : options.actModeThinkingBudgetTokens || 0,
      });
    case 'openrouter':
      return new OpenRouterHandler(
        createCleanOptions({
          openRouterApiKey: options.openRouterApiKey,
          openRouterModelId:
            mode === 'plan' ? options.planModeOpenRouterModelId : options.actModeOpenRouterModelId,
          openRouterModelInfo:
            mode === 'plan'
              ? options.planModeOpenRouterModelInfo
              : options.actModeOpenRouterModelInfo,
          openRouterProviderSorting: options.openRouterProviderSorting,
          reasoningEffort:
            mode === 'plan' ? options.planModeReasoningEffort : options.actModeReasoningEffort,
          thinkingBudgetTokens:
            mode === 'plan'
              ? options.planModeThinkingBudgetTokens
              : options.actModeThinkingBudgetTokens,
        })
      );
    case 'bedrock':
      return new AwsBedrockHandler({
        apiModelId:
          (mode === 'plan' ? options.planModeApiModelId : options.actModeApiModelId) || '',
        awsAccessKey: options.awsAccessKey || '',
        awsSecretKey: options.awsSecretKey || '',
        awsSessionToken: options.awsSessionToken || '',
        awsRegion: options.awsRegion || '',
        awsAuthentication: options.awsAuthentication || '',
        awsBedrockApiKey: options.awsBedrockApiKey || '',
        awsUseCrossRegionInference: options.awsUseCrossRegionInference || false,
        awsBedrockUsePromptCache: options.awsBedrockUsePromptCache || false,
        awsUseProfile: options.awsUseProfile || false,
        awsProfile: options.awsProfile || '',
        awsBedrockEndpoint: options.awsBedrockEndpoint || '',
        awsBedrockCustomSelected:
          mode === 'plan'
            ? options.planModeAwsBedrockCustomSelected || false
            : options.actModeAwsBedrockCustomSelected || false,
        awsBedrockCustomModelBaseId:
          mode === 'plan'
            ? options.planModeAwsBedrockCustomModelBaseId ||
              'anthropic.claude-sonnet-4-20250514-v1:0'
            : options.actModeAwsBedrockCustomModelBaseId ||
              'anthropic.claude-sonnet-4-20250514-v1:0',
        thinkingBudgetTokens:
          mode === 'plan'
            ? options.planModeThinkingBudgetTokens || 0
            : options.actModeThinkingBudgetTokens || 0,
      });
    case 'vertex':
      return new VertexHandler({
        vertexProjectId: options.vertexProjectId || '',
        vertexRegion: options.vertexRegion || '',
        apiModelId:
          (mode === 'plan' ? options.planModeApiModelId : options.actModeApiModelId) || '',
        thinkingBudgetTokens:
          mode === 'plan'
            ? options.planModeThinkingBudgetTokens || 0
            : options.actModeThinkingBudgetTokens || 0,
        geminiApiKey: options.geminiApiKey || '',
        geminiBaseUrl: options.geminiBaseUrl || '',
        taskId: options.taskId || '',
      });
    case 'openai':
      return new OpenAiHandler(
        createCleanOptions({
          openAiApiKey: options.openAiApiKey,
          openAiBaseUrl: options.openAiBaseUrl,
          azureApiVersion: options.azureApiVersion,
          openAiHeaders: options.openAiHeaders,
          openAiModelId:
            mode === 'plan' ? options.planModeOpenAiModelId : options.actModeOpenAiModelId,
          openAiModelInfo:
            mode === 'plan' ? options.planModeOpenAiModelInfo : options.actModeOpenAiModelInfo,
          reasoningEffort:
            mode === 'plan' ? options.planModeReasoningEffort : options.actModeReasoningEffort,
        })
      );
    case 'ollama':
      return new OllamaHandler(
        createCleanOptions({
          ollamaBaseUrl: options.ollamaBaseUrl,
          ollamaModelId:
            mode === 'plan' ? options.planModeOllamaModelId : options.actModeOllamaModelId,
          ollamaApiOptionsCtxNum: options.ollamaApiOptionsCtxNum,
          requestTimeoutMs: options.requestTimeoutMs,
        })
      );
    case 'lmstudio':
      // TODO: Implement LmStudioHandler
      throw new Error('LmStudioHandler not implemented yet');
    case 'gemini':
      return new GeminiHandler(
        createCleanOptions({
          vertexProjectId: options.vertexProjectId,
          vertexRegion: options.vertexRegion,
          geminiApiKey: options.geminiApiKey,
          geminiBaseUrl: options.geminiBaseUrl,
          thinkingBudgetTokens:
            mode === 'plan'
              ? options.planModeThinkingBudgetTokens
              : options.actModeThinkingBudgetTokens,
          apiModelId: mode === 'plan' ? options.planModeApiModelId : options.actModeApiModelId,
          taskId: options.taskId,
        })
      );
    case 'openai-native':
      return new OpenAiNativeHandler(
        createCleanOptions({
          openAiNativeApiKey: options.openAiNativeApiKey,
          reasoningEffort:
            mode === 'plan' ? options.planModeReasoningEffort : options.actModeReasoningEffort,
          apiModelId: mode === 'plan' ? options.planModeApiModelId : options.actModeApiModelId,
        })
      );
    case 'deepseek':
      return new DeepSeekHandler(
        createCleanOptions({
          deepSeekApiKey: options.deepSeekApiKey,
          apiModelId: mode === 'plan' ? options.planModeApiModelId : options.actModeApiModelId,
        })
      );

    case 'qwen':
      return new QwenHandler(
        createCleanOptions({
          qwenApiKey: options.qwenApiKey,
          qwenApiLine:
            options.qwenApiLine === QwenApiRegions.INTERNATIONAL
              ? QwenApiRegions.INTERNATIONAL
              : QwenApiRegions.CHINA,
          apiModelId: mode === 'plan' ? options.planModeApiModelId : options.actModeApiModelId,
          thinkingBudgetTokens:
            mode === 'plan'
              ? options.planModeThinkingBudgetTokens
              : options.actModeThinkingBudgetTokens,
        })
      );
    case 'doubao':
      // TODO: Implement DoubaoHandler
      throw new Error('DoubaoHandler not implemented yet');
    case 'mistral':
      return new MistralHandler(
        createCleanOptions({
          mistralApiKey: options.mistralApiKey,
          apiModelId: mode === 'plan' ? options.planModeApiModelId : options.actModeApiModelId,
        })
      );

    case 'xai':
      return new XAIHandler(
        createCleanOptions({
          xaiApiKey: options.xaiApiKey,
          reasoningEffort:
            mode === 'plan' ? options.planModeReasoningEffort : options.actModeReasoningEffort,
          apiModelId: mode === 'plan' ? options.planModeApiModelId : options.actModeApiModelId,
        })
      );

    case 'groq':
      return new GroqHandler(
        createCleanOptions({
          groqApiKey: options.groqApiKey,
          groqModelId: mode === 'plan' ? options.planModeGroqModelId : options.actModeGroqModelId,
          groqModelInfo:
            mode === 'plan' ? options.planModeGroqModelInfo : options.actModeGroqModelInfo,
          apiModelId: mode === 'plan' ? options.planModeApiModelId : options.actModeApiModelId,
        })
      );

    default:
      return new AnthropicHandler(
        createCleanOptions({
          apiKey: options.apiKey,
          anthropicBaseUrl: options.anthropicBaseUrl,
          apiModelId: mode === 'plan' ? options.planModeApiModelId : options.actModeApiModelId,
          thinkingBudgetTokens:
            mode === 'plan'
              ? options.planModeThinkingBudgetTokens
              : options.actModeThinkingBudgetTokens,
        })
      );
  }
}

export function buildApiHandler(configuration: ApiConfiguration, mode: Mode): ApiHandler {
  const { planModeApiProvider, actModeApiProvider, ...options } = configuration;

  const apiProvider = mode === 'plan' ? planModeApiProvider : actModeApiProvider;

  // Validate thinking budget tokens against model's maxTokens to prevent API errors
  // wrapped in a try-catch for safety, but this should never throw
  try {
    const thinkingBudgetTokens =
      mode === 'plan' ? options.planModeThinkingBudgetTokens : options.actModeThinkingBudgetTokens;
    if (thinkingBudgetTokens && thinkingBudgetTokens > 0) {
      const handler = createHandlerForProvider(apiProvider, options, mode);

      const modelInfo = handler.getModel().info;
      if (modelInfo.maxTokens && thinkingBudgetTokens > modelInfo.maxTokens) {
        const clippedValue = modelInfo.maxTokens - 1;
        if (mode === 'plan') {
          options.planModeThinkingBudgetTokens = clippedValue;
        } else {
          options.actModeThinkingBudgetTokens = clippedValue;
        }
      } else {
        return handler; // don't rebuild unless its necessary
      }
    }
  } catch (error) {
    // Log error silently - this is a validation step that shouldn't fail
    // but we want to handle it gracefully if it does
  }

  return createHandlerForProvider(apiProvider, options, mode);
}

export type { ModelInfo };

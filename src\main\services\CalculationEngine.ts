import { create, all, MathJsStatic } from 'mathjs';
import { createLogger } from '../utils/logger';
import { CalculationResult } from '../../shared/types/Document';

const logger = createLogger('CalculationEngine');

export interface CalculationOptions {
  precision?: number;
  enableHistory?: boolean;
  enableCaching?: boolean;
  maxHistorySize?: number;
  customFunctions?: Map<string, Function>;
  customConstants?: Map<string, any>;
  safeMode?: boolean;
}

export interface CalculationHistoryEntry {
  id: string;
  expression: string;
  result: any;
  timestamp: Date;
  executionTime: number;
  variables?: Record<string, any>;
  error?: string;
}

export interface ExpressionValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  dependencies: string[];
  returnType: 'number' | 'string' | 'boolean' | 'array' | 'object' | 'unknown';
}

export interface CalculationContext {
  variables: Record<string, any>;
  functions: Record<string, Function>;
  constants: Record<string, any>;
  precision: number;
}

export interface FinancialCalculationOptions {
  principal?: number;
  rate?: number;
  time?: number;
  compoundingFrequency?: number;
  taxRate?: number;
  currency?: string;
}

export interface StatisticalCalculationOptions {
  dataset: number[];
  confidenceLevel?: number;
  method?: 'parametric' | 'non-parametric';
}

/**
 * Mathematical Calculation Engine using MathJS
 * Provides comprehensive mathematical calculation capabilities with validation,
 * history tracking, and specialized financial/statistical functions
 */
export class CalculationEngine {
  private math: MathJsStatic;
  private options: Required<CalculationOptions>;
  private history: CalculationHistoryEntry[] = [];
  private cache: Map<string, any> = new Map();
  private customFunctions: Map<string, Function> = new Map();
  private customConstants: Map<string, any> = new Map();

  constructor(options: CalculationOptions = {}) {
    this.options = {
      precision: options.precision ?? 10,
      enableHistory: options.enableHistory ?? true,
      enableCaching: options.enableCaching ?? true,
      maxHistorySize: options.maxHistorySize ?? 1000,
      customFunctions: options.customFunctions ?? new Map(),
      customConstants: options.customConstants ?? new Map(),
      safeMode: options.safeMode ?? true,
    };

    // Initialize MathJS with all functions
    this.math = create(all as any, {
      number: 'BigNumber',
      precision: this.options.precision,
    });

    this.initializeCustomFunctions();
    this.initializeCustomConstants();
    this.configureSafeMode();

    logger.info('CalculationEngine initialized', {
      precision: this.options.precision,
      safeMode: this.options.safeMode,
      customFunctions: this.customFunctions.size,
      customConstants: this.customConstants.size,
    });
  }

  /**
   * Evaluate a mathematical expression
   */
  public async evaluate(
    expression: string,
    variables: Record<string, any> = {},
    context?: Partial<CalculationContext>
  ): Promise<CalculationResult> {
    const startTime = Date.now();
    const historyId = this.generateHistoryId();

    try {
      // Validate expression first
      const validation = await this.validateExpression(expression, variables);
      if (!validation.isValid) {
        throw new Error(`Invalid expression: ${validation.errors.join(', ')}`);
      }

      // Check cache if enabled
      const cacheKey = this.generateCacheKey(expression, variables);
      if (this.options.enableCaching && this.cache.has(cacheKey)) {
        const cachedResult = this.cache.get(cacheKey);
        logger.debug('Using cached result', { expression, cacheKey });
        return cachedResult;
      }

      // Create evaluation context
      const evalContext = this.createEvaluationContext(variables, context);

      // Evaluate expression
      const result = this.math.evaluate(expression, evalContext.variables);
      const executionTime = Date.now() - startTime;

      const calculationResult: CalculationResult = {
        formula: expression,
        result,
        dependencies: validation.dependencies,
        variables: Object.keys(variables),
        confidence: 1.0, // High confidence for mathematical calculations
      };

      // Cache result if enabled
      if (this.options.enableCaching) {
        this.cache.set(cacheKey, calculationResult);
      }

      // Add to history if enabled
      if (this.options.enableHistory) {
        this.addToHistory({
          id: historyId,
          expression,
          result,
          timestamp: new Date(),
          executionTime,
          variables,
        });
      }

      logger.debug('Expression evaluated successfully', {
        expression,
        result,
        executionTime,
        dependencies: validation.dependencies,
      });

      return calculationResult;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // Add error to history if enabled
      if (this.options.enableHistory) {
        this.addToHistory({
          id: historyId,
          expression,
          result: null,
          timestamp: new Date(),
          executionTime,
          variables,
          error: errorMessage,
        });
      }

      logger.error('Expression evaluation failed', {
        expression,
        error: errorMessage,
        variables,
        executionTime,
      });

      throw new Error(`Calculation failed: ${errorMessage}`);
    }
  }

  /**
   * Validate mathematical expression
   */
  public async validateExpression(
    expression: string,
    variables: Record<string, any> = {}
  ): Promise<ExpressionValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const dependencies: string[] = [];

    try {
      // Basic syntax validation
      if (!expression || expression.trim() === '') {
        errors.push('Expression cannot be empty');
        return { isValid: false, errors, warnings, dependencies, returnType: 'unknown' };
      }

      // Parse expression to check syntax
      const parsed = this.math.parse(expression);

      // Extract dependencies (variables used in expression)
      const extractedDeps = this.extractDependencies(expression);
      dependencies.push(...extractedDeps);

      // Check if all required variables are provided
      for (const dep of dependencies) {
        if (!(dep in variables) && !(dep in this.customConstants)) {
          warnings.push(`Variable '${dep}' is not defined`);
        }
      }

      // Safety checks in safe mode
      if (this.options.safeMode) {
        const safetyErrors = this.performSafetyChecks(expression);
        errors.push(...safetyErrors);
      }

      // Determine return type
      const returnType = this.inferReturnType(parsed);

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        dependencies,
        returnType,
      };
    } catch (error) {
      errors.push(`Syntax error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return { isValid: false, errors, warnings, dependencies, returnType: 'unknown' };
    }
  }

  /**
   * Get calculation history
   */
  public getHistory(limit?: number): CalculationHistoryEntry[] {
    const history = [...this.history].reverse(); // Most recent first
    return limit ? history.slice(0, limit) : history;
  }

  /**
   * Clear calculation history
   */
  public clearHistory(): void {
    this.history = [];
    logger.info('Calculation history cleared');
  }

  /**
   * Clear calculation cache
   */
  public clearCache(): void {
    this.cache.clear();
    logger.info('Calculation cache cleared');
  }

  /**
   * Add custom function
   */
  public addCustomFunction(name: string, func: Function, description?: string): void {
    this.customFunctions.set(name, func);
    this.math.import({ [name]: func }, { override: true });
    logger.info('Custom function added', { name, description });
  }

  /**
   * Add custom constant
   */
  public addCustomConstant(name: string, value: any, description?: string): void {
    this.customConstants.set(name, value);
    this.math.import({ [name]: value }, { override: true });
    logger.info('Custom constant added', { name, value, description });
  }

  /**
   * Get available functions
   */
  public getAvailableFunctions(): string[] {
    const mathFunctions = Object.keys(this.math).filter(
      key => typeof (this.math as any)[key] === 'function'
    );
    const customFunctions = Array.from(this.customFunctions.keys());
    return [...mathFunctions, ...customFunctions].sort();
  }

  /**
   * Get available constants
   */
  public getAvailableConstants(): Record<string, any> {
    const mathConstants = {
      pi: this.math.pi,
      e: this.math.e,
      tau: this.math.tau,
      phi: this.math.phi,
    };
    const customConstants = Object.fromEntries(this.customConstants);
    return { ...mathConstants, ...customConstants };
  }

  /**
   * Initialize custom mathematical functions
   */
  private initializeCustomFunctions(): void {
    // Financial functions
    this.addCustomFunction(
      'compound_interest',
      this.compoundInterest.bind(this),
      'Calculate compound interest: compound_interest(principal, rate, frequency, time)'
    );

    this.addCustomFunction(
      'simple_interest',
      this.simpleInterest.bind(this),
      'Calculate simple interest: simple_interest(principal, rate, time)'
    );

    this.addCustomFunction(
      'present_value',
      this.presentValue.bind(this),
      'Calculate present value: present_value(future_value, rate, time)'
    );

    this.addCustomFunction(
      'future_value',
      this.futureValue.bind(this),
      'Calculate future value: future_value(present_value, rate, time)'
    );

    // Tax calculation functions
    this.addCustomFunction(
      'tax_amount',
      this.calculateTaxAmount.bind(this),
      'Calculate tax amount: tax_amount(income, rate)'
    );

    this.addCustomFunction(
      'after_tax',
      this.calculateAfterTax.bind(this),
      'Calculate after-tax amount: after_tax(income, rate)'
    );

    // Statistical functions
    this.addCustomFunction(
      'variance',
      this.calculateVariance.bind(this),
      'Calculate variance: variance([data])'
    );

    this.addCustomFunction(
      'std_dev',
      this.calculateStandardDeviation.bind(this),
      'Calculate standard deviation: std_dev([data])'
    );

    // Percentage functions
    this.addCustomFunction(
      'percentage',
      this.calculatePercentage.bind(this),
      'Calculate percentage: percentage(value, total)'
    );

    this.addCustomFunction(
      'percentage_change',
      this.calculatePercentageChange.bind(this),
      'Calculate percentage change: percentage_change(old_value, new_value)'
    );
  }

  /**
   * Initialize custom constants
   */
  private initializeCustomConstants(): void {
    // Financial constants
    this.addCustomConstant('DAYS_PER_YEAR', 365, 'Standard days per year');
    this.addCustomConstant('MONTHS_PER_YEAR', 12, 'Months per year');
    this.addCustomConstant('WEEKS_PER_YEAR', 52, 'Weeks per year');

    // Tax rates (example - should be configurable)
    this.addCustomConstant('FEDERAL_TAX_RATE', 0.22, 'Example federal tax rate');
    this.addCustomConstant('STATE_TAX_RATE', 0.05, 'Example state tax rate');
  }

  /**
   * Configure safe mode restrictions
   */
  private configureSafeMode(): void {
    if (this.options.safeMode) {
      // Disable potentially dangerous functions
      const restrictedFunctions = ['import', 'createUnit', 'evaluate', 'parse'];
      restrictedFunctions.forEach(funcName => {
        if ((this.math as any)[funcName]) {
          delete (this.math as any)[funcName];
        }
      });
    }
  }

  /**
   * Create evaluation context
   */
  private createEvaluationContext(
    variables: Record<string, any>,
    context?: Partial<CalculationContext>
  ): CalculationContext {
    return {
      variables: {
        ...Object.fromEntries(this.customConstants),
        ...variables,
      },
      functions: Object.fromEntries(this.customFunctions),
      constants: Object.fromEntries(this.customConstants),
      precision: context?.precision ?? this.options.precision,
    };
  }

  /**
   * Extract variable dependencies from expression
   */
  private extractDependencies(expression: string): string[] {
    const dependencies: string[] = [];
    const variablePattern = /\b[a-zA-Z_][a-zA-Z0-9_]*\b/g;
    const matches = expression.match(variablePattern) || [];

    for (const match of matches) {
      // Skip known functions and constants
      if (!this.isKnownFunction(match) && !this.isKnownConstant(match)) {
        if (!dependencies.includes(match)) {
          dependencies.push(match);
        }
      }
    }

    return dependencies;
  }

  /**
   * Check if identifier is a known function
   */
  private isKnownFunction(identifier: string): boolean {
    return (
      typeof (this.math as any)[identifier] === 'function' || this.customFunctions.has(identifier)
    );
  }

  /**
   * Check if identifier is a known constant
   */
  private isKnownConstant(identifier: string): boolean {
    return (
      identifier in this.math ||
      this.customConstants.has(identifier) ||
      ['pi', 'e', 'tau', 'phi', 'true', 'false'].includes(identifier)
    );
  }

  /**
   * Perform safety checks on expression
   */
  private performSafetyChecks(expression: string): string[] {
    const errors: string[] = [];

    // Check for potentially dangerous patterns
    const dangerousPatterns = [
      /import\s*\(/i,
      /require\s*\(/i,
      /eval\s*\(/i,
      /function\s*\(/i,
      /=>/,
      /\bwhile\b/i,
      /\bfor\b/i,
      /\bif\b/i,
      /\btry\b/i,
      /\bcatch\b/i,
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(expression)) {
        errors.push(`Potentially unsafe pattern detected: ${pattern.source}`);
      }
    }

    return errors;
  }

  /**
   * Infer return type from parsed expression
   */
  private inferReturnType(
    parsed: any
  ): 'number' | 'string' | 'boolean' | 'array' | 'object' | 'unknown' {
    try {
      // This is a simplified type inference
      // In a real implementation, you'd analyze the AST more thoroughly
      if (parsed.type === 'ConstantNode') {
        if (typeof parsed.value === 'number') return 'number';
        if (typeof parsed.value === 'string') return 'string';
        if (typeof parsed.value === 'boolean') return 'boolean';
      }

      // Default to number for mathematical expressions
      return 'number';
    } catch {
      return 'unknown';
    }
  }

  /**
   * Generate cache key for expression and variables
   */
  private generateCacheKey(expression: string, variables: Record<string, any>): string {
    const variablesStr = JSON.stringify(variables, Object.keys(variables).sort());
    return `${expression}:${variablesStr}`;
  }

  /**
   * Generate unique history ID
   */
  private generateHistoryId(): string {
    return `calc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Add entry to calculation history
   */
  private addToHistory(entry: CalculationHistoryEntry): void {
    this.history.push(entry);

    // Maintain history size limit
    if (this.history.length > this.options.maxHistorySize) {
      this.history = this.history.slice(-this.options.maxHistorySize);
    }
  }

  // Financial calculation methods
  private compoundInterest(
    principal: number,
    rate: number,
    frequency: number,
    time: number
  ): number {
    return principal * Math.pow(1 + rate / frequency, frequency * time);
  }

  private simpleInterest(principal: number, rate: number, time: number): number {
    return principal * (1 + rate * time);
  }

  private presentValue(futureValue: number, rate: number, time: number): number {
    return futureValue / Math.pow(1 + rate, time);
  }

  private futureValue(presentValue: number, rate: number, time: number): number {
    return presentValue * Math.pow(1 + rate, time);
  }

  private calculateTaxAmount(income: number, rate: number): number {
    return income * rate;
  }

  private calculateAfterTax(income: number, rate: number): number {
    return income * (1 - rate);
  }

  private calculateVariance(data: number[]): number {
    const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
    const squaredDiffs = data.map(val => Math.pow(val - mean, 2));
    return squaredDiffs.reduce((sum, val) => sum + val, 0) / data.length;
  }

  private calculateStandardDeviation(data: number[]): number {
    return Math.sqrt(this.calculateVariance(data));
  }

  private calculatePercentage(value: number, total: number): number {
    return (value / total) * 100;
  }

  private calculatePercentageChange(oldValue: number, newValue: number): number {
    return ((newValue - oldValue) / oldValue) * 100;
  }
}

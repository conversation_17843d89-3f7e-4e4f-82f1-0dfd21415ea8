import { ocrConfigManager } from '../OCRConfiguration';

describe('OCR Integration Tests', () => {
  beforeEach(() => {
    // Reset configuration to defaults
    ocrConfigManager.resetToDefaults();
  });

  describe('OCRConfiguration', () => {
    it('should provide default configuration', () => {
      const config = ocrConfigManager.getConfig();

      expect(config).toBeDefined();
      expect(config.maxWorkers).toBeGreaterThan(0);
      expect(config.defaultLanguage).toBe('eng');
      expect(Array.isArray(config.supportedLanguages)).toBe(true);
    });

    it('should apply quality presets', () => {
      ocrConfigManager.applyQualityPreset('fast');
      let config = ocrConfigManager.getConfig();
      expect(config.processingQuality).toBe('fast');

      ocrConfigManager.applyQualityPreset('accurate');
      config = ocrConfigManager.getConfig();
      expect(config.processingQuality).toBe('accurate');
    });

    it('should validate configuration', () => {
      const validation = ocrConfigManager.validateConfig();
      expect(validation.isValid).toBe(true);
      expect(Array.isArray(validation.errors)).toBe(true);
    });

    it('should get document-specific OCR options', () => {
      const taxFormOptions = ocrConfigManager.getDocumentOCROptions('tax-form');

      expect(taxFormOptions).toBeDefined();
      expect(taxFormOptions.detectFormFields).toBe(true);
      expect(taxFormOptions.detectTables).toBe(true);
    });
  });
});

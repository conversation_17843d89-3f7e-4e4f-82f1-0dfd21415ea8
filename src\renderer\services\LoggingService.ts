export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4,
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: any;
  stack?: string;
  component?: string;
  userId?: string;
  sessionId: string;
  url: string;
  userAgent: string;
}

export interface LoggingConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableElectron: boolean;
  enableLocalStorage: boolean;
  maxLocalStorageEntries: number;
  enableAnalytics: boolean;
  bufferSize: number;
  flushInterval: number;
}

/**
 * Comprehensive logging service for the renderer process
 */
export class LoggingService {
  private config: LoggingConfig;
  private sessionId: string;
  private buffer: LogEntry[] = [];
  private flushTimer?: NodeJS.Timeout;

  constructor(config: Partial<LoggingConfig> = {}) {
    this.config = {
      level: LogLevel.INFO,
      enableConsole: true,
      enableElectron: true,
      enableLocalStorage: true,
      maxLocalStorageEntries: 1000,
      enableAnalytics: false,
      bufferSize: 50,
      flushInterval: 5000, // 5 seconds
      ...config,
    };

    this.sessionId = this.generateSessionId();
    this.setupGlobalErrorHandlers();
    this.startFlushTimer();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private setupGlobalErrorHandlers(): void {
    // Override console methods to capture logs
    if (this.config.enableConsole) {
      this.interceptConsole();
    }

    // Global error handler
    window.addEventListener('error', event => {
      this.error('Global JavaScript Error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
      });
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', event => {
      this.error('Unhandled Promise Rejection', {
        reason: event.reason?.toString(),
        stack: event.reason?.stack,
      });
    });

    // Performance monitoring
    if (typeof window.performance !== 'undefined') {
      window.addEventListener('load', () => {
        setTimeout(() => {
          const perfData = window.performance.timing;
          const loadTime = perfData.loadEventEnd - perfData.navigationStart;
          this.info('Application Performance', {
            loadTime,
            domContentLoaded: perfData.domContentLoadedEventEnd - perfData.navigationStart,
            domComplete: perfData.domComplete - perfData.navigationStart,
          });
        }, 0);
      });
    }
  }

  private interceptConsole(): void {
    const originalConsole = {
      log: console.log,
      info: console.info,
      warn: console.warn,
      error: console.error,
      debug: console.debug,
    };

    console.log = (...args) => {
      originalConsole.log(...args);
      this.debug('Console Log', { args });
    };

    console.info = (...args) => {
      originalConsole.info(...args);
      this.info('Console Info', { args });
    };

    console.warn = (...args) => {
      originalConsole.warn(...args);
      this.warn('Console Warning', { args });
    };

    console.error = (...args) => {
      originalConsole.error(...args);
      this.error('Console Error', { args });
    };

    console.debug = (...args) => {
      originalConsole.debug(...args);
      this.debug('Console Debug', { args });
    };
  }

  private createLogEntry(level: LogLevel, message: string, data?: any, stack?: string): LogEntry {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      sessionId: this.sessionId,
      url: window.location.href,
      userAgent: navigator.userAgent,
    };

    if (data !== undefined) {
      entry.data = data;
    }

    if (stack !== undefined) {
      entry.stack = stack;
    }

    return entry;
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.config.level;
  }

  private addToBuffer(entry: LogEntry): void {
    this.buffer.push(entry);

    if (this.buffer.length >= this.config.bufferSize) {
      this.flush();
    }
  }

  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.config.flushInterval);
  }

  private flush(): void {
    if (this.buffer.length === 0) return;

    const entries = [...this.buffer];
    this.buffer = [];

    // Send to Electron main process
    if (this.config.enableElectron && window.electronAPI?.logToMain) {
      entries.forEach(entry => {
        const levelName = LogLevel[entry.level].toLowerCase();
        window.electronAPI.logToMain(levelName, entry.message, {
          ...entry.data,
          timestamp: entry.timestamp,
          sessionId: entry.sessionId,
          stack: entry.stack,
        });
      });
    }

    // Store in localStorage
    if (this.config.enableLocalStorage) {
      this.storeInLocalStorage(entries);
    }

    // Send analytics (if enabled)
    if (this.config.enableAnalytics) {
      this.sendAnalytics(entries);
    }
  }

  private storeInLocalStorage(entries: LogEntry[]): void {
    try {
      const existingLogs = JSON.parse(localStorage.getItem('app-logs') || '[]');
      const allLogs = [...existingLogs, ...entries];

      // Keep only the most recent entries
      const trimmedLogs = allLogs.slice(-this.config.maxLocalStorageEntries);

      localStorage.setItem('app-logs', JSON.stringify(trimmedLogs));
    } catch (error) {
      console.error('Failed to store logs in localStorage:', error);
    }
  }

  private sendAnalytics(entries: LogEntry[]): void {
    // Placeholder for analytics integration
    // This could send to services like Sentry, LogRocket, etc.
    console.log('Analytics entries:', entries);
  }

  // Public logging methods
  public debug(message: string, data?: any, component?: string): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return;

    const entry = this.createLogEntry(LogLevel.DEBUG, message, data);
    if (component !== undefined) {
      entry.component = component;
    }
    this.addToBuffer(entry);
  }

  public info(message: string, data?: any, component?: string): void {
    if (!this.shouldLog(LogLevel.INFO)) return;

    const entry = this.createLogEntry(LogLevel.INFO, message, data);
    if (component !== undefined) {
      entry.component = component;
    }
    this.addToBuffer(entry);
  }

  public warn(message: string, data?: any, component?: string): void {
    if (!this.shouldLog(LogLevel.WARN)) return;

    const entry = this.createLogEntry(LogLevel.WARN, message, data);
    if (component !== undefined) {
      entry.component = component;
    }
    this.addToBuffer(entry);
  }

  public error(message: string, data?: any, component?: string, stack?: string): void {
    if (!this.shouldLog(LogLevel.ERROR)) return;

    const entry = this.createLogEntry(LogLevel.ERROR, message, data, stack);
    if (component !== undefined) {
      entry.component = component;
    }
    this.addToBuffer(entry);

    // Immediately flush errors
    this.flush();
  }

  public fatal(message: string, data?: any, component?: string, stack?: string): void {
    const entry = this.createLogEntry(LogLevel.FATAL, message, data, stack);
    if (component !== undefined) {
      entry.component = component;
    }
    this.addToBuffer(entry);

    // Immediately flush fatal errors
    this.flush();
  }

  // Utility methods
  public setLevel(level: LogLevel): void {
    this.config.level = level;
  }

  public getSessionId(): string {
    return this.sessionId;
  }

  public getLogs(): LogEntry[] {
    try {
      return JSON.parse(localStorage.getItem('app-logs') || '[]');
    } catch {
      return [];
    }
  }

  public clearLogs(): void {
    localStorage.removeItem('app-logs');
    this.buffer = [];
  }

  public exportLogs(): string {
    const logs = this.getLogs();
    return JSON.stringify(logs, null, 2);
  }

  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flush();
  }
}

// Create global logging service instance
export const logger = new LoggingService({
  level: process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO,
  enableConsole: process.env.NODE_ENV === 'development',
  enableElectron: true,
  enableLocalStorage: true,
  enableAnalytics: process.env.NODE_ENV === 'production',
});

// Export convenience functions
export const log = {
  debug: (message: string, data?: any, component?: string) =>
    logger.debug(message, data, component),
  info: (message: string, data?: any, component?: string) => logger.info(message, data, component),
  warn: (message: string, data?: any, component?: string) => logger.warn(message, data, component),
  error: (message: string, data?: any, component?: string, stack?: string) =>
    logger.error(message, data, component, stack),
  fatal: (message: string, data?: any, component?: string, stack?: string) =>
    logger.fatal(message, data, component, stack),
};

export default LoggingService;

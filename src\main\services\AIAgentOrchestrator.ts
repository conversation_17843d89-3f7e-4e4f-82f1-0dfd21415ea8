import { logger } from '../utils/logger';
import { aiModelClient } from './AIModelClient';
import { aiToolsManager, toolExecutor } from './AITools';
import {
  AIAgent,
  AgentType,
  AICapability,
  AIContext,
  ToolInput,
  ToolOutput,
} from '../../shared/types/AI';

/**
 * Agent Execution Result
 */
export interface AgentExecutionResult {
  success: boolean;
  result: string;
  toolsUsed: Array<{
    toolId: string;
    input: ToolInput;
    output: ToolOutput;
    executionTime: number;
  }>;
  totalExecutionTime: number;
  tokensUsed: number;
  cost: number;
  confidence: number;
  metadata: Record<string, unknown>;
}

/**
 * Agent Task Definition
 */
export interface AgentTask {
  id: string;
  type: string;
  description: string;
  input: Record<string, unknown>;
  context?: AIContext;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  timeout?: number;
  requiredCapabilities?: string[];
}

/**
 * AI Agent Orchestrator - Coordinates AI agents with tools and model clients
 * Implements comprehensive agent execution with tool selection and result synthesis
 */
export class AIAgentOrchestrator {
  private agents = new Map<string, AIAgent>();
  private executionHistory: Array<{
    agentId: string;
    task: AgentTask;
    result: AgentExecutionResult;
    timestamp: Date;
  }> = [];

  constructor() {
    this.initializeDefaultAgents();
  }

  /**
   * Initialize default AI agents
   */
  private initializeDefaultAgents(): void {
    // Document Processing Agent
    this.registerAgent({
      id: 'document_processor',
      name: 'Document Processor Agent',
      description: 'Specialized agent for document analysis, processing, and extraction',
      type: AgentType.DOCUMENT_PROCESSOR,
      capabilities: [
        AICapability.DOCUMENT_UNDERSTANDING,
        AICapability.TEXT_ANALYSIS,
        AICapability.ENTITY_EXTRACTION,
        AICapability.SUMMARIZATION,
      ],
      tools: [
        {
          id: 'document_analyzer',
          name: 'Document Analysis Tool',
          description: 'Analyzes document content and extracts insights',
          parameters: [],
        },
        {
          id: 'knowledge_search',
          name: 'Knowledge Search Tool',
          description: 'Searches knowledge base for relevant information',
          parameters: [],
        },
      ],
      configuration: {
        model: 'gpt-4',
        temperature: 0.3,
        maxTokens: 2000,
        systemPrompt: `You are a document processing specialist. Your role is to analyze documents, extract key information, and provide comprehensive insights. Use the available tools to:
1. Analyze document content and structure
2. Extract entities and relationships
3. Classify documents by type and purpose
4. Search for relevant context in the knowledge base
5. Provide clear, actionable summaries

Always be thorough but concise in your analysis.`,
        tools: ['document_analyzer', 'knowledge_search'],
        memorySize: 10,
        timeout: 30000,
      },
      isActive: true,
    });

    // Form Filling Agent
    this.registerAgent({
      id: 'form_filler',
      name: 'Form Filling Agent',
      description: 'Intelligent agent for automated form completion and validation',
      type: AgentType.FORM_FILLER,
      capabilities: [
        AICapability.FORM_FILLING,
        AICapability.ENTITY_EXTRACTION,
        AICapability.TEXT_ANALYSIS,
      ],
      tools: [
        {
          id: 'form_filler',
          name: 'Form Filling Tool',
          description: 'Intelligently fills form fields based on available data',
          parameters: [],
        },
        {
          id: 'document_analyzer',
          name: 'Document Analysis Tool',
          description: 'Analyzes source documents for form data',
          parameters: [],
        },
        {
          id: 'knowledge_search',
          name: 'Knowledge Search Tool',
          description: 'Searches for missing form data',
          parameters: [],
        },
      ],
      configuration: {
        model: 'gpt-4',
        temperature: 0.1,
        maxTokens: 1500,
        systemPrompt: `You are a form filling specialist. Your role is to intelligently complete forms using available data sources. Your process should be:
1. Analyze the form structure and required fields
2. Extract relevant data from source documents
3. Map data to appropriate form fields
4. Validate field values and formats
5. Search knowledge base for missing information
6. Provide confidence scores for each filled field

Be precise and conservative - only fill fields when you're confident about the data.`,
        tools: ['form_filler', 'document_analyzer', 'knowledge_search'],
        memorySize: 5,
        timeout: 20000,
      },
      isActive: true,
    });

    // Knowledge Manager Agent
    this.registerAgent({
      id: 'knowledge_manager',
      name: 'Knowledge Manager Agent',
      description: 'Manages knowledge base operations and semantic search',
      type: AgentType.KNOWLEDGE_MANAGER,
      capabilities: [
        AICapability.QUESTION_ANSWERING,
        AICapability.TEXT_ANALYSIS,
        AICapability.ENTITY_EXTRACTION,
      ],
      tools: [
        {
          id: 'knowledge_search',
          name: 'Knowledge Search Tool',
          description: 'Performs semantic search in knowledge base',
          parameters: [],
        },
        {
          id: 'document_analyzer',
          name: 'Document Analysis Tool',
          description: 'Analyzes content for knowledge extraction',
          parameters: [],
        },
      ],
      configuration: {
        model: 'gpt-3.5-turbo',
        temperature: 0.2,
        maxTokens: 1000,
        systemPrompt: `You are a knowledge management specialist. Your role is to help users find, organize, and understand information from the knowledge base. Your approach should be:
1. Understand the user's information needs
2. Perform targeted searches in the knowledge base
3. Analyze and synthesize relevant information
4. Provide clear, well-sourced answers
5. Suggest related information that might be useful

Always cite your sources and indicate confidence levels.`,
        tools: ['knowledge_search', 'document_analyzer'],
        memorySize: 8,
        timeout: 15000,
      },
      isActive: true,
    });

    // Analyst Agent
    this.registerAgent({
      id: 'analyst',
      name: 'Data Analyst Agent',
      description: 'Performs data analysis, calculations, and insights generation',
      type: AgentType.ANALYST,
      capabilities: [AICapability.TEXT_ANALYSIS, AICapability.ENTITY_EXTRACTION],
      tools: [
        {
          id: 'calculator',
          name: 'Calculator Tool',
          description: 'Performs mathematical calculations and analysis',
          parameters: [],
        },
        {
          id: 'document_analyzer',
          name: 'Document Analysis Tool',
          description: 'Analyzes documents for data extraction',
          parameters: [],
        },
        {
          id: 'knowledge_search',
          name: 'Knowledge Search Tool',
          description: 'Searches for relevant data and context',
          parameters: [],
        },
      ],
      configuration: {
        model: 'gpt-4',
        temperature: 0.1,
        maxTokens: 2000,
        systemPrompt: `You are a data analysis specialist. Your role is to analyze data, perform calculations, and generate insights. Your process should include:
1. Understanding the analysis requirements
2. Extracting relevant data from documents
3. Performing accurate calculations
4. Identifying patterns and trends
5. Providing clear, data-driven insights
6. Suggesting actionable recommendations

Always show your work and explain your reasoning.`,
        tools: ['calculator', 'document_analyzer', 'knowledge_search'],
        memorySize: 12,
        timeout: 45000,
      },
      isActive: true,
    });

    logger.info('Default AI agents initialized', {
      agentCount: this.agents.size,
      agents: Array.from(this.agents.keys()),
    });
  }

  /**
   * Register a new agent
   */
  registerAgent(agent: AIAgent): void {
    this.agents.set(agent.id, agent);
    logger.info('Agent registered', {
      id: agent.id,
      name: agent.name,
      type: agent.type,
      capabilities: agent.capabilities,
    });
  }

  /**
   * Get agent by ID
   */
  getAgent(agentId: string): AIAgent | undefined {
    return this.agents.get(agentId);
  }

  /**
   * Get all agents
   */
  getAllAgents(): AIAgent[] {
    return Array.from(this.agents.values());
  }

  /**
   * Get agents by capability
   */
  getAgentsByCapability(capability: AICapability): AIAgent[] {
    return this.getAllAgents().filter(agent => agent.capabilities.includes(capability));
  }

  /**
   * Select best agent for a task
   */
  selectAgentForTask(task: AgentTask): AIAgent | null {
    const candidates = this.getAllAgents().filter(agent => {
      if (!agent.isActive) return false;

      // Check if agent has required capabilities
      if (task.requiredCapabilities) {
        const hasAllCapabilities = task.requiredCapabilities.every(cap =>
          agent.capabilities.includes(cap as AICapability)
        );
        if (!hasAllCapabilities) return false;
      }

      return true;
    });

    if (candidates.length === 0) {
      return null;
    }

    // Score agents based on capability match and specialization
    const scoredCandidates = candidates.map(agent => {
      let score = 0;

      // Boost score for exact type match
      if (task.type === agent.type) {
        score += 10;
      }

      // Boost score for capability overlap
      if (task.requiredCapabilities) {
        const matchingCapabilities = task.requiredCapabilities.filter(cap =>
          agent.capabilities.includes(cap as AICapability)
        );
        score += matchingCapabilities.length * 2;
      }

      // Boost score for available tools
      const availableTools = aiToolsManager.discoverTools();
      const agentToolIds = agent.tools.map(t => t.id);
      const toolMatches = availableTools.filter(tool => agentToolIds.includes(tool.id));
      score += toolMatches.length;

      return { agent, score };
    });

    // Return agent with highest score
    scoredCandidates.sort((a, b) => b.score - a.score);
    if (scoredCandidates.length === 0) {
      throw new Error('No suitable agent found for the task');
    }
    return scoredCandidates[0]!.agent;
  }

  /**
   * Execute a task with the best available agent
   */
  async executeTask(task: AgentTask): Promise<AgentExecutionResult> {
    const startTime = Date.now();

    try {
      // Select appropriate agent
      const agent = this.selectAgentForTask(task);
      if (!agent) {
        throw new Error(`No suitable agent found for task type: ${task.type}`);
      }

      logger.info('Executing task with agent', {
        taskId: task.id,
        agentId: agent.id,
        agentName: agent.name,
        taskType: task.type,
      });

      // Execute the task
      const result = await this.executeAgentTask(agent, task);

      // Record execution
      this.executionHistory.push({
        agentId: agent.id,
        task,
        result,
        timestamp: new Date(),
      });

      logger.info('Task execution completed', {
        taskId: task.id,
        agentId: agent.id,
        success: result.success,
        executionTime: result.totalExecutionTime,
        toolsUsed: result.toolsUsed.length,
      });

      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      logger.error('Task execution failed', {
        taskId: task.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime,
      });

      return {
        success: false,
        result: `Task execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        toolsUsed: [],
        totalExecutionTime: executionTime,
        tokensUsed: 0,
        cost: 0,
        confidence: 0,
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date(),
        },
      };
    }
  }

  /**
   * Execute a specific agent task
   */
  private async executeAgentTask(agent: AIAgent, task: AgentTask): Promise<AgentExecutionResult> {
    const startTime = Date.now();
    const toolsUsed: AgentExecutionResult['toolsUsed'] = [];

    try {
      // Prepare system prompt with agent configuration
      const systemPrompt = `${agent.configuration.systemPrompt}

Available Tools:
${agent.tools.map(tool => `- ${tool.name}: ${tool.description}`).join('\n')}

Task: ${task.description}
Priority: ${task.priority}
${task.context ? `Context: ${JSON.stringify(task.context, null, 2)}` : ''}`;

      // Start AI model task tracking
      aiModelClient.startTask(`agent_${agent.id}_${task.id}`);

      // Create the user message with task details
      const userMessage = `Please help me with the following task:

${task.description}

Input data:
${JSON.stringify(task.input, null, 2)}

Please use the available tools as needed to complete this task effectively.`;

      // Execute with AI model
      const aiResponse = await aiModelClient.performReasoning({
        context: systemPrompt,
        query: userMessage,
        model: agent.configuration.model,
      });

      // Analyze response for tool usage
      const toolExecutions = await this.extractAndExecuteTools(
        aiResponse.conclusion,
        task.input,
        task.context
      );

      toolsUsed.push(...toolExecutions);

      // Generate final response incorporating tool results
      let finalResult = aiResponse.conclusion;
      if (toolExecutions.length > 0) {
        const toolResults = toolExecutions
          .map(exec => `${exec.toolId}: ${JSON.stringify(exec.output.result)}`)
          .join('\n');

        finalResult = await this.synthesizeResults(
          aiResponse.conclusion,
          toolResults,
          agent.configuration.systemPrompt
        );
      }

      // End task tracking
      aiModelClient.endTask();

      const totalExecutionTime = Date.now() - startTime;
      const sessionMetrics = aiModelClient.getSessionMetrics();

      return {
        success: true,
        result: finalResult,
        toolsUsed,
        totalExecutionTime,
        tokensUsed: sessionMetrics.totalTokens,
        cost: sessionMetrics.totalCost,
        confidence: aiResponse.confidence,
        metadata: {
          agentId: agent.id,
          agentName: agent.name,
          taskId: task.id,
          model: agent.configuration.model,
          reasoning: aiResponse.reasoning,
          sources: aiResponse.sources,
          timestamp: new Date(),
        },
      };
    } catch (error) {
      const totalExecutionTime = Date.now() - startTime;
      logger.error('Agent task execution failed', {
        agentId: agent.id,
        taskId: task.id,
        error,
      });

      return {
        success: false,
        result: `Agent execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        toolsUsed,
        totalExecutionTime,
        tokensUsed: 0,
        cost: 0,
        confidence: 0,
        metadata: {
          agentId: agent.id,
          taskId: task.id,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date(),
        },
      };
    }
  }

  /**
   * Extract tool usage from AI response and execute tools
   */
  private async extractAndExecuteTools(
    aiResponse: string,
    taskInput: Record<string, unknown>,
    context?: AIContext
  ): Promise<
    Array<{
      toolId: string;
      input: ToolInput;
      output: ToolOutput;
      executionTime: number;
    }>
  > {
    const toolExecutions: Array<{
      toolId: string;
      input: ToolInput;
      output: ToolOutput;
      executionTime: number;
    }> = [];

    // Simple pattern matching for tool usage (in a real implementation, this would be more sophisticated)
    const toolPatterns = [
      { pattern: /analyze.*document/i, toolId: 'document_analyzer', type: 'comprehensive' },
      { pattern: /search.*knowledge/i, toolId: 'knowledge_search', type: 'search' },
      { pattern: /fill.*form/i, toolId: 'form_filler', type: 'form_filling' },
      { pattern: /calculate|math|compute/i, toolId: 'calculator', type: 'calculation' },
    ];

    for (const { pattern, toolId, type } of toolPatterns) {
      if (pattern.test(aiResponse)) {
        const startTime = Date.now();

        try {
          let toolInput: ToolInput;

          // Prepare tool-specific input
          switch (toolId) {
            case 'document_analyzer':
              toolInput = {
                parameters: {
                  content: taskInput.content || taskInput.document || '',
                  analysisType: type,
                  options: { includeEmotions: true },
                },
              };
              break;
            case 'knowledge_search':
              toolInput = {
                parameters: {
                  query: taskInput.query || aiResponse.substring(0, 100),
                  limit: 5,
                  threshold: 0.7,
                },
              };
              break;
            case 'form_filler':
              toolInput = {
                parameters: {
                  formFields: taskInput.formFields || [],
                  availableData: taskInput.availableData || taskInput,
                  documentContext: taskInput.context || '',
                },
              };
              break;
            case 'calculator':
              // Extract mathematical expressions from the response
              const mathMatch = aiResponse.match(/(\d+(?:\.\d+)?[\+\-\*\/\(\)]+\d+(?:\.\d+)?)/);
              toolInput = {
                parameters: {
                  expression: mathMatch?.[1] || taskInput.expression || '1+1',
                  type: 'basic',
                },
              };
              break;
            default:
              continue;
          }

          const output = await toolExecutor.execute(toolId, toolInput, context);
          const executionTime = Date.now() - startTime;

          toolExecutions.push({
            toolId,
            input: toolInput,
            output,
            executionTime,
          });

          logger.info('Tool executed by agent', {
            toolId,
            success: output.success,
            executionTime,
          });
        } catch (error) {
          logger.error('Tool execution failed in agent', {
            toolId,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }
    }

    return toolExecutions;
  }

  /**
   * Synthesize AI response with tool results
   */
  private async synthesizeResults(
    aiResponse: string,
    toolResults: string,
    systemPrompt: string
  ): Promise<string> {
    try {
      const synthesisPrompt = `${systemPrompt}

Original response: ${aiResponse}

Tool execution results:
${toolResults}

Please provide a comprehensive final response that incorporates both the original analysis and the tool results. Be clear, concise, and actionable.`;

      const synthesis = await aiModelClient.performReasoning({
        context: synthesisPrompt,
        query: 'Synthesize the final response incorporating all available information.',
      });

      return synthesis.conclusion;
    } catch (error) {
      logger.error('Result synthesis failed', { error });
      return `${aiResponse}\n\nTool Results:\n${toolResults}`;
    }
  }

  /**
   * Get execution history
   */
  getExecutionHistory(): typeof this.executionHistory {
    return [...this.executionHistory];
  }

  /**
   * Get agent performance metrics
   */
  getAgentMetrics(agentId: string): {
    totalExecutions: number;
    successRate: number;
    averageExecutionTime: number;
    averageTokensUsed: number;
    totalCost: number;
  } {
    const agentExecutions = this.executionHistory.filter(exec => exec.agentId === agentId);

    if (agentExecutions.length === 0) {
      return {
        totalExecutions: 0,
        successRate: 0,
        averageExecutionTime: 0,
        averageTokensUsed: 0,
        totalCost: 0,
      };
    }

    const successfulExecutions = agentExecutions.filter(exec => exec.result.success);

    return {
      totalExecutions: agentExecutions.length,
      successRate: successfulExecutions.length / agentExecutions.length,
      averageExecutionTime:
        agentExecutions.reduce((sum, exec) => sum + exec.result.totalExecutionTime, 0) /
        agentExecutions.length,
      averageTokensUsed:
        agentExecutions.reduce((sum, exec) => sum + exec.result.tokensUsed, 0) /
        agentExecutions.length,
      totalCost: agentExecutions.reduce((sum, exec) => sum + exec.result.cost, 0),
    };
  }

  /**
   * Clear execution history
   */
  clearHistory(): void {
    this.executionHistory = [];
    logger.info('Agent execution history cleared');
  }
}

// Export singleton instance
export const aiAgentOrchestrator = new AIAgentOrchestrator();

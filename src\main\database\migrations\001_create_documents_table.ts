import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create documents table
  await knex.schema.createTable('documents', (table) => {
    // Primary key
    table.string('id', 36).primary().notNullable().comment('UUID primary key');

    // Basic document information
    table.string('name', 255).notNullable().comment('Document filename or title');
    table.string('type', 50).notNullable().comment('Document type (pdf, excel, word, csv, image)');
    table.text('path').notNullable().comment('Full file path to the document');

    // Content integrity and metadata
    table.string('content_hash', 64).notNullable().comment('SHA-256 hash of document content');
    table.json('metadata').comment('Document metadata (size, pages, author, etc.)');

    // Timestamps
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable().comment('Document creation timestamp');
    table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable().comment('Document last update timestamp');

    // Additional fields for document management
    table.bigInteger('file_size').unsigned().comment('File size in bytes');
    table.string('mime_type', 100).comment('MIME type of the document');
    table.string('encoding', 50).comment('Character encoding (for text documents)');
    table.integer('page_count').unsigned().comment('Number of pages (for paginated documents)');
    table.string('language', 10).comment('Detected or specified document language');
    table.text('description').comment('User-provided description of the document');
    table.json('tags').comment('Array of tags for categorization');
    table.boolean('is_processed').defaultTo(false).notNullable().comment('Whether document has been processed by AI');
    table.timestamp('processed_at').comment('When document was last processed');
    table.decimal('processing_confidence', 5, 4).comment('AI processing confidence score (0-1)');

    // Constraints
    table.check('file_size >= 0', [], 'file_size_positive');
    table.check('page_count >= 0', [], 'page_count_positive');
    table.check('processing_confidence >= 0 AND processing_confidence <= 1', [], 'confidence_range');
    table.check("type IN ('pdf', 'excel', 'word', 'csv', 'image', 'text', 'other')", [], 'valid_document_type');

    // Indexes for performance
    table.index(['type'], 'idx_documents_type');
    table.index(['created_at'], 'idx_documents_created_at');
    table.index(['updated_at'], 'idx_documents_updated_at');
    table.index(['path'], 'idx_documents_path');
    table.index(['content_hash'], 'idx_documents_content_hash');
    table.index(['is_processed'], 'idx_documents_is_processed');
    table.index(['processed_at'], 'idx_documents_processed_at');
    table.index(['name'], 'idx_documents_name');

    // Composite indexes for common queries
    table.index(['type', 'is_processed'], 'idx_documents_type_processed');
    table.index(['created_at', 'type'], 'idx_documents_created_type');
    table.index(['processing_confidence', 'is_processed'], 'idx_documents_confidence_processed');
  });

  // Create full-text search virtual table for document names and metadata
  // Note: SQLite FTS5 extension must be available
  await knex.raw(`
    CREATE VIRTUAL TABLE IF NOT EXISTS documents_fts USING fts5(
      id UNINDEXED,
      name,
      description,
      tags,
      metadata,
      content='documents',
      content_rowid='rowid'
    )
  `);

  // Create triggers to keep FTS table in sync
  await knex.raw(`
    CREATE TRIGGER documents_fts_insert AFTER INSERT ON documents BEGIN
      INSERT INTO documents_fts(id, name, description, tags, metadata)
      VALUES (new.id, new.name, new.description, new.tags, new.metadata);
    END
  `);

  await knex.raw(`
    CREATE TRIGGER documents_fts_delete AFTER DELETE ON documents BEGIN
      DELETE FROM documents_fts WHERE id = old.id;
    END
  `);

  await knex.raw(`
    CREATE TRIGGER documents_fts_update AFTER UPDATE ON documents BEGIN
      DELETE FROM documents_fts WHERE id = old.id;
      INSERT INTO documents_fts(id, name, description, tags, metadata)
      VALUES (new.id, new.name, new.description, new.tags, new.metadata);
    END
  `);

  // Create updated_at trigger
  await knex.raw(`
    CREATE TRIGGER update_documents_updated_at
    AFTER UPDATE ON documents
    FOR EACH ROW
    BEGIN
      UPDATE documents SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END
  `);

  // Create view for commonly accessed document information
  await knex.raw(`
    CREATE VIEW document_summary AS
    SELECT
      id,
      name,
      type,
      file_size,
      page_count,
      is_processed,
      processing_confidence,
      created_at,
      updated_at,
      json_extract(metadata, '$.author') as author,
      json_extract(metadata, '$.title') as title,
      json_extract(metadata, '$.subject') as subject
    FROM documents
  `);

  // Insert initial data or configuration if needed
  // This could include default document types or system documents
}

export async function down(knex: Knex): Promise<void> {
  // Drop triggers first
  await knex.raw('DROP TRIGGER IF EXISTS update_documents_updated_at');
  await knex.raw('DROP TRIGGER IF EXISTS documents_fts_update');
  await knex.raw('DROP TRIGGER IF EXISTS documents_fts_delete');
  await knex.raw('DROP TRIGGER IF EXISTS documents_fts_insert');

  // Drop FTS table
  await knex.raw('DROP TABLE IF EXISTS documents_fts');

  // Drop view
  await knex.raw('DROP VIEW IF EXISTS document_summary');

  // Drop main table (this will also drop all indexes)
  await knex.schema.dropTableIfExists('documents');
}

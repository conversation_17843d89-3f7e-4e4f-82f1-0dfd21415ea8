import sharp from 'sharp';
import * as tf from '@tensorflow/tfjs-node';
import {
  DocumentCoordinates,
  FeatureType,
  ExtractedData,
  ExtractedDataType,
  ExtractionMethod,
} from '../../shared/types/Document';
import { logger } from '../utils/logger';
import { ComputerVisionService, ComputerVisionResult } from './ComputerVisionService';

export interface MLModelResult {
  predictions: Array<{
    class: string;
    confidence: number;
    boundingBox?: DocumentCoordinates;
  }>;
  processingTime: number;
  modelUsed: string;
}

export interface DocumentClassificationResult {
  documentType: 'invoice' | 'receipt' | 'form' | 'letter' | 'contract' | 'unknown';
  confidence: number;
  features: string[];
  reasoning: string;
}

export interface AdvancedAnalysisOptions {
  enableMLClassification: boolean;
  enableObjectDetection: boolean;
  enableTextRegionAnalysis: boolean;
  enableLayoutAnalysis: boolean;
  enableQualityAssessment: boolean;
  modelPath?: string;
  confidenceThreshold: number;
}

export interface AdvancedAnalysisResult {
  computerVision: ComputerVisionResult;
  mlClassification?: MLModelResult;
  documentClassification: DocumentClassificationResult;
  qualityMetrics: {
    sharpness: number;
    contrast: number;
    brightness: number;
    noise: number;
    overall: number;
  };
  extractedData: ExtractedData[];
  processingTime: number;
  success: boolean;
  error?: string;
}

/**
 * Advanced Image Analysis Service that combines computer vision with machine learning
 * for sophisticated document analysis and classification
 */
export class AdvancedImageAnalysisService {
  private readonly computerVisionService: ComputerVisionService;
  private mlModel: tf.LayersModel | null = null;
  private isModelLoaded = false;

  private readonly defaultOptions: AdvancedAnalysisOptions = {
    enableMLClassification: true,
    enableObjectDetection: true,
    enableTextRegionAnalysis: true,
    enableLayoutAnalysis: true,
    enableQualityAssessment: true,
    confidenceThreshold: 0.7,
  };

  constructor() {
    this.computerVisionService = new ComputerVisionService();
  }

  /**
   * Initialize the service and load ML models
   */
  public async initialize(modelPath?: string): Promise<void> {
    try {
      if (modelPath) {
        await this.loadMLModel(modelPath);
      }
      logger.info('Advanced Image Analysis Service initialized');
    } catch (error) {
      logger.warn('Failed to load ML model, continuing without ML features', { error });
    }
  }

  /**
   * Perform comprehensive image analysis
   */
  public async analyzeImage(
    imageBuffer: Buffer,
    options: Partial<AdvancedAnalysisOptions> = {}
  ): Promise<AdvancedAnalysisResult> {
    const startTime = Date.now();
    const analysisOptions = { ...this.defaultOptions, ...options };

    try {
      logger.debug('Starting advanced image analysis', { options: analysisOptions });

      const extractedData: ExtractedData[] = [];

      // Step 1: Computer Vision Analysis
      const computerVision = await this.computerVisionService.analyzeDocument(imageBuffer, {
        detectText: analysisOptions.enableTextRegionAnalysis,
        detectImages: analysisOptions.enableObjectDetection,
        detectTables: analysisOptions.enableLayoutAnalysis,
        detectBarcodes: true,
        detectSignatures: true,
        detectStamps: true,
        analyzeLayout: analysisOptions.enableLayoutAnalysis,
        minimumConfidence: analysisOptions.confidenceThreshold,
      });

      // Step 2: ML-based Classification (if enabled and model available)
      let mlClassification: MLModelResult | undefined;
      if (analysisOptions.enableMLClassification && this.isModelLoaded && this.mlModel) {
        mlClassification = await this.performMLClassification(imageBuffer);
      }

      // Step 3: Document Type Classification
      const documentClassification = this.classifyDocument(imageBuffer, computerVision);

      // Step 4: Quality Assessment
      let qualityMetrics = {
        sharpness: 0,
        contrast: 0,
        brightness: 0,
        noise: 0,
        overall: 0,
      };

      if (analysisOptions.enableQualityAssessment) {
        qualityMetrics = await this.assessImageQuality(imageBuffer);
      }

      // Step 5: Extract structured data from analysis results
      extractedData.push(
        ...this.extractStructuredData(computerVision, documentClassification, mlClassification)
      );

      const processingTime = Date.now() - startTime;

      logger.debug('Advanced image analysis completed', {
        processingTime,
        featuresDetected: computerVision.features.length,
        documentType: documentClassification.documentType,
        qualityScore: qualityMetrics.overall,
      });

      return {
        computerVision,
        ...(mlClassification && { mlClassification }),
        documentClassification,
        qualityMetrics,
        extractedData,
        processingTime,
        success: true,
      };
    } catch (error) {
      logger.error('Advanced image analysis failed', { error });

      return {
        computerVision: {
          features: [],
          barcodes: [],
          layout: {
            regions: [],
            readingOrder: [],
            columns: 1,
            orientation: 'portrait',
            margins: { top: 0, right: 0, bottom: 0, left: 0 },
          },
          textRegions: [],
          imageRegions: [],
          tableRegions: [],
          processingTime: 0,
          confidence: 0,
        },
        documentClassification: {
          documentType: 'unknown',
          confidence: 0,
          features: [],
          reasoning: 'Analysis failed',
        },
        qualityMetrics: {
          sharpness: 0,
          contrast: 0,
          brightness: 0,
          noise: 0,
          overall: 0,
        },
        extractedData: [],
        processingTime: Date.now() - startTime,
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Load machine learning model for classification
   */
  private async loadMLModel(modelPath: string): Promise<void> {
    try {
      this.mlModel = await tf.loadLayersModel(`file://${modelPath}`);
      this.isModelLoaded = true;
      logger.info('ML model loaded successfully', { modelPath });
    } catch (error) {
      logger.error('Failed to load ML model', { error, modelPath });
      throw error;
    }
  }

  /**
   * Perform ML-based classification
   */
  private async performMLClassification(imageBuffer: Buffer): Promise<MLModelResult> {
    const startTime = Date.now();

    try {
      if (!this.mlModel) {
        throw new Error('ML model not loaded');
      }

      // Preprocess image for ML model
      const preprocessedImage = await this.preprocessImageForML(imageBuffer);

      // Run inference
      const predictions = this.mlModel.predict(preprocessedImage) as tf.Tensor;
      const predictionData = await predictions.data();

      // Process predictions (this would depend on your specific model)
      const results = Array.from(predictionData).map((confidence, index) => ({
        class: `class_${index}`,
        confidence,
      }));

      // Cleanup tensors
      preprocessedImage.dispose();
      predictions.dispose();

      return {
        predictions: results,
        processingTime: Date.now() - startTime,
        modelUsed: 'document_classifier_v1',
      };
    } catch (error) {
      logger.error('ML classification failed', { error });
      throw error;
    }
  }

  /**
   * Preprocess image for ML model input
   */
  private async preprocessImageForML(imageBuffer: Buffer): Promise<tf.Tensor> {
    // Resize and normalize image for ML model (typical preprocessing)
    const resizedBuffer = await sharp(imageBuffer)
      .resize(224, 224) // Common input size for many models
      .removeAlpha()
      .raw()
      .toBuffer();

    // Convert to tensor and normalize
    const tensor = tf.tensor3d(new Uint8Array(resizedBuffer), [224, 224, 3]);
    const normalized = tensor.div(255.0);
    const batched = normalized.expandDims(0);

    tensor.dispose();
    normalized.dispose();

    return batched;
  }

  /**
   * Classify document type based on visual features
   */
  private classifyDocument(
    _imageBuffer: Buffer,
    computerVision: ComputerVisionResult
  ): DocumentClassificationResult {
    try {
      const features: string[] = [];
      let documentType: DocumentClassificationResult['documentType'] = 'unknown';
      let confidence = 0;
      let reasoning = '';

      // Analyze layout and features
      const { layout, features: detectedFeatures, barcodes } = computerVision;

      // Check for invoice/receipt characteristics
      if (barcodes.length > 0) {
        features.push('has_barcode');
      }

      if (layout.columns === 1 && detectedFeatures.some(f => f.type === FeatureType.TABLE)) {
        features.push('has_table');
      }

      if (detectedFeatures.some(f => f.type === FeatureType.SIGNATURE)) {
        features.push('has_signature');
      }

      // Simple rule-based classification
      if (features.includes('has_barcode') && features.includes('has_table')) {
        documentType = 'receipt';
        confidence = 0.8;
        reasoning = 'Document contains barcode and tabular data, typical of receipts';
      } else if (features.includes('has_signature') && layout.columns === 1) {
        documentType = 'contract';
        confidence = 0.7;
        reasoning = 'Document contains signature and single-column layout, typical of contracts';
      } else if (features.includes('has_table') && layout.columns === 1) {
        documentType = 'invoice';
        confidence = 0.6;
        reasoning = 'Document contains tabular data in single-column layout, typical of invoices';
      } else if (layout.columns > 1) {
        documentType = 'letter';
        confidence = 0.5;
        reasoning = 'Multi-column layout suggests letter or newsletter format';
      }

      return {
        documentType,
        confidence,
        features,
        reasoning,
      };
    } catch (error) {
      logger.warn('Document classification failed', { error });
      return {
        documentType: 'unknown',
        confidence: 0,
        features: [],
        reasoning: 'Classification failed due to error',
      };
    }
  }

  /**
   * Assess image quality metrics
   */
  private async assessImageQuality(imageBuffer: Buffer): Promise<{
    sharpness: number;
    contrast: number;
    brightness: number;
    noise: number;
    overall: number;
  }> {
    try {
      // Get image statistics
      const stats = await sharp(imageBuffer).stats();

      // Calculate sharpness using Laplacian variance
      const sharpness = await this.calculateSharpness(imageBuffer);

      // Calculate contrast from standard deviation
      const contrast = (stats.channels?.[0]?.stdev || 0) / 128;

      // Calculate brightness from mean
      const brightness = (stats.channels?.[0]?.mean || 0) / 255;

      // Estimate noise (simplified)
      const noise = await this.estimateNoise(imageBuffer);

      // Calculate overall quality score
      const overall =
        sharpness * 0.3 + contrast * 0.3 + (1 - noise) * 0.2 + Math.abs(brightness - 0.5) * 0.2;

      return {
        sharpness: Math.min(sharpness, 1),
        contrast: Math.min(contrast, 1),
        brightness,
        noise,
        overall: Math.min(overall, 1),
      };
    } catch (error) {
      logger.warn('Quality assessment failed', { error });
      return {
        sharpness: 0,
        contrast: 0,
        brightness: 0,
        noise: 1,
        overall: 0,
      };
    }
  }

  /**
   * Calculate image sharpness using Laplacian variance
   */
  private async calculateSharpness(imageBuffer: Buffer): Promise<number> {
    try {
      // Apply Laplacian filter to detect edges
      const laplacianBuffer = await sharp(imageBuffer)
        .greyscale()
        .convolve({
          width: 3,
          height: 3,
          kernel: [0, -1, 0, -1, 4, -1, 0, -1, 0],
        })
        .raw()
        .toBuffer();

      // Calculate variance of Laplacian
      const pixels = new Uint8Array(laplacianBuffer);
      const mean = pixels.reduce((sum, pixel) => sum + pixel, 0) / pixels.length;
      const variance =
        pixels.reduce((sum, pixel) => sum + Math.pow(pixel - mean, 2), 0) / pixels.length;

      // Normalize to 0-1 range
      return Math.min(variance / 1000, 1);
    } catch (error) {
      logger.warn('Sharpness calculation failed', { error });
      return 0;
    }
  }

  /**
   * Estimate noise level in image
   */
  private async estimateNoise(imageBuffer: Buffer): Promise<number> {
    try {
      // Apply Gaussian blur and compare with original
      const blurredBuffer = await sharp(imageBuffer).greyscale().blur(1).raw().toBuffer();

      const originalBuffer = await sharp(imageBuffer).greyscale().raw().toBuffer();

      // Calculate difference
      const original = new Uint8Array(originalBuffer);
      const blurred = new Uint8Array(blurredBuffer);

      let totalDifference = 0;
      const minLength = Math.min(original.length, blurred.length);
      for (let i = 0; i < minLength; i++) {
        const origPixel = original[i] ?? 0;
        const blurPixel = blurred[i] ?? 0;
        totalDifference += Math.abs(origPixel - blurPixel);
      }

      const averageDifference = totalDifference / minLength;
      return Math.min(averageDifference / 50, 1); // Normalize
    } catch (error) {
      logger.warn('Noise estimation failed', { error });
      return 0.5; // Default moderate noise level
    }
  }

  /**
   * Extract structured data from analysis results
   */
  private extractStructuredData(
    computerVision: ComputerVisionResult,
    documentClassification: DocumentClassificationResult,
    mlClassification?: MLModelResult
  ): ExtractedData[] {
    const extractedData: ExtractedData[] = [];

    // Add computer vision features
    for (const feature of computerVision.features) {
      extractedData.push({
        id: `cv_feature_${Date.now()}_${Math.random()}`,
        documentId: '',
        type: this.mapFeatureTypeToExtractedDataType(feature.type),
        content: {
          featureType: feature.type,
          bounds: feature.bounds,
          properties: feature.properties,
        },
        confidence: feature.confidence,
        extractionMethod: ExtractionMethod.AI_ANALYSIS,
        coordinates: feature.bounds,
        createdAt: new Date(),
      });
    }

    // Add barcode data
    for (const barcode of computerVision.barcodes) {
      extractedData.push({
        id: `barcode_${Date.now()}_${Math.random()}`,
        documentId: '',
        type: ExtractedDataType.TEXT,
        content: {
          type: 'barcode',
          format: barcode.type,
          data: barcode.data,
        },
        confidence: barcode.confidence,
        extractionMethod: ExtractionMethod.AI_ANALYSIS,
        coordinates: barcode.bounds,
        createdAt: new Date(),
      });
    }

    // Add document classification
    extractedData.push({
      id: `classification_${Date.now()}`,
      documentId: '',
      type: ExtractedDataType.TEXT,
      content: {
        documentType: documentClassification.documentType,
        features: documentClassification.features,
        reasoning: documentClassification.reasoning,
      },
      confidence: documentClassification.confidence,
      extractionMethod: ExtractionMethod.AI_ANALYSIS,
      createdAt: new Date(),
    });

    // Add ML classification results if available
    if (mlClassification) {
      extractedData.push({
        id: `ml_classification_${Date.now()}`,
        documentId: '',
        type: ExtractedDataType.TEXT,
        content: {
          predictions: mlClassification.predictions,
          modelUsed: mlClassification.modelUsed,
        },
        confidence: Math.max(...mlClassification.predictions.map(p => p.confidence)),
        extractionMethod: ExtractionMethod.AI_ANALYSIS,
        createdAt: new Date(),
      });
    }

    // Add layout analysis
    extractedData.push({
      id: `layout_${Date.now()}`,
      documentId: '',
      type: ExtractedDataType.TEXT,
      content: {
        layout: computerVision.layout,
        readingOrder: computerVision.layout.readingOrder,
        columns: computerVision.layout.columns,
        orientation: computerVision.layout.orientation,
      },
      confidence: 0.9,
      extractionMethod: ExtractionMethod.AI_ANALYSIS,
      createdAt: new Date(),
    });

    return extractedData;
  }

  /**
   * Map computer vision feature types to extracted data types
   */
  private mapFeatureTypeToExtractedDataType(featureType: FeatureType): ExtractedDataType {
    switch (featureType) {
      case FeatureType.TEXT_BLOCK:
        return ExtractedDataType.TEXT;
      case FeatureType.TABLE:
        return ExtractedDataType.TABLE;
      case FeatureType.FORM_FIELD:
        return ExtractedDataType.FORM_FIELD;
      case FeatureType.IMAGE:
        return ExtractedDataType.IMAGE;
      case FeatureType.SIGNATURE:
      case FeatureType.STAMP:
      case FeatureType.BARCODE:
      default:
        return ExtractedDataType.IMAGE;
    }
  }

  /**
   * Cleanup resources
   */
  public cleanup(): void {
    if (this.mlModel) {
      this.mlModel.dispose();
      this.mlModel = null;
      this.isModelLoaded = false;
    }
    logger.info('Advanced Image Analysis Service cleaned up');
  }
}

@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* Global styles for AI Document Processor */
:root {
  /* Custom CSS variables for theming */
  --font-family-sans: "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "JetBrains Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  /* Animation durations */
  --duration-fast: 150ms;
  --duration-normal: 200ms;
  --duration-slow: 300ms;
  --duration-slower: 500ms;

  /* Easing functions */
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Z-index layers */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;

  /* Spacing scale */
  --space-px: 1px;
  --space-0: 0;
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;

  /* Border radius scale */
  --radius-none: 0;
  --radius-sm: 0.125rem;
  --radius-base: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* Shadow scale */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Document type colors */
  --color-pdf: #dc2626;
  --color-excel: #16a34a;
  --color-word: #2563eb;
  --color-csv: #7c3aed;
  --color-image: #ea580c;
  --color-text: #6b7280;
}

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  font-family: var(--font-family-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  background-color: #ffffff;
  color: #1f2937;
  overflow: hidden; /* Prevent body scroll in Electron */
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f8fafc;
}

::-webkit-scrollbar-thumb {
  background: #e2e8f0;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #60a5fa;
}

/* Focus styles for accessibility */
:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Custom utility classes */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn var(--duration-normal) ease-in-out;
}

.animate-slide-in {
  animation: slideIn var(--duration-slow) ease-out;
}

.animate-bounce-subtle {
  animation: bounceSubtle 0.6s ease-in-out;
}

/* Component-specific styles */
.monaco-editor {
  font-family: var(--font-family-mono) !important;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --tw-border-opacity: 1;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark mode CSS variables */
[data-theme="dark"] {
  --color-pdf: #f87171;
  --color-excel: #34d399;
  --color-word: #60a5fa;
  --color-csv: #a78bfa;
  --color-image: #fb923c;
  --color-text: #9ca3af;
}

/* Electron specific styles */
.drag-region {
  -webkit-app-region: drag;
  user-select: none;
}

.no-drag {
  -webkit-app-region: no-drag;
}

/* Enhanced focus styles */
.focus-ring:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

/* Selection styles */
::selection {
  background: rgba(59, 130, 246, 0.2);
  color: #1e3a8a;
}

::-moz-selection {
  background: rgba(59, 130, 246, 0.2);
  color: #1e3a8a;
}

/* Enhanced loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f8fafc 25%, #e2e8f0 50%, #f8fafc 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
  border-radius: 0.25rem;
}

@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Loading dots animation */
.loading-dots {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.loading-dots::after {
  content: "";
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: currentColor;
  animation: loading-dots 1.4s infinite ease-in-out both;
}

.loading-dots::before {
  content: "";
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: currentColor;
  margin-right: 0.25rem;
  animation: loading-dots 1.4s infinite ease-in-out both;
  animation-delay: -0.32s;
}

@keyframes loading-dots {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Document type indicators */
.doc-type-pdf {
  color: var(--color-pdf);
}

.doc-type-excel {
  color: var(--color-excel);
}

.doc-type-word {
  color: var(--color-word);
}

.doc-type-csv {
  color: var(--color-csv);
}

.doc-type-image {
  color: var(--color-image);
}

.doc-type-text {
  color: var(--color-text);
}

/* Glassmorphism effect */
.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .glass {
  background: rgba(31, 41, 55, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced shadows */
.shadow-soft {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.shadow-medium {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.shadow-strong {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.16);
}

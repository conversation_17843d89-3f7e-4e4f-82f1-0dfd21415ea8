import { v4 as uuidv4 } from 'uuid';
import {
  MergeResult,
  MergeConflict,
  ConflictResolution,
  ConflictType,
  ConflictSeverity,
  ResolutionStrategy,
  MergeStatistics,
  BranchId,
  CheckpointId,
} from '../../shared/types/Timeline';
import { DiffEngine } from './DiffEngine';

export interface MergeResolverOptions {
  autoResolveSimpleConflicts?: boolean;
  conflictThreshold?: number;
  enableThreeWayMerge?: boolean;
  preserveHistory?: boolean;
  validateResults?: boolean;
  maxConflictSize?: number;
}

export interface ThreeWayMergeInput {
  base: any;
  source: any;
  target: any;
  baseBranch?: BranchId;
  sourceBranch: BranchId;
  targetBranch: BranchId;
}

export interface MergeContext {
  mergeId: string;
  sourceBranch: BranchId;
  targetBranch: BranchId;
  baseCheckpoint?: CheckpointId;
  sourceCheckpoint: CheckpointId;
  targetCheckpoint: CheckpointId;
  timestamp: Date;
  author?: string;
}

export interface ConflictResolutionRule {
  id: string;
  name: string;
  description: string;
  conflictTypes: ConflictType[];
  strategy: ResolutionStrategy;
  condition: (conflict: MergeConflict) => boolean;
  resolver: (conflict: MergeConflict) => any;
  priority: number;
}

export interface MergeValidationResult {
  isValid: boolean;
  errors: MergeValidationError[];
  warnings: MergeValidationWarning[];
  suggestions: MergeValidationSuggestion[];
}

export interface MergeValidationError {
  id: string;
  type: 'syntax' | 'semantic' | 'structural' | 'data';
  message: string;
  location?: string;
  severity: 'error' | 'warning';
}

export interface MergeValidationWarning {
  id: string;
  message: string;
  location?: string;
  suggestion?: string;
}

export interface MergeValidationSuggestion {
  id: string;
  message: string;
  action: string;
  confidence: number;
}

export class MergeResolver {
  private options: Required<MergeResolverOptions>;
  private diffEngine: DiffEngine;
  private resolutionRules: ConflictResolutionRule[];

  constructor(options: MergeResolverOptions = {}) {
    this.options = {
      autoResolveSimpleConflicts: options.autoResolveSimpleConflicts ?? true,
      conflictThreshold: options.conflictThreshold || 0.1,
      enableThreeWayMerge: options.enableThreeWayMerge ?? true,
      preserveHistory: options.preserveHistory ?? true,
      validateResults: options.validateResults ?? true,
      maxConflictSize: options.maxConflictSize || 1000000, // 1MB
    };

    this.diffEngine = new DiffEngine();
    this.resolutionRules = this.initializeDefaultRules();
  }

  /**
   * Perform three-way merge with conflict detection and resolution
   */
  public async performThreeWayMerge(
    input: ThreeWayMergeInput,
    context: MergeContext
  ): Promise<MergeResult> {
    const startTime = Date.now();

    try {
      // Detect conflicts using three-way comparison
      const conflicts = await this.detectConflicts(input);

      // Attempt automatic resolution
      const resolutions = await this.resolveConflicts(conflicts);

      // Create merged result
      const mergedContent = await this.createMergedContent(input, resolutions);

      // Validate the merge result
      const validation = this.options.validateResults
        ? await this.validateMergeResult(mergedContent, input)
        : { isValid: true, errors: [], warnings: [], suggestions: [] };

      // Calculate statistics
      const statistics = this.calculateMergeStatistics(conflicts, resolutions, startTime);

      const result: MergeResult = {
        id: context.mergeId,
        sourceBranch: context.sourceBranch,
        targetBranch: context.targetBranch,
        resultCheckpoint: uuidv4(), // Would be generated by checkpoint system
        conflicts,
        resolutions,
        statistics,
        success:
          validation.isValid && resolutions.every(r => r.strategy !== ResolutionStrategy.SKIP),
        createdAt: new Date(),
      };

      return result;
    } catch (error) {
      throw new Error(`Merge failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Detect conflicts in three-way merge
   */
  private async detectConflicts(input: ThreeWayMergeInput): Promise<MergeConflict[]> {
    const conflicts: MergeConflict[] = [];

    // Compare base to source
    const baseToSource = await this.diffEngine.createDiff(input.base, input.source);

    // Compare base to target
    const baseToTarget = await this.diffEngine.createDiff(input.base, input.target);

    // Find conflicting changes
    for (const sourceChange of baseToSource.changes) {
      for (const targetChange of baseToTarget.changes) {
        if (this.changesConflict(sourceChange, targetChange)) {
          const conflict = this.createConflict(sourceChange, targetChange, input);
          conflicts.push(conflict);
        }
      }
    }

    return conflicts;
  }

  /**
   * Check if two changes conflict
   */
  private changesConflict(sourceChange: any, targetChange: any): boolean {
    // Changes conflict if they affect the same path but have different values
    return (
      sourceChange.path === targetChange.path &&
      sourceChange.afterValue !== targetChange.afterValue &&
      sourceChange.beforeValue === targetChange.beforeValue
    );
  }

  /**
   * Create conflict object from conflicting changes
   */
  private createConflict(
    sourceChange: any,
    targetChange: any,
    _input: ThreeWayMergeInput
  ): MergeConflict {
    const conflictType = this.determineConflictType(sourceChange, targetChange);
    const severity = this.determineConflictSeverity(sourceChange, targetChange);

    return {
      id: uuidv4(),
      type: conflictType,
      path: sourceChange.path,
      sourceValue: sourceChange.afterValue,
      targetValue: targetChange.afterValue,
      baseValue: sourceChange.beforeValue,
      description: this.generateConflictDescription(sourceChange, targetChange),
      severity,
    };
  }

  /**
   * Determine conflict type
   */
  private determineConflictType(sourceChange: any, targetChange: any): ConflictType {
    if (typeof sourceChange.afterValue !== typeof targetChange.afterValue) {
      return ConflictType.STRUCTURE;
    }

    if (sourceChange.path.includes('metadata')) {
      return ConflictType.METADATA;
    }

    return ConflictType.CONTENT;
  }

  /**
   * Determine conflict severity
   */
  private determineConflictSeverity(sourceChange: any, targetChange: any): ConflictSeverity {
    // Critical if both changes are deletions
    if (sourceChange.type === 'deleted' && targetChange.type === 'deleted') {
      return ConflictSeverity.CRITICAL;
    }

    // High if one is deletion, one is modification
    if ((sourceChange.type === 'deleted') !== (targetChange.type === 'deleted')) {
      return ConflictSeverity.HIGH;
    }

    // Medium for most content conflicts
    return ConflictSeverity.MEDIUM;
  }

  /**
   * Generate human-readable conflict description
   */
  private generateConflictDescription(sourceChange: any, targetChange: any): string {
    const path = sourceChange.path;
    const sourceAction = this.getChangeAction(sourceChange);
    const targetAction = this.getChangeAction(targetChange);

    return `Conflict at ${path}: Source ${sourceAction}, Target ${targetAction}`;
  }

  /**
   * Get human-readable change action
   */
  private getChangeAction(change: any): string {
    switch (change.type) {
      case 'added':
        return 'added content';
      case 'deleted':
        return 'deleted content';
      case 'modified':
        return 'modified content';
      default:
        return 'changed content';
    }
  }

  /**
   * Resolve conflicts using automatic and manual strategies
   */
  private async resolveConflicts(conflicts: MergeConflict[]): Promise<ConflictResolution[]> {
    const resolutions: ConflictResolution[] = [];

    for (const conflict of conflicts) {
      const resolution = await this.resolveConflict(conflict);
      resolutions.push(resolution);
    }

    return resolutions;
  }

  /**
   * Resolve a single conflict
   */
  private async resolveConflict(conflict: MergeConflict): Promise<ConflictResolution> {
    // Try automatic resolution first
    if (this.options.autoResolveSimpleConflicts) {
      const autoResolution = this.attemptAutomaticResolution(conflict);
      if (autoResolution) {
        return autoResolution;
      }
    }

    // Apply resolution rules
    for (const rule of this.resolutionRules) {
      if (rule.conflictTypes.includes(conflict.type) && rule.condition(conflict)) {
        const resolvedValue = rule.resolver(conflict);
        return {
          conflictId: conflict.id,
          strategy: rule.strategy,
          resolvedValue,
          reasoning: rule.description,
          confidence: this.calculateResolutionConfidence(rule, conflict),
          isManual: false,
          resolvedAt: new Date(),
        };
      }
    }

    // Default to manual resolution required
    return {
      conflictId: conflict.id,
      strategy: ResolutionStrategy.SKIP,
      resolvedValue: null,
      reasoning: 'Manual resolution required',
      confidence: 0,
      isManual: true,
      resolvedAt: new Date(),
    };
  }

  /**
   * Attempt automatic resolution for simple conflicts
   */
  private attemptAutomaticResolution(conflict: MergeConflict): ConflictResolution | null {
    // Auto-resolve if one side is empty/null
    if (!conflict.sourceValue && conflict.targetValue) {
      return {
        conflictId: conflict.id,
        strategy: ResolutionStrategy.TAKE_TARGET,
        resolvedValue: conflict.targetValue,
        reasoning: 'Source is empty, taking target value',
        confidence: 0.9,
        isManual: false,
        resolvedAt: new Date(),
      };
    }

    if (conflict.sourceValue && !conflict.targetValue) {
      return {
        conflictId: conflict.id,
        strategy: ResolutionStrategy.TAKE_SOURCE,
        resolvedValue: conflict.sourceValue,
        reasoning: 'Target is empty, taking source value',
        confidence: 0.9,
        isManual: false,
        resolvedAt: new Date(),
      };
    }

    // Auto-resolve if values are similar (for strings)
    if (typeof conflict.sourceValue === 'string' && typeof conflict.targetValue === 'string') {
      const similarity = this.calculateStringSimilarity(conflict.sourceValue, conflict.targetValue);
      if (similarity > 0.8) {
        // Take the longer string (likely more complete)
        const resolvedValue =
          conflict.sourceValue.length > conflict.targetValue.length
            ? conflict.sourceValue
            : conflict.targetValue;

        return {
          conflictId: conflict.id,
          strategy: ResolutionStrategy.TAKE_SOURCE,
          resolvedValue,
          reasoning: 'Values are similar, taking longer version',
          confidence: similarity,
          isManual: false,
          resolvedAt: new Date(),
        };
      }
    }

    return null;
  }

  /**
   * Calculate string similarity (simple implementation)
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1.0;

    const editDistance = this.calculateEditDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /**
   * Calculate edit distance between two strings
   */
  private calculateEditDistance(str1: string, str2: string): number {
    const matrix: number[][] = Array(str2.length + 1)
      .fill(null)
      .map(() => Array(str1.length + 1).fill(0));

    for (let i = 0; i <= str1.length; i++) matrix[0]![i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j]![0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j]![i] = Math.min(
          matrix[j]![i - 1]! + 1, // deletion
          matrix[j - 1]![i]! + 1, // insertion
          matrix[j - 1]![i - 1]! + indicator // substitution
        );
      }
    }

    return matrix[str2.length]![str1.length]!;
  }

  /**
   * Calculate resolution confidence based on rule and conflict
   */
  private calculateResolutionConfidence(
    rule: ConflictResolutionRule,
    conflict: MergeConflict
  ): number {
    let confidence = 0.5; // Base confidence

    // Increase confidence for exact type matches
    if (rule.conflictTypes.includes(conflict.type)) {
      confidence += 0.2;
    }

    // Increase confidence for lower severity conflicts
    switch (conflict.severity) {
      case ConflictSeverity.LOW:
        confidence += 0.3;
        break;
      case ConflictSeverity.MEDIUM:
        confidence += 0.2;
        break;
      case ConflictSeverity.HIGH:
        confidence += 0.1;
        break;
      case ConflictSeverity.CRITICAL:
        confidence += 0.0;
        break;
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * Create merged content from input and resolutions
   */
  private async createMergedContent(
    input: ThreeWayMergeInput,
    resolutions: ConflictResolution[]
  ): Promise<any> {
    let mergedContent = JSON.parse(JSON.stringify(input.target)); // Start with target

    // Apply resolutions
    for (const resolution of resolutions) {
      if (resolution.strategy !== ResolutionStrategy.SKIP) {
        const conflict = await this.findConflictById(resolution.conflictId, input);
        if (conflict) {
          mergedContent = this.applyResolution(mergedContent, conflict, resolution);
        }
      }
    }

    return mergedContent;
  }

  /**
   * Find conflict by ID (helper method)
   */
  private async findConflictById(
    _conflictId: string,
    _input: ThreeWayMergeInput
  ): Promise<MergeConflict | null> {
    // This would typically be stored during conflict detection
    // For now, return null as a placeholder
    return null;
  }

  /**
   * Apply resolution to merged content
   */
  private applyResolution(
    content: any,
    conflict: MergeConflict,
    resolution: ConflictResolution
  ): any {
    const pathParts = conflict.path.split('.');
    let current = content;

    // Navigate to the parent of the target property
    for (let i = 0; i < pathParts.length - 1; i++) {
      const key = pathParts[i];
      if (key && !current[key]) {
        current[key] = {};
      }
      if (key) {
        current = current[key];
      }
    }

    // Apply the resolved value
    const finalKey = pathParts[pathParts.length - 1];
    if (finalKey) {
      current[finalKey] = resolution.resolvedValue;
    }

    return content;
  }

  /**
   * Validate merge result
   */
  private async validateMergeResult(
    mergedContent: any,
    input: ThreeWayMergeInput
  ): Promise<MergeValidationResult> {
    const errors: MergeValidationError[] = [];
    const warnings: MergeValidationWarning[] = [];
    const suggestions: MergeValidationSuggestion[] = [];

    // Validate structure
    try {
      JSON.stringify(mergedContent);
    } catch (error) {
      errors.push({
        id: uuidv4(),
        type: 'structural',
        message: 'Merged content is not valid JSON',
        severity: 'error',
      });
    }

    // Validate against source and target
    const sourceKeys = Object.keys(input.source || {});
    const targetKeys = Object.keys(input.target || {});
    const mergedKeys = Object.keys(mergedContent || {});

    // Check for missing keys
    for (const key of [...sourceKeys, ...targetKeys]) {
      if (!mergedKeys.includes(key)) {
        warnings.push({
          id: uuidv4(),
          message: `Key '${key}' from source/target is missing in merged result`,
          location: key,
          suggestion: 'Consider adding this key to the merged result',
        });
      }
    }

    // Check for unexpected keys
    for (const key of mergedKeys) {
      if (!sourceKeys.includes(key) && !targetKeys.includes(key)) {
        suggestions.push({
          id: uuidv4(),
          message: `New key '${key}' was added during merge`,
          action: 'Review if this key should be included',
          confidence: 0.7,
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
    };
  }

  /**
   * Calculate merge statistics
   */
  private calculateMergeStatistics(
    conflicts: MergeConflict[],
    resolutions: ConflictResolution[],
    startTime: number
  ): MergeStatistics {
    const totalConflicts = conflicts.length;
    const resolvedConflicts = resolutions.filter(
      r => r.strategy !== ResolutionStrategy.SKIP
    ).length;
    const unresolvedConflicts = totalConflicts - resolvedConflicts;
    const automaticResolutions = resolutions.filter(r => !r.isManual).length;
    const manualResolutions = resolutions.filter(r => r.isManual).length;
    const processingTime = Date.now() - startTime;

    return {
      totalConflicts,
      resolvedConflicts,
      unresolvedConflicts,
      automaticResolutions,
      manualResolutions,
      processingTime,
    };
  }

  /**
   * Initialize default resolution rules
   */
  private initializeDefaultRules(): ConflictResolutionRule[] {
    return [
      {
        id: 'prefer-newer-timestamps',
        name: 'Prefer Newer Timestamps',
        description: 'For timestamp conflicts, prefer the newer value',
        conflictTypes: [ConflictType.METADATA],
        strategy: ResolutionStrategy.CUSTOM,
        condition: conflict =>
          conflict.path.includes('timestamp') || conflict.path.includes('date'),
        resolver: conflict => {
          const sourceDate = new Date(conflict.sourceValue as string);
          const targetDate = new Date(conflict.targetValue as string);
          return sourceDate > targetDate ? conflict.sourceValue : conflict.targetValue;
        },
        priority: 1,
      },
      {
        id: 'merge-arrays',
        name: 'Merge Arrays',
        description: 'For array conflicts, merge unique values',
        conflictTypes: [ConflictType.CONTENT],
        strategy: ResolutionStrategy.MERGE_BOTH,
        condition: conflict =>
          Array.isArray(conflict.sourceValue) && Array.isArray(conflict.targetValue),
        resolver: conflict => {
          const sourceArray = conflict.sourceValue as any[];
          const targetArray = conflict.targetValue as any[];
          return [...new Set([...sourceArray, ...targetArray])];
        },
        priority: 2,
      },
      {
        id: 'prefer-non-empty',
        name: 'Prefer Non-Empty Values',
        description: 'For conflicts where one value is empty, prefer the non-empty value',
        conflictTypes: [ConflictType.CONTENT, ConflictType.METADATA],
        strategy: ResolutionStrategy.CUSTOM,
        condition: conflict => !conflict.sourceValue || !conflict.targetValue,
        resolver: conflict => conflict.sourceValue || conflict.targetValue,
        priority: 3,
      },
      {
        id: 'prefer-longer-strings',
        name: 'Prefer Longer Strings',
        description: 'For string conflicts, prefer the longer string (likely more complete)',
        conflictTypes: [ConflictType.CONTENT],
        strategy: ResolutionStrategy.CUSTOM,
        condition: conflict =>
          typeof conflict.sourceValue === 'string' &&
          typeof conflict.targetValue === 'string' &&
          Math.abs(conflict.sourceValue.length - conflict.targetValue.length) > 10,
        resolver: conflict => {
          const sourceLength = (conflict.sourceValue as string).length;
          const targetLength = (conflict.targetValue as string).length;
          return sourceLength > targetLength ? conflict.sourceValue : conflict.targetValue;
        },
        priority: 4,
      },
    ];
  }

  /**
   * Add custom resolution rule
   */
  public addResolutionRule(rule: ConflictResolutionRule): void {
    this.resolutionRules.push(rule);
    this.resolutionRules.sort((a, b) => a.priority - b.priority);
  }

  /**
   * Remove resolution rule
   */
  public removeResolutionRule(ruleId: string): void {
    this.resolutionRules = this.resolutionRules.filter(rule => rule.id !== ruleId);
  }

  /**
   * Get all resolution rules
   */
  public getResolutionRules(): ConflictResolutionRule[] {
    return [...this.resolutionRules];
  }

  /**
   * Manually resolve conflict
   */
  public createManualResolution(
    conflictId: string,
    strategy: ResolutionStrategy,
    resolvedValue: any,
    reasoning: string,
    resolvedBy: string
  ): ConflictResolution {
    return {
      conflictId,
      strategy,
      resolvedValue,
      reasoning,
      confidence: 1.0, // Manual resolutions have full confidence
      isManual: true,
      resolvedBy,
      resolvedAt: new Date(),
    };
  }

  /**
   * Test merge without applying changes
   */
  public async testMerge(
    input: ThreeWayMergeInput,
    _context: MergeContext
  ): Promise<{ conflicts: MergeConflict[]; preview: any }> {
    const conflicts = await this.detectConflicts(input);
    const resolutions = await this.resolveConflicts(conflicts);
    const preview = await this.createMergedContent(input, resolutions);

    return { conflicts, preview };
  }
}

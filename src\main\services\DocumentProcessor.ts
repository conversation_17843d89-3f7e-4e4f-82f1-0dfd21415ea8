import { createHash } from 'crypto';
import { EventEmitter } from 'events';
import { fileTypeFromBuffer } from 'file-type';
import {
  Document,
  DocumentType,
  ExtractedData,
  ProcessedDocument,
  ProcessingOptions,
  ProcessingResult,
  ValidationResult,
} from '../../shared/types/Document';

/**
 * Custom error class for document processing errors
 */
export class DocumentProcessingError extends Error {
  public readonly code: string;
  public readonly documentId?: string;
  public readonly processingStep?: string;
  public readonly originalError?: Error;

  constructor(
    message: string,
    code: string,
    documentId?: string,
    processingStep?: string,
    originalError?: Error
  ) {
    super(message);
    this.name = 'DocumentProcessingError';
    this.code = code;
    if (documentId !== undefined) this.documentId = documentId;
    if (processingStep !== undefined) this.processingStep = processingStep;
    if (originalError !== undefined) this.originalError = originalError;

    // Maintain proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, DocumentProcessingError);
    }
  }

  /**
   * Convert error to JSON for logging and serialization
   */
  toJSON(): Record<string, unknown> {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      documentId: this.documentId,
      processingStep: this.processingStep,
      stack: this.stack,
      originalError: this.originalError
        ? {
            name: this.originalError.name,
            message: this.originalError.message,
            stack: this.originalError.stack,
          }
        : undefined,
    };
  }
}

/**
 * Progress reporting interface for long-running operations
 */
export interface ProcessingProgress {
  documentId: string;
  step: string;
  progress: number; // 0-100
  message: string;
  startTime: Date;
  estimatedTimeRemaining?: number;
  currentOperation?: string;
  totalOperations?: number;
  completedOperations?: number;
}

/**
 * Document processor capabilities interface
 */
export interface ProcessorCapabilities {
  supportedTypes: DocumentType[];
  canExtractText: boolean;
  canExtractImages: boolean;
  canExtractTables: boolean;
  canDetectFormFields: boolean;
  canPerformOCR: boolean;
  canPreserveFormatting: boolean;
  maxFileSize: number; // in bytes
  supportedEncodings: string[];
  requiresNetwork: boolean;
  processingTimeEstimate: number; // average seconds per MB
}

/**
 * Abstract base class for all document processors
 * Provides common functionality and defines the interface for specific processors
 */
export abstract class DocumentProcessor extends EventEmitter {
  protected readonly processorName: string;
  protected readonly capabilities: ProcessorCapabilities;
  protected readonly maxRetries: number = 3;
  protected readonly retryDelay: number = 1000; // milliseconds

  constructor(processorName: string, capabilities: ProcessorCapabilities) {
    super();
    this.processorName = processorName;
    this.capabilities = capabilities;
  }

  /**
   * Get processor capabilities
   */
  public getCapabilities(): ProcessorCapabilities {
    return { ...this.capabilities };
  }

  /**
   * Check if processor can handle the given document type
   */
  public canProcess(documentType: DocumentType): boolean {
    return this.capabilities.supportedTypes.includes(documentType);
  }

  /**
   * Detect document type from buffer using file-type library
   */
  public async detectDocumentType(buffer: Buffer, filename?: string): Promise<DocumentType> {
    try {
      // First try to detect using file-type library
      const fileType = await fileTypeFromBuffer(buffer);

      if (fileType) {
        const mimeType = fileType.mime as string;
        switch (mimeType) {
          case 'application/pdf':
            return DocumentType.PDF;
          case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
          case 'application/msword':
            return DocumentType.WORD;
          case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
          case 'application/vnd.ms-excel':
            return DocumentType.EXCEL;
          case 'text/csv':
            return DocumentType.CSV;
          case 'image/jpeg':
          case 'image/png':
          case 'image/tiff':
          case 'image/bmp':
          case 'image/webp':
            return DocumentType.IMAGE;
          case 'text/plain':
            return DocumentType.TEXT;
          default:
            break;
        }
      }

      // Fallback to filename extension if available
      if (filename) {
        const extension = filename.toLowerCase().split('.').pop();
        switch (extension) {
          case 'pdf':
            return DocumentType.PDF;
          case 'docx':
          case 'doc':
            return DocumentType.WORD;
          case 'xlsx':
          case 'xls':
            return DocumentType.EXCEL;
          case 'csv':
            return DocumentType.CSV;
          case 'jpg':
          case 'jpeg':
          case 'png':
          case 'tiff':
          case 'tif':
          case 'bmp':
          case 'webp':
            return DocumentType.IMAGE;
          case 'txt':
            return DocumentType.TEXT;
          default:
            break;
        }
      }

      // Additional heuristics for CSV detection
      if (this.isLikelyCSV(buffer)) {
        return DocumentType.CSV;
      }

      return DocumentType.UNKNOWN;
    } catch (error) {
      throw new DocumentProcessingError(
        'Failed to detect document type',
        'TYPE_DETECTION_FAILED',
        undefined,
        'type_detection',
        error as Error
      );
    }
  }

  /**
   * Heuristic to detect CSV files that might not have proper MIME type
   */
  private isLikelyCSV(buffer: Buffer): boolean {
    try {
      const text = buffer.toString('utf8', 0, Math.min(1024, buffer.length));
      const lines = text.split('\n').slice(0, 5); // Check first 5 lines

      if (lines.length < 2) return false;

      // Check for common CSV patterns
      const firstLine = lines[0];
      if (!firstLine) return false;

      const commaCount = firstLine.split(',').length;
      const semicolonCount = firstLine.split(';').length;
      const tabCount = firstLine.split('\t').length;

      const maxDelimiterCount = Math.max(commaCount, semicolonCount, tabCount);

      // Must have at least 2 columns
      if (maxDelimiterCount < 2) return false;

      // Check consistency across lines
      let consistentLines = 0;
      for (const line of lines) {
        if (line.trim().length === 0) continue;

        const lineCommaCount = line.split(',').length;
        const lineSemicolonCount = line.split(';').length;
        const lineTabCount = line.split('\t').length;

        if (
          lineCommaCount === commaCount ||
          lineSemicolonCount === semicolonCount ||
          lineTabCount === tabCount
        ) {
          consistentLines++;
        }
      }

      // At least 80% of lines should have consistent delimiter count
      return consistentLines / lines.length >= 0.8;
    } catch {
      return false;
    }
  }

  /**
   * Generate content hash for document integrity checking
   */
  public generateContentHash(buffer: Buffer): string {
    return createHash('sha256').update(buffer).digest('hex');
  }

  /**
   * Validate document before processing
   */
  public async validateDocument(buffer: Buffer, filename?: string): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    try {
      // Check file size
      if (buffer.length > this.capabilities.maxFileSize) {
        results.push({
          fieldId: 'file_size',
          isValid: false,
          errors: [
            {
              code: 'FILE_TOO_LARGE',
              message: `File size ${buffer.length} bytes exceeds maximum allowed size of ${this.capabilities.maxFileSize} bytes`,
              severity: 'error',
            },
          ],
          warnings: [],
        });
      }

      // Check if buffer is empty
      if (buffer.length === 0) {
        results.push({
          fieldId: 'file_content',
          isValid: false,
          errors: [
            {
              code: 'EMPTY_FILE',
              message: 'File is empty',
              severity: 'error',
            },
          ],
          warnings: [],
        });
      }

      // Detect and validate document type
      const documentType = await this.detectDocumentType(buffer, filename);

      if (documentType === DocumentType.UNKNOWN) {
        results.push({
          fieldId: 'document_type',
          isValid: false,
          errors: [
            {
              code: 'UNKNOWN_DOCUMENT_TYPE',
              message: 'Unable to determine document type',
              severity: 'error',
            },
          ],
          warnings: [],
        });
      } else if (!this.canProcess(documentType)) {
        results.push({
          fieldId: 'document_type',
          isValid: false,
          errors: [
            {
              code: 'UNSUPPORTED_DOCUMENT_TYPE',
              message: `Document type ${documentType} is not supported by this processor`,
              severity: 'error',
            },
          ],
          warnings: [],
        });
      }

      // Check for potential corruption (basic heuristics)
      if (await this.isLikelyCorrupted(buffer, documentType)) {
        results.push({
          fieldId: 'file_integrity',
          isValid: false,
          errors: [
            {
              code: 'POTENTIALLY_CORRUPTED',
              message: 'File may be corrupted or incomplete',
              severity: 'warning',
            },
          ],
          warnings: [],
        });
      }
    } catch (error) {
      results.push({
        fieldId: 'validation',
        isValid: false,
        errors: [
          {
            code: 'VALIDATION_ERROR',
            message: `Validation failed: ${(error as Error).message}`,
            severity: 'error',
          },
        ],
        warnings: [],
      });
    }

    return results;
  }

  /**
   * Basic corruption detection heuristics
   */
  private async isLikelyCorrupted(buffer: Buffer, documentType: DocumentType): Promise<boolean> {
    try {
      switch (documentType) {
        case DocumentType.PDF:
          // PDF should start with %PDF- and end with %%EOF
          const pdfStart = buffer.toString('ascii', 0, 5);
          const pdfEnd = buffer.toString('ascii', -6);
          return !pdfStart.startsWith('%PDF-') || !pdfEnd.includes('%%EOF');

        case DocumentType.EXCEL:
          // XLSX files are ZIP archives, should have PK signature
          return buffer[0] !== 0x50 || buffer[1] !== 0x4b;

        case DocumentType.WORD:
          // DOCX files are also ZIP archives
          return buffer[0] !== 0x50 || buffer[1] !== 0x4b;

        default:
          return false;
      }
    } catch {
      return false;
    }
  }

  /**
   * Main processing method with retry logic and progress reporting
   */
  public async processDocument(
    buffer: Buffer,
    filename: string,
    options: ProcessingOptions
  ): Promise<ProcessedDocument> {
    const documentId = this.generateDocumentId(buffer, filename);
    const startTime = new Date();

    this.emitProgress({
      documentId,
      step: 'validation',
      progress: 0,
      message: 'Validating document...',
      startTime,
    });

    try {
      // Validate document first
      const validationResults = await this.validateDocument(buffer, filename);
      const hasErrors = validationResults.some(result =>
        result.errors.some(error => error.severity === 'error')
      );

      if (hasErrors) {
        throw new DocumentProcessingError(
          'Document validation failed',
          'VALIDATION_FAILED',
          documentId,
          'validation'
        );
      }

      this.emitProgress({
        documentId,
        step: 'type_detection',
        progress: 10,
        message: 'Detecting document type...',
        startTime,
      });

      // Detect document type
      const documentType = await this.detectDocumentType(buffer, filename);

      this.emitProgress({
        documentId,
        step: 'processing',
        progress: 20,
        message: 'Processing document...',
        startTime,
      });

      // Process with retry logic
      let lastError: Error | undefined;
      for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
        try {
          const result = await this.processDocumentInternal(
            buffer,
            filename,
            documentType,
            options,
            documentId,
            startTime
          );

          this.emitProgress({
            documentId,
            step: 'completed',
            progress: 100,
            message: 'Processing completed successfully',
            startTime,
          });

          return result;
        } catch (error) {
          lastError = error as Error;

          if (attempt < this.maxRetries) {
            this.emitProgress({
              documentId,
              step: 'retry',
              progress: 20 + attempt * 10,
              message: `Retry attempt ${attempt}/${this.maxRetries}...`,
              startTime,
            });

            await this.delay(this.retryDelay * attempt);
          }
        }
      }

      throw new DocumentProcessingError(
        `Processing failed after ${this.maxRetries} attempts`,
        'MAX_RETRIES_EXCEEDED',
        documentId,
        'processing',
        lastError
      );
    } catch (error) {
      this.emitProgress({
        documentId,
        step: 'error',
        progress: 0,
        message: `Processing failed: ${(error as Error).message}`,
        startTime,
      });

      if (error instanceof DocumentProcessingError) {
        throw error;
      }

      throw new DocumentProcessingError(
        'Document processing failed',
        'PROCESSING_FAILED',
        documentId,
        'processing',
        error as Error
      );
    }
  }

  /**
   * Internal processing method to be implemented by subclasses
   */
  private async processDocumentInternal(
    buffer: Buffer,
    filename: string,
    documentType: DocumentType,
    options: ProcessingOptions,
    documentId: string,
    startTime: Date
  ): Promise<ProcessedDocument> {
    // Create document metadata
    const document: Document = {
      id: documentId,
      name: filename,
      path: filename,
      type: documentType,
      size: buffer.length,
      createdAt: startTime,
      updatedAt: startTime,
      metadata: {
        hasFormFields: false,
        isEncrypted: false,
        permissions: {
          canPrint: true,
          canModify: true,
          canCopy: true,
          canAnnotate: true,
          canFillForms: true,
        },
      },
    };

    // Extract text
    const text = await this.extractText(buffer, options);

    // Extract structured data
    const structuredData = await this.extractStructuredData(buffer, options);

    // Validate extraction
    const extractionValidation = await this.validateExtraction(structuredData);

    // Create processing result
    const processingResult: ProcessingResult = {
      documentId,
      success: true,
      content: {
        text,
        pages: [],
        formFields: [],
        images: [],
        tables: [],
      },
      processingTime: Date.now() - startTime.getTime(),
      confidence: this.calculateOverallConfidence(structuredData),
      warnings: extractionValidation
        .filter(v => !v.isValid)
        .map(v => v.errors.map(e => e.message).join('; ')),
    };

    return {
      document,
      extractedData: structuredData,
      processingResult,
      knowledgeEntries: [],
      templateMatches: [],
      validationResults: extractionValidation,
    };
  }

  /**
   * Generate unique document ID
   */
  private generateDocumentId(buffer: Buffer, _filename: string): string {
    const hash = this.generateContentHash(buffer);
    const timestamp = Date.now();
    return `doc_${hash.substring(0, 8)}_${timestamp}`;
  }

  /**
   * Calculate overall confidence score from extracted data
   */
  private calculateOverallConfidence(extractedData: ExtractedData[]): number {
    if (extractedData.length === 0) return 0;

    const totalConfidence = extractedData.reduce((sum, data) => sum + data.confidence, 0);
    return totalConfidence / extractedData.length;
  }

  /**
   * Emit progress event
   */
  private emitProgress(progress: ProcessingProgress): void {
    this.emit('progress', progress);
  }

  /**
   * Delay utility for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Abstract methods to be implemented by subclasses

  /**
   * Extract text content from document
   */
  public abstract extractText(buffer: Buffer, options: ProcessingOptions): Promise<string>;

  /**
   * Extract structured data from document
   */
  public abstract extractStructuredData(
    buffer: Buffer,
    options: ProcessingOptions
  ): Promise<ExtractedData[]>;

  /**
   * Validate extracted data
   */
  public abstract validateExtraction(extractedData: ExtractedData[]): Promise<ValidationResult[]>;
}

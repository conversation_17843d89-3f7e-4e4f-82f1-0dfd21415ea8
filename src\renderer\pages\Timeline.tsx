import React from 'react';
import { useAppNavigation } from '../router/RouterProvider';

const Timeline: React.FC = () => {
  const { navigate } = useAppNavigation();

  return (
    <div className="min-h-screen bg-base-100 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <button
            className="btn btn-ghost btn-sm mb-4"
            onClick={() => navigate('/')}
          >
            ← Back to Dashboard
          </button>
          <h1 className="text-3xl font-bold text-base-content">Timeline</h1>
          <p className="text-base-content/70">Document version history and changes</p>
        </div>

        <div className="bg-base-200 rounded-lg p-8 text-center">
          <div className="w-16 h-16 bg-success/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-8 h-8 text-success"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h2 className="text-xl font-semibold mb-2">Timeline</h2>
          <p className="text-base-content/70">
            This page will show document version history, changes, and timeline visualization.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Timeline;

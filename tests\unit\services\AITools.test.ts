import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import {
  CalculatorTool,
  DocumentAnalysisTool,
  FormFillingTool,
  KnowledgeSearchTool,
  ToolRegistry,
  ToolExecutor,
  AIToolsManager,
  aiToolsManager,
} from '../../../src/main/services/AITools';
import { ToolCategory } from '../../../src/shared/types/AI';

// Mock dependencies
jest.mock('../../../src/main/utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

jest.mock('../../../src/main/services/ChromaKnowledgeBase', () => ({
  chromaKnowledgeBase: {
    semanticSearch: jest.fn().mockResolvedValue([
      {
        item: { content: 'Test knowledge result' },
        relevance: 0.85,
      },
    ]),
  },
}));

describe('AI Tools Framework', () => {
  describe('CalculatorTool', () => {
    let calculatorTool: CalculatorTool;

    beforeEach(() => {
      calculatorTool = new CalculatorTool();
    });

    it('should have correct metadata', () => {
      expect(calculatorTool.id).toBe('calculator');
      expect(calculatorTool.name).toBe('calculator');
      expect(calculatorTool.metadata.category).toBe(ToolCategory.CALCULATION);
      expect(calculatorTool.getCapabilities()).toContain('basic_math');
    });

    it('should perform basic calculations', async () => {
      const result = await calculatorTool.execute({
        parameters: {
          expression: '2 + 3 * 4',
          type: 'basic',
          precision: 2,
        },
      });

      expect(result.success).toBe(true);
      expect(result.result).toHaveProperty('value', 14);
      expect(result.result).toHaveProperty('expression', '2 + 3 * 4');
    });

    it('should handle financial calculations', async () => {
      const result = await calculatorTool.execute({
        parameters: {
          expression: 'compound_interest(1000, 0.05, 12, 5)',
          type: 'financial',
          precision: 2,
        },
      });

      expect(result.success).toBe(true);
      expect(result.result).toHaveProperty('value');
      expect(result.result).toHaveProperty('type', 'financial');
    });

    it('should validate input', async () => {
      const isValid = await calculatorTool.validate({
        parameters: {
          expression: '2 + 3',
        },
      });

      expect(isValid).toBe(true);

      const isInvalid = await calculatorTool.validate({
        parameters: {
          expression: 'function test() { return 1; }',
        },
      });

      expect(isInvalid).toBe(false);
    });

    it('should handle errors gracefully', async () => {
      const result = await calculatorTool.execute({
        parameters: {
          expression: 'invalid expression',
        },
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to evaluate expression');
    });
  });

  describe('DocumentAnalysisTool', () => {
    let documentTool: DocumentAnalysisTool;

    beforeEach(() => {
      documentTool = new DocumentAnalysisTool();
    });

    it('should have correct metadata', () => {
      expect(documentTool.id).toBe('document_analyzer');
      expect(documentTool.metadata.category).toBe(ToolCategory.ANALYSIS);
      expect(documentTool.getCapabilities()).toContain('document_summarization');
    });

    it('should generate document summary', async () => {
      const testContent =
        'This is a test document. It contains multiple sentences. The content is used for testing purposes. We want to verify summarization works correctly.';

      const result = await documentTool.execute({
        parameters: {
          content: testContent,
          analysisType: 'summary',
          options: { maxSentences: 2 },
        },
      });

      expect(result.success).toBe(true);
      expect(result.result).toHaveProperty('summary');
      expect(result.result).toHaveProperty('sentenceCount');
      expect(result.result).toHaveProperty('compressionRatio');
    });

    it('should extract entities', async () => {
      const testContent =
        'Contact John <NAME_EMAIL> or call ************. The meeting is on 12/25/2023 and costs $500.';

      const result = await documentTool.execute({
        parameters: {
          content: testContent,
          analysisType: 'entities',
          options: { includePositions: true },
        },
      });

      expect(result.success).toBe(true);
      expect(result.result).toHaveProperty('entities');
      expect(result.result).toHaveProperty('confidence');
      expect(result.result).toHaveProperty('totalEntities');
    });

    it('should analyze sentiment', async () => {
      const testContent =
        'This is a great product! I love it and would highly recommend it to others. Excellent quality and amazing service.';

      const result = await documentTool.execute({
        parameters: {
          content: testContent,
          analysisType: 'sentiment',
          options: { includeEmotions: true },
        },
      });

      expect(result.success).toBe(true);
      expect(result.result).toHaveProperty('sentiment');
      expect(result.result).toHaveProperty('polarity');
      expect(result.result).toHaveProperty('confidence');
      expect(result.result).toHaveProperty('emotions');
    });

    it('should classify documents', async () => {
      const testContent =
        'Tax return form 1040. Income: $50,000. Deductions: $12,000. Withholding: $8,000. Refund expected.';

      const result = await documentTool.execute({
        parameters: {
          content: testContent,
          analysisType: 'classification',
        },
      });

      expect(result.success).toBe(true);
      expect(result.result).toHaveProperty('category');
      expect(result.result).toHaveProperty('confidence');
      expect(result.result).toHaveProperty('details');
    });

    it('should perform comprehensive analysis', async () => {
      const testContent = 'This is a comprehensive test document for analysis.';

      const result = await documentTool.execute({
        parameters: {
          content: testContent,
          analysisType: 'comprehensive',
        },
      });

      expect(result.success).toBe(true);
      expect(result.result).toHaveProperty('summary');
      expect(result.result).toHaveProperty('entities');
      expect(result.result).toHaveProperty('sentiment');
      expect(result.result).toHaveProperty('classification');
      expect(result.result).toHaveProperty('quality');
    });
  });

  describe('FormFillingTool', () => {
    let formTool: FormFillingTool;

    beforeEach(() => {
      formTool = new FormFillingTool();
    });

    it('should have correct metadata', () => {
      expect(formTool.id).toBe('form_filler');
      expect(formTool.metadata.category).toBe(ToolCategory.FORM_FILLING);
      expect(formTool.getCapabilities()).toContain('form_field_detection');
    });

    it('should fill form fields', async () => {
      const formFields = [
        { id: 'name', name: 'full_name', type: 'text', required: true },
        { id: 'email', name: 'email_address', type: 'email', required: true },
        { id: 'phone', name: 'phone_number', type: 'phone', required: false },
      ];

      const availableData = {
        full_name: 'John Doe',
        email_address: '<EMAIL>',
        phone_number: '************',
      };

      const result = await formTool.execute({
        parameters: {
          formFields,
          availableData,
        },
      });

      expect(result.success).toBe(true);
      expect(result.result).toHaveProperty('filledFields');
      expect(result.result).toHaveProperty('confidence');
      expect(result.result).toHaveProperty('completionRate');
      expect(result.result.filledFields).toHaveProperty('name', 'John Doe');
      expect(result.result.filledFields).toHaveProperty('email', '<EMAIL>');
    });

    it('should validate form field values', async () => {
      const formFields = [
        {
          id: 'email',
          name: 'email',
          type: 'email',
          required: true,
          validation: { pattern: '^[^@]+@[^@]+\\.[^@]+$' },
        },
      ];

      const availableData = {
        email: 'invalid-email',
      };

      const result = await formTool.execute({
        parameters: {
          formFields,
          availableData,
        },
      });

      expect(result.success).toBe(true);
      expect(result.result.validationErrors).toHaveLength(1);
    });
  });

  describe('KnowledgeSearchTool', () => {
    let searchTool: KnowledgeSearchTool;

    beforeEach(() => {
      searchTool = new KnowledgeSearchTool();
    });

    it('should have correct metadata', () => {
      expect(searchTool.id).toBe('knowledge_search');
      expect(searchTool.metadata.category).toBe(ToolCategory.KNOWLEDGE_BASE);
      expect(searchTool.getCapabilities()).toContain('semantic_search');
    });

    it('should perform semantic search', async () => {
      const result = await searchTool.execute({
        parameters: {
          query: 'test query',
          collection: 'test_collection',
          limit: 3,
          threshold: 0.7,
        },
      });

      expect(result.success).toBe(true);
      expect(result.result).toHaveProperty('results');
      expect(result.result).toHaveProperty('summary');
      expect(result.result).toHaveProperty('searchMetadata');
    });

    it('should validate search parameters', async () => {
      const isValid = await searchTool.validate({
        parameters: {
          query: 'valid query',
          limit: 5,
          threshold: 0.8,
        },
      });

      expect(isValid).toBe(true);

      const isInvalid = await searchTool.validate({
        parameters: {
          query: '',
          limit: -1,
          threshold: 2.0,
        },
      });

      expect(isInvalid).toBe(false);
    });
  });

  describe('ToolRegistry', () => {
    let registry: ToolRegistry;

    beforeEach(() => {
      registry = new ToolRegistry();
    });

    it('should register and retrieve tools', () => {
      const tool = new CalculatorTool();
      registry.register(tool);

      expect(registry.getTool('calculator')).toBe(tool);
      expect(registry.getAllTools()).toContain(tool);
    });

    it('should organize tools by category', () => {
      const calculatorTool = new CalculatorTool();
      const documentTool = new DocumentAnalysisTool();

      registry.register(calculatorTool);
      registry.register(documentTool);

      const calculationTools = registry.getToolsByCategory(ToolCategory.CALCULATION);
      const analysisTools = registry.getToolsByCategory(ToolCategory.ANALYSIS);

      expect(calculationTools).toContain(calculatorTool);
      expect(analysisTools).toContain(documentTool);
    });

    it('should search tools by capability', () => {
      const calculatorTool = new CalculatorTool();
      registry.register(calculatorTool);

      const mathTools = registry.searchByCapability('basic_math');
      expect(mathTools).toContain(calculatorTool);
    });
  });

  describe('ToolExecutor', () => {
    let registry: ToolRegistry;
    let executor: ToolExecutor;

    beforeEach(() => {
      registry = new ToolRegistry();
      executor = new ToolExecutor(registry);
      registry.register(new CalculatorTool());
    });

    it('should execute tools successfully', async () => {
      const result = await executor.execute('calculator', {
        parameters: {
          expression: '5 + 3',
        },
      });

      expect(result.success).toBe(true);
      expect(result.result).toHaveProperty('value', 8);
    });

    it('should handle tool not found', async () => {
      const result = await executor.execute('nonexistent_tool', {
        parameters: {},
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Tool not found');
    });

    it('should maintain execution history', async () => {
      await executor.execute('calculator', {
        parameters: { expression: '1 + 1' },
      });

      const history = executor.getExecutionHistory();
      expect(history).toHaveLength(1);
      expect(history[0].toolId).toBe('calculator');
    });
  });

  describe('AIToolsManager', () => {
    it('should initialize with default tools', () => {
      const manager = new AIToolsManager();
      const tools = manager.discoverTools();

      expect(tools).toHaveLength(4);
      expect(tools.map(t => t.id)).toContain('calculator');
      expect(tools.map(t => t.id)).toContain('document_analyzer');
      expect(tools.map(t => t.id)).toContain('form_filler');
      expect(tools.map(t => t.id)).toContain('knowledge_search');
    });

    it('should execute tools through manager', async () => {
      const manager = new AIToolsManager();
      const result = await manager.executeTool('calculator', {
        parameters: {
          expression: '10 / 2',
        },
      });

      expect(result.success).toBe(true);
      expect(result.result).toHaveProperty('value', 5);
    });

    it('should find tools for capabilities', () => {
      const manager = new AIToolsManager();
      const mathTools = manager.getToolsForCapabilities(['basic_math']);

      expect(mathTools).toHaveLength(1);
      expect(mathTools[0].id).toBe('calculator');
    });
  });

  describe('Singleton Instance', () => {
    it('should provide singleton access', () => {
      expect(aiToolsManager).toBeInstanceOf(AIToolsManager);

      const tools = aiToolsManager.discoverTools();
      expect(tools.length).toBeGreaterThan(0);
    });
  });
});

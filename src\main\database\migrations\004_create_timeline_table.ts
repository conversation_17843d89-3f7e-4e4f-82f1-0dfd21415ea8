import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create timeline table for version control and change tracking
  await knex.schema.createTable('timeline', (table) => {
    // Primary key
    table.string('id', 36).primary().notNullable().comment('UUID primary key');

    // Action classification
    table.string('action_type', 100).notNullable().comment('Type of action performed');
    table.text('description').notNullable().comment('Human-readable description of the action');
    table.string('category', 50).comment('Action category (document, knowledge, form, etc.)');

    // State information (compressed for storage efficiency)
    table.binary('before_state').comment('Compressed state before the action');
    table.binary('after_state').comment('Compressed state after the action');
    table.string('compression_method', 50).defaultTo('lz4').comment('Compression method used for state data');
    table.integer('before_state_size').unsigned().comment('Uncompressed size of before_state');
    table.integer('after_state_size').unsigned().comment('Uncompressed size of after_state');

    // Affected entities
    table.json('affected_documents').comment('Array of document IDs affected by this action');
    table.json('affected_entities').comment('Other affected entities (knowledge, templates, etc.)');
    table.string('primary_entity_id', 36).comment('Primary entity affected by this action');
    table.string('primary_entity_type', 50).comment('Type of primary entity');

    // User and session information
    table.string('user_id', 100).comment('User who performed the action');
    table.string('session_id', 36).comment('Session ID when action was performed');
    table.string('client_info', 255).comment('Client information (OS, app version, etc.)');

    // Temporal information
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable().comment('When the action occurred');
    table.bigInteger('sequence_number').notNullable().comment('Sequential number for ordering');
    table.string('batch_id', 36).comment('Batch ID for grouped operations');

    // Metadata and context
    table.json('metadata').comment('Additional metadata about the action');
    table.json('context').comment('Context information at the time of action');
    table.text('tags').comment('Comma-separated tags for categorization');

    // Reversibility and rollback information
    table.boolean('is_reversible').defaultTo(true).notNullable().comment('Whether this action can be undone');
    table.text('rollback_instructions').comment('Instructions for rolling back this action');
    table.json('rollback_metadata').comment('Metadata needed for rollback');
    table.boolean('is_rolled_back').defaultTo(false).notNullable().comment('Whether this action has been rolled back');
    table.timestamp('rolled_back_at').comment('When this action was rolled back');
    table.string('rollback_by', 100).comment('Who rolled back this action');

    // Performance and impact metrics
    table.integer('execution_time_ms').unsigned().comment('Time taken to execute the action (milliseconds)');
    table.decimal('impact_score', 5, 4).comment('Impact score of the action (0-1)');
    table.json('performance_metrics').comment('Detailed performance metrics');

    // Relationships and dependencies
    table.string('parent_action_id', 36).comment('Parent action if this is a sub-action');
    table.foreign('parent_action_id').references('id').inTable('timeline').onDelete('SET NULL');
    table.json('dependent_actions').comment('Array of action IDs that depend on this action');
    table.string('checkpoint_id', 36).comment('Associated checkpoint ID');

    // Validation and integrity
    table.string('state_hash', 64).comment('Hash of the combined state for integrity checking');
    table.boolean('is_validated').defaultTo(false).notNullable().comment('Whether action has been validated');
    table.timestamp('validated_at').comment('When action was validated');

    // Constraints
    table.check("action_type IN ('create', 'update', 'delete', 'process', 'extract', 'fill_form', 'annotate', 'merge', 'split', 'import', 'export', 'backup', 'restore', 'other')", [], 'valid_action_type');
    table.check("category IN ('document', 'knowledge', 'form', 'template', 'annotation', 'system', 'user', 'other')", [], 'valid_category');
    table.check("compression_method IN ('lz4', 'gzip', 'brotli', 'none')", [], 'valid_compression_method');
    table.check('before_state_size >= 0', [], 'before_state_size_positive');
    table.check('after_state_size >= 0', [], 'after_state_size_positive');
    table.check('execution_time_ms >= 0', [], 'execution_time_positive');
    table.check('impact_score IS NULL OR (impact_score >= 0 AND impact_score <= 1)', [], 'impact_score_range');
    table.check('sequence_number > 0', [], 'sequence_number_positive');

    // JSON validation
    table.check("json_valid(affected_documents) OR affected_documents IS NULL", [], 'valid_affected_documents');
    table.check("json_valid(affected_entities) OR affected_entities IS NULL", [], 'valid_affected_entities');
    table.check("json_valid(metadata) OR metadata IS NULL", [], 'valid_metadata');
    table.check("json_valid(context) OR context IS NULL", [], 'valid_context');
    table.check("json_valid(rollback_metadata) OR rollback_metadata IS NULL", [], 'valid_rollback_metadata');
    table.check("json_valid(performance_metrics) OR performance_metrics IS NULL", [], 'valid_performance_metrics');
    table.check("json_valid(dependent_actions) OR dependent_actions IS NULL", [], 'valid_dependent_actions');

    // Indexes for performance
    table.index(['created_at'], 'idx_timeline_created_at');
    table.index(['action_type'], 'idx_timeline_action_type');
    table.index(['user_id'], 'idx_timeline_user');
    table.index(['session_id'], 'idx_timeline_session');
    table.index(['category'], 'idx_timeline_category');
    table.index(['sequence_number'], 'idx_timeline_sequence');
    table.index(['batch_id'], 'idx_timeline_batch');
    table.index(['primary_entity_id'], 'idx_timeline_primary_entity');
    table.index(['primary_entity_type'], 'idx_timeline_entity_type');
    table.index(['is_reversible'], 'idx_timeline_reversible');
    table.index(['is_rolled_back'], 'idx_timeline_rolled_back');
    table.index(['checkpoint_id'], 'idx_timeline_checkpoint');
    table.index(['parent_action_id'], 'idx_timeline_parent');
    table.index(['is_validated'], 'idx_timeline_validated');

    // Composite indexes for common queries
    table.index(['created_at', 'action_type'], 'idx_timeline_created_action');
    table.index(['user_id', 'created_at'], 'idx_timeline_user_created');
    table.index(['category', 'action_type'], 'idx_timeline_category_action');
    table.index(['primary_entity_id', 'created_at'], 'idx_timeline_entity_created');
    table.index(['is_rolled_back', 'is_reversible'], 'idx_timeline_rollback_status');
    table.index(['batch_id', 'sequence_number'], 'idx_timeline_batch_sequence');
    table.index(['session_id', 'created_at'], 'idx_timeline_session_created');
  });

  // Create sequence for timeline entries
  await knex.raw(`
    CREATE TABLE timeline_sequence (
      id INTEGER PRIMARY KEY,
      next_value BIGINT NOT NULL DEFAULT 1
    )
  `);

  // Insert initial sequence value
  await knex.raw(`INSERT INTO timeline_sequence (id, next_value) VALUES (1, 1)`);

  // Create trigger to auto-increment sequence number
  await knex.raw(`
    CREATE TRIGGER timeline_sequence_trigger
    BEFORE INSERT ON timeline
    FOR EACH ROW
    WHEN NEW.sequence_number IS NULL
    BEGIN
      UPDATE timeline_sequence SET next_value = next_value + 1 WHERE id = 1;
      SELECT next_value - 1 FROM timeline_sequence WHERE id = 1 INTO NEW.sequence_number;
    END
  `);

  // Create full-text search virtual table for timeline descriptions and metadata
  await knex.raw(`
    CREATE VIRTUAL TABLE IF NOT EXISTS timeline_fts USING fts5(
      id UNINDEXED,
      action_type,
      description,
      category,
      user_id UNINDEXED,
      metadata,
      tags,
      content='timeline',
      content_rowid='rowid'
    )
  `);

  // Create triggers to keep FTS table in sync
  await knex.raw(`
    CREATE TRIGGER timeline_fts_insert AFTER INSERT ON timeline BEGIN
      INSERT INTO timeline_fts(id, action_type, description, category, user_id, metadata, tags)
      VALUES (new.id, new.action_type, new.description, new.category, new.user_id, new.metadata, new.tags);
    END
  `);

  await knex.raw(`
    CREATE TRIGGER timeline_fts_delete AFTER DELETE ON timeline BEGIN
      DELETE FROM timeline_fts WHERE id = old.id;
    END
  `);

  await knex.raw(`
    CREATE TRIGGER timeline_fts_update AFTER UPDATE ON timeline BEGIN
      DELETE FROM timeline_fts WHERE id = old.id;
      INSERT INTO timeline_fts(id, action_type, description, category, user_id, metadata, tags)
      VALUES (new.id, new.action_type, new.description, new.category, new.user_id, new.metadata, new.tags);
    END
  `);

  // Create view for recent timeline entries
  await knex.raw(`
    CREATE VIEW recent_timeline AS
    SELECT
      id,
      action_type,
      description,
      category,
      primary_entity_id,
      primary_entity_type,
      user_id,
      created_at,
      is_reversible,
      is_rolled_back,
      execution_time_ms,
      impact_score
    FROM timeline
    WHERE created_at >= datetime('now', '-30 days')
    ORDER BY sequence_number DESC
    LIMIT 1000
  `);

  // Create view for reversible actions (for undo functionality)
  await knex.raw(`
    CREATE VIEW reversible_actions AS
    SELECT *
    FROM timeline
    WHERE is_reversible = 1
      AND is_rolled_back = 0
      AND before_state IS NOT NULL
    ORDER BY sequence_number DESC
  `);

  // Create view for timeline statistics by user
  await knex.raw(`
    CREATE VIEW timeline_user_stats AS
    SELECT
      user_id,
      COUNT(*) as total_actions,
      COUNT(DISTINCT action_type) as unique_action_types,
      COUNT(CASE WHEN is_rolled_back = 1 THEN 1 END) as rolled_back_actions,
      AVG(execution_time_ms) as avg_execution_time,
      SUM(execution_time_ms) as total_execution_time,
      AVG(impact_score) as avg_impact_score,
      MIN(created_at) as first_action,
      MAX(created_at) as last_action,
      COUNT(CASE WHEN created_at >= datetime('now', '-1 day') THEN 1 END) as actions_last_24h,
      COUNT(CASE WHEN created_at >= datetime('now', '-7 days') THEN 1 END) as actions_last_week
    FROM timeline
    WHERE user_id IS NOT NULL
    GROUP BY user_id
  `);

  // Create view for timeline statistics by action type
  await knex.raw(`
    CREATE VIEW timeline_action_stats AS
    SELECT
      action_type,
      category,
      COUNT(*) as total_occurrences,
      COUNT(DISTINCT user_id) as unique_users,
      AVG(execution_time_ms) as avg_execution_time,
      MIN(execution_time_ms) as min_execution_time,
      MAX(execution_time_ms) as max_execution_time,
      AVG(impact_score) as avg_impact_score,
      COUNT(CASE WHEN is_rolled_back = 1 THEN 1 END) as rollback_count,
      COUNT(CASE WHEN is_reversible = 1 THEN 1 END) as reversible_count,
      MIN(created_at) as first_occurrence,
      MAX(created_at) as last_occurrence
    FROM timeline
    GROUP BY action_type, category
    ORDER BY total_occurrences DESC
  `);

  // Create view for batch operations
  await knex.raw(`
    CREATE VIEW batch_operations AS
    SELECT
      batch_id,
      COUNT(*) as action_count,
      MIN(created_at) as batch_start,
      MAX(created_at) as batch_end,
      (julianday(MAX(created_at)) - julianday(MIN(created_at))) * 86400 as duration_seconds,
      SUM(execution_time_ms) as total_execution_time,
      AVG(impact_score) as avg_impact_score,
      COUNT(CASE WHEN is_rolled_back = 1 THEN 1 END) as rolled_back_count,
      GROUP_CONCAT(DISTINCT action_type) as action_types,
      GROUP_CONCAT(DISTINCT user_id) as users
    FROM timeline
    WHERE batch_id IS NOT NULL
    GROUP BY batch_id
    ORDER BY batch_start DESC
  `);
}

export async function down(knex: Knex): Promise<void> {
  // Drop triggers first
  await knex.raw('DROP TRIGGER IF EXISTS timeline_sequence_trigger');
  await knex.raw('DROP TRIGGER IF EXISTS timeline_fts_update');
  await knex.raw('DROP TRIGGER IF EXISTS timeline_fts_delete');
  await knex.raw('DROP TRIGGER IF EXISTS timeline_fts_insert');

  // Drop FTS table
  await knex.raw('DROP TABLE IF EXISTS timeline_fts');

  // Drop views
  await knex.raw('DROP VIEW IF EXISTS batch_operations');
  await knex.raw('DROP VIEW IF EXISTS timeline_action_stats');
  await knex.raw('DROP VIEW IF EXISTS timeline_user_stats');
  await knex.raw('DROP VIEW IF EXISTS reversible_actions');
  await knex.raw('DROP VIEW IF EXISTS recent_timeline');

  // Drop sequence table
  await knex.raw('DROP TABLE IF EXISTS timeline_sequence');

  // Drop main table (this will also drop all indexes and foreign keys)
  await knex.schema.dropTableIfExists('timeline');
}

import { ChromaClient, Collection } from 'chromadb';
import { app } from 'electron';
import * as path from 'path';
import * as fs from 'fs-extra';
import { logger } from '../utils/logger';
import { EventEmitter } from 'events';

export interface KnowledgeItem {
  id: string;
  content: string;
  metadata: Record<string, unknown>;
  embedding?: number[];
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface KnowledgeSearchResult {
  item: KnowledgeItem;
  score: number;
  relevance: number;
  snippet?: string;
}

export interface ChromaHealthStatus {
  isHealthy: boolean;
  lastHeartbeat: Date;
  collections: string[];
  totalDocuments: number;
  errors: string[];
  uptime: number;
}

export interface BackupOptions {
  includeEmbeddings: boolean;
  compressionLevel: number;
  format: 'json' | 'binary';
  destination?: string;
}

export interface RestoreOptions {
  overwriteExisting: boolean;
  validateIntegrity: boolean;
  batchSize: number;
}

export interface EmbeddingMetadata {
  sourceId: string;
  sourceType: 'document' | 'text' | 'chunk';
  chunkIndex?: number;
  totalChunks?: number;
  model: string;
  dimensions: number;
  version: number;
  confidence: number;
  processingTime: number;
  createdAt: string;
  updatedAt: string;
  hash: string; // For deduplication
}

export interface EmbeddingStorageOptions {
  enableDeduplication: boolean;
  enableVersioning: boolean;
  maxVersions: number;
  compressionLevel: number;
  batchSize: number;
}

export interface EmbeddingConflictResolution {
  strategy: 'overwrite' | 'keep_existing' | 'merge' | 'version';
  mergeFunction?: (existing: KnowledgeItem, incoming: KnowledgeItem) => KnowledgeItem;
}

export interface AdvancedSearchOptions {
  enableQueryExpansion: boolean;
  enableCaching: boolean;
  enableReranking: boolean;
  expansionTerms: number;
  cacheTimeout: number; // in milliseconds
  rerankingModel?: string;
  facetFilters: FacetFilter[];
  temporalFilters: TemporalFilter[];
  boostFactors: BoostFactor[];
}

export interface FacetFilter {
  field: string;
  values: string[];
  operator: 'AND' | 'OR' | 'NOT';
}

export interface TemporalFilter {
  field: string;
  startDate?: Date;
  endDate?: Date;
  relativeTime?: string; // e.g., '7d', '1m', '1y'
}

export interface BoostFactor {
  field: string;
  value: string | number;
  boost: number;
}

export interface SearchResult extends KnowledgeSearchResult {
  expandedQuery?: string;
  facetMatches: string[];
  temporalScore: number;
  boostScore: number;
  finalScore: number;
  cached: boolean;
}

export interface QueryExpansion {
  originalQuery: string;
  expandedTerms: string[];
  synonyms: string[];
  relatedConcepts: string[];
  finalQuery: string;
}

export interface IndexingOptions {
  enableAutoIndexing: boolean;
  batchSize: number;
  indexingInterval: number; // in milliseconds
  enableOptimization: boolean;
  optimizationThreshold: number; // number of operations before optimization
  enablePerformanceMonitoring: boolean;
}

export interface IndexStats {
  collectionName: string;
  totalDocuments: number;
  totalEmbeddings: number;
  averageEmbeddingDimensions: number;
  indexSize: number; // in bytes
  lastOptimized: Date;
  operationsSinceOptimization: number;
  averageQueryTime: number;
  cacheHitRate: number;
}

export interface PerformanceMetrics {
  queryCount: number;
  averageQueryTime: number;
  slowQueries: Array<{ query: string; time: number; timestamp: Date }>;
  cacheStats: {
    hits: number;
    misses: number;
    hitRate: number;
  };
  indexingStats: {
    documentsIndexed: number;
    indexingTime: number;
    lastIndexing: Date;
  };
}

export interface BooleanQuery {
  operator: 'AND' | 'OR' | 'NOT';
  terms: Array<string | BooleanQuery>;
  field?: string;
  boost?: number;
}

export interface FuzzySearchOptions {
  enableFuzzySearch: boolean;
  maxEditDistance: number;
  minSimilarity: number;
  prefixLength: number;
}

export interface TemporalQuery {
  field: string;
  operator: 'before' | 'after' | 'between' | 'within';
  date?: Date;
  startDate?: Date;
  endDate?: Date;
  duration?: string; // e.g., '7d', '1m', '1y'
}

export interface AggregationOptions {
  groupBy: string[];
  aggregations: Array<{
    field: string;
    operation: 'count' | 'sum' | 'avg' | 'min' | 'max';
    alias?: string;
  }>;
  having?: Array<{
    field: string;
    operator: '>' | '<' | '=' | '>=' | '<=';
    value: number;
  }>;
}

export interface QueryResult {
  items: KnowledgeSearchResult[];
  aggregations?: Record<string, unknown>;
  totalCount: number;
  queryTime: number;
  facets?: Record<string, Array<{ value: string; count: number }>>;
}

export interface ExportOptions {
  format: 'json' | 'csv' | 'xml';
  includeEmbeddings: boolean;
  includeMetadata: boolean;
  compression: boolean;
  batchSize: number;
  collections?: string[];
}

export interface ImportOptions {
  format: 'json' | 'csv' | 'xml';
  overwriteExisting: boolean;
  validateData: boolean;
  batchSize: number;
  generateEmbeddings: boolean;
  targetCollection?: string;
}

export interface AnalyticsReport {
  overview: {
    totalCollections: number;
    totalDocuments: number;
    totalEmbeddings: number;
    totalSize: number;
    lastUpdated: Date;
  };
  collections: Array<{
    name: string;
    documentCount: number;
    embeddingCount: number;
    averageDimensions: number;
    size: number;
    lastModified: Date;
  }>;
  usage: {
    queryCount: number;
    averageQueryTime: number;
    cacheHitRate: number;
    slowQueries: number;
  };
  performance: {
    indexingTime: number;
    optimizationCount: number;
    lastOptimization: Date;
    fragmentationLevel: number;
  };
  trends: {
    documentsPerDay: Array<{ date: string; count: number }>;
    queriesPerDay: Array<{ date: string; count: number }>;
    popularQueries: Array<{ query: string; count: number }>;
  };
}

export interface MigrationOptions {
  sourceVersion: string;
  targetVersion: string;
  backupBeforeMigration: boolean;
  validateAfterMigration: boolean;
  dryRun: boolean;
}

export class ChromaKnowledgeBaseService extends EventEmitter {
  private readonly chromaClient: ChromaClient;
  private readonly collections: Map<string, Collection> = new Map();
  private isInitialized = false;
  private readonly chromaPath: string;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private lastHealthCheck: Date = new Date();
  private connectionRetryCount = 0;
  private readonly maxRetries = 5;
  private readonly retryDelay = 2000; // 2 seconds
  private readonly embeddingHashes: Map<string, string> = new Map(); // For deduplication
  private readonly embeddingVersions: Map<string, number> = new Map(); // For versioning
  private readonly searchCache: Map<string, { result: SearchResult[]; timestamp: number }> =
    new Map(); // Search cache
  private readonly queryExpansionCache: Map<string, QueryExpansion> = new Map(); // Query expansion cache
  private readonly performanceMetrics: PerformanceMetrics = {
    queryCount: 0,
    averageQueryTime: 0,
    slowQueries: [],
    cacheStats: { hits: 0, misses: 0, hitRate: 0 },
    indexingStats: { documentsIndexed: 0, indexingTime: 0, lastIndexing: new Date() },
  };
  private readonly operationCounts: Map<string, number> = new Map(); // Track operations per collection
  private indexingInterval: NodeJS.Timeout | null = null;
  private readonly indexingOptions: IndexingOptions = {
    enableAutoIndexing: true,
    batchSize: 100,
    indexingInterval: 300000, // 5 minutes
    enableOptimization: true,
    optimizationThreshold: 1000,
    enablePerformanceMonitoring: true,
  };

  constructor() {
    super();
    // Initialize ChromaDB in userData directory
    this.chromaPath = path.join(app.getPath('userData'), 'chroma_db');
    this.chromaClient = new ChromaClient({
      path: this.chromaPath,
    });

    // Start health monitoring
    this.startHealthMonitoring();
  }

  async initialize(): Promise<void> {
    try {
      // Ensure ChromaDB directory exists
      await fs.ensureDir(this.chromaPath);

      // Ensure ChromaDB is running and accessible with retry logic
      await this.connectWithRetry();

      // Create default collections
      await this.ensureCollection('documents');
      await this.ensureCollection('knowledge_base');
      await this.ensureCollection('extracted_data');
      await this.ensureCollection('embeddings'); // Dedicated collection for embeddings

      this.isInitialized = true;
      this.connectionRetryCount = 0; // Reset retry count on successful connection

      logger.info('ChromaDB initialized successfully', {
        collections: Array.from(this.collections.keys()),
        path: this.chromaPath,
      });

      this.emit('initialized');
    } catch (error) {
      logger.error('Failed to initialize ChromaDB', error);
      this.emit('error', error);
      throw error;
    }
  }

  private async connectWithRetry(): Promise<void> {
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        await this.chromaClient.heartbeat();
        this.lastHealthCheck = new Date();
        return;
      } catch (error) {
        this.connectionRetryCount = attempt;

        if (attempt === this.maxRetries) {
          logger.error(`Failed to connect to ChromaDB after ${this.maxRetries} attempts`, error);
          throw error;
        }

        logger.warn(
          `ChromaDB connection attempt ${attempt} failed, retrying in ${this.retryDelay}ms`,
          error
        );
        await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
      }
    }
  }

  private startHealthMonitoring(): void {
    // Check health every 30 seconds
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck().catch(error => {
        logger.error('Health check failed', error);
        this.emit('healthCheckFailed', error);
      });
    }, 30000);
  }

  private async performHealthCheck(): Promise<ChromaHealthStatus> {
    try {
      await this.chromaClient.heartbeat();
      this.lastHealthCheck = new Date();

      const collections = Array.from(this.collections.keys());
      let totalDocuments = 0;

      // Count documents in all collections
      for (const collectionName of collections) {
        try {
          const stats = await this.getCollectionStats(collectionName);
          totalDocuments += stats.count;
        } catch (error) {
          logger.warn(`Failed to get stats for collection ${collectionName}`, error);
        }
      }

      const healthStatus: ChromaHealthStatus = {
        isHealthy: true,
        lastHeartbeat: this.lastHealthCheck,
        collections,
        totalDocuments,
        errors: [],
        uptime: Date.now() - this.lastHealthCheck.getTime(),
      };

      this.emit('healthCheck', healthStatus);
      return healthStatus;
    } catch (error) {
      const healthStatus: ChromaHealthStatus = {
        isHealthy: false,
        lastHeartbeat: this.lastHealthCheck,
        collections: [],
        totalDocuments: 0,
        errors: [error instanceof Error ? error.message : String(error)],
        uptime: 0,
      };

      this.emit('healthCheckFailed', healthStatus);

      // Attempt to reconnect if health check fails
      if (this.isInitialized) {
        logger.warn('ChromaDB health check failed, attempting to reconnect');
        try {
          await this.connectWithRetry();
          logger.info('ChromaDB reconnection successful');
          this.emit('reconnected');
        } catch (reconnectError) {
          logger.error('ChromaDB reconnection failed', reconnectError);
          this.emit('connectionLost', reconnectError);
        }
      }

      return healthStatus;
    }
  }

  private async ensureCollection(name: string): Promise<Collection> {
    try {
      const collection = await this.chromaClient.getCollection({ name });
      this.collections.set(name, collection);
      return collection;
    } catch (error) {
      // Collection doesn't exist, create it
      logger.info(`Creating ChromaDB collection: ${name}`, {
        error: error instanceof Error ? error.message : String(error),
      });
      const collection = await this.chromaClient.createCollection({
        name,
        metadata: {
          description: `AI Document Processor ${name} collection`,
          created_at: new Date().toISOString(),
        },
      });
      this.collections.set(name, collection);
      logger.info(`Created ChromaDB collection: ${name}`);
      return collection;
    }
  }

  async storeInformation(
    data: KnowledgeItem,
    collectionName: string = 'knowledge_base'
  ): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const collection = this.collections.get(collectionName);
    if (!collection) {
      throw new Error(`Collection ${collectionName} not found`);
    }

    try {
      const addData: {
        ids: string[];
        documents: string[];
        metadatas: Array<Record<string, string | number | boolean | null>>;
        embeddings?: number[][];
      } = {
        ids: [data.id],
        documents: [data.content],
        metadatas: [
          {
            ...data.metadata,
            tags: data.tags?.join(',') || '',
            createdAt: data.createdAt,
            updatedAt: data.updatedAt,
          },
        ],
      };

      if (data.embedding) {
        addData.embeddings = [data.embedding];
      }

      await collection.add(addData);

      // Track operation for maintenance
      this.incrementOperationCount(collectionName);

      logger.info('Stored knowledge item in ChromaDB', {
        id: data.id,
        collection: collectionName,
      });
    } catch (error) {
      logger.error('Failed to store knowledge item', { error, id: data.id });
      throw error;
    }
  }

  async semanticSearch(
    query: string,
    collectionName: string = 'knowledge_base',
    limit: number = 10,
    threshold: number = 0.7
  ): Promise<KnowledgeSearchResult[]> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const collection = this.collections.get(collectionName);
    if (!collection) {
      throw new Error(`Collection ${collectionName} not found`);
    }

    try {
      const startTime = Date.now();

      const results = await collection.query({
        queryTexts: [query],
        nResults: limit,
        include: ['documents', 'metadatas', 'distances'],
      });

      // Track query performance
      const queryTime = Date.now() - startTime;
      this.trackQueryPerformance(queryTime, query);

      const searchResults: KnowledgeSearchResult[] = [];

      if (results.documents?.[0]) {
        for (let i = 0; i < results.documents[0].length; i++) {
          const distance = results.distances?.[0]?.[i] || 1;
          const similarity = 1 - distance;

          // Filter by threshold
          if (similarity >= threshold) {
            const metadata = results.metadatas?.[0]?.[i] || {};

            searchResults.push({
              item: {
                id: results.ids?.[0]?.[i] || `result_${i}`,
                content: results.documents[0][i] || '',
                metadata,
                tags: typeof metadata.tags === 'string' ? metadata.tags.split(',') : [],
                createdAt:
                  typeof metadata.createdAt === 'string'
                    ? metadata.createdAt
                    : new Date().toISOString(),
                updatedAt:
                  typeof metadata.updatedAt === 'string'
                    ? metadata.updatedAt
                    : new Date().toISOString(),
              },
              score: distance,
              relevance: similarity,
              snippet: this.generateSnippet(results.documents[0][i] || '', query),
            });
          }
        }
      }

      logger.info('Semantic search completed', {
        query,
        collection: collectionName,
        results: searchResults.length,
      });

      return searchResults.sort((a, b) => b.relevance - a.relevance);
    } catch (error) {
      logger.error('Semantic search failed', { error, query });
      throw error;
    }
  }

  private generateSnippet(content: string, query: string, maxLength: number = 200): string {
    const queryWords = query.toLowerCase().split(/\s+/);
    const contentLower = content.toLowerCase();

    // Find the best match position
    let bestPosition = 0;
    let bestScore = 0;

    for (let i = 0; i < content.length - maxLength; i += 50) {
      const snippet = contentLower.slice(i, i + maxLength);
      const score = queryWords.reduce((acc, word) => {
        return acc + (snippet.includes(word) ? 1 : 0);
      }, 0);

      if (score > bestScore) {
        bestScore = score;
        bestPosition = i;
      }
    }

    const snippet = content.slice(bestPosition, bestPosition + maxLength);
    return bestPosition > 0 ? '...' + snippet + '...' : snippet + '...';
  }

  /**
   * Advanced semantic search with query expansion, ranking, and caching
   */
  async advancedSemanticSearch(
    query: string,
    collectionName: string = 'knowledge_base',
    limit: number = 10,
    threshold: number = 0.7,
    options: AdvancedSearchOptions = {
      enableQueryExpansion: true,
      enableCaching: true,
      enableReranking: true,
      expansionTerms: 5,
      cacheTimeout: 300000, // 5 minutes
      facetFilters: [],
      temporalFilters: [],
      boostFactors: [],
    }
  ): Promise<SearchResult[]> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const cacheKey = `${query}_${collectionName}_${JSON.stringify(options)}`;

    // Check cache first
    if (options.enableCaching) {
      const cached = this.searchCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < options.cacheTimeout) {
        logger.info('Returning cached search results', { query, collection: collectionName });
        return cached.result.map(r => ({ ...r, cached: true }));
      }
    }

    const collection = this.collections.get(collectionName);
    if (!collection) {
      throw new Error(`Collection ${collectionName} not found`);
    }

    try {
      // Step 1: Query expansion
      let finalQuery = query;
      let queryExpansion: QueryExpansion | undefined;

      if (options.enableQueryExpansion) {
        queryExpansion = this.expandQuery(query, options.expansionTerms);
        finalQuery = queryExpansion.finalQuery;
      }

      // Step 2: Perform semantic search
      const results = await collection.query({
        queryTexts: [finalQuery],
        nResults: limit * 2, // Get more results for reranking
        include: ['documents', 'metadatas', 'distances'],
      });

      let searchResults: SearchResult[] = [];

      if (results.documents?.[0]) {
        for (let i = 0; i < results.documents[0].length; i++) {
          const distance = results.distances?.[0]?.[i] || 1;
          const similarity = 1 - distance;

          if (similarity >= threshold) {
            const metadata = results.metadatas?.[0]?.[i] || {};
            const content = results.documents[0][i] || '';

            // Step 3: Apply facet filters
            const facetMatches = this.applyFacetFilters(metadata, options.facetFilters);
            if (options.facetFilters.length > 0 && facetMatches.length === 0) {
              continue; // Skip if doesn't match facet filters
            }

            // Step 4: Apply temporal filters
            const temporalScore = this.applyTemporalFilters(metadata, options.temporalFilters);
            if (temporalScore === 0 && options.temporalFilters.length > 0) {
              continue; // Skip if doesn't match temporal filters
            }

            // Step 5: Apply boost factors
            const boostScore = this.applyBoostFactors(metadata, content, options.boostFactors);

            // Step 6: Calculate final score
            const finalScore = similarity * (1 + temporalScore + boostScore);

            const searchResult: SearchResult = {
              item: {
                id: results.ids?.[0]?.[i] || `result_${i}`,
                content,
                metadata,
                tags: typeof metadata.tags === 'string' ? metadata.tags.split(',') : [],
                createdAt:
                  typeof metadata.createdAt === 'string'
                    ? metadata.createdAt
                    : new Date().toISOString(),
                updatedAt:
                  typeof metadata.updatedAt === 'string'
                    ? metadata.updatedAt
                    : new Date().toISOString(),
              },
              score: distance,
              relevance: similarity,
              snippet: this.generateSnippet(content, finalQuery),
              ...(queryExpansion?.finalQuery && { expandedQuery: queryExpansion.finalQuery }),
              facetMatches,
              temporalScore,
              boostScore,
              finalScore,
              cached: false,
            };

            searchResults.push(searchResult);
          }
        }
      }

      // Step 7: Rerank results if enabled
      if (options.enableReranking) {
        searchResults = this.rerankResults(searchResults, query, options.rerankingModel);
      }

      // Step 8: Sort by final score and limit results
      searchResults.sort((a, b) => b.finalScore - a.finalScore);
      searchResults = searchResults.slice(0, limit);

      // Step 9: Cache results
      if (options.enableCaching) {
        this.searchCache.set(cacheKey, {
          result: searchResults,
          timestamp: Date.now(),
        });
      }

      logger.info('Advanced semantic search completed', {
        query,
        expandedQuery: queryExpansion?.finalQuery,
        collection: collectionName,
        results: searchResults.length,
        cached: false,
      });

      this.emit('advancedSearchCompleted', {
        query,
        queryExpansion,
        results: searchResults.length,
        options,
      });

      return searchResults;
    } catch (error) {
      logger.error('Advanced semantic search failed', { error, query });
      throw error;
    }
  }

  /**
   * Expand query with synonyms and related terms
   */
  private expandQuery(query: string, maxTerms: number = 5): QueryExpansion {
    // Check cache first
    const cached = this.queryExpansionCache.get(query);
    if (cached) {
      return cached;
    }

    try {
      // Simple query expansion using word similarity and common synonyms
      const queryWords = query.toLowerCase().split(/\s+/);
      const expandedTerms: string[] = [];
      const synonyms: string[] = [];
      const relatedConcepts: string[] = [];

      // Basic synonym mapping (in a real implementation, this would use a proper thesaurus or AI model)
      const synonymMap: Record<string, string[]> = {
        document: ['file', 'paper', 'record', 'text'],
        search: ['find', 'locate', 'discover', 'query'],
        information: ['data', 'details', 'facts', 'knowledge'],
        process: ['handle', 'manage', 'execute', 'perform'],
        analyze: ['examine', 'study', 'review', 'investigate'],
        extract: ['retrieve', 'obtain', 'get', 'pull'],
        form: ['document', 'application', 'sheet', 'template'],
        tax: ['taxation', 'revenue', 'fiscal', 'IRS'],
        financial: ['monetary', 'economic', 'fiscal', 'money'],
        legal: ['law', 'juridical', 'court', 'attorney'],
      };

      for (const word of queryWords) {
        if (synonymMap[word]) {
          synonyms.push(...synonymMap[word].slice(0, 2)); // Add up to 2 synonyms per word
        }
      }

      // Add related concepts based on domain knowledge
      if (queryWords.some(w => ['tax', 'financial', 'income'].includes(w))) {
        relatedConcepts.push('deduction', 'filing', 'return', 'IRS');
      }
      if (queryWords.some(w => ['legal', 'contract', 'agreement'].includes(w))) {
        relatedConcepts.push('clause', 'terms', 'conditions', 'liability');
      }

      // Combine all expansion terms
      expandedTerms.push(...synonyms, ...relatedConcepts);

      // Limit to maxTerms
      const finalExpandedTerms = expandedTerms.slice(0, maxTerms);

      // Create final expanded query
      const finalQuery = [query, ...finalExpandedTerms].join(' ');

      const expansion: QueryExpansion = {
        originalQuery: query,
        expandedTerms: finalExpandedTerms,
        synonyms,
        relatedConcepts,
        finalQuery,
      };

      // Cache the expansion
      this.queryExpansionCache.set(query, expansion);

      logger.debug('Query expanded', {
        original: query,
        expanded: finalQuery,
        terms: finalExpandedTerms.length,
      });

      return expansion;
    } catch (error) {
      logger.error('Failed to expand query', { error, query });
      // Return original query if expansion fails
      return {
        originalQuery: query,
        expandedTerms: [],
        synonyms: [],
        relatedConcepts: [],
        finalQuery: query,
      };
    }
  }

  /**
   * Apply facet filters to metadata
   */
  private applyFacetFilters(metadata: Record<string, unknown>, filters: FacetFilter[]): string[] {
    const matches: string[] = [];

    for (const filter of filters) {
      const fieldValue = metadata[filter.field];
      if (!fieldValue) continue;

      const valueStr = String(fieldValue).toLowerCase();
      const filterValues = filter.values.map(v => v.toLowerCase());

      let hasMatch = false;
      switch (filter.operator) {
        case 'AND':
          hasMatch = filterValues.every(v => valueStr.includes(v));
          break;
        case 'OR':
          hasMatch = filterValues.some(v => valueStr.includes(v));
          break;
        case 'NOT':
          hasMatch = !filterValues.some(v => valueStr.includes(v));
          break;
      }

      if (hasMatch) {
        matches.push(`${filter.field}:${filter.operator}:${filter.values.join(',')}`);
      }
    }

    return matches;
  }

  /**
   * Apply temporal filters and calculate temporal score
   */
  private applyTemporalFilters(
    metadata: Record<string, unknown>,
    filters: TemporalFilter[]
  ): number {
    if (filters.length === 0) return 0;

    let totalScore = 0;
    let validFilters = 0;

    for (const filter of filters) {
      const fieldValue = metadata[filter.field];
      if (!fieldValue) continue;

      const date = new Date(fieldValue as string | number | Date);
      if (isNaN(date.getTime())) continue;

      let score = 0;
      const now = new Date();

      if (filter.startDate && filter.endDate) {
        // Date range filter
        if (date >= filter.startDate && date <= filter.endDate) {
          score = 1.0;
        }
      } else if (filter.relativeTime) {
        // Relative time filter (e.g., '7d', '1m', '1y')
        const match = filter.relativeTime.match(/^(\d+)([dmyh])$/);
        if (match && match[1] && match[2]) {
          const [, amount, unit] = match;
          const num = parseInt(amount, 10);
          const cutoffDate = new Date(now);

          switch (unit) {
            case 'h':
              cutoffDate.setHours(cutoffDate.getHours() - num);
              break;
            case 'd':
              cutoffDate.setDate(cutoffDate.getDate() - num);
              break;
            case 'm':
              cutoffDate.setMonth(cutoffDate.getMonth() - num);
              break;
            case 'y':
              cutoffDate.setFullYear(cutoffDate.getFullYear() - num);
              break;
          }

          if (date >= cutoffDate) {
            // Calculate recency score (more recent = higher score)
            const timeDiff = now.getTime() - date.getTime();
            const maxDiff = now.getTime() - cutoffDate.getTime();
            score = 1 - timeDiff / maxDiff;
          }
        }
      }

      totalScore += score;
      validFilters++;
    }

    return validFilters > 0 ? totalScore / validFilters : 0;
  }

  /**
   * Apply boost factors to calculate boost score
   */
  private applyBoostFactors(
    metadata: Record<string, any>,
    content: string,
    factors: BoostFactor[]
  ): number {
    if (factors.length === 0) return 0;

    let totalBoost = 0;

    for (const factor of factors) {
      const fieldValue = metadata[factor.field] || content;
      if (!fieldValue) continue;

      const valueStr = String(fieldValue).toLowerCase();
      const boostValue = String(factor.value).toLowerCase();

      if (valueStr.includes(boostValue)) {
        totalBoost += factor.boost;
      }
    }

    return Math.min(totalBoost, 1.0); // Cap boost at 1.0
  }

  /**
   * Rerank search results using advanced scoring
   */
  private rerankResults(
    results: SearchResult[],
    originalQuery: string,
    model?: string
  ): SearchResult[] {
    try {
      // Simple reranking based on query term frequency and position
      const queryTerms = originalQuery.toLowerCase().split(/\s+/);

      for (const result of results) {
        let rerankScore = result.relevance;
        const content = result.item.content.toLowerCase();

        // Boost based on query term frequency
        for (const term of queryTerms) {
          const termCount = (content.match(new RegExp(term, 'g')) || []).length;
          rerankScore += termCount * 0.1;

          // Boost if term appears in title/metadata
          const title = String(result.item.metadata.title || '').toLowerCase();
          if (title.includes(term)) {
            rerankScore += 0.2;
          }
        }

        // Update final score with reranking
        result.finalScore = result.finalScore * 0.7 + rerankScore * 0.3;
      }

      logger.debug('Results reranked', {
        query: originalQuery,
        count: results.length,
        model: model || 'default',
      });

      return results;
    } catch (error) {
      logger.error('Failed to rerank results', { error, query: originalQuery });
      return results; // Return original results if reranking fails
    }
  }

  /**
   * Clear search cache
   */
  clearSearchCache(): void {
    this.searchCache.clear();
    this.queryExpansionCache.clear();
    logger.info('Search cache cleared');
  }

  /**
   * Get search cache statistics
   */
  getSearchCacheStats(): {
    searchCacheSize: number;
    queryExpansionCacheSize: number;
    oldestEntry: Date | null;
    newestEntry: Date | null;
  } {
    let oldestTimestamp = Infinity;
    let newestTimestamp = 0;

    for (const entry of this.searchCache.values()) {
      if (entry.timestamp < oldestTimestamp) oldestTimestamp = entry.timestamp;
      if (entry.timestamp > newestTimestamp) newestTimestamp = entry.timestamp;
    }

    return {
      searchCacheSize: this.searchCache.size,
      queryExpansionCacheSize: this.queryExpansionCache.size,
      oldestEntry: oldestTimestamp === Infinity ? null : new Date(oldestTimestamp),
      newestEntry: newestTimestamp === 0 ? null : new Date(newestTimestamp),
    };
  }

  /**
   * Start automatic indexing and optimization
   */
  startAutoIndexing(options?: Partial<IndexingOptions>): void {
    if (options) {
      Object.assign(this.indexingOptions, options);
    }

    if (!this.indexingOptions.enableAutoIndexing) {
      return;
    }

    // Stop existing interval if running
    if (this.indexingInterval) {
      clearInterval(this.indexingInterval);
    }

    // Start new indexing interval
    this.indexingInterval = setInterval(() => {
      this.performMaintenanceTasks().catch(error => {
        logger.error('Auto indexing maintenance failed', error);
        this.emit('indexingError', error);
      });
    }, this.indexingOptions.indexingInterval);

    logger.info('Auto indexing started', {
      interval: this.indexingOptions.indexingInterval,
      batchSize: this.indexingOptions.batchSize,
      optimizationThreshold: this.indexingOptions.optimizationThreshold,
    });

    this.emit('autoIndexingStarted', this.indexingOptions);
  }

  /**
   * Stop automatic indexing
   */
  stopAutoIndexing(): void {
    if (this.indexingInterval) {
      clearInterval(this.indexingInterval);
      this.indexingInterval = null;
      logger.info('Auto indexing stopped');
      this.emit('autoIndexingStopped');
    }
  }

  /**
   * Perform maintenance tasks (optimization, cleanup, etc.)
   */
  async performMaintenanceTasks(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      logger.info('Starting maintenance tasks');
      const startTime = Date.now();

      // Check each collection for optimization needs
      for (const [collectionName] of this.collections) {
        const operationCount = this.operationCounts.get(collectionName) || 0;

        if (
          this.indexingOptions.enableOptimization &&
          operationCount >= this.indexingOptions.optimizationThreshold
        ) {
          await this.optimizeCollection(collectionName);
          this.operationCounts.set(collectionName, 0); // Reset counter
        }
      }

      // Clean up old cache entries
      this.cleanupCache();

      // Update performance metrics
      if (this.indexingOptions.enablePerformanceMonitoring) {
        await this.updatePerformanceMetrics();
      }

      const duration = Date.now() - startTime;
      logger.info('Maintenance tasks completed', { duration });

      this.emit('maintenanceCompleted', { duration, timestamp: new Date() });
    } catch (error) {
      logger.error('Maintenance tasks failed', error);
      this.emit('maintenanceError', error);
    }
  }

  /**
   * Optimize a specific collection
   */
  async optimizeCollection(collectionName: string): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const collection = this.collections.get(collectionName);
    if (!collection) {
      throw new Error(`Collection ${collectionName} not found`);
    }

    try {
      logger.info(`Starting optimization for collection: ${collectionName}`);
      const startTime = Date.now();

      // Get collection statistics before optimization
      const statsBefore = await this.getCollectionIndexStats(collectionName);

      // Perform optimization tasks
      await this.compactCollection(collectionName);
      this.rebuildIndexes(collectionName);
      await this.validateCollectionIntegrity(collectionName);

      // Get statistics after optimization
      const statsAfter = await this.getCollectionIndexStats(collectionName);

      const duration = Date.now() - startTime;

      logger.info(`Collection optimization completed: ${collectionName}`, {
        duration,
        documentsBefore: statsBefore.totalDocuments,
        documentsAfter: statsAfter.totalDocuments,
        sizeBefore: statsBefore.indexSize,
        sizeAfter: statsAfter.indexSize,
      });

      this.emit('collectionOptimized', {
        collectionName,
        duration,
        statsBefore,
        statsAfter,
      });
    } catch (error) {
      logger.error(`Collection optimization failed: ${collectionName}`, error);
      throw error;
    }
  }

  /**
   * Compact collection by removing fragmentation
   */
  private async compactCollection(collectionName: string): Promise<void> {
    const collection = this.collections.get(collectionName);
    if (!collection) {
      return;
    }

    try {
      // Get all data from collection
      const allData = await collection.get({
        include: ['documents', 'metadatas', 'embeddings'],
      });

      if (!allData.ids || allData.ids.length === 0) {
        return;
      }

      // Remove duplicates and invalid entries
      const validEntries: Array<{
        id: string;
        document: string;
        metadata: Record<string, any>;
        embedding?: number[];
      }> = [];

      const seenIds = new Set<string>();

      for (let i = 0; i < allData.ids.length; i++) {
        const id = allData.ids[i];
        const document = allData.documents?.[i];
        const metadata = allData.metadatas?.[i];
        const embedding = allData.embeddings?.[i];

        // Skip duplicates and invalid entries
        if (!id || seenIds.has(id) || !document || !metadata) {
          continue;
        }

        seenIds.add(id);
        validEntries.push({
          id,
          document,
          metadata,
          ...(embedding && { embedding }),
        });
      }

      // If we removed any entries, recreate the collection
      if (validEntries.length < allData.ids.length) {
        logger.info(
          `Compacting collection ${collectionName}: ${allData.ids.length} -> ${validEntries.length}`
        );

        // Delete all existing data
        await collection.delete({ where: {} });

        // Re-add valid data in batches
        for (let i = 0; i < validEntries.length; i += this.indexingOptions.batchSize) {
          const batch = validEntries.slice(i, i + this.indexingOptions.batchSize);

          const batchData: any = {
            ids: batch.map(e => e.id),
            documents: batch.map(e => e.document),
            metadatas: batch.map(e => e.metadata),
          };

          const hasEmbeddings = batch.every(e => e.embedding && e.embedding.length > 0);
          if (hasEmbeddings) {
            batchData.embeddings = batch.map(e => e.embedding);
          }

          await collection.add(batchData);
        }
      }
    } catch (error) {
      logger.error(`Failed to compact collection ${collectionName}`, error);
      throw error;
    }
  }

  /**
   * Rebuild indexes for a collection
   */
  private rebuildIndexes(collectionName: string): void {
    // ChromaDB handles indexing automatically, but we can trigger optimization
    logger.info(`Rebuilding indexes for collection: ${collectionName}`);
    // In a real implementation, this might involve ChromaDB-specific optimization calls
  }

  /**
   * Validate collection integrity
   */
  private async validateCollectionIntegrity(collectionName: string): Promise<void> {
    const collection = this.collections.get(collectionName);
    if (!collection) {
      return;
    }

    try {
      const allData = await collection.get({
        include: ['documents', 'metadatas', 'embeddings'],
      });

      let issues = 0;

      if (allData.ids) {
        for (let i = 0; i < allData.ids.length; i++) {
          const id = allData.ids[i];
          const document = allData.documents?.[i];
          const metadata = allData.metadatas?.[i];
          const embedding = allData.embeddings?.[i];

          // Check for missing required fields
          if (!id || !document || !metadata) {
            issues++;
            logger.warn(
              `Integrity issue in ${collectionName}: missing required fields for index ${i}`
            );
          }

          // Check embedding dimensions consistency
          if (embedding && embedding.length > 0) {
            // Validate embedding dimensions (should be consistent across collection)
            // This is a simplified check - in practice, you'd want more sophisticated validation
          }
        }
      }

      if (issues > 0) {
        logger.warn(`Collection ${collectionName} has ${issues} integrity issues`);
        this.emit('integrityIssuesFound', { collectionName, issues });
      } else {
        logger.info(`Collection ${collectionName} integrity validation passed`);
      }
    } catch (error) {
      logger.error(`Failed to validate collection integrity: ${collectionName}`, error);
      throw error;
    }
  }

  /**
   * Clean up old cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    const maxAge = 3600000; // 1 hour

    // Clean search cache
    let removedSearchEntries = 0;
    for (const [key, entry] of this.searchCache.entries()) {
      if (now - entry.timestamp > maxAge) {
        this.searchCache.delete(key);
        removedSearchEntries++;
      }
    }

    // Clean query expansion cache (keep longer as it's more expensive to regenerate)
    let removedExpansionEntries = 0;
    for (const [key] of this.queryExpansionCache.entries()) {
      // Simple cleanup - in practice, you'd want to track timestamps for expansion cache too
      if (this.queryExpansionCache.size > 1000) {
        // Arbitrary limit
        this.queryExpansionCache.delete(key);
        removedExpansionEntries++;
        if (removedExpansionEntries >= 100) break; // Remove in batches
      }
    }

    if (removedSearchEntries > 0 || removedExpansionEntries > 0) {
      logger.info('Cache cleanup completed', {
        searchEntriesRemoved: removedSearchEntries,
        expansionEntriesRemoved: removedExpansionEntries,
      });
    }
  }

  /**
   * Update performance metrics
   */
  private async updatePerformanceMetrics(): Promise<void> {
    try {
      // Update cache hit rate
      const totalCacheRequests =
        this.performanceMetrics.cacheStats.hits + this.performanceMetrics.cacheStats.misses;
      if (totalCacheRequests > 0) {
        this.performanceMetrics.cacheStats.hitRate =
          this.performanceMetrics.cacheStats.hits / totalCacheRequests;
      }

      // Clean up old slow queries (keep only last 100)
      if (this.performanceMetrics.slowQueries.length > 100) {
        this.performanceMetrics.slowQueries = this.performanceMetrics.slowQueries
          .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
          .slice(0, 100);
      }

      logger.debug('Performance metrics updated', {
        queryCount: this.performanceMetrics.queryCount,
        averageQueryTime: this.performanceMetrics.averageQueryTime,
        cacheHitRate: this.performanceMetrics.cacheStats.hitRate,
        slowQueries: this.performanceMetrics.slowQueries.length,
      });
    } catch (error) {
      logger.error('Failed to update performance metrics', error);
    }
  }

  /**
   * Get collection index statistics
   */
  async getCollectionIndexStats(collectionName: string): Promise<IndexStats> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const collection = this.collections.get(collectionName);
    if (!collection) {
      throw new Error(`Collection ${collectionName} not found`);
    }

    try {
      const count = await collection.count();
      const allData = await collection.get({
        include: ['embeddings'],
      });

      let totalEmbeddings = 0;
      let totalDimensions = 0;
      let dimensionCount = 0;

      if (allData.embeddings) {
        for (const embedding of allData.embeddings) {
          if (embedding && embedding.length > 0) {
            totalEmbeddings++;
            totalDimensions += embedding.length;
            dimensionCount++;
          }
        }
      }

      const averageEmbeddingDimensions = dimensionCount > 0 ? totalDimensions / dimensionCount : 0;

      // Estimate index size (this is a rough approximation)
      const estimatedSize = count * (averageEmbeddingDimensions * 4 + 1000); // 4 bytes per float + metadata

      return {
        collectionName,
        totalDocuments: count,
        totalEmbeddings,
        averageEmbeddingDimensions,
        indexSize: estimatedSize,
        lastOptimized: new Date(), // Would track this in real implementation
        operationsSinceOptimization: this.operationCounts.get(collectionName) || 0,
        averageQueryTime: this.performanceMetrics.averageQueryTime,
        cacheHitRate: this.performanceMetrics.cacheStats.hitRate,
      };
    } catch (error) {
      logger.error(`Failed to get index stats for collection ${collectionName}`, error);
      throw error;
    }
  }

  /**
   * Get overall performance metrics
   */
  getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * Track query performance
   */
  private trackQueryPerformance(queryTime: number, query: string): void {
    if (!this.indexingOptions.enablePerformanceMonitoring) {
      return;
    }

    this.performanceMetrics.queryCount++;

    // Update average query time
    const totalTime =
      this.performanceMetrics.averageQueryTime * (this.performanceMetrics.queryCount - 1) +
      queryTime;
    this.performanceMetrics.averageQueryTime = totalTime / this.performanceMetrics.queryCount;

    // Track slow queries (> 1 second)
    if (queryTime > 1000) {
      this.performanceMetrics.slowQueries.push({
        query,
        time: queryTime,
        timestamp: new Date(),
      });

      // Keep only recent slow queries
      if (this.performanceMetrics.slowQueries.length > 50) {
        this.performanceMetrics.slowQueries = this.performanceMetrics.slowQueries.slice(-50);
      }
    }
  }

  /**
   * Increment operation count for a collection
   */
  private incrementOperationCount(collectionName: string): void {
    const current = this.operationCounts.get(collectionName) || 0;
    this.operationCounts.set(collectionName, current + 1);
  }

  /**
   * Advanced query with boolean search, fuzzy search, temporal queries, and aggregation
   */
  async queryInformation(
    query: string | BooleanQuery,
    collectionName: string = 'knowledge_base',
    options: {
      limit?: number;
      threshold?: number;
      fuzzyOptions?: FuzzySearchOptions;
      temporalQueries?: TemporalQuery[];
      aggregationOptions?: AggregationOptions;
      enableFacets?: boolean;
    } = {}
  ): Promise<QueryResult> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const collection = this.collections.get(collectionName);
    if (!collection) {
      throw new Error(`Collection ${collectionName} not found`);
    }

    const startTime = Date.now();
    const {
      limit = 10,
      threshold = 0.7,
      fuzzyOptions = {
        enableFuzzySearch: false,
        maxEditDistance: 2,
        minSimilarity: 0.6,
        prefixLength: 1,
      },
      temporalQueries = [],
      aggregationOptions,
      enableFacets = false,
    } = options;

    try {
      // Step 1: Parse and process query
      let processedQuery: string;
      if (typeof query === 'string') {
        processedQuery = query;

        // Apply fuzzy search if enabled
        if (fuzzyOptions.enableFuzzySearch) {
          processedQuery = await this.applyFuzzySearch(query, fuzzyOptions);
        }
      } else {
        // Process boolean query
        processedQuery = await this.processBooleanQuery(query);
      }

      // Step 2: Get all matching documents
      const results = await collection.query({
        queryTexts: [processedQuery],
        nResults: limit * 3, // Get more for filtering
        include: ['documents', 'metadatas', 'distances'],
      });

      let searchResults: KnowledgeSearchResult[] = [];

      if (results.documents?.[0]) {
        for (let i = 0; i < results.documents[0].length; i++) {
          const distance = results.distances?.[0]?.[i] || 1;
          const similarity = 1 - distance;

          if (similarity >= threshold) {
            const metadata = results.metadatas?.[0]?.[i] || {};
            const content = results.documents[0][i] || '';

            // Step 3: Apply temporal filters
            if (temporalQueries.length > 0) {
              const passesTemporalFilter = this.applyTemporalQueries(metadata, temporalQueries);
              if (!passesTemporalFilter) {
                continue;
              }
            }

            const searchResult: KnowledgeSearchResult = {
              item: {
                id: results.ids?.[0]?.[i] || `result_${i}`,
                content,
                metadata,
                tags: typeof metadata.tags === 'string' ? metadata.tags.split(',') : [],
                createdAt:
                  typeof metadata.createdAt === 'string'
                    ? metadata.createdAt
                    : new Date().toISOString(),
                updatedAt:
                  typeof metadata.updatedAt === 'string'
                    ? metadata.updatedAt
                    : new Date().toISOString(),
              },
              score: distance,
              relevance: similarity,
              snippet: this.generateSnippet(content, processedQuery),
            };

            searchResults.push(searchResult);
          }
        }
      }

      // Step 4: Sort and limit results
      searchResults.sort((a, b) => b.relevance - a.relevance);
      const totalCount = searchResults.length;
      searchResults = searchResults.slice(0, limit);

      // Step 5: Generate aggregations if requested
      let aggregations: Record<string, any> | undefined;
      if (aggregationOptions) {
        aggregations = await this.generateAggregations(searchResults, aggregationOptions);
      }

      // Step 6: Generate facets if requested
      let facets: Record<string, Array<{ value: string; count: number }>> | undefined;
      if (enableFacets) {
        facets = this.generateFacets(searchResults);
      }

      const queryTime = Date.now() - startTime;
      this.trackQueryPerformance(
        queryTime,
        typeof query === 'string' ? query : JSON.stringify(query)
      );

      const result: QueryResult = {
        items: searchResults,
        ...(aggregations && { aggregations }),
        totalCount,
        queryTime,
        ...(facets && { facets }),
      };

      logger.info('Advanced query completed', {
        query: typeof query === 'string' ? query : 'boolean_query',
        collection: collectionName,
        results: searchResults.length,
        totalCount,
        queryTime,
        hasAggregations: !!aggregations,
        hasFacets: !!facets,
      });

      return result;
    } catch (error) {
      logger.error('Advanced query failed', { error, query });
      throw error;
    }
  }

  /**
   * Process boolean query into search string
   */
  private async processBooleanQuery(query: BooleanQuery): Promise<string> {
    try {
      const processTerms = (terms: Array<string | BooleanQuery>): string[] => {
        return terms.map(term => {
          if (typeof term === 'string') {
            return term;
          } else {
            // Recursive processing for nested boolean queries
            const nestedTerms = processTerms(term.terms);
            return `(${nestedTerms.join(` ${term.operator} `)})`;
          }
        });
      };

      const processedTerms = processTerms(query.terms);
      return processedTerms.join(` ${query.operator} `);
    } catch (error) {
      logger.error('Failed to process boolean query', { error, query });
      // Fallback to simple string concatenation
      return query.terms.filter(t => typeof t === 'string').join(' ');
    }
  }

  /**
   * Apply fuzzy search to query terms
   */
  private async applyFuzzySearch(query: string, options: FuzzySearchOptions): Promise<string> {
    try {
      const words = query.toLowerCase().split(/\s+/);
      const fuzzyWords: string[] = [];

      for (const word of words) {
        if (word.length <= options.prefixLength) {
          fuzzyWords.push(word);
          continue;
        }

        // Generate fuzzy variants (simplified implementation)
        const variants = [word];

        // Add variants with character substitutions
        for (let i = options.prefixLength; i < word.length; i++) {
          const chars = 'abcdefghijklmnopqrstuvwxyz';
          for (const char of chars) {
            if (char !== word[i]) {
              const variant = word.substring(0, i) + char + word.substring(i + 1);
              variants.push(variant);
            }
          }
        }

        // Add variants with character deletions
        for (let i = options.prefixLength; i < word.length; i++) {
          const variant = word.substring(0, i) + word.substring(i + 1);
          variants.push(variant);
        }

        // Add variants with character insertions
        const chars = 'abcdefghijklmnopqrstuvwxyz';
        for (let i = options.prefixLength; i <= word.length; i++) {
          for (const char of chars) {
            const variant = word.substring(0, i) + char + word.substring(i);
            variants.push(variant);
          }
        }

        // Limit variants and join with OR
        const limitedVariants = variants.slice(0, 5); // Limit to prevent query explosion
        fuzzyWords.push(limitedVariants.join(' OR '));
      }

      return fuzzyWords.join(' ');
    } catch (error) {
      logger.error('Failed to apply fuzzy search', { error, query });
      return query; // Fallback to original query
    }
  }

  /**
   * Apply temporal queries to metadata
   */
  private applyTemporalQueries(metadata: Record<string, any>, queries: TemporalQuery[]): boolean {
    for (const query of queries) {
      const fieldValue = metadata[query.field];
      if (!fieldValue) {
        return false; // Required temporal field is missing
      }

      const date = new Date(fieldValue);
      if (isNaN(date.getTime())) {
        return false; // Invalid date
      }

      const now = new Date();

      switch (query.operator) {
        case 'before':
          if (query.date && date >= query.date) {
            return false;
          }
          break;
        case 'after':
          if (query.date && date <= query.date) {
            return false;
          }
          break;
        case 'between':
          if (query.startDate && query.endDate) {
            if (date < query.startDate || date > query.endDate) {
              return false;
            }
          }
          break;
        case 'within':
          if (query.duration) {
            const match = query.duration.match(/^(\d+)([dmyh])$/);
            if (match && match[1] && match[2]) {
              const [, amount, unit] = match;
              const num = parseInt(amount, 10);
              const cutoffDate = new Date(now);

              switch (unit) {
                case 'h':
                  cutoffDate.setHours(cutoffDate.getHours() - num);
                  break;
                case 'd':
                  cutoffDate.setDate(cutoffDate.getDate() - num);
                  break;
                case 'm':
                  cutoffDate.setMonth(cutoffDate.getMonth() - num);
                  break;
                case 'y':
                  cutoffDate.setFullYear(cutoffDate.getFullYear() - num);
                  break;
              }

              if (date < cutoffDate) {
                return false;
              }
            }
          }
          break;
      }
    }

    return true; // All temporal queries passed
  }

  /**
   * Generate aggregations from search results
   */
  private async generateAggregations(
    results: KnowledgeSearchResult[],
    options: AggregationOptions
  ): Promise<Record<string, any>> {
    const aggregations: Record<string, any> = {};

    try {
      // Group results by specified fields
      const groups: Map<string, KnowledgeSearchResult[]> = new Map();

      for (const result of results) {
        const groupKey = options.groupBy
          .map(field => String(result.item.metadata[field] || 'unknown'))
          .join('|');

        if (!groups.has(groupKey)) {
          groups.set(groupKey, []);
        }
        groups.get(groupKey)!.push(result);
      }

      // Calculate aggregations for each group
      for (const [groupKey, groupResults] of groups) {
        const groupData: Record<string, any> = {};

        for (const agg of options.aggregations) {
          const alias = agg.alias || `${agg.operation}_${agg.field}`;
          const values = groupResults
            .map(r => r.item.metadata[agg.field])
            .filter(v => v !== undefined && v !== null)
            .map(v => (typeof v === 'number' ? v : parseFloat(String(v))))
            .filter(v => !isNaN(v));

          switch (agg.operation) {
            case 'count':
              groupData[alias] = groupResults.length;
              break;
            case 'sum':
              groupData[alias] = values.reduce((sum, val) => sum + val, 0);
              break;
            case 'avg':
              groupData[alias] =
                values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
              break;
            case 'min':
              groupData[alias] = values.length > 0 ? Math.min(...values) : null;
              break;
            case 'max':
              groupData[alias] = values.length > 0 ? Math.max(...values) : null;
              break;
          }
        }

        // Apply having filters
        let includeGroup = true;
        if (options.having) {
          for (const having of options.having) {
            const value = groupData[having.field];
            if (value === undefined || value === null) {
              includeGroup = false;
              break;
            }

            switch (having.operator) {
              case '>':
                if (!(value > having.value)) includeGroup = false;
                break;
              case '<':
                if (!(value < having.value)) includeGroup = false;
                break;
              case '=':
                if (!(value === having.value)) includeGroup = false;
                break;
              case '>=':
                if (!(value >= having.value)) includeGroup = false;
                break;
              case '<=':
                if (!(value <= having.value)) includeGroup = false;
                break;
            }

            if (!includeGroup) break;
          }
        }

        if (includeGroup) {
          aggregations[groupKey] = groupData;
        }
      }

      return aggregations;
    } catch (error) {
      logger.error('Failed to generate aggregations', { error, options });
      return {};
    }
  }

  /**
   * Generate facets from search results
   */
  private generateFacets(
    results: KnowledgeSearchResult[]
  ): Record<string, Array<{ value: string; count: number }>> {
    const facets: Record<string, Map<string, number>> = {};

    // Common facet fields
    const facetFields = ['category', 'type', 'source', 'author', 'language'];

    for (const result of results) {
      for (const field of facetFields) {
        const value = result.item.metadata[field];
        if (value) {
          const valueStr = String(value);

          if (!facets[field]) {
            facets[field] = new Map();
          }

          const current = facets[field].get(valueStr) || 0;
          facets[field].set(valueStr, current + 1);
        }
      }

      // Process tags separately
      if (result.item.tags && result.item.tags.length > 0) {
        if (!facets.tags) {
          facets.tags = new Map();
        }

        for (const tag of result.item.tags) {
          const current = facets.tags.get(tag) || 0;
          facets.tags.set(tag, current + 1);
        }
      }
    }

    // Convert to final format and sort by count
    const finalFacets: Record<string, Array<{ value: string; count: number }>> = {};

    for (const [field, valueMap] of Object.entries(facets)) {
      finalFacets[field] = Array.from(valueMap.entries())
        .map(([value, count]) => ({ value, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 20); // Limit to top 20 values per facet
    }

    return finalFacets;
  }

  async deleteInformation(id: string, collectionName: string = 'knowledge_base'): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const collection = this.collections.get(collectionName);
    if (!collection) {
      throw new Error(`Collection ${collectionName} not found`);
    }

    try {
      await collection.delete({ ids: [id] });
      logger.info('Deleted knowledge item from ChromaDB', { id, collection: collectionName });
    } catch (error) {
      logger.error('Failed to delete knowledge item', { error, id });
      throw error;
    }
  }

  async getCollectionStats(collectionName: string): Promise<{ count: number; name: string }> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const collection = this.collections.get(collectionName);
    if (!collection) {
      throw new Error(`Collection ${collectionName} not found`);
    }

    try {
      const count = await collection.count();
      return { count, name: collectionName };
    } catch (error) {
      logger.error('Failed to get collection stats', { error, collection: collectionName });
      throw error;
    }
  }

  /**
   * Store multiple knowledge items in batch for better performance
   */
  async batchStoreInformation(
    items: KnowledgeItem[],
    collectionName: string = 'knowledge_base'
  ): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const collection = this.collections.get(collectionName);
    if (!collection) {
      throw new Error(`Collection ${collectionName} not found`);
    }

    if (items.length === 0) {
      return;
    }

    try {
      const addData: {
        ids: string[];
        documents: string[];
        metadatas: Array<Record<string, string | number | boolean | null>>;
        embeddings?: number[][];
      } = {
        ids: items.map(item => item.id),
        documents: items.map(item => item.content),
        metadatas: items.map(item => ({
          ...item.metadata,
          tags: item.tags?.join(',') || '',
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
        })),
      };

      // Only add embeddings if all items have them
      const hasEmbeddings = items.every(item => item.embedding && item.embedding.length > 0);
      if (hasEmbeddings) {
        addData.embeddings = items.map(item => item.embedding as number[]);
      }

      await collection.add(addData);

      logger.info('Batch stored knowledge items in ChromaDB', {
        count: items.length,
        collection: collectionName,
        hasEmbeddings,
      });
    } catch (error) {
      logger.error('Failed to batch store knowledge items', {
        error,
        count: items.length,
        collection: collectionName,
      });
      throw error;
    }
  }

  /**
   * Update existing knowledge item
   */
  async updateInformation(
    id: string,
    updates: Partial<KnowledgeItem>,
    collectionName: string = 'knowledge_base'
  ): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const collection = this.collections.get(collectionName);
    if (!collection) {
      throw new Error(`Collection ${collectionName} not found`);
    }

    try {
      // ChromaDB doesn't have direct update, so we need to delete and re-add
      await collection.delete({ ids: [id] });

      // Get the updated item data
      const updateData: {
        ids: string[];
        documents: string[];
        metadatas: Array<Record<string, string | number | boolean | null>>;
        embeddings?: number[][];
      } = {
        ids: [id],
        documents: [updates.content || ''],
        metadatas: [
          {
            ...updates.metadata,
            tags: updates.tags?.join(',') || '',
            createdAt: updates.createdAt || new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ],
      };

      if (updates.embedding) {
        updateData.embeddings = [updates.embedding];
      }

      await collection.add(updateData);

      logger.info('Updated knowledge item in ChromaDB', {
        id,
        collection: collectionName,
      });
    } catch (error) {
      logger.error('Failed to update knowledge item', { error, id });
      throw error;
    }
  }

  /**
   * Store embedding with advanced features (deduplication, versioning, metadata)
   */
  async storeEmbeddingWithMetadata(
    data: KnowledgeItem,
    embeddingMetadata: EmbeddingMetadata,
    options: EmbeddingStorageOptions = {
      enableDeduplication: true,
      enableVersioning: true,
      maxVersions: 5,
      compressionLevel: 6,
      batchSize: 100,
    },
    conflictResolution: EmbeddingConflictResolution = { strategy: 'version' },
    collectionName: string = 'embeddings'
  ): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const collection = this.collections.get(collectionName);
    if (!collection) {
      throw new Error(`Collection ${collectionName} not found`);
    }

    try {
      // Check for deduplication
      if (options.enableDeduplication) {
        const existingHash = this.embeddingHashes.get(data.id);
        if (existingHash === embeddingMetadata.hash) {
          logger.info('Embedding already exists with same hash, skipping', { id: data.id });
          return;
        }
      }

      // Handle versioning
      let finalId = data.id;
      if (options.enableVersioning) {
        const currentVersion = this.embeddingVersions.get(data.id) || 0;
        const newVersion = currentVersion + 1;

        // Check if we need to handle conflicts
        if (currentVersion > 0) {
          switch (conflictResolution.strategy) {
            case 'overwrite':
              // Delete existing versions
              await this.deleteEmbeddingVersions(data.id, collectionName);
              break;
            case 'keep_existing':
              logger.info('Keeping existing embedding version', { id: data.id });
              return;
            case 'version':
              finalId = `${data.id}_v${newVersion}`;
              break;
            case 'merge':
              if (conflictResolution.mergeFunction) {
                // Get existing data and merge
                const existing = await this.getEmbeddingById(data.id, collectionName);
                if (existing) {
                  data = conflictResolution.mergeFunction(existing, data);
                }
              }
              break;
          }
        }

        this.embeddingVersions.set(data.id, newVersion);
        embeddingMetadata.version = newVersion;

        // Clean up old versions if exceeding max
        if (newVersion > options.maxVersions) {
          await this.cleanupOldVersions(data.id, options.maxVersions, collectionName);
        }
      }

      // Store the embedding with enhanced metadata
      const enhancedMetadata = {
        ...data.metadata,
        ...embeddingMetadata,
        tags: data.tags?.join(',') || '',
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
        storageOptions: JSON.stringify(options),
      };

      const addData: {
        ids: string[];
        documents: string[];
        metadatas: Array<Record<string, string | number | boolean | null>>;
        embeddings?: number[][];
      } = {
        ids: [finalId],
        documents: [data.content],
        metadatas: [enhancedMetadata],
      };

      if (data.embedding) {
        addData.embeddings = [data.embedding];
      }

      await collection.add(addData);

      // Update deduplication cache
      if (options.enableDeduplication) {
        this.embeddingHashes.set(data.id, embeddingMetadata.hash);
      }

      logger.info('Stored embedding with advanced metadata', {
        id: finalId,
        originalId: data.id,
        version: embeddingMetadata.version,
        collection: collectionName,
        deduplication: options.enableDeduplication,
        versioning: options.enableVersioning,
      });

      this.emit('embeddingStored', {
        id: finalId,
        metadata: embeddingMetadata,
        options,
      });
    } catch (error) {
      logger.error('Failed to store embedding with metadata', { error, id: data.id });
      throw error;
    }
  }

  /**
   * Get embedding by ID
   */
  private async getEmbeddingById(
    id: string,
    collectionName: string
  ): Promise<KnowledgeItem | null> {
    const collection = this.collections.get(collectionName);
    if (!collection) {
      return null;
    }

    try {
      const results = await collection.get({
        ids: [id],
        include: ['documents', 'metadatas', 'embeddings'],
      });

      if (results.ids && results.ids.length > 0 && results.ids[0]) {
        const metadata = results.metadatas?.[0] || {};
        return {
          id: results.ids[0],
          content: results.documents?.[0] || '',
          metadata,
          ...(results.embeddings?.[0] && { embedding: results.embeddings[0] }),
          tags: typeof metadata.tags === 'string' ? metadata.tags.split(',') : [],
          createdAt:
            typeof metadata.createdAt === 'string' ? metadata.createdAt : new Date().toISOString(),
          updatedAt:
            typeof metadata.updatedAt === 'string' ? metadata.updatedAt : new Date().toISOString(),
        };
      }

      return null;
    } catch (error) {
      logger.error('Failed to get embedding by ID', { error, id });
      return null;
    }
  }

  /**
   * Delete all versions of an embedding
   */
  private async deleteEmbeddingVersions(baseId: string, collectionName: string): Promise<void> {
    const collection = this.collections.get(collectionName);
    if (!collection) {
      return;
    }

    try {
      // Get all items that start with the base ID
      const allItems = await collection.get({
        include: ['documents', 'metadatas'],
      });

      const versionIds =
        allItems.ids?.filter(id => id === baseId || id.startsWith(`${baseId}_v`)) || [];

      if (versionIds.length > 0) {
        await collection.delete({ ids: versionIds });
        logger.info(`Deleted ${versionIds.length} versions of embedding ${baseId}`);
      }
    } catch (error) {
      logger.error('Failed to delete embedding versions', { error, baseId });
    }
  }

  /**
   * Clean up old versions keeping only the latest N versions
   */
  private async cleanupOldVersions(
    baseId: string,
    maxVersions: number,
    collectionName: string
  ): Promise<void> {
    const collection = this.collections.get(collectionName);
    if (!collection) {
      return;
    }

    try {
      // Get all versions
      const allItems = await collection.get({
        include: ['documents', 'metadatas'],
      });

      const versionItems =
        allItems.ids
          ?.map((id, index) => ({
            id,
            metadata: allItems.metadatas?.[index] || {},
          }))
          .filter(item => item.id === baseId || item.id.startsWith(`${baseId}_v`)) || [];

      // Sort by version number (descending)
      versionItems.sort((a, b) => {
        const versionA = typeof a.metadata.version === 'number' ? a.metadata.version : 0;
        const versionB = typeof b.metadata.version === 'number' ? b.metadata.version : 0;
        return versionB - versionA;
      });

      // Delete old versions
      if (versionItems.length > maxVersions) {
        const toDelete = versionItems.slice(maxVersions).map(item => item.id);
        await collection.delete({ ids: toDelete });
        logger.info(`Cleaned up ${toDelete.length} old versions of embedding ${baseId}`);
      }
    } catch (error) {
      logger.error('Failed to cleanup old versions', { error, baseId });
    }
  }

  /**
   * Batch store embeddings with advanced features
   */
  async batchStoreEmbeddingsWithMetadata(
    items: Array<{
      data: KnowledgeItem;
      metadata: EmbeddingMetadata;
    }>,
    options: EmbeddingStorageOptions = {
      enableDeduplication: true,
      enableVersioning: true,
      maxVersions: 5,
      compressionLevel: 6,
      batchSize: 100,
    },
    conflictResolution: EmbeddingConflictResolution = { strategy: 'version' },
    collectionName: string = 'embeddings'
  ): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (items.length === 0) {
      return;
    }

    try {
      // Process items in batches
      for (let i = 0; i < items.length; i += options.batchSize) {
        const batch = items.slice(i, i + options.batchSize);

        // Process each item in the batch
        await Promise.all(
          batch.map(
            async item =>
              await this.storeEmbeddingWithMetadata(
                item.data,
                item.metadata,
                options,
                conflictResolution,
                collectionName
              )
          )
        );

        logger.info(
          `Processed batch ${Math.floor(i / options.batchSize) + 1}/${Math.ceil(items.length / options.batchSize)}`,
          {
            processed: Math.min(i + options.batchSize, items.length),
            total: items.length,
          }
        );
      }

      logger.info('Batch embedding storage completed', {
        total: items.length,
        collection: collectionName,
      });

      this.emit('batchEmbeddingStored', {
        count: items.length,
        collection: collectionName,
        options,
      });
    } catch (error) {
      logger.error('Failed to batch store embeddings', { error, count: items.length });
      throw error;
    }
  }

  /**
   * Create a backup of the knowledge base
   */
  async createBackup(
    options: BackupOptions = {
      includeEmbeddings: true,
      compressionLevel: 6,
      format: 'json',
    }
  ): Promise<string> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = options.destination || path.join(this.chromaPath, 'backups');
    const backupPath = path.join(backupDir, `backup_${timestamp}`);

    try {
      await fs.ensureDir(backupDir);
      await fs.ensureDir(backupPath);

      const backupManifest: {
        timestamp: string;
        version: string;
        collections: Array<{ name: string; file: string; count: number }>;
        options: BackupOptions;
      } = {
        timestamp: new Date().toISOString(),
        version: '1.0',
        collections: [],
        options,
      };

      // Backup each collection
      for (const [collectionName, collection] of this.collections) {
        logger.info(`Backing up collection: ${collectionName}`);

        const collectionData = await collection.get({
          include: options.includeEmbeddings
            ? ['documents', 'metadatas', 'embeddings']
            : ['documents', 'metadatas'],
        });

        const collectionBackup = {
          name: collectionName,
          count: collectionData.ids?.length || 0,
          data: collectionData,
        };

        const collectionPath = path.join(backupPath, `${collectionName}.json`);
        await fs.writeJson(collectionPath, collectionBackup, { spaces: 2 });

        backupManifest.collections.push({
          name: collectionName,
          file: `${collectionName}.json`,
          count: collectionBackup.count,
        });
      }

      // Write backup manifest
      const manifestPath = path.join(backupPath, 'manifest.json');
      await fs.writeJson(manifestPath, backupManifest, { spaces: 2 });

      logger.info('ChromaDB backup completed successfully', {
        path: backupPath,
        collections: backupManifest.collections.length,
      });

      this.emit('backupCompleted', { path: backupPath, manifest: backupManifest });
      return backupPath;
    } catch (error) {
      logger.error('Failed to create ChromaDB backup', error);
      this.emit('backupFailed', error);
      throw error;
    }
  }

  /**
   * Restore knowledge base from backup
   */
  async restoreFromBackup(
    backupPath: string,
    options: RestoreOptions = {
      overwriteExisting: false,
      validateIntegrity: true,
      batchSize: 100,
    }
  ): Promise<void> {
    try {
      // Validate backup directory
      const manifestPath = path.join(backupPath, 'manifest.json');
      if (!(await fs.pathExists(manifestPath))) {
        throw new Error('Invalid backup: manifest.json not found');
      }

      const manifest = await fs.readJson(manifestPath);
      logger.info('Starting ChromaDB restore', {
        backup: manifest.timestamp,
        collections: manifest.collections.length,
      });

      // Initialize if not already done
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Restore each collection
      for (const collectionInfo of manifest.collections) {
        const collectionPath = path.join(backupPath, collectionInfo.file);
        const collectionBackup = await fs.readJson(collectionPath);

        logger.info(`Restoring collection: ${collectionInfo.name}`);

        // Create or get collection
        let collection: Collection;
        try {
          collection = await this.chromaClient.getCollection({ name: collectionInfo.name });

          if (!options.overwriteExisting) {
            logger.warn(`Collection ${collectionInfo.name} already exists, skipping`);
            continue;
          }

          // Clear existing data if overwriting
          await collection.delete({ where: {} });
        } catch {
          // Collection doesn't exist, create it
          collection = await this.ensureCollection(collectionInfo.name);
        }

        // Restore data in batches
        const data = collectionBackup.data;
        if (data.ids && data.ids.length > 0) {
          for (let i = 0; i < data.ids.length; i += options.batchSize) {
            const batchEnd = Math.min(i + options.batchSize, data.ids.length);

            const batchData: any = {
              ids: data.ids.slice(i, batchEnd),
              documents: data.documents?.slice(i, batchEnd) || [],
              metadatas: data.metadatas?.slice(i, batchEnd) || [],
            };

            if (data.embeddings && manifest.options?.includeEmbeddings) {
              batchData.embeddings = data.embeddings.slice(i, batchEnd);
            }

            await collection.add(batchData);
          }
        }

        logger.info(
          `Restored collection ${collectionInfo.name} with ${collectionInfo.count} items`
        );
      }

      logger.info('ChromaDB restore completed successfully');
      this.emit('restoreCompleted', { backupPath, manifest });
    } catch (error) {
      logger.error('Failed to restore ChromaDB backup', error);
      this.emit('restoreFailed', error);
      throw error;
    }
  }

  /**
   * Get health status
   */
  async getHealthStatus(): Promise<ChromaHealthStatus> {
    return this.performHealthCheck();
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): {
    isConnected: boolean;
    retryCount: number;
    lastHealthCheck: Date;
  } {
    return {
      isConnected: this.isInitialized,
      retryCount: this.connectionRetryCount,
      lastHealthCheck: this.lastHealthCheck,
    };
  }

  /**
   * Export knowledge base data in various formats
   */
  async exportKnowledgeBase(options: ExportOptions): Promise<string> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const exportDir = path.join(this.chromaPath, 'exports');
    const exportPath = path.join(exportDir, `export_${timestamp}`);

    try {
      await fs.ensureDir(exportDir);
      await fs.ensureDir(exportPath);

      const collectionsToExport = options.collections || Array.from(this.collections.keys());
      const exportManifest: {
        timestamp: string;
        version: string;
        format: string;
        options: ExportOptions;
        collections: Array<{ name: string; file: string; count: number }>;
      } = {
        timestamp: new Date().toISOString(),
        version: '1.0',
        format: options.format,
        options,
        collections: [],
      };

      for (const collectionName of collectionsToExport) {
        const collection = this.collections.get(collectionName);
        if (!collection) {
          logger.warn(`Collection ${collectionName} not found, skipping export`);
          continue;
        }

        logger.info(`Exporting collection: ${collectionName}`);

        const collectionData = await collection.get({
          include: options.includeEmbeddings
            ? ['documents', 'metadatas', 'embeddings']
            : ['documents', 'metadatas'],
        });

        let exportData: any;
        let fileName: string;

        switch (options.format) {
          case 'json':
            exportData = {
              collection: collectionName,
              count: collectionData.ids?.length || 0,
              data: collectionData,
              metadata: options.includeMetadata
                ? await this.getCollectionIndexStats(collectionName)
                : undefined,
            };
            fileName = `${collectionName}.json`;
            await fs.writeJson(path.join(exportPath, fileName), exportData, { spaces: 2 });
            break;

          case 'csv':
            exportData = this.convertToCSV(collectionData, options.includeEmbeddings);
            fileName = `${collectionName}.csv`;
            await fs.writeFile(path.join(exportPath, fileName), exportData);
            break;

          case 'xml':
            exportData = this.convertToXML(collectionData, options.includeEmbeddings);
            fileName = `${collectionName}.xml`;
            await fs.writeFile(path.join(exportPath, fileName), exportData);
            break;
        }

        exportManifest.collections.push({
          name: collectionName,
          file: fileName,
          count: collectionData.ids?.length || 0,
        });
      }

      // Write export manifest
      const manifestPath = path.join(exportPath, 'manifest.json');
      await fs.writeJson(manifestPath, exportManifest, { spaces: 2 });

      // Compress if requested
      if (options.compression) {
        // In a real implementation, you'd use a compression library like JSZip
        logger.info('Compression requested but not implemented in this example');
      }

      logger.info('Knowledge base export completed', {
        path: exportPath,
        collections: exportManifest.collections.length,
        format: options.format,
      });

      this.emit('exportCompleted', { path: exportPath, manifest: exportManifest });
      return exportPath;
    } catch (error) {
      logger.error('Failed to export knowledge base', error);
      this.emit('exportFailed', error);
      throw error;
    }
  }

  /**
   * Import knowledge base data from various formats
   */
  async importKnowledgeBase(importPath: string, options: ImportOptions): Promise<void> {
    try {
      // Validate import directory
      const manifestPath = path.join(importPath, 'manifest.json');
      if (!(await fs.pathExists(manifestPath))) {
        throw new Error('Invalid import: manifest.json not found');
      }

      const manifest = await fs.readJson(manifestPath);
      logger.info('Starting knowledge base import', {
        source: manifest.timestamp,
        collections: manifest.collections.length,
        format: options.format,
      });

      if (!this.isInitialized) {
        await this.initialize();
      }

      for (const collectionInfo of manifest.collections) {
        const collectionPath = path.join(importPath, collectionInfo.file);
        const targetCollection = options.targetCollection || collectionInfo.name;

        logger.info(`Importing collection: ${collectionInfo.name} -> ${targetCollection}`);

        let importData: any;

        switch (options.format) {
          case 'json':
            importData = await fs.readJson(collectionPath);
            break;
          case 'csv': {
            const csvContent = await fs.readFile(collectionPath, 'utf-8');
            importData = this.parseCSV(csvContent);
            break;
          }
          case 'xml': {
            const xmlContent = await fs.readFile(collectionPath, 'utf-8');
            importData = this.parseXML(xmlContent);
            break;
          }
        }

        // Validate data if requested
        if (options.validateData) {
          const isValid = this.validateImportData(importData);
          if (!isValid) {
            throw new Error(`Invalid data in collection ${collectionInfo.name}`);
          }
        }

        // Create or get target collection
        let collection: Collection;
        try {
          collection = await this.chromaClient.getCollection({ name: targetCollection });

          if (!options.overwriteExisting) {
            logger.warn(`Collection ${targetCollection} already exists, skipping`);
            continue;
          }

          // Clear existing data if overwriting
          await collection.delete({ where: {} });
        } catch {
          collection = await this.ensureCollection(targetCollection);
        }

        // Import data in batches
        const data = importData.data || importData;
        if (data.ids && data.ids.length > 0) {
          for (let i = 0; i < data.ids.length; i += options.batchSize) {
            const batchEnd = Math.min(i + options.batchSize, data.ids.length);

            const batchData: any = {
              ids: data.ids.slice(i, batchEnd),
              documents: data.documents?.slice(i, batchEnd) || [],
              metadatas: data.metadatas?.slice(i, batchEnd) || [],
            };

            if (data.embeddings && !options.generateEmbeddings) {
              batchData.embeddings = data.embeddings.slice(i, batchEnd);
            }

            await collection.add(batchData);
          }
        }

        logger.info(`Imported collection ${targetCollection} with ${collectionInfo.count} items`);
      }

      logger.info('Knowledge base import completed successfully');
      this.emit('importCompleted', { importPath, manifest });
    } catch (error) {
      logger.error('Failed to import knowledge base', error);
      this.emit('importFailed', error);
      throw error;
    }
  }

  /**
   * Generate comprehensive analytics report
   */
  async generateAnalyticsReport(): Promise<AnalyticsReport> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const report: AnalyticsReport = {
        overview: {
          totalCollections: this.collections.size,
          totalDocuments: 0,
          totalEmbeddings: 0,
          totalSize: 0,
          lastUpdated: new Date(),
        },
        collections: [],
        usage: {
          queryCount: this.performanceMetrics.queryCount,
          averageQueryTime: this.performanceMetrics.averageQueryTime,
          cacheHitRate: this.performanceMetrics.cacheStats.hitRate,
          slowQueries: this.performanceMetrics.slowQueries.length,
        },
        performance: {
          indexingTime: this.performanceMetrics.indexingStats.indexingTime,
          optimizationCount: 0, // Would track this in real implementation
          lastOptimization: new Date(),
          fragmentationLevel: 0, // Would calculate this based on collection analysis
        },
        trends: {
          documentsPerDay: [],
          queriesPerDay: [],
          popularQueries: [],
        },
      };

      // Analyze each collection
      for (const [collectionName] of this.collections) {
        const stats = await this.getCollectionIndexStats(collectionName);

        report.overview.totalDocuments += stats.totalDocuments;
        report.overview.totalEmbeddings += stats.totalEmbeddings;
        report.overview.totalSize += stats.indexSize;

        report.collections.push({
          name: collectionName,
          documentCount: stats.totalDocuments,
          embeddingCount: stats.totalEmbeddings,
          averageDimensions: stats.averageEmbeddingDimensions,
          size: stats.indexSize,
          lastModified: new Date(), // Would track this in real implementation
        });
      }

      // Generate trends (simplified implementation)
      report.trends.popularQueries = this.performanceMetrics.slowQueries
        .slice(0, 10)
        .map(sq => ({ query: sq.query, count: 1 })); // Would aggregate properly in real implementation

      logger.info('Analytics report generated', {
        collections: report.overview.totalCollections,
        documents: report.overview.totalDocuments,
        size: report.overview.totalSize,
      });

      this.emit('analyticsGenerated', report);
      return report;
    } catch (error) {
      logger.error('Failed to generate analytics report', error);
      throw error;
    }
  }

  /**
   * Convert collection data to CSV format
   */
  private convertToCSV(data: any, includeEmbeddings: boolean): string {
    if (!data.ids || data.ids.length === 0) {
      return 'id,document,metadata\n';
    }

    const headers = ['id', 'document'];

    // Add metadata headers
    const metadataKeys = new Set<string>();
    if (data.metadatas) {
      for (const metadata of data.metadatas) {
        if (metadata) {
          Object.keys(metadata).forEach(key => metadataKeys.add(key));
        }
      }
    }
    headers.push(...Array.from(metadataKeys));

    if (includeEmbeddings && data.embeddings) {
      headers.push('embeddings');
    }

    const rows = [headers.join(',')];

    for (let i = 0; i < data.ids.length; i++) {
      const row = [];
      row.push(this.escapeCsvValue(data.ids[i]));
      row.push(this.escapeCsvValue(data.documents?.[i] || ''));

      const metadata = data.metadatas?.[i] || {};
      for (const key of metadataKeys) {
        row.push(this.escapeCsvValue(String(metadata[key] || '')));
      }

      if (includeEmbeddings && data.embeddings?.[i]) {
        row.push(this.escapeCsvValue(JSON.stringify(data.embeddings[i])));
      }

      rows.push(row.join(','));
    }

    return rows.join('\n');
  }

  /**
   * Convert collection data to XML format
   */
  private convertToXML(data: any, includeEmbeddings: boolean): string {
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n<collection>\n';

    if (data.ids) {
      for (let i = 0; i < data.ids.length; i++) {
        xml += '  <item>\n';
        xml += `    <id>${this.escapeXmlValue(data.ids[i])}</id>\n`;
        xml += `    <document><![CDATA[${data.documents?.[i] || ''}]]></document>\n`;

        const metadata = data.metadatas?.[i] || {};
        xml += '    <metadata>\n';
        for (const [key, value] of Object.entries(metadata)) {
          xml += `      <${key}>${this.escapeXmlValue(String(value))}</${key}>\n`;
        }
        xml += '    </metadata>\n';

        if (includeEmbeddings && data.embeddings?.[i]) {
          xml += `    <embeddings>${JSON.stringify(data.embeddings[i])}</embeddings>\n`;
        }

        xml += '  </item>\n';
      }
    }

    xml += '</collection>';
    return xml;
  }

  /**
   * Parse CSV data
   */
  private parseCSV(csvContent: string): any {
    const lines = csvContent.split('\n').filter(line => line.trim());
    if (lines.length === 0) {
      return { ids: [], documents: [], metadatas: [] };
    }

    const headers = lines[0]?.split(',').map(h => h.trim()) || [];
    const data: any = {
      ids: [],
      documents: [],
      metadatas: [],
    };

    for (let i = 1; i < lines.length; i++) {
      const line = lines[i];
      if (!line) continue;

      const values = this.parseCsvLine(line);
      const metadata: Record<string, any> = {};

      for (let j = 0; j < headers.length; j++) {
        const header = headers[j];
        if (!header) continue;

        const value = values[j] || '';

        switch (header) {
          case 'id':
            data.ids.push(value);
            break;
          case 'document':
            data.documents.push(value);
            break;
          case 'embeddings':
            if (!data.embeddings) data.embeddings = [];
            try {
              data.embeddings.push(JSON.parse(value));
            } catch {
              data.embeddings.push([]);
            }
            break;
          default:
            metadata[header] = value;
            break;
        }
      }

      data.metadatas.push(metadata);
    }

    return data;
  }

  /**
   * Parse XML data
   */
  private parseXML(xmlContent: string): any {
    // Simplified XML parsing - in a real implementation, use a proper XML parser
    const data: any = {
      ids: [],
      documents: [],
      metadatas: [],
    };

    const itemMatches = xmlContent.match(/<item>(.*?)<\/item>/gs);
    if (!itemMatches) {
      return data;
    }

    for (const itemMatch of itemMatches) {
      const idMatch = itemMatch.match(/<id>(.*?)<\/id>/s);
      const docMatch =
        itemMatch.match(/<document><!\[CDATA\[(.*?)\]\]><\/document>/s) ||
        itemMatch.match(/<document>(.*?)<\/document>/s);
      const metadataMatch = itemMatch.match(/<metadata>(.*?)<\/metadata>/s);

      data.ids.push(idMatch?.[1] || '');
      data.documents.push(docMatch?.[1] || '');

      const metadata: Record<string, any> = {};
      if (metadataMatch && metadataMatch[1]) {
        const metadataContent = metadataMatch[1];
        const fieldMatches = metadataContent.match(/<(\w+)>(.*?)<\/\1>/g);
        if (fieldMatches) {
          for (const fieldMatch of fieldMatches) {
            const fieldParts = fieldMatch.match(/<(\w+)>(.*?)<\/\1>/);
            if (fieldParts && fieldParts[1] && fieldParts[2] !== undefined) {
              metadata[fieldParts[1]] = fieldParts[2];
            }
          }
        }
      }
      data.metadatas.push(metadata);
    }

    return data;
  }

  /**
   * Validate import data
   */
  private validateImportData(data: any): boolean {
    try {
      // Basic validation
      if (!data || typeof data !== 'object') {
        return false;
      }

      const actualData = data.data || data;

      if (!actualData.ids || !Array.isArray(actualData.ids)) {
        return false;
      }

      if (!actualData.documents || !Array.isArray(actualData.documents)) {
        return false;
      }

      if (actualData.ids.length !== actualData.documents.length) {
        return false;
      }

      if (actualData.metadatas && actualData.metadatas.length !== actualData.ids.length) {
        return false;
      }

      if (actualData.embeddings && actualData.embeddings.length !== actualData.ids.length) {
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Data validation failed', error);
      return false;
    }
  }

  /**
   * Helper methods for CSV/XML escaping
   */
  private escapeCsvValue(value: string): string {
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`;
    }
    return value;
  }

  private escapeXmlValue(value: string): string {
    return value
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;');
  }

  private parseCsvLine(line: string): string[] {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];

      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          current += '"';
          i++; // Skip next quote
        } else {
          inQuotes = !inQuotes;
        }
      } else if (char === ',' && !inQuotes) {
        result.push(current);
        current = '';
      } else {
        current += char;
      }
    }

    result.push(current);
    return result;
  }

  cleanup(): void {
    try {
      // Stop health monitoring
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }

      // Stop auto indexing
      this.stopAutoIndexing();

      // Clear caches
      this.clearSearchCache();

      // Perform any necessary cleanup
      this.collections.clear();
      this.isInitialized = false;

      logger.info('ChromaDB service cleaned up');
      this.emit('cleanup');
    } catch (error) {
      logger.error('Error during ChromaDB cleanup', error);
    }
  }
}

// Export singleton instance
export const chromaKnowledgeBase = new ChromaKnowledgeBaseService();

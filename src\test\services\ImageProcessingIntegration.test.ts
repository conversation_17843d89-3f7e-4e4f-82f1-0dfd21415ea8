import { describe, test, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import fs from 'fs';
import path from 'path';
import sharp from 'sharp';
import { ImageProcessor } from '../../main/services/ImageProcessor';
import { ComputerVisionService } from '../../main/services/ComputerVisionService';
import { AdvancedImageAnalysisService } from '../../main/services/AdvancedImageAnalysisService';
import { ImageProcessingWorkerPool } from '../../main/workers/ImageProcessingWorker';
import { ProcessingOptions } from '../../shared/types/Document';

describe('Image Processing and Computer Vision Integration', () => {
  let imageProcessor: ImageProcessor;
  let computerVisionService: ComputerVisionService;
  let advancedAnalysisService: AdvancedImageAnalysisService;
  let workerPool: ImageProcessingWorkerPool;
  let testImageBuffer: Buffer;
  let testDocumentBuffer: Buffer;

  beforeAll(async () => {
    // Initialize services
    imageProcessor = new ImageProcessor();
    computerVisionService = new ComputerVisionService();
    advancedAnalysisService = new AdvancedImageAnalysisService();
    workerPool = new ImageProcessingWorkerPool({ maxWorkers: 2 });

    await advancedAnalysisService.initialize();
    await workerPool.initialize();

    // Create test images
    testImageBuffer = await createTestImage();
    testDocumentBuffer = await createTestDocument();
  });

  afterAll(async () => {
    await advancedAnalysisService.cleanup();
    await workerPool.shutdown();
  });

  beforeEach(() => {
    // Reset any state if needed
  });

  describe('ImageProcessor', () => {
    test('should process image with default options', async () => {
      const result = await imageProcessor.processImage(testImageBuffer);

      expect(result.success).toBe(true);
      expect(result.originalImage).toBeDefined();
      expect(result.processedImage).toBeDefined();
      expect(result.enhancements).toBeInstanceOf(Array);
      expect(result.processingTime).toBeGreaterThan(0);
    });

    test('should enhance image for OCR', async () => {
      const enhancedBuffer = await imageProcessor.enhanceForOCR(testImageBuffer);

      expect(enhancedBuffer).toBeInstanceOf(Buffer);
      expect(enhancedBuffer.length).toBeGreaterThan(0);

      // Verify the enhanced image has expected properties
      const metadata = await sharp(enhancedBuffer).metadata();
      expect(metadata.width).toBeGreaterThanOrEqual(2000);
      expect(metadata.format).toBe('png');
    });

    test('should upscale image correctly', async () => {
      const originalMetadata = await sharp(testImageBuffer).metadata();
      const upscaledBuffer = await imageProcessor.upscaleImage(testImageBuffer, 2);
      const upscaledMetadata = await sharp(upscaledBuffer).metadata();

      expect(upscaledMetadata.width).toBe((originalMetadata.width || 0) * 2);
      expect(upscaledMetadata.height).toBe((originalMetadata.height || 0) * 2);
    });

    test('should remove noise from image', async () => {
      const denoisedBuffer = await imageProcessor.removeNoise(testImageBuffer);

      expect(denoisedBuffer).toBeInstanceOf(Buffer);
      expect(denoisedBuffer.length).toBeGreaterThan(0);
    });

    test('should adjust contrast', async () => {
      const contrastBuffer = await imageProcessor.adjustContrast(testImageBuffer);

      expect(contrastBuffer).toBeInstanceOf(Buffer);
      expect(contrastBuffer.length).toBeGreaterThan(0);
    });

    test('should convert image format', async () => {
      const jpegBuffer = await imageProcessor.convertFormat(testImageBuffer, 'jpeg', 90);
      const metadata = await sharp(jpegBuffer).metadata();

      expect(metadata.format).toBe('jpeg');
    });

    test('should detect features in image', async () => {
      const features = await imageProcessor.detectFeatures(testImageBuffer);

      expect(features).toBeInstanceOf(Array);
      // Features array might be empty for simple test images
    });

    test('should batch process multiple images', async () => {
      const images = [testImageBuffer, testImageBuffer];
      const results = await imageProcessor.batchProcess(images);

      expect(results).toHaveLength(2);
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(true);
    });
  });

  describe('ComputerVisionService', () => {
    test('should analyze document and detect features', async () => {
      const result = await computerVisionService.analyzeDocument(testDocumentBuffer);

      expect(result.features).toBeInstanceOf(Array);
      expect(result.barcodes).toBeInstanceOf(Array);
      expect(result.layout).toBeDefined();
      expect(result.layout.orientation).toMatch(/portrait|landscape/);
      expect(result.processingTime).toBeGreaterThan(0);
      expect(result.confidence).toBeGreaterThanOrEqual(0);
      expect(result.confidence).toBeLessThanOrEqual(1);
    });

    test('should detect text regions', async () => {
      const result = await computerVisionService.analyzeDocument(testDocumentBuffer, {
        detectText: true,
        detectImages: false,
        detectTables: false,
        detectBarcodes: false,
        detectSignatures: false,
        detectStamps: false,
        analyzeLayout: false,
        minimumConfidence: 0.5,
      });

      expect(result.textRegions).toBeInstanceOf(Array);
    });

    test('should analyze document layout', async () => {
      const result = await computerVisionService.analyzeDocument(testDocumentBuffer, {
        analyzeLayout: true,
        minimumConfidence: 0.3,
      });

      expect(result.layout).toBeDefined();
      expect(result.layout.regions).toBeInstanceOf(Array);
      expect(result.layout.readingOrder).toBeInstanceOf(Array);
      expect(result.layout.columns).toBeGreaterThanOrEqual(1);
      expect(result.layout.margins).toBeDefined();
    });
  });

  describe('AdvancedImageAnalysisService', () => {
    test('should perform comprehensive image analysis', async () => {
      const result = await advancedAnalysisService.analyzeImage(testDocumentBuffer);

      expect(result.success).toBe(true);
      expect(result.computerVision).toBeDefined();
      expect(result.documentClassification).toBeDefined();
      expect(result.qualityMetrics).toBeDefined();
      expect(result.extractedData).toBeInstanceOf(Array);
      expect(result.processingTime).toBeGreaterThan(0);
    });

    test('should classify document type', async () => {
      const result = await advancedAnalysisService.analyzeImage(testDocumentBuffer);

      expect(result.documentClassification.documentType).toMatch(
        /invoice|receipt|form|letter|contract|unknown/
      );
      expect(result.documentClassification.confidence).toBeGreaterThanOrEqual(0);
      expect(result.documentClassification.confidence).toBeLessThanOrEqual(1);
      expect(result.documentClassification.features).toBeInstanceOf(Array);
      expect(result.documentClassification.reasoning).toBeDefined();
    });

    test('should assess image quality', async () => {
      const result = await advancedAnalysisService.analyzeImage(testImageBuffer);

      expect(result.qualityMetrics.sharpness).toBeGreaterThanOrEqual(0);
      expect(result.qualityMetrics.sharpness).toBeLessThanOrEqual(1);
      expect(result.qualityMetrics.contrast).toBeGreaterThanOrEqual(0);
      expect(result.qualityMetrics.contrast).toBeLessThanOrEqual(1);
      expect(result.qualityMetrics.brightness).toBeGreaterThanOrEqual(0);
      expect(result.qualityMetrics.brightness).toBeLessThanOrEqual(1);
      expect(result.qualityMetrics.noise).toBeGreaterThanOrEqual(0);
      expect(result.qualityMetrics.noise).toBeLessThanOrEqual(1);
      expect(result.qualityMetrics.overall).toBeGreaterThanOrEqual(0);
      expect(result.qualityMetrics.overall).toBeLessThanOrEqual(1);
    });

    test('should extract structured data', async () => {
      const result = await advancedAnalysisService.analyzeImage(testDocumentBuffer);

      expect(result.extractedData).toBeInstanceOf(Array);
      expect(result.extractedData.length).toBeGreaterThan(0);

      // Check that extracted data has required properties
      for (const data of result.extractedData) {
        expect(data.id).toBeDefined();
        expect(data.type).toBeDefined();
        expect(data.confidence).toBeGreaterThanOrEqual(0);
        expect(data.confidence).toBeLessThanOrEqual(1);
        expect(data.extractionMethod).toBeDefined();
        expect(data.createdAt).toBeInstanceOf(Date);
      }
    });
  });

  describe('ImageProcessingWorkerPool', () => {
    test('should process tasks in worker pool', async () => {
      const options: ProcessingOptions = {
        enhanceImages: true,
        performOCR: false,
        extractTables: false,
        detectFormFields: false,
      };

      const taskId = await workerPool.addTask(testImageBuffer, options, 'normal');

      expect(taskId).toBeDefined();
      expect(typeof taskId).toBe('string');

      // Wait for task completion
      await new Promise<void>((resolve) => {
        workerPool.on('taskCompleted', (result) => {
          if (result.taskId === taskId) {
            expect(result.success).toBe(true);
            expect(result.result).toBeDefined();
            resolve();
          }
        });

        workerPool.on('taskError', (result) => {
          if (result.taskId === taskId) {
            throw new Error(`Task failed: ${result.error}`);
          }
        });
      });
    });

    test('should handle task priorities correctly', async () => {
      const options: ProcessingOptions = {
        enhanceImages: true,
        performOCR: false,
        extractTables: false,
        detectFormFields: false,
      };

      // Add tasks with different priorities
      const lowPriorityTask = await workerPool.addTask(testImageBuffer, options, 'low');
      const highPriorityTask = await workerPool.addTask(testImageBuffer, options, 'high');
      const normalPriorityTask = await workerPool.addTask(testImageBuffer, options, 'normal');

      const stats = workerPool.getQueueStats();
      expect(stats.queuedTasks).toBeGreaterThanOrEqual(0);
      expect(stats.totalWorkers).toBe(2);
    });

    test('should provide queue statistics', () => {
      const stats = workerPool.getQueueStats();

      expect(stats).toHaveProperty('queuedTasks');
      expect(stats).toHaveProperty('activeTasks');
      expect(stats).toHaveProperty('availableWorkers');
      expect(stats).toHaveProperty('totalWorkers');
      expect(stats.totalWorkers).toBe(2);
    });
  });
});

// Helper functions for creating test images
async function createTestImage(): Promise<Buffer> {
  return await sharp({
    create: {
      width: 800,
      height: 600,
      channels: 3,
      background: { r: 255, g: 255, b: 255 },
    },
  })
    .png()
    .toBuffer();
}

async function createTestDocument(): Promise<Buffer> {
  // Create a more complex test document with text-like patterns
  return await sharp({
    create: {
      width: 1200,
      height: 1600,
      channels: 3,
      background: { r: 255, g: 255, b: 255 },
    },
  })
    .composite([
      {
        input: await sharp({
          create: {
            width: 1000,
            height: 100,
            channels: 3,
            background: { r: 0, g: 0, b: 0 },
          },
        })
          .png()
          .toBuffer(),
        top: 100,
        left: 100,
      },
      {
        input: await sharp({
          create: {
            width: 800,
            height: 50,
            channels: 3,
            background: { r: 0, g: 0, b: 0 },
          },
        })
          .png()
          .toBuffer(),
        top: 300,
        left: 200,
      },
    ])
    .png()
    .toBuffer();
}

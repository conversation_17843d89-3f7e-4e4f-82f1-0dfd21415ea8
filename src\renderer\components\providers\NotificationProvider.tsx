import React, { createContext, useContext } from 'react';
import { Toaster, toast, ToastOptions } from 'react-hot-toast';

interface NotificationContextType {
  success: (message: string, options?: ToastOptions) => void;
  error: (message: string, options?: ToastOptions) => void;
  info: (message: string, options?: ToastOptions) => void;
  warning: (message: string, options?: ToastOptions) => void;
  loading: (message: string, options?: ToastOptions) => string;
  dismiss: (toastId?: string) => void;
  promise: <T>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    },
    options?: ToastOptions
  ) => Promise<T>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

/**
 * Custom toast styles and configuration
 */
const toastConfig: ToastOptions = {
  duration: 4000,
  position: 'top-right',
  style: {
    background: 'hsl(var(--b1))',
    color: 'hsl(var(--bc))',
    border: '1px solid hsl(var(--b3))',
    borderRadius: '0.5rem',
    fontSize: '0.875rem',
    fontWeight: '500',
    padding: '12px 16px',
    boxShadow: '0 4px 12px hsl(var(--bc) / 0.1)',
    maxWidth: '400px',
  },
};

/**
 * Notification provider component with enhanced toast functionality
 */
export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const success = (message: string, options?: ToastOptions) => {
    toast.success(message, {
      ...toastConfig,
      iconTheme: {
        primary: 'hsl(var(--su))',
        secondary: 'hsl(var(--suc))',
      },
      ...options,
    });
  };

  const error = (message: string, options?: ToastOptions) => {
    toast.error(message, {
      ...toastConfig,
      duration: 6000, // Longer duration for errors
      iconTheme: {
        primary: 'hsl(var(--er))',
        secondary: 'hsl(var(--erc))',
      },
      ...options,
    });
  };

  const info = (message: string, options?: ToastOptions) => {
    toast(message, {
      ...toastConfig,
      icon: '💡',
      iconTheme: {
        primary: 'hsl(var(--in))',
        secondary: 'hsl(var(--inc))',
      },
      ...options,
    });
  };

  const warning = (message: string, options?: ToastOptions) => {
    toast(message, {
      ...toastConfig,
      icon: '⚠️',
      iconTheme: {
        primary: 'hsl(var(--wa))',
        secondary: 'hsl(var(--wac))',
      },
      ...options,
    });
  };

  const loading = (message: string, options?: ToastOptions) => {
    return toast.loading(message, {
      ...toastConfig,
      duration: Infinity, // Loading toasts don't auto-dismiss
      ...options,
    });
  };

  const dismiss = (toastId?: string) => {
    toast.dismiss(toastId);
  };

  const promiseToast = <T,>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    },
    options?: ToastOptions
  ) => {
    return toast.promise(
      promise,
      messages,
      {
        ...toastConfig,
        success: {
          iconTheme: {
            primary: 'hsl(var(--su))',
            secondary: 'hsl(var(--suc))',
          },
        },
        error: {
          duration: 6000,
          iconTheme: {
            primary: 'hsl(var(--er))',
            secondary: 'hsl(var(--erc))',
          },
        },
        loading: {
          duration: Infinity,
        },
        ...options,
      }
    );
  };

  const value: NotificationContextType = {
    success,
    error,
    info,
    warning,
    loading,
    dismiss,
    promise: promiseToast,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
      <Toaster
        position="top-right"
        reverseOrder={false}
        gutter={8}
        containerClassName="toast-container"
        containerStyle={{
          top: 40,
          right: 20,
          zIndex: 9999,
        }}
        toastOptions={{
          ...toastConfig,
          className: 'toast-notification',
        }}
      />
    </NotificationContext.Provider>
  );
};

/**
 * Hook to use notification context
 */
export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

/**
 * Utility functions for common notification patterns
 */
export const notifications = {
  // Document processing notifications
  documentProcessing: (filename: string) => {
    return toast.loading(`Processing ${filename}...`, {
      ...toastConfig,
      duration: Infinity,
    });
  },

  documentProcessed: (filename: string, toastId?: string) => {
    if (toastId) {
      toast.success(`${filename} processed successfully!`, {
        id: toastId,
        ...toastConfig,
      });
    } else {
      toast.success(`${filename} processed successfully!`, toastConfig);
    }
  },

  documentError: (filename: string, error: string, toastId?: string) => {
    if (toastId) {
      toast.error(`Failed to process ${filename}: ${error}`, {
        id: toastId,
        ...toastConfig,
        duration: 8000,
      });
    } else {
      toast.error(`Failed to process ${filename}: ${error}`, {
        ...toastConfig,
        duration: 8000,
      });
    }
  },

  // AI operation notifications
  aiThinking: (operation: string) => {
    return toast.loading(`AI is ${operation}...`, {
      ...toastConfig,
      duration: Infinity,
      icon: '🤖',
    });
  },

  aiComplete: (operation: string, toastId?: string) => {
    if (toastId) {
      toast.success(`AI ${operation} completed!`, {
        id: toastId,
        ...toastConfig,
        icon: '✨',
      });
    } else {
      toast.success(`AI ${operation} completed!`, {
        ...toastConfig,
        icon: '✨',
      });
    }
  },

  // Form operations
  formSaved: () => {
    toast.success('Form saved successfully!', {
      ...toastConfig,
      icon: '💾',
    });
  },

  formFilled: () => {
    toast.success('Form filled automatically!', {
      ...toastConfig,
      icon: '📝',
    });
  },

  // System notifications
  connectionLost: () => {
    toast.error('Connection lost. Please check your network.', {
      ...toastConfig,
      duration: 8000,
      icon: '🔌',
    });
  },

  connectionRestored: () => {
    toast.success('Connection restored!', {
      ...toastConfig,
      icon: '✅',
    });
  },

  updateAvailable: () => {
    toast('A new update is available!', {
      ...toastConfig,
      duration: 8000,
      icon: '🔄',
    });
  },
};

export default NotificationProvider;

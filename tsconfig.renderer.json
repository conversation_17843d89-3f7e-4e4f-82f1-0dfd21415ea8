{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "node", "jsx": "react-jsx", "noEmit": false, "composite": true, "outDir": "./dist/renderer", "rootDir": "./src", "types": ["node", "react", "react-dom", "jest", "@testing-library/jest-dom"], "skipLibCheck": true}, "include": ["src/renderer/**/*", "src/shared/**/*", "src/preload/**/*", "src/types/**/*"], "exclude": ["src/main/**/*", "node_modules", "dist", "tests/**/*"]}
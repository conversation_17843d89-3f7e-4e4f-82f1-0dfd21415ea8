---
type: 'always_apply'
description: 'Example description'
---

Review the changed files for violations of core CS principles: DRY (Don't Repeat
Yourself), KISS (Keep It Simple), SRP (Single Responsibility Principle),
Separation of Concerns, Fail Fast/Fail Loud, Use Established Interfaces,
Command-Query Separation, and Modularity/Reusability. Identify any code that
violates these principles and suggest specific refactoring improvements. Focus
on: 1) Repetitive logic that should be extracted into reusable functions, 2)
Overly complex or clever code that should be simplified, 3) Functions doing
multiple things that should be split, 4) Mixed concerns that should be
separated, 5) Silent failures that should fail fast and loud, 6) Reinvented
functionality that should use existing interfaces, 7) Functions that both
command and query, 8) Non-modular code that should be made reusable.

# Product Overview

## AI Document Processor - Enterprise Paperwork Management System

A sophisticated cross-platform Electron desktop application that serves as an
enterprise-grade AI-powered document processing and intelligent paperwork
management system. The application employs advanced AI agents with comprehensive
ETL-like pipelines, multi-process architecture, and enterprise-level features to
analyze, process, and automatically fill complex document types including tax
forms, legal documents, financial statements, and regulatory filings.

## Core Value Proposition

### Intelligent AI-Powered Processing

- **Advanced ETL Pipeline**: Multi-stage document processing with AI-powered
  extraction, transformation, and loading
- **Multi-Model AI Integration**: Azure AI, OpenAI, LangChain agents with
  embedding, reasoning, and chat generation
- **Semantic Knowledge Base**: ChromaDB-powered vector storage with semantic
  search and relationship mapping
- **Natural Language Processing**: Entity extraction, relationship mapping, and
  knowledge graph construction
- **Mathematical Computation**: Complex calculations, tax computations, and
  financial analysis

### Enterprise Document Management

- **Multi-Format Processing**: PDF (forms/text), Excel (formulas/charts), Word
  (structure/media), CSV (large datasets), Images (OCR)
- **Template Engine**: Coordinate mapping, variable assignment, bulk processing,
  and reusable configurations
- **Version Control System**: Git-like timeline management with branching,
  merging, and visual diff capabilities
- **Multi-File Concurrency**: Browser-style tab management with state isolation
  and session persistence
- **Project Management**: VSCode-inspired file explorer with project-based
  organization

### Advanced User Experience

- **Real-Time Collaboration**: Multi-user editing with conflict resolution and
  change tracking
- **Annotation System**: Digital signatures, comments, highlights, drawings, and
  markup tools
- **Accessibility Compliance**: WCAG 2.1 AA compliance with screen reader and
  keyboard navigation support
- **Internationalization**: Multi-language support with localization and
  cultural adaptations
- **Performance Optimization**: Multi-level caching, lazy loading,
  virtualization, and resource management

## Comprehensive Feature Set

### AI and Machine Learning Capabilities

- **Document Analysis**: Intelligent classification, structure detection, and
  content analysis
- **Form Field Detection**: Computer vision-based field identification and type
  classification
- **OCR and Text Extraction**: Multi-language OCR with confidence scoring and
  quality assessment
- **Entity Recognition**: Named entity extraction, relationship mapping, and
  knowledge graph construction
- **Semantic Search**: Vector-based similarity search with faceted filtering and
  relevance scoring
- **Automated Form Filling**: Intelligent field mapping with validation and
  confidence scoring

### Document Processing Engine

- **PDF Processing**: Form field extraction, manipulation, generation,
  annotation, and signature support
- **Excel Integration**: Formula evaluation, chart extraction, data validation,
  and statistical analysis
- **Word Document Handling**: Structure preservation, media extraction, and
  formatting maintenance
- **CSV Processing**: Large dataset handling, type inference, validation, and
  transformation
- **Image Processing**: Enhancement, preprocessing, table detection, and
  computer vision analysis
- **Multi-Format Export**: Flexible output formats with customizable templates
  and styling

### Enterprise Architecture Features

- **Multi-Process Design**: Separate main, renderer, and worker processes for
  security and performance
- **Database Management**: SQLite with migrations, indexing, transactions, and
  backup/recovery
- **Security Framework**: AES-256 encryption, input validation, audit logging,
  and compliance features
- **Performance Monitoring**: Comprehensive metrics, profiling, health checks,
  and resource tracking
- **Error Handling**: Production-grade error recovery, logging, reporting, and
  user feedback systems
- **Testing Infrastructure**: Unit, integration, E2E, performance, and security
  testing with 95%+ coverage

### User Interface and Experience

- **Modern React UI**: TypeScript, TailwindCSS, DaisyUI with responsive design
  and theming
- **Advanced Components**: Monaco Editor, timeline viewer, diff viewer, form
  editor, and knowledge base interface
- **State Management**: Zustand stores with React Query for server state and
  caching
- **Animation System**: Framer Motion with performance optimization and
  accessibility considerations
- **Keyboard Shortcuts**: Comprehensive hotkey system with customization and
  conflict detection
- **Drag and Drop**: File import, tab reordering, form field mapping with visual
  feedback

### Timeline and Version Control

- **Checkpoint System**: Full application state capture with compression and
  deduplication
- **Undo/Redo Stack**: Action grouping, selective undo, and cross-session
  persistence
- **Branch Management**: Git-like branching with visualization, comparison, and
  merge capabilities
- **Visual Diff Engine**: Text, PDF, and structured data comparison with
  highlighting
- **Document Versioning**: Individual document history with delta calculation
  and restoration
- **Audit Trail**: Comprehensive change tracking with user attribution and
  compliance reporting

### Knowledge Base and Search

- **Vector Storage**: ChromaDB integration with embedding generation and
  similarity search
- **Knowledge Graph**: Entity relationships, graph traversal, and visualization
- **Semantic Indexing**: Automatic content indexing with metadata and context
  preservation
- **Query System**: Boolean search, fuzzy matching, temporal queries, and result
  aggregation
- **Data Management**: Import/export, backup/restore, cleanup, and analytics
- **Integration APIs**: External system integration with authentication and rate
  limiting

## Target Users and Use Cases

### Primary Users

- **Tax Professionals**: Complex tax form preparation with multi-source data
  integration
- **Legal Professionals**: Contract analysis, document review, and regulatory
  compliance
- **Financial Analysts**: Financial statement processing, data extraction, and
  reporting
- **Administrative Staff**: Bulk document processing, form completion, and data
  entry automation
- **Compliance Officers**: Regulatory filing, audit preparation, and
  documentation management

### Enterprise Use Cases

- **Tax Season Processing**: Automated tax form completion from bank statements,
  receipts, and financial documents
- **Legal Document Review**: Contract analysis, clause extraction, and
  compliance checking
- **Financial Reporting**: Automated report generation from multiple data
  sources
- **Regulatory Compliance**: Form completion for regulatory filings and
  compliance documentation
- **Data Migration**: Legacy document processing and data extraction for system
  migrations

### Advanced Workflows

- **Multi-Document Analysis**: Cross-referencing information across multiple
  related documents
- **Template-Based Processing**: Bulk processing of similar documents using
  saved templates
- **Collaborative Review**: Multi-user document review with annotations and
  approval workflows
- **Quality Assurance**: Automated validation, error detection, and consistency
  checking
- **Archive Processing**: Historical document digitization and information
  extraction

## Technical Excellence Standards

### Production Quality Requirements

- **Security First**: Enterprise-grade security with encryption, validation, and
  audit trails
- **Performance Optimized**: Sub-second response times with efficient resource
  utilization
- **Scalability Designed**: Support for large documents, concurrent users, and
  enterprise workloads
- **Reliability Assured**: 99.9% uptime with comprehensive error handling and
  recovery
- **Accessibility Compliant**: Full WCAG 2.1 AA compliance with assistive
  technology support
- **Maintainability Focused**: Clean architecture, comprehensive documentation,
  and testing

### Innovation and Differentiation

- **AI-First Approach**: Deep integration of multiple AI models for intelligent
  document processing
- **User-Centric Design**: Intuitive interface with powerful features accessible
  to non-technical users
- **Enterprise Ready**: Production-grade architecture with security, compliance,
  and scalability
- **Extensible Platform**: Plugin architecture for custom integrations and
  workflow extensions
- **Cross-Platform Excellence**: Native performance and user experience across
  all platforms

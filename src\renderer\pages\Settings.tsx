import React from 'react';
import { useAppNavigation } from '../router/RouterProvider';

const Settings: React.FC = () => {
  const { navigate } = useAppNavigation();

  return (
    <div className="min-h-screen bg-base-100 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <button
            className="btn btn-ghost btn-sm mb-4"
            onClick={() => navigate('/')}
          >
            ← Back to Dashboard
          </button>
          <h1 className="text-3xl font-bold text-base-content">Settings</h1>
          <p className="text-base-content/70">Application settings and preferences</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            className="bg-base-200 rounded-lg p-6 cursor-pointer hover:bg-base-300 transition-colors"
            onClick={() => navigate('/settings/ai')}
          >
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
              <svg
                className="w-6 h-6 text-primary"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">AI Configuration</h3>
            <p className="text-base-content/70 text-sm">
              Configure AI models, providers, and processing settings
            </p>
          </div>

          <div
            className="bg-base-200 rounded-lg p-6 cursor-pointer hover:bg-base-300 transition-colors"
            onClick={() => navigate('/settings/appearance')}
          >
            <div className="w-12 h-12 bg-secondary/10 rounded-full flex items-center justify-center mb-4">
              <svg
                className="w-6 h-6 text-secondary"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">Appearance</h3>
            <p className="text-base-content/70 text-sm">
              Customize themes, colors, and interface preferences
            </p>
          </div>

          <div
            className="bg-base-200 rounded-lg p-6 cursor-pointer hover:bg-base-300 transition-colors"
            onClick={() => navigate('/settings/advanced')}
          >
            <div className="w-12 h-12 bg-accent/10 rounded-full flex items-center justify-center mb-4">
              <svg
                className="w-6 h-6 text-accent"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100-4m0 4v2m0-6V4"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">Advanced</h3>
            <p className="text-base-content/70 text-sm">
              Advanced configuration options and developer settings
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
